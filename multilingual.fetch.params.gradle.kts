buildscript {
    // ***************** 公共参数 *****************
    // 特殊处理flavor列表
    project.extra.set("multilingual.fetch.flavor", arrayOf("gpark", "party"))
//    // 翻译语言 "en", "ja", "ko", "zh-Hant", "zh"
//    project.extra.set("multilingual.fetch.locale", arrayOf("en", "ja", "ko"))
    // 不同flavor支持的翻译语言 "en", "ja", "ko", "zh-Hant", "zh"
    project.extra.set("multilingual.fetch.gpark-locale", arrayOf("en"))
    project.extra.set("multilingual.fetch.party-locale", arrayOf("zh"))
    // 使用平台：vol、phrase
    project.extra.set("multilingual.fetch.platform.choose", "phrase")
    // ***************** end 公共参数 *****************

    // ***************** fetchTranslateFile-job 参数 *****************
    // 是否只修改values/strings.xml中有的key，不新增key：1不新增key、0新增key
    project.extra.set("multilingual.fetch.job.change.onlyAlreadyHave", "0")
    // phrase：翻译工作的job-tag
    project.extra.set("multilingual.fetch.job.phrase.jobtag", "job-89AE2F93")
    // ***************** end fetchTranslateFile-job 参数 *****************


    // ***************** fetchTranslateFile-all 参数 *****************
    // 检查网络翻译完整性失败时，是否仍然修改本地string文件：1失败不覆盖、0失败也覆盖
    project.extra.set("multilingual.fetch.all.check.completeness", "0")
    // 是否只修改values/strings.xml中有的key，不新增key：1不新增key、0新增key
    project.extra.set("multilingual.fetch.all.change.onlyAlreadyHave", "1")
    // 非values/strings.xml文件是否使用比较替换：1比较替换、0直接覆盖文件
    project.extra.set("multilingual.fetch.all.change.compare", "1")
    // 仅火山云：是否执行本地翻译版本检查（与上次拉取的版本信息比较一致时，不继续操作）：1执行检查、0不执行检查
    project.extra.set("multilingual.fetch.all.check.version", "0")
    // ***************** end fetchTranslateFile-job 参数 *****************
}