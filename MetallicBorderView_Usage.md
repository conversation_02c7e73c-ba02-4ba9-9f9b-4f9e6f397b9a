# MetallicBorderView 使用说明

## 概述

`MetallicBorderView` 是一个自定义View，用于绘制带有光照效果的金属边框。它支持：

- 可调节的光照角度（支持动态调整和动画）
- 可调节的边框厚度
- 透明的中心区域
- 高光和阴影效果
- 圆角矩形支持
- 多种金属材质预设

## 基本用法

### 1. 在XML布局中使用

```xml
<com.socialplay.gpark.ui.view.MetallicBorderView
    android:id="@+id/metallicBorder"
    android:layout_width="200dp"
    android:layout_height="100dp"
    app:borderThickness="2dp"
    app:lightAngle="45"
    app:cornerRadius="8dp"
    app:highlightColor="#FFFFFF"
    app:shadowColor="#000000"
    app:metallicBaseColor="#C0C0C0"
    app:highlightIntensity="0.8"
    app:shadowIntensity="0.3" />
```

### 2. 在代码中动态调整

```kotlin
val metallicBorder = findViewById<MetallicBorderView>(R.id.metallicBorder)

// 设置光照角度
metallicBorder.setLightAngle(135f)

// 设置边框厚度
metallicBorder.setBorderThickness(4f * density)

// 设置圆角半径
metallicBorder.setCornerRadius(16f * density)

// 设置高光强度
metallicBorder.setHighlightIntensity(0.9f)

// 设置金属基础颜色
metallicBorder.setMetallicBaseColor(Color.parseColor("#FFD700"))
```

## 属性说明

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `borderThickness` | dimension | 1dp | 边框厚度 |
| `lightAngle` | float | 45 | 光照角度（0-360度） |
| `cornerRadius` | dimension | 0dp | 圆角半径 |
| `highlightColor` | color | #FFFFFF | 高光颜色 |
| `shadowColor` | color | #000000 | 阴影颜色 |
| `metallicBaseColor` | color | #C0C0C0 | 金属基础颜色 |
| `highlightIntensity` | float | 0.8 | 高光强度（0.0-1.0） |
| `shadowIntensity` | float | 0.3 | 阴影强度（0.0-1.0） |

## 动画效果

### 1. 光照角度动画

```kotlin
// 动画旋转到指定角度
metallicBorder.animateLightAngle(180f, 1000)

// 开始连续旋转
metallicBorder.startContinuousRotation(5000) // 5秒一圈

// 停止连续旋转
metallicBorder.stopContinuousRotation()
```

### 2. 自定义动画

```kotlin
val animator = ValueAnimator.ofFloat(0f, 360f).apply {
    duration = 3000
    repeatCount = ValueAnimator.INFINITE
    addUpdateListener { animation ->
        val angle = animation.animatedValue as Float
        metallicBorder.setLightAngle(angle)
    }
}
animator.start()
```

## 预设样式

使用 `MetallicBorderPresets` 快速应用预设样式：

```kotlin
// 银色边框
MetallicBorderPresets.applySilverStyle(metallicBorder)

// 金色边框
MetallicBorderPresets.applyGoldStyle(metallicBorder)

// 铜色边框
MetallicBorderPresets.applyCopperStyle(metallicBorder)

// 钢铁边框
MetallicBorderPresets.applySteelStyle(metallicBorder)
```

## 作为容器使用

可以将 `MetallicBorderView` 作为容器的背景：

```xml
<FrameLayout
    android:layout_width="250dp"
    android:layout_height="120dp">

    <com.socialplay.gpark.ui.view.MetallicBorderView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:borderThickness="4dp"
        app:lightAngle="315"
        app:cornerRadius="16dp"
        app:metallicBaseColor="#9370DB" />

    <!-- 内容区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="4dp"
        android:background="#80000000"
        android:gravity="center">
        
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="金属边框容器"
            android:textColor="#FFFFFF" />
            
    </LinearLayout>

</FrameLayout>
```

## 性能优化建议

1. **避免频繁调用** - 不要在短时间内频繁调用 `setLightAngle()` 等方法
2. **使用动画** - 对于连续的角度变化，使用 `animateLightAngle()` 而不是手动循环调用
3. **合理设置强度** - 高光和阴影强度不需要设置得太高，0.3-0.9 之间通常就足够了
4. **复用View** - 在列表中使用时，注意复用View实例

## 常见问题

### Q: 为什么看不到光照效果？
A: 检查以下几点：
- 确保 `highlightIntensity` 和 `shadowIntensity` 不为0
- 确保 `borderThickness` 大于0
- 确保高光和阴影颜色与背景有足够对比度

### Q: 如何实现呼吸灯效果？
A: 可以通过动画改变高光强度：

```kotlin
val animator = ValueAnimator.ofFloat(0.3f, 0.9f).apply {
    duration = 1000
    repeatCount = ValueAnimator.INFINITE
    repeatMode = ValueAnimator.REVERSE
    addUpdateListener { animation ->
        metallicBorder.setHighlightIntensity(animation.animatedValue as Float)
    }
}
animator.start()
```

### Q: 如何实现多层边框效果？
A: 可以嵌套多个 `MetallicBorderView`：

```xml
<com.socialplay.gpark.ui.view.MetallicBorderView
    android:layout_width="200dp"
    android:layout_height="100dp"
    app:borderThickness="6dp"
    app:metallicBaseColor="#FFD700">
    
    <com.socialplay.gpark.ui.view.MetallicBorderView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="6dp"
        app:borderThickness="2dp"
        app:metallicBaseColor="#C0C0C0" />
        
</com.socialplay.gpark.ui.view.MetallicBorderView>
```
