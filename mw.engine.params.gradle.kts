buildscript {
    project.extra.set("mw.gradle.engine.configflavors", arrayOf("party", "gpark"))
    // Party配置
    val jenkinsEngineVersion = System.getenv("mw_engine_version") ?: System.getProperty("mw_engine_version")
    // 注意：如果改默认配置，请一起修改Version.PARTY_MW_ENGINE_VERSION中的版本号
    project.extra.set("party.mw.gradle.engine.version", jenkinsEngineVersion ?: "v0.43.0.0.20250211132010")
    project.extra.set("party.mw.gradle.engine.auto.config.project", arrayOf("run-party"))
    project.extra.set("party.mw.gradle.engine.service", "china")
    // 注意：如果要改这个配置，请一起修改Version.PARTY_MW_ENGINE_EXT
    project.extra.set("party.mw.gradle.engine.ext", "oodle_chunk")
    project.extra.set("party.mw.gradle.engine.abi", arrayOf(System.getenv("mw-engine-abi") ?: "arm64-v8a"))
    project.extra.set("party.mw.gradle.engine.encode", "1")
    project.extra.set("party.mw.gradle.engine.tag", "1")
    //服务地址
//    ext.set("mw.gradle.engine.test.host", "http://metaverse-api.metaworld.fun")
    //开启接口备份
    project.extra.set("party.mw.gradle.engine.need.backup", true)
    project.extra.set("party.mw.gradle.engine.test.host", "http://test1010-api.meta-verse.co")
    project.extra.set("party.mw.gradle.engine.dev.host", "http://dev-api.meta-verse.co")
    project.extra.set("party.mw.gradle.engine.pre.host", "http://pre-api.meta-verse.co")
    project.extra.set("party.mw.gradle.engine.oneline.host", "https://api.meta-verse.co")
    project.extra.set("party.mw.gradle.engine.main.project", "run-party")

    // ==========Gpark配置=====================
    // 支持Jenkins配置引擎版本
    // 注意：如果改默认配置，请一起修改Version.GPARK_MW_ENGINE_VERSION中的版本号
    project.extra.set("gpark.mw.gradle.engine.version", jenkinsEngineVersion ?: "v0.44.0.1.20250401085753")
    project.extra.set("gpark.mw.gradle.engine.service", "i18n")
    project.extra.set("gpark.mw.gradle.engine.auto.config.project", arrayOf("run-gpark-dev", "product"))
    // 注意：如果要改这个配置，请一起修改Version.GPARK_MW_ENGINE_EXT
    project.extra.set("gpark.mw.gradle.engine.ext", "zip")
    project.extra.set("gpark.mw.gradle.engine.abi", arrayOf("armeabi-v7a", "arm64-v8a"))
    project.extra.set("gpark.mw.gradle.engine.encode", "0")
    project.extra.set("gpark.mw.gradle.engine.tag", "1")
    //服务地址
    project.extra.set("gpark.mw.gradle.engine.test.host", "http://metaverse-api.metaworld.fun")
    //开启接口备份
    project.extra.set("gpark.mw.gradle.engine.need.backup", true)
    //Bundle打包
    project.extra.set("gpark.mw.gradle.engine.bundle.obb.project", "asset_engine_obb")
    //
    project.extra.set(
        "gpark.mw.gradle.engine.i18n.engine.file.alias",
        mapOf(
            "libUE4.so" to "libM6U2E5.so", //MetaAppUE4Engine => M6U2E5
            "main.obb.png" to "M3.O2.P2",//main.obb.png => M3.O2.P2
            "libc++_shared.so" to "",
            "libtry-alloc-lib.so" to "",
        )
    )
    project.extra.set("gpark.mw.gradle.engine.main.project", System.getProperty("MW_APP_PROJECT_NAME", "run-gpark-dev"))

    //OBB文件是否压缩
    project.extra.set("gpark.mw.gradle.engine.i18n.obb.file.compression", false)
    project.extra.set("gpark.mw.gradle.engine.i18n.obb.file.encrypt", false)

    //海外引擎下载地址Host映射
    project.extra.set(
        "gpark.mw.gradle.engine.i18n.engine.host.mapper",
        mapOf(
            "cdn-meta-verse-flow-pre.gpark.io" to "cdn-meta-verse-pre.233niu.cn",
            "qn-cdn-meta-verse-flow-pre.gpark.io" to "cdn-meta-verse-pre.233niu.cn",
            "cdn-meta-verse-flow.gpark.io" to "cdn-meta-verse-flow.233niu.cn",
            "qn-cdn-meta-verse-flow.gpark.io" to "cdn-meta-verse-flow.233niu.cn"
        )
    )
}