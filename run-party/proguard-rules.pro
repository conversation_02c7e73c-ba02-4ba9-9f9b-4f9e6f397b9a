# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

-keepattributes LineNumberTable
-renamesourcefileattribute MetaFile

# START 移动安全联盟 OAID 混淆配置
# sdk
-keep class com.bun.miitmdid.** { *; }
-keep interface com.bun.supplier.** { *; }
-keep class androidx.core.**{*;}
# asus
-keep class com.asus.msa.SupplementaryDID.** { *; }
-keep class com.asus.msa.sdid.** { *; }
# freeme
-keep class com.android.creator.** { *; }
-keep class com.android.msasdk.** { *; }
# huawei
-keep class com.huawei.hms.** {*;}
-keep interface com.huawei.hms.** {*;}
# lenovo
-keep class com.zui.deviceidservice.** { *; }
-keep class com.zui.opendeviceidlibrary.** { *; }
# meizu
-keep class com.meizu.flyme.openidsdk.** { *; }
# nubia
-keep class com.bun.miitmdid.provider.nubia.NubiaIdentityImpl
# oppo
-keep class com.heytap.openid.** { *; }
# samsung
-keep class com.samsung.android.deviceidservice.** { *; }
# vivo
-keep class com.vivo.identifier.** { *; }
# xiaomi
-keep class com.bun.miitmdid.provider.xiaomi.IdentifierManager
# zte
-keep class com.bun.lib.** { *; }
# coolpad
-keep class com.coolpad.deviceidsupport.** { *; }
# EEBBK
#None
# honor
-keep class com.hihonor.** {*; }
# END 移动安全联盟 OAID 混淆配置

# 数盟混淆配置
-keep class cn.shuzilm.core.** {*;}

# START EventBus混淆配置
-keepattributes *Annotation*
-keepclassmembers class * {
    @org.greenrobot.eventbus.Subscribe <methods>;
}
-keep enum org.greenrobot.eventbus.ThreadMode { *; }

# And if you use AsyncExecutor:
-keepclassmembers class * extends org.greenrobot.eventbus.util.ThrowableFailureEvent {
    <init>(java.lang.Throwable);
}
# END EventBus混淆配置
# 防止实体类混淆
-keep class com.meta.box.data.model.** { *; }

# Start 各部门反射安卓的API列表
-keep class com.meta.box.httpinit.HttpInit{ *; }
-keep class com.meta.analytics.libra.ToggleControl{ *; }
-keep class com.meta.analytics.Analytics { *; }
-keep class com.meta.analytics.Analytics$Builder { *; }
-keep class com.meta.analytics.Event{ *; }
-keep class com.socialplay.gpark.util.ChannelUtil{ *; }
-keep class com.meta.home.privilege.constant.UserPrivilegeKV{ *; }
-keep class com.meta.box.purchase.PurchaseReflectionStub{ *; }
-keep class com.meta.purchase.PurchaseReflectionStub{ *; }
-keep class com.meta.web.js.JsBridgeApi{ *; }
-keep class com.meta.web.js.JsBridgeHelper{ *; }
-keep class com.meta.box.floatball.QuitGameFloatView{ *; }
# End   各部门反射安卓的API列表
-keep class okhttp3.** { *; }
-keep class com.meta.p4n.** {*;}
-keep class com.meta.core.** { *; }
-keep class com.meta.box.data.base.DataResult { *; }
-keep class com.meta.box.data.base.ApiResult { *; }
-keep class com.meta.box.data.base.PagingApiResult { *; }
-keep class com.meta.box.data.base.DataResult$Status { *; }
-keep class com.meta.box.function.repair.MarketingType { *; }
-keep class com.meta.box.function.repair.RepairStatus { *; }
-keep class com.meta.box.function.repair.RepairParamsRequest { *; }
-keep class com.meta.box.ui.parental.PswdStatus { *; }
-keep class com.meta.box.ui.parental.GameManageStatus { *; }
-keep class com.meta.box.ui.parental.Status { *; }
-keep class com.meta.box.data.base.LoadType { *; }

-keep class com.meta.box.ui.view.stacklayoutmanager.** {*;}

# Start umeng混淆配置
-keep class com.umeng.** {*;}
-keep class com.uc.** { *; }
-keep class com.efs.** { *; }
-keepclassmembers class * {
   public <init> (org.json.JSONObject);
}
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}
#End umeng混淆配置

#Start TalkingData
-dontwarn com.tendcloud.tenddata.**
-keep class com.tendcloud.** {*;}
-keep public class com.tendcloud.tenddata.** { public protected *;}
-keepclassmembers class com.tendcloud.tenddata.**{
public void *(***);
}
-keep class com.talkingdata.sdk.TalkingDataSDK {public *;}
-keep class com.apptalkingdata.** {*;}
# TD 广告
-keep class com.talkingdata.sdk.** {*;}
-keep class com.tendcloud.** {*;}
-keep public class com.tendcloud.** {  public protected *;}

-keep class com.meta.box.gamelib.mv.bean.** { *; }
#End TalkingData

-keep class xiaofei.library.hermes.** {*;}

-keep @com.meta.box.util.KeepClass class * {*;}

# Start wechat混淆配置
-keep class com.tencent.mm.opensdk.** { *; }
-keep class com.tencent.wxop.** { *; }
-keep class com.tencent.mm.sdk.** { *; }
# End wechat混淆配置

#TrackingIO
###针对移动智能终端补充设备标识体系统一调用SDK###
-dontwarn org.bouncycastle.**
-keep class org.bouncycastle.** {*;}
-keep class XI.CA.XI.**{*;}
-keep class XI.K0.XI.**{*;}
-keep class XI.XI.K0.**{*;}
-keep class XI.xo.XI.XI.**{*;}
-keep class com.asus.msa.SupplementaryDID.**{*;}
-keep class com.asus.msa.sdid.**{*;}
-keep class com.bun.lib.**{*;}
-keep class com.bun.miitmdid.**{*;}
-keep class com.huawei.hms.ads.identifier.**{*;}
-keep class com.samsung.android.deviceidservice.**{*;}
-keep class com.zui.opendeviceidlibrary.**{*;}
-keep class org.json.**{*;}
-keep public class com.netease.nis.sdkwrapper.Utils {public <methods>;}
-dontwarn com.reyun.tracking.**
-keep class com.reyun.tracking.** {*;}
#TrackingIO
# Start aliyun 混淆配置
-keep class com.alibaba.sdk.android.oss.** { *; }
-dontwarn okio.**
-dontwarn org.apache.commons.codec.binary.**
# End aliyun 混淆配置
-keep class com.meta.box.BuildConfig { public <fields>; }

#融云
-keepattributes Exceptions,InnerClasses
-keepattributes Signature
-keep class io.rong.** {*;}
-keep class cn.rongcloud.** {*;}
-keep class * implements io.rong.imlib.model.MessageContent {*;}
-dontwarn io.rong.push.**
-dontnote com.xiaomi.**
-dontnote com.google.android.gms.gcm.**
-dontnote io.rong.**
-ignorewarnings
-keep class com.meta.box.function.im.ImNotificationReceiver {*;}
-keep class com.ly123.tes.mgs.im.emoticon.GifEmojiInfo { *; }
-keep class com.ly123.tes.mgs.im.emoticon.EmojiData { *; }
-keep class com.ly123.tes.mgs.im.emoticon.EmojiInfo { *; }



-keep class  com.meta.router.** {*;}
-keepclassmembers class com.meta.box.function.virtualcore.PluginWebViewDataDirFixer$WebViewProxy{*;}

-dontwarn com.google.zxing.**
-keep class com.google.zxing.**

-keep class **.R$* {*;}

-keep class com.tencent.imsdk.** { *; }
-keep class org.apache.tools.zip.** { *; }

-keep class com.meta.box.function.metaverse.bean.** { *; }

-keep class com.meta.box.ui.view.richeditor.** { *; }

# Gson 样例：https://github.com/google/gson/blob/master/examples/android-proguard-example/proguard.cfg
-dontwarn sun.misc.**

-keep class com.google.gson.Gson{
    <methods>;
}

-keep class * extends com.google.gson.TypeAdapter
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer
# Prevent R8 from leaving Data object members always null
-keepclassmembers,allowobfuscation class * {
  @com.google.gson.annotations.SerializedName <fields>;
}
-keep class com.google.gson.reflect.TypeToken
-keep class * extends com.google.gson.reflect.TypeToken
-keep public class * implements java.lang.reflect.Type
-keep class com.meta.box.function.metaverse.biztemp.** { *; }
-keep enum androidx.lifecycle.Lifecycle$State { *; }
-keep class androidx.** { *; }

-dontwarn com.tencent.bugly.**
-keep public class com.tencent.bugly.**{*;}
-keep public class com.meta.ipc.**{*;}

-keep class com.anxinxu.bugs.nowebview.WebViewFactoryReflection{*;}
-keep class com.anxinxu.bugs.nowebview.WebViewProviderResponseReflection{*;}
-keep class com.anxinxu.bugs.nowebview.GeolocationPermissionsReflection{*;}
-keep class com.anxinxu.bugs.nowebview.WebStorageReflection{*;}

-keep class com.meta.box.app.initialize.WebViewFix$WebViewFactoryReflection{*;}


-keep class com.anxinxu.lib.reflection.android.ActivityThreadReflection{*;}
-keep class com.anxinxu.lib.reflection.android.ServiceManagerReflection{*;}
-keep class com.anxinxu.lib.reflection.android.VMRuntimeReflection{*;}

# for assets pack
-keep class com.bytedance.pangle.plugin.Plugin{*;}
-keep class com.qq.e.comm.managers.plugin.PM{*;}

-keep class * implements com.meta.box.data.interactor.UpdateProcess
-keep class com.meta.box.function.virtualcore.lifecycle.SchemeGameLaunchParam

#Keep实体
-keep class com.meta.box.biz.friend.model.** { *; }
-keep class com.meta.box.biz.friend.internal.model.** { *; }
# 一键登录
-dontwarn com.cmic.gen.sdk.**
-keep class com.cmic.gen.sdk.**{*;}
-dontwarn com.sdk.**
-keep class com.sdk.** { *;}
-dontwarn com.unikuwei.mianmi.account.shield.**
-keep class com.unikuwei.mianmi.account.shield.** {*;}
-keep class cn.com.chinatelecom.account.api.**{*;}

-keep class * extends com.meta.box.util.CatchException

-keepclassmembers class * extends androidx.viewbinding.ViewBinding {
    public static *** bind(android.view.View);
}

-keepclassmembers class ** extends com.airbnb.mvrx.MavericksViewModel {
    ** Companion;
}
-keepclassmembers,includedescriptorclasses,allowobfuscation class ** implements com.airbnb.mvrx.MavericksState {
   *;
}
-keepnames class com.airbnb.mvrx.MavericksState
-keepnames class * implements com.airbnb.mvrx.MavericksState
-keepnames class * implements com.airbnb.mvrx.MavericksViewModelFactory

# viewpager2 allow loss state
-keep class androidx.viewpager2.adapter.FragmentStateAdapter {
   final androidx.fragment.app.FragmentManager mFragmentManager;
}
-keep class androidx.viewpager2.widget.ViewPager2 {
    private int mPendingCurrentItem;
    private android.os.Parcelable mPendingAdapterState;
}
-keep class androidx.viewpager2.widget.ViewPager2$SavedState {
    int mCurrentItem;
    android.os.Parcelable mAdapterState;
}
-keep class com.alipay.share.sdk.** {*;}



#小米SDK
-keep class com.xiaomi.** {*;}
-keep class com.wali.** {*;}
-keep class cn.com.wali.** {*;}
-keep class com.miui.**{*;}


# 腾讯QQ小游戏混淆Keep规则

# Keep class members annotated with @MiniKepp
# 保护代码中的注解不被混淆
-keepattributes *Annotation*
-keep,allowobfuscation @interface com.tencent.qqmini.sdk.annotation.MiniKeep
-keep @com.tencent.qqmini.sdk.annotation.MiniKeep class *
-keepclassmembers @com.tencent.qqmini.sdk.annotation.MiniKeep class ** {
    public <methods>; <fields>;
}
-keepclassmembers class * {
    @com.tencent.qqmini.sdk.annotation.MiniKeep *;
}

# Keep class members annotated with @JsEvent
-keep,allowobfuscation @interface com.tencent.qqmini.sdk.annotation.JsEvent
-keepclassmembers class * {
    @com.tencent.qqmini.sdk.annotation.JsEvent *;
}

# Keep minigame triton
-keep interface com.tencent.mobileqq.triton.** { *; }
-keep class com.tencent.mobileqq.triton.** { *; }
-keep @interface com.tencent.mobileqq.triton.jni.TTNativeModule, com.tencent.mobileqq.triton.jni.TTNativeCall
-keep @com.tencent.mobileqq.triton.jni.TTNativeModule class ** {
    @com.tencent.mobileqq.triton.jni.TTNativeCall <methods>;
    @com.tencent.mobileqq.triton.jni.TTNativeCall <fields>;
}
-keep @interface io.github.landerlyoung.jenny.NativeClass, io.github.landerlyoung.jenny.NativeFieldProxy, io.github.landerlyoung.jenny.NativeMethodProxy
-keep @io.github.landerlyoung.jenny.NativeClass class ** {
    @io.github.landerlyoung.jenny.NativeFieldProxy <fields>;
    @io.github.landerlyoung.jenny.NativeMethodProxy <methods>;
}

# Keep minigame sdk
-keep class * extends com.tencent.qqmini.sdk.launcher.ui.MiniActivity
-keep class com.tencent.qqmini.sdk.core.generated.** { *; }
-keep class com.tencent.qqmini.sdk.launcher.** { *; }
-keep class com.tencent.qqmini.sdk.MiniSDK { *; }
-keep class com.tencent.qqmini.sdk.MiniSDK$* { *; }
-keep class com.tencent.qqmini.sdk.BuildConfig { *; }
-keep class com.tencent.qqmini.sdk.annotation.** {* ;}
-keep class com.tencent.qqmini.sdk.utils.MiniSDKConst$AdConst{*;}
-keep class com.tencent.qqmini.sdk.receiver.** {* ;}
-keepclassmembers class com.tencent.qqmini.sdk.** {
    @android.webkit.JavascriptInterface <methods>;
}
-keep class com.tencent.qqmini.sdk.core.proxy.service.ChannelProxyDefault { *; }

# protocol: should keep field name because reflection
-keep class cooperation.** { *; }
-keep class com.tencent.mobileqq.pb.MessageMicro { *; }
-keepclassmembers class * extends com.tencent.mobileqq.pb.MessageMicro {
    <fields>;
}

# extra_ad
-keep class com.tencent.qqmini.ad.** {* ;}

# ad
-keep class com.qq.e.** {* ;}

# extraad模块直接引用了协议，先keep
-keep class NS_MINI_AD.** { *; }
-keep class NS_QWEB_PROTOCAL.**  { *; }
-keep class com.tencent.mobileqq.pb.PBStringField {*;}
-keep class com.tencent.qqmini.sdk.request.ProtoBufRequest {
    public *;
}

# 微信互联登录
-keep class com.tencent.mm.opensdk.** {
    *;
}
-keep class com.tencent.wxop.** {
    *;
}
-keep class com.tencent.mm.sdk.** {
    *;
}
#快手sdk
-keep class com.kwai.opensdk.sdk.** {*;}
-keep class com.kwai.auth.** {*;}



-dontwarn com.qq.gdt.action.**
-keep class com.qq.gdt.action.** {*;}

#OrmLite，sqlcipher
-keep class com.j256.** { *; }
-keep class com.j256.**
-keepclassmembers class com.j256.**
-keep enum com.j256.**
-keepclassmembers enum com.j256.**
-keep interface com.j256.**
-keepclassmembers interface com.j256.**
-dontwarn net.sqlcipher.**
-keep class net.sqlcipher.** {*;}
#**************************************imkf客服**必要配置**************************************


-keep class net.sqlcipher.** { *; }
-keep class net.sqlcipher.database.* { *; }

-keep class androidx.lifecycle.** { *; }


-keep class com.meta.biz.mgs.data.** { *; }
-keep class com.meta.biz.mgs.im.** { *; }
-keep class com.ly123.metacloud.data.** { *; }
-keep class com.ly123.tes.mgs.metacloud.origin.** { *; }
-keep class com.meta.lib.api.resolve.data.model.** { *; }

-keep class com.bytedance.ads.convert.broadcast.common.EncryptionTools {*;}

# 防止实体类成员混淆
-keep class com.socialplay.gpark.data.model.** { *; }

# Keep the Navigation classes
-keep class androidx.navigation.** { *; }
# Keep all Fragment classes that are used in the Navigation graph
-keep public class * extends androidx.fragment.app.Fragment
# Keep all classes that are used as arguments in the Navigation graph
-keepclassmembers class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

-keep class com.meta.sdk.open.** {*;}
-keep class com.meta.sdk.open.mix.** {*;}