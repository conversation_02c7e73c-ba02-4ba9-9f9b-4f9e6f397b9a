package com.meta.web.jsb

import com.meta.lib.web.core.WebCore
import com.meta.web.contract.IWebPlatformContract
import com.meta.web.helper.WebNavigationHelper
import com.meta.web.model.StandaloneWebFragmentArgs
import com.meta.web.ui.StandaloneWebCoreFragment

internal class StandaloneNativeInterfaceImpl(
    webCore: WebCore,
    platformContract: IWebPlatformContract,
    private val fragment: StandaloneWebCoreFragment,
    private val args: StandaloneWebFragmentArgs,
    private val navigationHelper: WebNavigationHelper,
) : FragmentNativeInterfaceImpl(webCore, platformContract, fragment) {


    override fun _closeAll(removeWebView: Boolean) {
        navigationHelper.navigateToPreviousPage()
    }

    override fun _goBack(): Boolean {
        navigationHelper.goBack(true, 1)
        return true
    }
}