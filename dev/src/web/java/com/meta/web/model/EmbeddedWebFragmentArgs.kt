package com.meta.web.model

import android.os.Parcelable
import androidx.annotation.IntRange
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.util.extension.NavParams
import kotlinx.parcelize.Parcelize

@Parcelize
data class EmbeddedWebFragmentArgs(
    override val extras: TypedMap = TypedMap(),
    override val url: String,
    override val resIdBean: ResIdBean,
    val title: String? = null,
    override val from: String? = null,
    @IntRange(-1, 100) override val textZoom: Int = -1,
    override val preloadUniqueId: String?,
    override val releaseWhenNavigateUp: Boolean,
) : Parcelable, NavParams, BasicWebArgs