package com.meta.web.ui

import android.os.Bundle
import android.view.WindowManager
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.clearFragmentResult
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.setFragmentResultListener
import com.airbnb.mvrx.args
import com.airbnb.mvrx.asMavericksArgs
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.meta.web.constans.EventConstants
import com.meta.web.constans.PageNameConstants
import com.meta.web.model.WebMenuDialogArgs
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.DialogWebMenuBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.ui.core.BaseBottomSheetDialogFragment
import com.socialplay.gpark.util.extension.setHeight
import com.socialplay.gpark.util.property.viewBinding
import java.util.UUID

class WebMenuDialog : BaseBottomSheetDialogFragment() {

    companion object {
        private const val RESULT_KEY_ACTION = "action"

        private const val REFRESH = 1
        private const val FEEDBACK = 2
        private const val COPY_LINK = 3

        fun show(
            fragmentManager: FragmentManager,
            url: String,
            onRefresh: () -> Unit,
            onFeedback: () -> Unit,
            onCopyLink: () -> Unit
        ) {
            val reqKey = UUID.randomUUID().toString()
            val dialog = WebMenuDialog()
            dialog.arguments = WebMenuDialogArgs(url, reqKey).asMavericksArgs()
            dialog.show(fragmentManager, WebMenuDialog::class.java.simpleName)
            dialog.setFragmentResultListener(reqKey) { _, bundle ->
                dialog.clearFragmentResult(reqKey)

                if (bundle.containsKey(RESULT_KEY_ACTION)) {
                    when (bundle.getInt(RESULT_KEY_ACTION)) {
                        REFRESH -> {
                            onRefresh()
                        }

                        FEEDBACK -> {
                            onFeedback()
                        }

                        COPY_LINK -> {
                            onCopyLink()
                        }
                    }
                }
            }
        }
    }

    override val binding by viewBinding(DialogWebMenuBinding::inflate)

    private val args by args<WebMenuDialogArgs>()


    override fun init() {
        dialog?.apply {
            getBottomSheet()?.setHeight(WindowManager.LayoutParams.WRAP_CONTENT)
            behavior.peekHeight = BottomSheetBehavior.PEEK_HEIGHT_AUTO
            behavior.state = BottomSheetBehavior.STATE_EXPANDED
        }

        Analytics.track(
            EventConstants.EVENT_WEB_COMMON_POP_SHOW, mapOf(
                "url" to args.url
            )
        )

        binding.llCopyLink.setOnClickListener {
            setActionButtonClickAnalytics("copylink")
            setResult(COPY_LINK)
        }

        binding.llRefresh.setOnClickListener {
            setActionButtonClickAnalytics("Refresh")
            setResult(REFRESH)
        }

        binding.llFeedback.setOnClickListener {
            setActionButtonClickAnalytics("feedback")
            setResult(FEEDBACK)
        }
    }

    override fun needCountTime() = false

    override fun getPageName() = PageNameConstants.FRAGMENT_NAME_WEB_MENU

    private fun setActionButtonClickAnalytics(button: String) {
        Analytics.track(
            EventConstants.EVENT_WEB_COMMON_POP_CLICK, mapOf(
                "url" to args.url,
                "button" to button
            )
        )
    }


    override fun getTheme() = R.style.CustomBottomDialog
    override fun getStyle() = R.style.CustomBottomDialog

    private fun setResult(action: Int? = null) {
        setFragmentResult(args.reqKey, Bundle().apply {
            action?.let { putInt(RESULT_KEY_ACTION, it) }
        })
        dismiss()
    }

    override var heightPercent = 0.0f
}