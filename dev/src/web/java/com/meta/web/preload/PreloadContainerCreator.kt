package com.meta.web.preload

import com.meta.biz.web.assets.WebAssetsBiz
import com.meta.lib.web.core.assets.IWebAssetsProvider
import com.meta.lib.web.core.preload.AbsPreloadWebContainer
import com.meta.lib.web.core.preload.IPreloadContainerCreator
import com.meta.lib.web.core.preload.PreloadArgs
import com.meta.lib.web.core.preload.PreloadFragment
import com.meta.web.contract.IWebPlatformContract
import com.meta.web.model.TypedMap
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import org.koin.core.context.GlobalContext

internal class PreloadContainerCreator : IPreloadContainerCreator{

    override fun createContainer(
        fragment: PreloadFragment,
        preloadArgs: PreloadArgs
    ): AbsPreloadWebContainer {

        val platformContract = GlobalContext.get().get<IWebPlatformContract>()
        val context = fragment.requireContext()

        val webArgs = PreloadWebArgs(
            TypedMap(),
            preloadArgs.url,
            ResIdBean.newInstance(),
            -1,
            null,
            preloadArgs.preloadId,
            false
        )
        return PreloadWebContainer(fragment, platformContract, context, webArgs)
    }

    override fun getWebAssetsProvider(): IWebAssetsProvider? {
        return WebAssetsBiz.assetsProvider
    }
}