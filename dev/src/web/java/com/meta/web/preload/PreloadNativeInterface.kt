package com.meta.web.preload

import com.meta.lib.web.core.WebCore
import com.meta.lib.web.core.jsb.BaseNativeInterface
import com.meta.web.contract.IWebPlatformContract
import timber.log.Timber

internal class PreloadNativeInterface(
    webCore: WebCore,
    private val platformContract: IWebPlatformContract
) : BaseNativeInterface(webCore) {

    var legacyJsApi: IWebPlatformContract.LegacyJsApi? = null

    override fun _closeAll(removeWebView: Boolean) {}

    override fun _executeLegacyJsApi(json: String): String? {
        if(legacyJsApi == null){
            Timber.d("_executeLegacyJsApi preload ${legacyJsApi}")
        }
        return legacyJsApi?.exec(json)
    }

    override fun _goBack(): Boolean {
        return false
    }

}

