<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <FrameLayout
        android:id="@+id/fl_web_container"
        android:layout_width="match_parent"
        android:focusableInTouchMode="true"
        android:focusable="true"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        />


    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/v_loading"
        android:background="@color/white"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@id/fl_web_container"
        app:layout_constraintTop_toTopOf="@id/fl_web_container"
        android:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>