package com.socialplay.gpark.app.initialize

import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.data.kv.BuildConfigKV
import com.tencent.mmkv.MMKV
import timber.log.Timber
import java.lang.reflect.Field

object LibBuildConfigInit {

    val buildConfigMMKV = MMKV.mmkvWithID("id_build_config", MMKV.MULTI_PROCESS_MODE)!!
    val buildCfg = BuildConfigKV(buildConfigMMKV)

    private var buildConfigClazz: Class<BuildConfig> = BuildConfig::class.java

    fun initConfig() {
        val alreadyOpenDev = buildCfg.isAlreadyOpenDev()
        Timber.d("initConfig isOpenDeveloper:$alreadyOpenDev")
        if (!alreadyOpenDev) {
            return
        }
        buildConfigClazz.fields.forEach {
            val fieldName = it.name
            if (buildCfg.hasKey(fieldName)) {
                val oldFieldValue = getFieldValue(fieldName)
                var newFieldValue: Any? = null
                when (oldFieldValue) {
                    is String -> {
                        newFieldValue = buildCfg.getString(fieldName)
                    }

                    is Int -> {
                        newFieldValue = buildCfg.getInt(fieldName)
                    }

                    is Long -> {
                        newFieldValue = buildCfg.getLong(fieldName)
                    }

                    is Boolean -> {
                        newFieldValue = buildCfg.getBoolean(fieldName)
                    }

                    is Float -> {
                        newFieldValue = buildCfg.getFloat(fieldName)
                    }
                }
//                Timber.d("initConfig 1 BaseUrl:${BuildConfig.BASE_URL} ,ENV_TYPE:${BuildConfig.ENV_TYPE}  fieldName:$fieldName oldFieldValue:$oldFieldValue, newFieldValue:$newFieldValue setAfter:${getFieldValue(fieldName)}")
                if (null != newFieldValue) {
                    updateField(fieldName, newFieldValue)
                }

                Timber.d("initConfig 2 BaseUrl:${BuildConfig.BASE_URL} ,ENV_TYPE:${BuildConfig.ENV_TYPE}  fieldName:$fieldName oldFieldValue:$oldFieldValue, newFieldValue:$newFieldValue setAfter:${getFieldValue(fieldName)}")
            }
        }
    }

    fun fields(): List<String> {
        return buildConfigClazz.fields.map {
            it.name
        }
    }


    fun getFieldValue(fieldName: String): Any? {
        return getField(fieldName)?.get(buildConfigClazz)
    }

    private fun getField(fieldName: String): Field? {
        return kotlin.runCatching { buildConfigClazz.getField(fieldName) }
            .getOrNull()
    }

    /**
     *
     */
    fun remove(key: String) {
        buildCfg.removeKey(key)
    }

    /**
     *
     */
    fun updateField(fieldName: String, fieldValue: Any) {
        val field = getField(fieldName)
        if (null != field) {
            val success = runCatching {
                field.isAccessible = true
                field.set(buildConfigClazz, fieldValue)
                true
            }.getOrElse { false }
            if (success) {
                when (fieldValue) {
                    is String -> {
                        buildCfg.putString(fieldName, fieldValue)
                    }

                    is Int -> {
                        buildCfg.putInt(fieldName, fieldValue)
                    }

                    is Boolean -> {
                        buildCfg.putBoolean(fieldName, fieldValue)
                    }

                    is Float -> {
                        buildCfg.putFloat(fieldName, fieldValue)
                    }

                    is Long -> {
                        buildCfg.putLong(fieldName, fieldValue)
                    }
                }
            }

        } else {
            Timber.e("fieldName: $fieldName Not Find!")
        }

    }

}