package com.socialplay.gpark.data.interactor

import android.app.Application
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Context.RECEIVER_EXPORTED
import android.content.Intent
import android.content.IntentFilter
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkInfo
import android.net.NetworkRequest
import android.os.Build
import android.os.SystemClock
import android.telephony.TelephonyManager
import androidx.lifecycle.LifecycleOwner
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.data.model.NetStatus
import com.socialplay.gpark.data.model.NetType
import com.socialplay.gpark.util.extension.LifecycleCallback
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/08/13
 * desc   :
 * </pre>
 */

typealias NetworkChangedCallback = (old: NetType, new: NetType) -> Unit

class NetworkInteractor(private val application: Application) {

    @Volatile
    var netStatus: NetStatus = NetStatus.Unknown
        private set

    @Volatile
    var netType: NetType = NetType.Unknown
        private set

    private val callbacks = LifecycleCallback<NetworkChangedCallback>()
    private val scope = MainScope()

    private var curMills = 0L
    private val networkReceiver: BroadcastReceiver by lazy {
        object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                Timber.d("anxin_network onReceive")
                updateNetType()
            }
        }
    }

    private val networkCallback: ConnectivityManager.NetworkCallback by lazy {
        object : ConnectivityManager.NetworkCallback() {
            override fun onAvailable(network: Network) {
                super.onAvailable(network)
                Timber.d("anxin_network onAvailable")
                updateNetType()
            }

            override fun onLost(network: Network) {
                super.onLost(network)
                Timber.d("anxin_network onLost")
                updateNetType()
            }
        }
    }

    init {
        val connectivityManager = application.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager?
        registerNetworkReceiver(application)
        when {
            connectivityManager == null                    -> {
                Timber.d("connectivityManager == null")
            }
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.N -> {
                kotlin.runCatching {
                    connectivityManager.registerDefaultNetworkCallback(networkCallback)
                }
            }
            else                                           -> {
                kotlin.runCatching {
                    connectivityManager.registerNetworkCallback(NetworkRequest.Builder().build(), networkCallback)
                }
            }
        }
    }

    fun addNetworkChangedCallback(callback: NetworkChangedCallback) {
        callbacks.addCallback(callback)
    }

    fun removeNetworkChangedCallback(callback: NetworkChangedCallback) {
        callbacks.removeCallback(callback)
    }

    fun observeNetworkChanged(owner: LifecycleOwner, callback: NetworkChangedCallback) {
        callbacks.observe(owner, callback)
    }

    private fun registerNetworkReceiver(application: Application) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            application.registerReceiver(networkReceiver, IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION), RECEIVER_EXPORTED)
        } else {
            application.registerReceiver(networkReceiver, IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION))
        }
    }

    fun isWifiConnected(): Boolean {
        return isNetworkAvailable() && (netType == NetType.Wifi || netType == NetType.Unknown)
    }

    fun isStrictWifiConnected() = isNetworkAvailable() && netType == NetType.Wifi

    fun isNetworkAvailable(): Boolean {
        getNetTypeSafe()
        return netStatus != NetStatus.Unavailable
    }

    fun getNetTypeSafe(): String {
        return if (SystemClock.elapsedRealtime() - curMills < 500) {
            netType.desc
        } else {
            curMills = SystemClock.elapsedRealtime()
            updateNetType()
            netType.desc
        }
    }

    suspend fun getNetType(): String = withContext(Dispatchers.IO) {
        curMills = SystemClock.elapsedRealtime()
        updateNetType()
        netType.desc
    }


    private fun updateNetType() {
        scope.launch(Dispatchers.IO){
            try {
                val newNetStatus: NetStatus
                val newNetType: NetType
                val cm = application.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager? ?: return@launch
                val mobileNetInfo = cm.getNetworkInfo(ConnectivityManager.TYPE_MOBILE)
                val wifiNetInfo = cm.getNetworkInfo(ConnectivityManager.TYPE_WIFI)
                var activeNetInfo = cm.activeNetworkInfo
                newNetStatus = if (activeNetInfo == null) {
                    if (mobileNetInfo != null && (mobileNetInfo.isConnected || mobileNetInfo.isConnectedOrConnecting)) {
                        activeNetInfo = mobileNetInfo
                        NetStatus.Mobile
                    } else if (wifiNetInfo != null && (wifiNetInfo.isConnected || wifiNetInfo.isConnectedOrConnecting)) {
                        activeNetInfo = wifiNetInfo
                        NetStatus.Wifi
                    } else {
                        NetStatus.Unavailable
                    }
                } else {
                    if (activeNetInfo.isConnected || activeNetInfo.isConnectedOrConnecting) {
                        if (mobileNetInfo != null && (mobileNetInfo.isConnected || mobileNetInfo.isConnectedOrConnecting)) {
                            NetStatus.Mobile
                        } else {
                            NetStatus.Wifi
                        }
                    } else if (mobileNetInfo != null && (mobileNetInfo.isConnected || mobileNetInfo.isConnectedOrConnecting)) {
                        activeNetInfo = mobileNetInfo
                        NetStatus.Mobile
                    } else if (wifiNetInfo != null && (wifiNetInfo.isConnected || wifiNetInfo.isConnectedOrConnecting)) {
                        activeNetInfo = wifiNetInfo
                        NetStatus.Wifi
                    } else {
                        NetStatus.Unavailable
                    }
                }
                newNetType = when (netStatus) {
                    NetStatus.Unknown,
                    NetStatus.Unavailable -> NetType.Unknown
                    NetStatus.Wifi        -> NetType.Wifi
                    NetStatus.Mobile      -> getNetTypeSafe(activeNetInfo)
                }
                Timber.d("anxin_network oldType:%s oldStatus:%s newNetType:%s newNetStatus:%s", netType, netStatus, newNetType, newNetStatus)

                if (newNetType != netType || newNetStatus != netStatus) {
                    synchronized(this) {
                        if (newNetType != netType || newNetStatus != netStatus) {
                            if (newNetType != netType) {
                                dispatchNetworkChanged(netType, newNetType)
                            }
                            netType = newNetType
                            netStatus = newNetStatus
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                if (BuildConfig.DEBUG) {
                    throw e
                }
            }
        }
    }

    private fun dispatchNetworkChanged(oldType: NetType, newType: NetType) {
        if (BuildConfig.LOG_DEBUG) {
            Timber.d("dispatchNetworkChanged netStatus:$netStatus, netType:$oldType->$newType")
        }
        callbacks.post {
            if (netType == newType) {
                this.invoke(oldType, newType)
            }
        }
    }

    private fun getNetTypeSafe(activeNetInfo: NetworkInfo?): NetType {
        return when (activeNetInfo?.subtype) {
            TelephonyManager.NETWORK_TYPE_UNKNOWN       -> NetType.Unknown
            TelephonyManager.NETWORK_TYPE_GSM,
            TelephonyManager.NETWORK_TYPE_EDGE,
            TelephonyManager.NETWORK_TYPE_GPRS,
            TelephonyManager.NETWORK_TYPE_CDMA,
            TelephonyManager.NETWORK_TYPE_1xRTT,
            TelephonyManager.NETWORK_TYPE_IDEN          -> NetType.M2G
            TelephonyManager.NETWORK_TYPE_UMTS,
            TelephonyManager.NETWORK_TYPE_EVDO_0,
            TelephonyManager.NETWORK_TYPE_EVDO_A,
            TelephonyManager.NETWORK_TYPE_HSDPA,
            TelephonyManager.NETWORK_TYPE_HSUPA,
            TelephonyManager.NETWORK_TYPE_HSPA,
            TelephonyManager.NETWORK_TYPE_EVDO_B,
            TelephonyManager.NETWORK_TYPE_EHRPD,
            TelephonyManager.NETWORK_TYPE_HSPAP,
            TelephonyManager.NETWORK_TYPE_TD_SCDMA      -> NetType.M3G
            TelephonyManager.NETWORK_TYPE_LTE,
            TelephonyManager.NETWORK_TYPE_IWLAN,
            19 /*TelephonyManager.NETWORK_TYPE_LTE_CA*/ -> NetType.M4G
            20 /*TelephonyManager.NETWORK_TYPE_NR*/     -> NetType.M5G
            else                                        -> NetType.Unknown
        }
    }

}