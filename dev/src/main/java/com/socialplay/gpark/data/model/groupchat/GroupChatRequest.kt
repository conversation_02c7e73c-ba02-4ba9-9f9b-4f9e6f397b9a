package com.socialplay.gpark.data.model.groupchat

/**
 * 群成员列表请求参数
 */
data class MgsGroupChatMemberListRequest(
    /**
     * 群聊成员最后变更时间 查询第一页时如果不知道当前最新的变更时间，可以不传
     */
    val memberLastChange: Long?,
    /**
     * 加载的滚动id
     */
    val rollId: String?,
    /**
     * 群聊id
     */
    val chatGroupId: String,
    /**
     * 加载数量
     */
    val size: Int,
)

/**
 * 用户群聊列表请求参数
 */
data class MgsGroupChatListRequest(
    /**
     * 需要查询群聊列表的目标用户
     */
    val targetUid: String?,
    /**
     * 搜索方式
     * all 查询所有的群聊，
     * create 搜索当前用户创建的群聊
     * join 搜索当前用户加入的群聊
     */
    val searchType: String?,
    /**
     * 查询滚动id
     */
    val rollId: String?,
    /**
     * 是否获取当前用户加入状态
     */
    val checkJoin: <PERSON>olean,
    /**
     * 查询数量
     */
    val size: Int,
) {
    companion object {
        const val SEARCH_TYPE_ALL = "all"
        const val SEARCH_TYPE_CREATE = "create"
        const val SEARCH_TYPE_JOIN = "join"
    }
}

/**
 * 搜索群聊信息
 */
data class MgsGroupChatCountRequest(
    /**
     * 搜索的uuid
     */
    val targetUuid: String?,
)


/**
 * 搜索群聊信息
 */
data class MgsGroupChatSearchRequest(
    /**
     * 搜索的Key
     */
    val searchKey: String?,
)

/**
 * 群聊id请求体
 */
data class MgsGroupChatIdRequest(
    /**
     * 群聊id
     */
    val chatGroupId: Long?,
)


data class MgsGroupApplyPageRequest(
    val pageNum: Int?,
    val pageSize: Int?,
    /**
     * 要查询指定的群聊id
     */
    val chatGroupId: Long? = null,
)

/**
 * 群聊申请加入
 */
data class MgsGroupChatApplyJoinRequest(
    /**
     * 群聊id
     */
    val chatGroupId: Int?,
    /**
     * 申请理由
     */
    val reason: String?,
)

/**
 * 群聊申请审核
 */
data class MgsGroupChatApplyJoinProcessRequest(
    /**
     * 申请记录id
     */
    val askId: Long?,
    /**
     * 审核状态
     * 1 拒绝
     * 2 同意
     */
    val status: Int?,
) {
    companion object {
        const val STATUS_REFUSE = 1
        const val STATUS_AGREE = 2
    }
}

/**
 * 创建群聊
 */
data class MgsGroupChatCreateRequest(
    /**
     * 群聊名称
     */
    val name: String?,
    /**
     * 群聊icon
     */
    val icon: String?,
    /**
     * 群聊描述
     */
    val describe: String?,
    /**
     * 群聊公告
     */
    val announcement: String?,
    /**
     * 选择的群成员列表的用户id列表
     */
    val members: List<String>?
)

/**
 * 编辑群基础信息
 */
data class MgsGroupChatEditRequest(
    /**
     * 群聊id
     */
    val chatGroupId: Long?,
    /**
     * 群聊名称 为null表示不修改此项
     */
    val name: String? = null,
    /**
     * 群聊icon 为null表示不修改此项
     */
    val icon: String? = null,
    /**
     * 群聊描述 为null表示不修改此项
     */
    val describe: String? = null,
    /**
     * 群聊公告 为null表示不修改此项
     */
    val announcement: String? = null,
    /**
     * 成员进入群方式
     * 1 任何人都可以进入
     * 2 通过申请可以进入
     * 为null表示不修改此项
     */
    val joinType: Int? = null,
    /**
     * 邀请入群方式
     * 1 成员都可以邀请进入
     * 2 管理员邀请才能进入
     * 为null表示不修改此项
     */
    val inviteType: Int? = null,
)

/**
 * 邀请群成员
 */
data class MgsGroupChatInviteMembersRequest(
    /**
     * 群聊id
     */
    val chatGroupId: Long?,
    /**
     * 选择的群成员列表
     */
    val members: List<String>?
)

/**
 * 踢出群组成员
 */
data class MgsGroupChatRemoveMemberRequest(
    /**
     * 群聊id
     */
    val chatGroupId: Long?,
    /**
     * 群成员id
     */
    val member: String?
)

/**
 * 编辑群组成员通知开关
 */
data class MgsGroupChatEditNotificationRequest(
    /**
     * 群聊id
     */
    val chatGroupId: Long?,
    /**
     * 是否打开通知开关
     */
    val enableNotifications: Boolean?
)

/**
 * 编辑群组成员通知开关
 */
data class MgsGroupChatEditMemberPowerRequest(
    /**
     * 群聊成员id
     */
    val member: String?,
    /**
     * 群聊id
     */
    val chatGroupId: Long?,
    /**
     * 权限
     * 1 所有者
     * 2 管理者
     * 3 普通成员
     */
    val power: Int?
)

/**
 * 加载群聊成员
 */
data class MgsGroupChatMembersRequest(
    /**
     * 群聊成员最后变更时间 查询第一页时如果不知道当前最新的变更时间，可以不传
     */
    val memberLastChange: Long?,
    /**
     * 加载的滚动id
     */
    val rollId: Long?,
    /**
     * 群聊id
     */
    val chatGroupId: String?,
    /**
     * 加载数量
     */
    val size: Int?,
    /**
     * 是否重新加载成员 默认false
     * 主要用于传入了成员变更时间时，如果传入的时间与最新时间相同，并且该值为false,说明不需要重新加载成员，直接返回
     */
    val reLoading: Boolean?,
)

data class MgsGetSimpleGroupInfoByImIdsRequest(
    val imIds:List<String>
)