package com.socialplay.gpark.data.repository

import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import com.google.gson.reflect.TypeToken
import com.meta.biz.ugc.local.EditorLocalHelper
import com.meta.biz.ugc.model.EditorConfigJsonEntity
import com.meta.biz.ugc.model.EditorTemplate
import com.socialplay.gpark.data.base.ApiResult
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.DataSource
import com.socialplay.gpark.data.base.code
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.base.suspendApi
import com.socialplay.gpark.data.base.suspendApiNotNull
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.data.local.CacheKey
import com.socialplay.gpark.data.local.SimpleDiskLruCache
import com.socialplay.gpark.data.mapper.MetaMapper
import com.socialplay.gpark.data.model.RoleGameIdConfig
import com.socialplay.gpark.data.model.editor.DefaultRoleInfo
import com.socialplay.gpark.data.model.editor.DeleteRoleStyleRequest
import com.socialplay.gpark.data.model.editor.EditorCreationShowInfo
import com.socialplay.gpark.data.model.editor.EditorLocalStatusInfo
import com.socialplay.gpark.data.model.editor.EditorNotice
import com.socialplay.gpark.data.model.editor.FormworkList
import com.socialplay.gpark.data.model.editor.LikeRoleStyleRequest
import com.socialplay.gpark.data.model.editor.MultiTsGameResult
import com.socialplay.gpark.data.model.editor.MyCreationsV3Request
import com.socialplay.gpark.data.model.editor.NoticeWrapper
import com.socialplay.gpark.data.model.editor.PinPgcRequest
import com.socialplay.gpark.data.model.editor.PinUgcRequest
import com.socialplay.gpark.data.model.editor.ReqFormWorkArchiveBody
import com.socialplay.gpark.data.model.editor.ReqFormWorkV4Body
import com.socialplay.gpark.data.model.editor.RoleOtherStyleListRequest
import com.socialplay.gpark.data.model.editor.TSTypeInfo
import com.socialplay.gpark.data.model.editor.UgcBannerInfo
import com.socialplay.gpark.data.model.editor.UgcFormWorkArchiveData
import com.socialplay.gpark.data.model.editor.UgcFormWorkV4Data
import com.socialplay.gpark.data.model.editor.UgcGameConfig
import com.socialplay.gpark.data.model.editor.UgcGameInfo
import com.socialplay.gpark.data.model.editor.cloud.UgcBackupInfo
import com.socialplay.gpark.data.model.editor.cloud.UgcCloudProject
import com.socialplay.gpark.data.model.post.PostCardResult
import com.socialplay.gpark.data.repository.api.editor.*
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.profile.ugc.ProfilePublishedUgcModelState
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.extension.ifEmptyDefault
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.singleOrNull
import org.koin.core.context.GlobalContext

/**
 * Created by bo.li
 * Date: 2022/3/7
 * Desc: 移动编辑器
 */
class EditorRepository(private val repository: com.socialplay.gpark.data.IMetaRepository, private val metaApi: com.socialplay.gpark.data.api.MetaApi, private val metaKV: MetaKV, private val cache: SimpleDiskLruCache, private val metaMapper: MetaMapper) {

    companion object {
        val DEFAULT_PLACEHOLDER_AVATAR_INFO = DefaultRoleInfo(
            "1",
            com.socialplay.gpark.BuildConfig.CDN_CHOOSE_ROLE_DEFAULT_WHOLE,
            com.socialplay.gpark.BuildConfig.CDN_CHOOSE_ROLE_DEFAULT_WHOLE,
            com.socialplay.gpark.BuildConfig.CDN_CHOOSE_ROLE_DEFAULT_PORTRAIT
        )
        val DEFAULT_PLACEHOLDER_AVATAR_INFO_V2 = listOf(
            DefaultRoleInfo(
                "15",
                com.socialplay.gpark.BuildConfig.CDN_ROLE_V2_DEFAULT_1_WHOLE,
                com.socialplay.gpark.BuildConfig.CDN_ROLE_V2_DEFAULT_1_WHOLE2,
                com.socialplay.gpark.BuildConfig.CDN_ROLE_V2_DEFAULT_1_PORTRAIT
            ),
            DefaultRoleInfo(
                "16",
                com.socialplay.gpark.BuildConfig.CDN_ROLE_V2_DEFAULT_2_WHOLE,
                com.socialplay.gpark.BuildConfig.CDN_ROLE_V2_DEFAULT_2_WHOLE2,
                com.socialplay.gpark.BuildConfig.CDN_ROLE_V2_DEFAULT_2_PORTRAIT
            )
        )
    }

    /**
     * 获取首页ugc游戏列表
     */
    fun getUgcGameList(pageSize: Int): Flow<PagingData<MultiTsGameResult>> {
        return Pager(
            config = PagingConfig(
                pageSize = pageSize,
                enablePlaceholders = true,
                initialLoadSize = pageSize,
                prefetchDistance = 2
            ),
            initialKey = 1
        ) {
            UgcGamesPagingSource(metaApi, pageSize)
        }.flow
    }

    /**
     * 获取ugc游戏id配置
     */
    suspend fun getUgcGameConfig(): Flow<DataResult<UgcGameConfig>> = flow {
        var result = DataSource.getDataResultForApi {
            metaApi.getUgcGameConfig()
        }
        if (result.succeeded && result.data != null) {
            cache.put(CacheKey.CACHE_KEY_EDITOR_CONFIG_GAME, result.data)
        } else {
            val data = getCacheUgcGameConfig() ?: UgcGameConfig(
                roleViewGameId = RoleGameIdConfig.AVATAR_GAME_ID,
                roleViewMWGameId = null,
                plazaGameId = null,
                hutGameId = null,
                raiseChildGameId = null
            )
            result = DataResult.Success(data, isCache = true)
        }
        emit(result)
    }

    private suspend fun getCacheUgcGameConfig(): UgcGameConfig?{
        return cache.get<UgcGameConfig>(CacheKey.CACHE_KEY_EDITOR_CONFIG_GAME, object : TypeToken<UgcGameConfig>() {}.type)
    }

    /**
     * 获取移动编辑器最新一条通知
     */
    suspend fun getNewestEditorNotice(): Flow<DataResult<EditorNotice.OuterShowNotice?>> = flow {
        val dataResult = DataSource.getDataResultForApi {
            metaApi.getEditorNotice(1, 1, EditorNotice.Notice.TYPE_METAVERSE)
        }
        val rawResult = dataResult.data?.dataList?.firstOrNull()
        emit(if (!dataResult.succeeded) {
            DataResult.Error(dataResult.code ?: -1, dataResult.message ?: "")
        } else {
            DataResult.Success(metaMapper.map(rawResult, EditorNotice.OuterShowNotice.TYPE_EDITOR))
        })
    }

    /**
     * 获取移动编辑器通知
     */
    fun getEditorNotice(pageSize: Int): Flow<PagingData<NoticeWrapper>> {
        return Pager(
            config = PagingConfig(
                pageSize = pageSize,
                enablePlaceholders = true,
                initialLoadSize = pageSize,
                prefetchDistance = 2
            ),
            initialKey = 1
        ) {
            EditorNoticePagingSource(metaApi, pageSize, metaMapper)
        }.flow
    }

    /**
     * 获取我喜欢的ugc游戏列表
     */
    fun getUgcGameLikeList(pageSize: Int): Flow<PagingData<UgcGameInfo.Games>> {
        return Pager(
            config = PagingConfig(
                pageSize = pageSize,
                enablePlaceholders = true,
                initialLoadSize = pageSize,
                prefetchDistance = 2
            ),
            initialKey = 1
        ) {
            EditorLikePagingSource(metaApi, pageSize)
        }.flow
    }

    /**
     * 获取ugc模板列表：换接口
     */
    suspend fun getUgcTemplateList(pageSize: Int, sinceId: String?, currentPage: Int?): Flow<DataResult<ArrayList<EditorTemplate>>> = flow {
        val result = DataSource.getDataResultForApi {
            metaApi.getUgcTemplateList(pageSize, sinceId, currentPage)
        }
        metaKV.tsKV.saveLocalTemplateGameSet(result.data?.map { it.id ?: "" })
        emit(result)
    }

    suspend fun getEditorPublished(pageNum: String?): Flow<DataResult<UgcGameInfo>> = flow {
        val result = DataSource.getDataResultForApi {
            val map = if (pageNum.isNullOrEmpty()){ HashMap() }else{ hashMapOf("lastId" to pageNum) }
            metaApi.getEditorPublish(map)
        }
        emit(result)
    }

    /**
     * 删除已发布游戏
     */
    fun deleteEditorPublished(ugid: String = ""): Flow<DataResult<Boolean>> = flow {
        val result = DataSource.getDataResultForApi {
            metaApi.deleteEditorPublished(hashMapOf("ugid" to ugid))
        }
        emit(result)
    }

    /**
     * 删除已发布游戏
     */
    fun deleteEditorPublishedV2(ugid: String): suspend () -> Boolean = suspendApiNotNull {
        metaApi.deleteEditorPublished(hashMapOf("ugid" to ugid))
    }

    /**
     * 判断ts游戏的id对应的ugc游戏、mgs功能
     */
    suspend fun getTsTypeInfo(id: String): DataResult<TSTypeInfo> {
        val result = DataSource.getDataResultForApi {
            metaApi.getTsTypeInfo(id)
        }
        if (result.data?.isUgcGame() == true) {
            metaKV.tsKV.saveUgcGameSet(id)
        }
        return result
    }

    /**
     * 标记所有的通知为已读
     */
    suspend fun markAllNoticeAsRead(type: Int): DataResult<Boolean> {
        return DataSource.getDataResultForApi {
            metaApi.markAllNoticeAsRead(hashMapOf("resType" to type))
        }
    }

    /**
     * 获取未读的通知数量
     */
    fun getUnreadNoticeCount(type: Int): Flow<DataResult<Int>> = flow {
        emit(
            DataSource.getDataResultForApi {
                metaApi.getUnreadNoticeCount(type)
            }
        )
    }

    /**
     * 选择默认角色形象
     */
    fun chooseDefaultRole(id: String) = flow {
        emit(DataSource.getDataResultForApi {
            metaApi.chooseDefaultRole(mapOf("code" to id))
        })
    }

    /**
     * 获取ugc横幅列表
     */
    suspend fun fetchUgcBannerList(): Flow<DataResult<List<UgcBannerInfo>?>> = flow {
        emit(
            DataSource.getDataResultForApi { metaApi.fetchUgcBannerList() }
        )
    }

    /**
     * 用gameCode获取模板信息
     */
    suspend fun fetchTemplateInfoByCode(gameCode: String) = flow {
        val result = DataSource.getDataResultForApi {
            metaApi.fetchTemplateInfoByCode(gameCode)
        }
        result.data?.id?.let {
            metaKV.tsKV.saveLocalTemplateGameSet(it)
        }
        emit(result)
    }

    /**
     * 设置主小屋
     */
    suspend fun setMyHome(ugcId: String) = flow {
        emit(DataSource.getDataResultForApi {
            metaApi.setMyHome(mapOf("ugid" to ugcId))
        })
    }

    /**
     * 用uid查询用户的主小屋信息
     */
    suspend fun fetchHomeInfo(userUuid: String?) = flow {
        emit(DataSource.getDataResultForApi {
            metaApi.fetchHomeInfo(mapOf("userUuid" to userUuid))
        })
    }

    suspend fun getUgcIdByPackageName(packageName: String) = flow {
        emit(DataSource.getDataResultForApi {
            metaApi.getUgcIdByPackageName(packageName)
        })
    }

    suspend fun getEditorLocalStatus(bizIdList: List<String>): DataResult<EditorLocalStatusInfo> {
        return DataSource.getDataResultForApi {
            metaApi.getEditorLocalStatus(hashMapOf("bizIdList" to bizIdList))
        }
    }

    /**
     * 获取已发布创作列表
     */
    fun getPublishedCreationList(uuid: String?, orderId: String?): Flow<DataResult<UgcGameInfo>> =
        flow {
            val map = hashMapOf("orderId" to orderId).apply {
                if (!uuid.isNullOrEmpty()) {
                    put("userUuid", uuid)
                }
            }
            emit(DataSource.getDataResultForApi { metaApi.getEditorPublishByUpdateTime(map) })
        }

    suspend fun getUgcInfoByIdLIst(ugids: List<String>): DataResult<UgcGameInfo?> {
        return DataSource.getDataResultForApi { metaApi.getUgcInfoByIdLIst(hashMapOf("ugids" to ugids)) }
    }

    suspend fun getFormworkList(page: Int, formworkCode: String?): Flow<DataResult<FormworkList>> = flow {
        val map = buildMap {
            if (page > 1 && !formworkCode.isNullOrBlank()) {
                put("page", page.toString())
                put("formworkCode", formworkCode)
            }
        }
        val result = DataSource.getDataResultForApi { metaApi.getFormworkList(map) }
        val formworks = result.data?.list
        if (!formworks.isNullOrEmpty()) {
            val ts = System.currentTimeMillis()
            for (formwork in formworks) {
                formwork.tag = "${ts}_${formwork.formworkCode}"
                val gameList = formwork.gameList
                if (gameList.isNullOrEmpty()) continue
                formwork.gameList = gameList.filterNotNull() // 这是有原因的
            }
        }
        emit(result)
    }

    suspend fun getFormWorkV4List(body: ReqFormWorkV4Body): Flow<DataResult<UgcFormWorkV4Data>> =
        flow {
            emit(DataSource.getDataResultForApi { metaApi.getFormWorkV4List(body) })
        }

    fun getFormWorkV4ListMvrk(body: ReqFormWorkV4Body) = suspendApiNotNull {
        val result = metaApi.getFormWorkV4List(body)
        result.copy(data = result.data?.copy(formworkGameList = result.data.formworkGameList?.filter { it.supported }
            ?.toMutableList()))
    }

    suspend fun checkFormWorkArchive(body: ReqFormWorkArchiveBody): Flow<DataResult<UgcFormWorkArchiveData>> =
        flow {
            emit(DataSource.getDataResultForApi { metaApi.checkFormWorkArchive(body) })
        }

    fun checkFormWorkArchiveMvrk(body: ReqFormWorkArchiveBody) = suspendApi {
        metaApi.checkFormWorkArchive(body)
    }

    fun getRoleList() = suspendApiNotNull {
        val roleJson = metaKV.tTaiKV.defaultRoleList.ifEmpty {
            repository.getTTaiConfigById(TTaiKV.ID_DEFAULT_ROLE_LIST).singleOrNull()?.data?.value
        }
        val roles = GsonUtil.gsonSafeParseCollection<List<DefaultRoleInfo>>(roleJson)
            .ifEmptyDefault(listOf(DEFAULT_PLACEHOLDER_AVATAR_INFO))
        ApiResult(code = ApiResult.CODE_OK, message = "OK", data = roles)
    }

    fun getRoleListV2() = suspendApiNotNull {
        val roleJson = metaKV.tTaiKV.defaultRoleListV2.ifEmpty {
            repository.getTTaiConfigById(TTaiKV.ID_DEFAULT_ROLE_LIST_V2).singleOrNull()?.data?.value
        }
        val roles = GsonUtil.gsonSafeParseCollection<List<DefaultRoleInfo>>(roleJson)
            ?.filter { it.id == "15" || it.id == "16" }
            . ifEmptyDefault(DEFAULT_PLACEHOLDER_AVATAR_INFO_V2)
        ApiResult(code = ApiResult.CODE_OK, message = "OK", data = roles)
    }

    fun getRoleStyleList(
        isMe: Boolean,
        otherUuid: String,
        beginIndex: Int,
        length: Int
    ) = suspendApiNotNull {
        if (isMe) {
            metaApi.getRoleStyleList(beginIndex, length)
        } else {
            metaApi.getOtherRoleStyleList(
                RoleOtherStyleListRequest(
                    otherUuid,
                    beginIndex,
                    length
                )
            )
        }
    }

    fun likeRoleStyle(styleId: String, isLike: Boolean) = suspendApiNotNull {
        metaApi.likeRoleStyle(LikeRoleStyleRequest(styleId, isLike))
    }

    fun deleteRoleStyle(styleId: String) = suspendApiNotNull {
        metaApi.deleteRoleStyle(DeleteRoleStyleRequest(styleId))
    }

    fun getPublishedCreationListV2(uuid: String?, orderId: String?): suspend () -> UgcGameInfo =
        suspendApiNotNull {
            val map = hashMapOf("orderId" to orderId).apply {
                if (!uuid.isNullOrEmpty()) {
                    put("userUuid", uuid)
                }
            }
            metaApi.getEditorPublishByUpdateTime(map)
        }

    fun getUgcBackup(type: Int, fileId: String,  gameIdentity: String): suspend () -> List<UgcBackupInfo>? = suspendApi {
        when (type) {
            EditorConfigJsonEntity.TYPE_MODULE ->metaApi.getUgcModuleProjectBackup(fileId, gameIdentity)
            else -> metaApi.getUgcBackup(fileId,gameIdentity)
        }
    }

    suspend fun fetchAllCloudGames(type: Int): List<UgcCloudProject> {
        val list = mutableListOf<UgcCloudProject>()
        getCloudProject(null, list, type)
        return list
    }

    private suspend fun getCloudProject(
        sinceId: Long?,
        list: MutableList<UgcCloudProject>,
        type: Int
    ) {
        val result =
            DataSource.getDataResultForApi {
                when (type) {
                    EditorConfigJsonEntity.TYPE_MODULE -> metaApi.getUgcModuleProjectList(10, sinceId)
                    else -> metaApi.getUgcCloudProject(10, sinceId)
                }
            }
        val data = result.data
        if (result.succeeded && !data.isNullOrEmpty()) {
            list.addAll(data)
            getCloudProject(data.last().id, list, type)
        }
    }

    /**
     * 合并云工程列表, 并按时间倒序
     */
    suspend fun mergeCloudList(localList: MutableList<EditorCreationShowInfo>, type: Int) {
        val cloudList = fetchAllCloudGames(type)
        cloudList.forEach { cloud ->
            val local =
                localList.firstOrNull { it.draftInfo?.jsonConfig?.fileId == cloud.projectId }
            if (local == null) {
                localList.add(EditorCreationShowInfo(cloudProject = cloud))
            } else {
                local.cloudProject = cloud
            }
        }
        localList.sortBy { -it.getShowTime() }
    }

    fun mergeCloudListFlow(localList: MutableList<EditorCreationShowInfo>, type: Int) = flow {
        val cloudList = fetchAllCloudGames(type)
        cloudList.forEach { cloud ->
            val local =
                localList.firstOrNull { it.draftInfo?.jsonConfig?.fileId == cloud.projectId }
            if (local == null) {
                localList.add(EditorCreationShowInfo(cloudProject = cloud))
            } else {
                local.cloudProject = cloud
            }
        }
        localList.sortBy { -it.getShowTime() }
        emit(localList)
    }

    private var maxCloudProject = 30L
    private var lastGetMaxCloudTime = 0L

    suspend fun checkMaxCloud(type: Int): DataResult<Boolean> {
        val localList = if (type == EditorConfigJsonEntity.TYPE_MODULE) {
            EditorLocalHelper.getAllLocalGamesWithoutPreload()
                .map { EditorCreationShowInfo(draftInfo = it) }
                .sortedBy { -it.moduleTime() }
                .distinctBy { it.draftInfo?.jsonConfig?.fileId ?: it.cloudProject?.projectId }
                .toMutableList()
        } else {
            EditorLocalHelper.getAllLocalGamesWithoutPreload()
                .map { EditorCreationShowInfo(draftInfo = it) }
                .toMutableList()
        }
        mergeCloudList(localList, type)
        return if (lastGetMaxCloudTime == 0L) {
            DataSource.getDataResultForApi {
                when (type) {
                    EditorConfigJsonEntity.TYPE_MODULE -> metaApi.getUgcModuleProjectLimit()
                    else -> metaApi.getMaxCloud()
                }
            }.map {
                maxCloudProject = it.maxCloudProject
                (it?.maxCloudProject ?: 30) <= localList.size
            }.also {
                lastGetMaxCloudTime = System.currentTimeMillis()
            }
        } else {
            DataResult.Success(maxCloudProject <= localList.size, true)
        }
    }

    fun checkMaxCloudV2(type: Int) = suspendApiNotNull {
        val result = checkMaxCloud(type)
        ApiResult(
            code = result.code ?: ApiResult.CODE_OK,
            data = result.data,
            message = result.message.orEmpty()
        )
    }

    suspend fun deleteAllBackup(type: Int, fileId:String): DataResult<Boolean> {
        return DataSource.getDataResultForApi {
            when (type) {
                EditorConfigJsonEntity.TYPE_MODULE -> metaApi.deleteUgcModuleProjectBackups(fileId)
                else -> metaApi.deleteAllBackup(fileId)
            }
        }
    }

    fun deleteAllBackupV2(type: Int, fileId: String) = suspendApiNotNull {
        val result = deleteAllBackup(type, fileId)
        ApiResult(
            code = result.code ?: ApiResult.CODE_OK,
            data = result.data,
            message = result.message.orEmpty()
        )
    }

    suspend fun getMaxCloud(type: Int) =
        DataSource.getDataResultForApi {
            when (type) {
                EditorConfigJsonEntity.TYPE_MODULE -> metaApi.getUgcModuleProjectLimit()
                else -> metaApi.getMaxCloud()
            }
        }

    fun getMaxCloudV2(type: Int) = suspendApiNotNull {
        when (type) {
            EditorConfigJsonEntity.TYPE_MODULE -> metaApi.getUgcModuleProjectLimit()
            else -> metaApi.getMaxCloud()
        }
    }

    fun getMyCreations(body: MyCreationsV3Request) = suspendApiNotNull {
        metaApi.getMyCreationsV3(body)
    }

    fun pinMyCreation(
        gameId: String,
        gameType: Int,
        pinOrNot: Boolean
    ): suspend () -> Any? = suspendApi {
        if (gameType == ProfilePublishedUgcModelState.GAME_TYPE_UGC) {
            metaApi.pinUgcGame(
                PinUgcRequest(
                    gameId,
                    pinOrNot
                )
            )
        } else {
            metaApi.pinPgcGame(
                PinPgcRequest(
                    gameId,
                    pinOrNot
                )
            )
        }
    }
}