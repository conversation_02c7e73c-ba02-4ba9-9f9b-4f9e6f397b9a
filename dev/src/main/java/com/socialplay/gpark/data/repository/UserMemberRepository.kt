package com.socialplay.gpark.data.repository

import com.socialplay.gpark.data.api.MetaApi
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.DataSource
import com.socialplay.gpark.data.model.member.MemberInfo
import com.socialplay.gpark.data.model.member.MemberRequest
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

/**
 * 会员相关repository
 */
class UserMemberRepository(private val metaApi: MetaApi) {

    //获取会员信息数组
    fun getUserMemberInfoList(request: MemberRequest): Flow<DataResult<List<MemberInfo>>> = flow {
        val dataResult = DataSource.getDataResultForApi { metaApi.getUserMemberInfos(request) }
        emit(dataResult)
    }

}