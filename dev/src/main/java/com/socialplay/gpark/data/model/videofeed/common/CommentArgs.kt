package com.socialplay.gpark.data.model.videofeed.common

import android.os.Parcelable
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import kotlinx.parcelize.Parcelize

/**
 *     author : wei.zhu
 *     e-mail : <EMAIL>
 *     time   : 2023/12/28
 *     desc   :
 */

@Parcelize
data class CommentArgs(
    val reqId: String,
    val postId: String,
    val commentCount: Long,
    val resId: ResIdBean,
) : Parcelable