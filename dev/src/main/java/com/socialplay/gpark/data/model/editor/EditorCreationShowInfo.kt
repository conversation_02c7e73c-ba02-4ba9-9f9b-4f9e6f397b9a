package com.socialplay.gpark.data.model.editor

import android.content.Context
import com.meta.biz.ugc.local.EditorLocalHelper
import com.socialplay.gpark.R
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.meta.biz.ugc.model.UgcDraftInfo
import com.socialplay.gpark.data.model.editor.cloud.UgcCloudProject
import com.socialplay.gpark.function.pandora.PandoraToggle
import kotlin.math.max

class EditorCreationCombineResult(
    // 混合创作列表
    var list: MutableList<EditorCreationShowInfo>?,
    // 是否有下一页
    var end: Boolean?
)

/**
 * Created by bo.li
 * Date: 2023/3/1
 * Desc:
 */
data class EditorCreationShowInfo(
    // 本地工程信息
    var draftInfo: UgcDraftInfo? = null,
    // 已发布游戏信息
    var ugcInfo: UgcGameInfo.Games? = null,
    // 已有云备份
    var cloudProject: UgcCloudProject? = null
) {

    val isOnline: Boolean
        get() = getAuditStatusCode() == 3

    val auditReject: Boolean
        get() = getAuditStatusCode() == 4

    //模组专用，其他别用
    val moduleEpoxyId: String? get() {
        return draftInfo?.let {
            it.path + it.jsonConfig.fileId.orEmpty()
        } ?: cloudProject?.let {
            it.id.toString() + it.projectId
        }
    }

    fun isClouded(): Boolean {
        return cloudProject != null
    }

    fun getShowTime(): Long {
        return if (draftInfo != null) {
            draftInfo?.lastModifiedTime ?: 0
        } else if (cloudProject != null) {
            cloudProject?.updateTime ?: 0
        } else if (ugcInfo != null) {
            ugcInfo?.releaseTime ?: 0
        } else {
            0
        }
    }

    fun moduleTime(): Long {
        return draftInfo?.let { max(it.lastModifiedTime ?: 0, it.jsonConfig.createFileTime ?: 0) }
            ?: cloudProject?.updateTime ?: 0
    }

    fun isOnlyCloud(): Boolean {
        return draftInfo == null && cloudProject != null
    }

    fun getAuditStatus(context: Context): String {
        return if (draftInfo == null) {
            context.getString(R.string.published_cap)
        } else if (draftInfo?.auditStatusDesc.isNullOrBlank() && !isOnlyCloud()) {
            context.getString(R.string.editor_local_game)
        } else {
            draftInfo?.auditStatusDesc ?: context.getString(R.string.unknown_cap)
        }
    }

    fun getAuditStatusCode(): Int {
        return if (draftInfo == null) {
            3
        } else {
            draftInfo?.auditStatus ?: 1
        }
    }

    fun getGameBanner(): String? {
        return if (draftInfo != null){
            EditorLocalHelper.getRelativeIconPath(draftInfo?.path ?:"", draftInfo?.jsonConfig?.thumb?:"")
        } else if (ugcInfo != null) {
            ugcInfo?.banner
        } else if (cloudProject != null) {
            cloudProject?.thumbnailFileUrl
        } else {
            null
        }
    }

    fun getGameName(): String? {
        return if (draftInfo != null){
            draftInfo?.jsonConfig?.name
        } else if (ugcInfo != null) {
            ugcInfo?.ugcGameName
        } else if (cloudProject != null) {
            cloudProject?.projectName
        } else {
            null
        }
    }

    fun hasLocalGame(): Boolean {
        return draftInfo != null
    }

    /**
     * 是否对应一个能玩的ugc游戏
     */
    fun hasAvailableUgcGame(): Boolean {
        return ugcInfo != null
    }

    fun getUgcId(): String? {
        return ugcInfo?.id ?: draftInfo?.ugid
    }

    fun getUgcPackageName(): String? {
        return if (ugcInfo != null) {
            ugcInfo?.packageName
        } else if (draftInfo != null) {
            draftInfo?.jsonConfig?.packageName
        } else {
            null
        }
    }

    fun getParentGameCode(): String? {
        return if (ugcInfo != null) {
            ugcInfo?.availableGameCode
        } else if (draftInfo != null) {
            draftInfo?.jsonConfig?.gid
        } else {
            null
        }
    }

    fun trackShow() {
        Analytics.track(
            EventConstants.UGC_CREATE_PRODUCTION_SHOW,
            "gameid" to getParentGameCode().orEmpty(),
            "fileid" to draftInfo?.jsonConfig?.fileId.orEmpty(),
            "status" to getAuditStatusCode().toLong(),
            "ugcid" to (if (isOnline) (getUgcId() ?: 0L).toString() else "")
        )
    }

}