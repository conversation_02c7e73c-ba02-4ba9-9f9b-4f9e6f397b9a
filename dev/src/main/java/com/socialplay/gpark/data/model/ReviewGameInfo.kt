package com.socialplay.gpark.data.model

import android.content.Context
import com.socialplay.gpark.R

data class ReviewGameInfo(
    val code: String, // 游戏id标识
    val name: String, // 游戏名称
    val packageName: String,
    val icon: String, // 游戏icon图片
    val description: String, // 游戏描述文案
    val images: List<String>, // 游戏详情图片
    val versionList: List<VersionInfo>
)

data class VersionInfo(
    val viewerId: String, // 版本编号标识【透传字段】
    val id: String, // 该版本数据库id
    val version: String, // 版本号，例如:1.0.0
    val status: Int, // 版本状态：版本状态（1:待提审,2:待审核,4:审核拒绝,8:已撤回（取消版本）, 16:待发布, 32:已发布【上线】;64下线）
    val statusDesc: String?, // 版本状态文字
    val upgradeDescription: String, // 升级描述
    val createTime: String, // 版本创建时间
    val type: Int, // 游戏类型；1：普通安装包游戏(android)；     3：普通存档类游戏；4:   MetaVerse游戏；
) {

    fun statusTxt(context: Context): String = if (statusDesc.isNullOrEmpty()) {
        when (status) {
            1 -> context.getString(R.string.to_be_submitted)
            2 -> context.getString(R.string.under_review)
            4 -> context.getString(R.string.rejected)
            8 -> context.getString(R.string.withdrawn)
            16 -> context.getString(R.string.to_be_published)
            32 -> context.getString(R.string.published_cap)
            64 -> context.getString(R.string.offline_cap)
            else -> context.getString(R.string.unknown_cap)
        }
    } else {
        statusDesc
    }

    var gameInfo: ReviewGameInfo? = null
}