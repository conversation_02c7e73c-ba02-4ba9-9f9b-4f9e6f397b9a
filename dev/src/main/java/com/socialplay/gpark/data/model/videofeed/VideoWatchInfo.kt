package com.socialplay.gpark.data.model.videofeed

data class VideoWatchInfo(
    val wrapped: WrappedVideoFeedItem,
    val positionInList: Int,
    val watchStartTime: Long,
    val watchMaxPosition: Long = 0,
    val videoTotalDuration: Long = 0,

    // Item第一帧渲染完成的时间戳(SystemClock.elapsedRealtime())
    val firstFrameRenderedTimestamp: Long = 0,

    // 视频播放过程中的缓冲记录
    val bufferingOpRecords: List<BufferingOpRecord> = emptyList(),

    // 视频开始观看的时候本地已经有多少缓存了
    val preloadSize: Long,
)

data class BufferingRecord(
    val bufferStartTimestamp: Long,
    val bufferEndTimestamp: Long,
)

data class BufferingOpRecord(
    val event: BufferEvent,
    val timestamp: Long,
)

enum class BufferEvent {
    BUFFER_START,
    BUFFER_END
}