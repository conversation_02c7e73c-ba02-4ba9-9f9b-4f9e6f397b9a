package com.socialplay.gpark.data.model.sdk.resp

import android.os.Bundle
import com.socialplay.gpark.data.model.sdk.SdkCommandType
import com.socialplay.gpark.data.model.sdk.SdkErrorCode

class AuthResp(
    val code: Int = SdkErrorCode.CODE_UNKNOWN_ERROR,
    val message: String,
    val state: String? = null,
    val accessToken:String? = null
) : BaseResp(code, message) {

    override fun getType(): Int {
       return SdkCommandType.TYPE_AUTHORIZE
    }

    override fun toBundle(bundle: Bundle) {
        super.toBundle(bundle)
        bundle.putString("gpark_auth_request_state", state)
        bundle.putString("gpark_auth_resp_access_token", accessToken)
    }
}