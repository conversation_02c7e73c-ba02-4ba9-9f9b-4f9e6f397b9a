package com.socialplay.gpark.data.model.im

import com.meta.biz.mgs.data.model.MgsBriefRoomInfo
import java.io.Serializable

/**
 * Created by bo.li
 * Date: 2021/8/16
 * Desc:
 */
data class MgsGameInviteEventInfo(
    val roomInfo: MgsBriefRoomInfo,
    val gameInfo: MgsInviteGameBriefInfo,
    val otherUuid: String
): Serializable

data class MgsInviteGameBriefInfo(
    val gameIcon: String? = "",
    val gameName: String? = "",
    val gameId: String? = "",
    val packageName: String? = "",
): Serializable