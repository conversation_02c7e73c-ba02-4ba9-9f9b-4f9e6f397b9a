package com.socialplay.gpark.data.interactor

import android.app.Activity
import android.app.Application
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.graphics.SurfaceTexture
import android.os.Build
import android.os.IBinder
import android.os.SystemClock
import android.view.Surface
import androidx.core.app.NotificationCompat
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import androidx.lifecycle.asFlow
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.meta.biz.ugc.model.ActionOnlyMsg
import com.meta.biz.ugc.model.AndroidCommonResult
import com.meta.biz.ugc.model.MWGameLoadFailedMsg
import com.meta.biz.ugc.model.RoleGameLoadStatusChangedMsg
import com.meta.biz.ugc.model.SurfaceStatusMsg
import com.meta.biz.ugc.protocol.UGCProtocolReceiver
import com.meta.biz.ugc.protocol.UGCProtocolSender
import com.meta.biz.ugc.protocol.constants.ProtocolReceiveConstants
import com.meta.biz.ugc.protocol.constants.ProtocolSendConstant
import com.meta.biz.ugc.protocol.listener.SingleProtocolListener
import com.meta.lib.mwbiz.MWBizProxy
import com.meta.verse.lib.util.ProcessUtils
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.code
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.editor.AvatarGameLoadConfig
import com.socialplay.gpark.data.model.editor.DefaultRoleInfo
import com.socialplay.gpark.data.model.editor.EditorGameLoadData
import com.socialplay.gpark.data.model.editor.GameLoadState
import com.socialplay.gpark.data.model.editor.MWGameLoadErrorInfo
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.deeplink.MetaDeepLink
import com.socialplay.gpark.function.editor.EditorGameInteractHelper
import com.socialplay.gpark.function.locale.MetaLanguageListener
import com.socialplay.gpark.function.locale.MetaLanguages
import com.socialplay.gpark.function.mw.launch.exception.BaseTSLaunchException
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.developer.mw.MWHelper
import com.socialplay.gpark.ui.editor.AvatarAnalytics
import com.socialplay.gpark.ui.editor.AvatarAnalyticsV2
import com.socialplay.gpark.ui.main.MainBottomNavigationItem
import com.socialplay.gpark.util.ActivityLifecycleCallbacksAdapter
import com.socialplay.gpark.util.NetUtil
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.SingleReplyMutableSharedFlow
import com.socialplay.gpark.util.extension.collectIn
import com.socialplay.gpark.util.lifecycle.DefaultLifecycleOwner
import com.socialplay.gpark.util.lifecycle.LifecycleOwnerMediator
import com.socialplay.gpark.util.suspendAwait
import com.socialplay.gpark.util.waitUntil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChangedBy
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.last
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.retry
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.takeWhile
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withTimeout
import kotlinx.coroutines.withTimeoutOrNull
import kotlinx.coroutines.yield
import org.koin.core.context.GlobalContext
import timber.log.Timber
import java.io.IOException
import java.util.Locale
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.code
import kotlin.coroutines.coroutineContext
import kotlin.coroutines.resume


/**
 * 文档：
 * 2022/1/12 https://meta.feishu.cn/wiki/Az8aw0TpIiCH6ckjAzucxbRpnEi
 * 2024/5/8 https://project.feishu.cn/androidcc/story/detail/**********
 * 2024/8/1 https://meta.feishu.cn/wiki/JT4awcbcNiw8P5kYWHycSe8Snqe
 *
 */
class EditorGameLoadInteractor(
    private val app: Application,
    private val editorInteractor: EditorInteractor,
    private val accountInteractor: AccountInteractor,
    private val repository: IMetaRepository,
    private val mvCoreProxyInteractor: MVCoreProxyInteractor,
    private val metaKV: MetaKV
) {

    companion object{

        // 获取用户配置的重试次数
        private const val GAME_CONFIG_RETRY_MAX_COUNT = 3
        private val GAME_CONFIG_RETRY_INTERVAL = TimeUnit.SECONDS.toMillis(10)

        // 获取用户Token失败的重试次数
        private const val USER_TOKEN_RETRY_MAX_COUNT = 3
        private val USER_TOKEN_RETRY_INTERVAL = TimeUnit.SECONDS.toMillis(10)

        const val ERR_CODE_NETWORK_UNAVAILABLE = 1
        const val ERR_CODE_GET_USER_INFO_FAILED = 2
        const val ERR_CODE_GET_GAME_ID_FAILED = 3
        const val ERR_CODE_GAME_LOAD_FAILED = 4
        const val ERR_CODE_DISCONNECTED = 5
        const val ERR_CODE_START_GAME_CALL_FAILED = 6
        const val ERR_CODE_ENGINE_INTERNAL_ERROR = 10

    }

    private val isLoadCalled = AtomicBoolean(false)
    private val isAppRunningForeground = AtomicBoolean(false)

    //游戏预加载状态
    private val _gameLoadState = SingleReplyMutableSharedFlow<GameLoadState>(GameLoadState.Unknown)
    val gameLoadState: StateFlow<GameLoadState> get() = _gameLoadState

    // 给预加载启动游戏用
    private val manuallyStartGameFlow: MutableSharedFlow<ManuallyStartGameEvent> = MutableSharedFlow()

    private val scope = MainScope()

    private var loadAvatarGameJob: Job? = null
    private var suspableTimeoutJob: Job? = null

    private var categoryId: Int = 0

    // 是否收到了等待游戏可挂起事件超时
    private val _waitGameSuspendableTimeoutFlow = MutableStateFlow(false)

    // 是否打开了TS游戏
    private val _isTsRunningFlow = MutableStateFlow(false)


    // 是否连接上了远程进程
    private val _isConnectedRenderProcess = AtomicBoolean(false)

    private val _isReceivedSceneReady = MutableStateFlow(false)

    private val _receivedMwGameLoadError = MutableStateFlow<MWGameLoadErrorInfo?>(null)

    // 当前正在加载的游戏信息
    private var _loadingData: EditorGameLoadData? = null

    // 用来加载游戏用的假Surface
    private var _surface: Surface? = null

    // 角色游戏是否能挂起
    // 前提条件: 引擎准备好了
    // 1、收到了游戏的挂起事件
    // 2、等待超时
    // 3、用户打开了TS游戏,这期先不做
    val suspendable: StateFlow<Boolean> = combine(
        mvCoreProxyInteractor.engineReadyFlow,
        EditorGameInteractHelper.roleUserDataLiveData.asFlow().map { it == EditorGameInteractHelper.ROLE_STATE_ROLE_LOAD_END },
        _waitGameSuspendableTimeoutFlow,
        /*
                    _isTsRunningFlow,*/
    ) { engineStatus, receivedSuspendable, timeout/*, isTsRunning*/ ->
        engineStatus && (receivedSuspendable || timeout/* || isTsRunning*/)
    }.stateIn(scope, SharingStarted.Eagerly, false)

    // 用于外部通知角色View状态
    private val avatarViewLifecycleOwner = DefaultLifecycleOwner()

    // 用于处理等待SceneReady事件期间的生命周期控制
    private val waitingSceneReadyLifecycleOwner: LifecycleOwner = LifecycleOwnerMediator(
        listOf(
            avatarViewLifecycleOwner,
            ProcessLifecycleOwner.get()
        )
    )

    private val loadStatusListener by lazy {
        object : SingleProtocolListener<RoleGameLoadStatusChangedMsg>(ProtocolReceiveConstants.PROTOCOL_ROLE_GAME_LOAD_STATUS_CHANGED) {
            override fun handleProtocol(message: RoleGameLoadStatusChangedMsg?, messageId: Int) {
                message ?: return
                scope.launch {
                    _gameLoadState.emit(
                        GameLoadState.LoadingProgressUpdated(
                            message.progress.toFloatOrNull() ?: 0F,
                            message.message,
                            getStateLoadConfig()
                        )
                    )
                }
            }
        }
    }

    private val loadErrorListener by lazy {
        object :
            SingleProtocolListener<MWGameLoadFailedMsg>(ProtocolReceiveConstants.PROTOCOL_MW_GAME_LOADING_FAILED) {
            override fun handleProtocol(message: MWGameLoadFailedMsg?, messageId: Int) {
                Timber.d("Received MWGameLoadFailedMsg code:${message?.code} message:${message?.message}")
                if (message != null) {
                    val errorInfo = MWGameLoadErrorInfo(
                        code = message.code ?: "",
                        message = "%s\n(%s)".format(message.message ?: "", message.code)
                    )

                    _receivedMwGameLoadError.value = errorInfo
                    cancelLoadAvatarGameJob()
                }
            }
        }
    }

    // 接收引擎发送的第一次Surface设置成功事件
    private val sceneReadyListener by lazy {
        object : SingleProtocolListener<ActionOnlyMsg>(ProtocolReceiveConstants.PROTOCOL_MW_GAME_SCENE_READY) {
            override fun handleProtocol(message: ActionOnlyMsg?, messageId: Int) {
                Timber.d("Received scene ready event from engine")
                _isReceivedSceneReady.value = true
                Timber.d("Update scene ready status to true")
            }
        }
    }


    // 角色view是否可见
    private var _avatarViewVisibility: MutableStateFlow<Boolean> = MutableStateFlow(false)
    val avatarViewVisibilityStateFlow: StateFlow<Boolean> = _avatarViewVisibility



    // 是否是预加载周期
    // 如果预加载周期结束(成功或失败)，这个标记位会被立马清除，转换为非预加载
    private var isPreloadCycle: Boolean = false

    // 应用启动时间
    private var appStartupTime: Long = 0

    // 应用启动到预加载开始的等待时间
    private var preloadWaitTime: Long? = null

    // 在开始触发load的时候引擎是否可用
    private var isEngineValidOnLoad: Boolean = false

    // 本地有是否有效的MW引擎
    private val isMwEngineValid by lazy {
        /*MWHelper.isMWEngineValid(app)*/
        true
    }

    private var loadingCnt: Long = 0

    init {
        gameLoadState.collectIn(scope) {
            Timber.d("checkcheck_role LoadState changed to $it")

            when (it) {
                is GameLoadState.Loading -> {
                    AvatarAnalytics.start(
                        isAvatarViewVisible(),
                        it.loadConfig.isPreloadCycle,
                        !it.loadConfig.isEngineValidOnLoad,
                        it.loadConfig.preloadWaitTime,
                        it.loadConfig.loadingCnt == 1L
                    )
                }

                is GameLoadState.LoadSuccess -> {
                }

                is GameLoadState.LoadFailed -> {
                    val subErrorCode = if (it is GameLoadState.LoadFailed.GameLoadInternalFailed) {
                        it.internalErrorCode
                    } else {
                        "0"
                    }
                    AvatarAnalytics.end(
                        false,
                        it.code,
                        subErrorCode,
                        isAvatarViewVisible(),
                        isReceivedUserLoadEventThisLoadCycle(),
                        it.subMessage
                    )

                    AvatarAnalyticsV2.end(AvatarAnalyticsV2.EndReason.Fail, it.code, subErrorCode, isAvatarViewVisible())
                    resetPreloadCycle()
                }

                else -> {
                    // do nothing
                }
            }
        }

        suspendable.collectIn(scope) {
            Timber.d("checkcheck_role suspendable changed to $it")
        }

        accountInteractor.accountLiveData.asFlow().filterNotNull().collectIn(scope) {
            // 预加载一下图片，加快显示速度
            it.bgMaskImage?.let {
                Glide.with(app)
                    .load(it)
                    .diskCacheStrategy(DiskCacheStrategy.DATA)
                    .preload()
            }
        }

        // 监控TS游戏是否运行
        app.registerActivityLifecycleCallbacks(object : ActivityLifecycleCallbacksAdapter() {
            override fun onActivityPaused(activity: Activity) {
                super.onActivityPaused(activity)
                scope.launch(Dispatchers.Default) {
                    _isTsRunningFlow.value = ProcessUtils.gameProcessAlive(app)
                }
            }

            override fun onActivityResumed(activity: Activity) {
                super.onActivityResumed(activity)
                scope.launch(Dispatchers.Default) {
                    _isTsRunningFlow.value = ProcessUtils.gameProcessAlive(app)
                }
            }
        })


        EditorGameInteractHelper.roleUserDataLiveData.asFlow().collectIn(scope) {
            if (_loadingData != null) {
                val surface = _surface
                val isReceivedLoaded = it == EditorGameInteractHelper.ROLE_STATE_ROLE_LOAD_END
                if (isReceivedLoaded && surface != null) {
                    val renderIsTakenByUser = renderIsTakenByUser(surface)
                    UGCProtocolSender.sendProtocol(
                        protocol = ProtocolSendConstant.PROTOCOL_UE_ACTION_GAME_SURFACE_READY,
                        0,
                        platformMsg = SurfaceStatusMsg(renderIsTakenByUser)
                    )
                    Timber.d("checkcheck_role Send surface ready event to engine renderIsTakenByUser:${renderIsTakenByUser}")
                }

                if(isReceivedLoaded){
                    AvatarAnalytics.end(
                        true,
                        0,
                        "",
                        isAvatarViewVisible(),
                        isReceivedUserLoadEventThisLoadCycle(),
                        null
                    )

                    AvatarAnalyticsV2.end(AvatarAnalyticsV2.EndReason.Success, 0, "", isAvatarViewVisible())
                    resetPreloadCycle()
                }
            }
        }

        waitingSceneReadyLifecycleOwner.lifecycle.addObserver(object : LifecycleEventObserver {
            override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
                val isReceivedUserLoad = isReceivedAvatarLoadSuccessEvent()
                val isReceivedSceneReady = _isReceivedSceneReady.value

                Timber.d("checkcheck_role waitingSceneReadyLifecycleOwner onStateChanged" +
                        " event:${event}" +
                        " isReceivedUserLoad:${isReceivedUserLoad}" +
                        " isReceivedSceneReady:${isReceivedSceneReady}"
                )

                // 这个控制器，只能控制后台加载阶段的生命周期，用于sceneReady空等的问题
                // 在收到userload后，收到sceneready前的这段时间
                if (isReceivedUserLoad && !isReceivedSceneReady) {
                    if (event == Lifecycle.Event.ON_RESUME) {
                        MWBizProxy.resume()
                    } else if (event == Lifecycle.Event.ON_PAUSE) {
                        MWBizProxy.suspend()
                    }
                }
            }
        })
    }

    private val languageChangeCallback: MetaLanguageListener by lazy {
        object : MetaLanguageListener {
            override fun onAppLocaleChange(oldLocale: Locale?, newLocale: Locale?) {
                refreshLoad(LoadTrigger.LanguageChange, getDefaultTimeoutInMs())
            }

            override fun onSystemLocaleChange(oldLocale: Locale?, newLocale: Locale?) {

            }

        }
    }

    fun init(){
        this.appStartupTime = System.currentTimeMillis()
    }

    /**
     * 启动角色游戏R进程
     */
    private fun startAvatarRenderProcess() {
        mvCoreProxyInteractor.startup(app)
    }

    private fun resetPreloadCycle(){
        if(isPreloadCycle){
            isPreloadCycle = false
            preloadWaitTime = null
        }
        isEngineValidOnLoad = MWHelper.isMWEngineValid(app)
    }


    fun isLoadCalled(): Boolean {
        return isLoadCalled.get()
    }

    /**
     * 在一个角色加载周期内是否收到了UserLoad事件
     */
    fun isReceivedUserLoadEventThisLoadCycle(): Boolean {
        if (_gameLoadState.value is GameLoadState.LoadSuccess) {
            return true
        }

        return EditorGameInteractHelper.isRoleGameLoaded()
    }


    fun load(loadCategoryId: Int, isPreload: Boolean = false) = scope.launch {
        if (!isLoadCalled.compareAndSet(false, true)) {
            return@launch
        }

        <EMAIL> = loadCategoryId

        isPreloadCycle = isPreload

        if (isPreload) {
            preloadWaitTime = System.currentTimeMillis() - appStartupTime
        }

        Analytics.track(EventConstants.EVENT_START_ROLE_PRE_LOAD) {
            put("show_categoryid", categoryId)
            put("is_preload", if (isPreload) 1 else 0)
        }

        // 不管预加载与否，都启动R进程
        startAvatarRenderProcess()


        Timber.d("preload start ")

        bindAppForegroundObserver()

        _gameLoadState.emit(GameLoadState.Start(getStateLoadConfig()))


        loopTsGameRunningStatus()

        // 这几种情况下重新触发加载游戏
        // 1、手动触发重新加载游戏
        // 2、R进程连接状态变化
        // 3、账号变化
        // 4、语言变化
        // 这几个分开是为了追踪触发来源，方便调试
        manuallyStartGameFlow.collectIn(scope) {
            Timber.d("StartGame manually value:${it.timeout}")
            refreshLoad(LoadTrigger.Manually, it.timeout)
        }

        accountInteractor.accountLiveData.asFlow().distinctUntilChangedBy { it?.uuid }.collectIn(scope) {
            Timber.d("Account changed. uuid:${it?.uuid}")
            refreshLoad(LoadTrigger.AccountChanged, getDefaultTimeoutInMs())
            if (!it?.uuid.isNullOrBlank()) {
                EditorGameInteractHelper.resetCheckRoleData()
            }
        }

        MetaLanguages.addCallback(languageChangeCallback)

        mvCoreProxyInteractor.connectionStatusFlow.collectIn(scope) {
            val isConnectedBefore = _isConnectedRenderProcess.getAndSet(it)
            Timber.d("MVCoreProxy connection changed. isConnected:${it} isConnectedBefore:${isConnectedBefore}")
            if (it) {
                bindForegroundNotificationIfNeeded()
                refreshLoad(LoadTrigger.ConnectionChanged, getDefaultTimeoutInMs())
            } else if (isConnectedBefore) {  // 仅在发生变化后才触发,第一次不触发断开连接事件
                resetSuspendableStatus()
                cancelSuspenableTimeoutTimer()

                cancelLoadAvatarGameJob()

                _gameLoadState.emit(
                    GameLoadState.LoadFailed.EngineLoadFailed(
                        code = ERR_CODE_DISCONNECTED,
                        message = app.getString(R.string.editor_role_game_disconnected_from_backend),
                        loadConfig = getStateLoadConfig()
                    )
                )
            }
        }
    }



    /**
     * 当前角色View是否可见
     */
    fun isAvatarViewVisible(): Boolean {
        return avatarViewVisibilityStateFlow.value
    }

    /**
     * 通知角色View可见状态变化
     */
    fun notifyAvatarViewVisibilityChanged(visible: Boolean) {
        Timber.d("notifyAvatarViewVisibilityChanged visible:${visible}")
        _avatarViewVisibility.value = visible

        metaKV.avatarKv.isAvatarRunningForeground = visible

        val event = if (visible) Lifecycle.Event.ON_RESUME else Lifecycle.Event.ON_PAUSE
        avatarViewLifecycleOwner.registry.handleLifecycleEvent(event)
    }


    /**
     * 立即停止角色进程，并阻止内部自动重新拉起
     */
    fun stop(message: String) {
        MWBizProxy.unbindForegroundNotification(true)
        MWBizProxy.kill(false, message)
    }

    /**
     * 监听前后台切换，如果从后台切换到前台时游戏处于加载失败状态则重新加载游戏
     */
    private fun bindAppForegroundObserver() {
        ProcessLifecycleOwner.get().lifecycle.addObserver(object : DefaultLifecycleObserver {
            override fun onResume(owner: LifecycleOwner) {
                super.onResume(owner)
                isAppRunningForeground.set(true)

                Timber.d("bindAppForegroundObserver onResume")
                if (_gameLoadState.value is GameLoadState.LoadFailed) {
                    Timber.d("bindAppForegroundObserver onResume reLaunchRoleGame")
                    reLaunchRoleGame()
                }

                if (mvCoreProxyInteractor.connectionStatusFlow.value) {
                    bindForegroundNotificationIfNeeded()
                }
            }

            override fun onPause(owner: LifecycleOwner) {
                super.onPause(owner)
                Timber.d("bindAppForegroundObserver onPause")
                isAppRunningForeground.set(false)
            }
        })
    }

    // 启动等待游戏可挂起事件超时定时器
    private fun startSuspenableTimeoutTimer(timeout: Long) {
        cancelSuspenableTimeoutTimer()

        this.suspableTimeoutJob = scope.launch {
            delay(timeout)
            _waitGameSuspendableTimeoutFlow.emit(true)
            Timber.d("SuspenableTimeoutTimer timeout")
        }.apply {
            invokeOnCompletion {
                Timber.d("SuspenableTimeoutTimer completed reason:${it}")
            }
        }
    }

    // 取消等待游戏可挂起事件超时定时器
    private fun cancelSuspenableTimeoutTimer() {
        suspableTimeoutJob?.run {
            if (isActive && !isCancelled) {
                cancel()
            }
            suspableTimeoutJob = null
        }
    }

    private fun resetSuspendableStatus() {
        _waitGameSuspendableTimeoutFlow.value = false
    }

    private fun cancelLoadAvatarGameJob() {
        loadAvatarGameJob?.run {
            if (isActive && !isCancelled) {
                cancel()
            }
            loadAvatarGameJob = null
        }
    }

    // 轮询判断TS游戏是否在运行中
    private fun loopTsGameRunningStatus() {
        scope.launch(Dispatchers.Default) {
            while (true) {
                delay(1000)
                _isTsRunningFlow.value = ProcessUtils.gameProcessAlive(app)
            }
        }
    }

    private fun refreshLoad(trigger: LoadTrigger, timeout: Long) {
        Timber.d("Trigger refreshLoad trigger:${trigger}")

        val loadingData = EditorGameLoadData(uuid = accountInteractor.curUuid)
        if (_loadingData == loadingData) {
            Timber.d("Trigger refreshLoad but already on loading status onlyKey:${loadingData}")
            return
        }

        this._loadingData = loadingData

        cancelLoadAvatarGameJob()
        loadAvatarGameJob = scope.launch {
            UGCProtocolReceiver.addProtocolObserver(loadStatusListener)
            UGCProtocolReceiver.addProtocolObserver(sceneReadyListener)
            UGCProtocolReceiver.addProtocolObserver(loadErrorListener)
            loadInternal(trigger, categoryId, timeout)
        }.apply {
            invokeOnCompletion {
                UGCProtocolReceiver.removeProtocolObserver(loadStatusListener)
                UGCProtocolReceiver.removeProtocolObserver(sceneReadyListener)
                UGCProtocolReceiver.removeProtocolObserver(loadErrorListener)
                Timber.d("Avatar load job completed reason:${it}")
                _loadingData = null
            }
        }
    }

    private suspend fun loadInternal(trigger: LoadTrigger, categoryId: Int, timeout: Long) {
        Timber.d("Trigger loadInternal trigger:${trigger} categoryId:${categoryId}")

        _gameLoadState.emit(GameLoadState.Loading(getStateLoadConfig()))

        resetSuspendableStatus()

        // 获取角色游戏ID
        val (roleViewGameId, getRoleViewGameErrCode, getRoleViewGameMessage) = getRoleViewGameId(categoryId)

        if (roleViewGameId.isBlank()) {
            _gameLoadState.emit(
                GameLoadState.LoadFailed.GameConfigLoadFailed(
                    code = getRoleViewGameErrCode,
                    message = getRoleViewGameMessage,
                    loadConfig = getStateLoadConfig()
                )
            )
            return
        }

        // 获取用户信息,有用户信息能请求游戏信息接口成功
        val (userInfo, getUserInfoErrorCode, getUserInfoErrorMessage) = getUserInfo()

        if (userInfo == null) {
            _gameLoadState.emit(
                GameLoadState.LoadFailed.GameConfigLoadFailed(
                    getUserInfoErrorCode,
                    getUserInfoErrorMessage,
                    loadConfig = getStateLoadConfig()
                )
            )
            return
        }

        _gameLoadState.emit(GameLoadState.Prepared(roleViewGameId,loadConfig=getStateLoadConfig()))

        doLoad(roleViewGameId, categoryId, timeout)
    }

    private suspend fun waitForSceneReady() {
        Timber.d("waitForSceneReady start")
        while (!_isReceivedSceneReady.value) {
            delay(100)
        }
        Timber.d("waitForSceneReady finish")
    }

    private suspend fun doLoad(gameId: String, categoryId: Int, timeout: Long) {
        Analytics.track(EventConstants.EVENT_ROLE_DO_LOAD) {
            put("show_categoryid", categoryId)
        }
        val loadStartTime = SystemClock.elapsedRealtime()
        Timber.d("checkcheck_role doLoad gameId $gameId startTime:${loadStartTime}")

        //重新加载需要清除之前的加载状态
        EditorGameInteractHelper.resetLoadStatus()

        _isReceivedSceneReady.value = false
        Timber.d("Reset scene ready status to false")

        _receivedMwGameLoadError.value = null

        _gameLoadState.emit(GameLoadState.LoadingProgressUpdated(0.0F, app.getString(R.string.editor_role_game_loading_phase_starting_game), getStateLoadConfig()))

        if (!PandoraToggle.avatarLoadingStrategy.disablePreNetworkCheck && !NetUtil.isNetworkAvailable()) {
            _gameLoadState.emit(
                GameLoadState.LoadFailed.GameLoadFailed(
                    code = ERR_CODE_NETWORK_UNAVAILABLE,
                    message = app.getString(R.string.editor_role_game_loading_failure_network_unavailable),
                    loadConfig = getStateLoadConfig()
                )
            )
            return
        }

        // 加载前再给次机会重新连接
        if (!mvCoreProxyInteractor.connectionStatusFlow.value) {
            if (!connectWithTimeout(10_000)) {
                _gameLoadState.emit(
                    GameLoadState.LoadFailed.GameLoadFailed(
                        code = ERR_CODE_DISCONNECTED,
                        message = app.getString(R.string.editor_role_game_disconnected_from_backend),
                        loadConfig = getStateLoadConfig()
                    )
                )
                return
            }
        }

        EditorGameInteractHelper.setRoleGameId(gameId)
        val context: Context = GlobalContext.get().get()
        val screenWidth = ScreenUtil.getScreenWidth(context)
        val screenHeight = ScreenUtil.getScreenHeight(context)

        val surfaceTexture = SurfaceTexture(0)
        val surface = Surface(surfaceTexture).also {
            this._surface = it
        }

        MWBizProxy.setRender(surface, screenWidth, screenHeight)

        Timber.d("startGameUseView gameId $gameId userinfo ${accountInteractor.accountLiveData.value}")

        try {
            EditorGameInteractHelper.startGameUseView(context, gameId, categoryId)
        } catch (e: Exception) {
            Timber.e(e)

            val message = when (e) {
                is IOException -> {
                    context.getString(R.string.editor_role_gamme_load_failed_api_request_exception)
                }

                is BaseTSLaunchException -> {
                    context.getString(
                        R.string.editor_role_gamme_load_failed_program_exception_formatted,
                        e.getErrorType()
                    )
                }

                else -> {
                    context.getString(R.string.editor_role_gamme_load_failed_program_exception)
                }
            }

            _gameLoadState.emit(
                GameLoadState.LoadFailed.GameLoadFailed(
                    ERR_CODE_START_GAME_CALL_FAILED,
                    message,
                    subMessage = "${e.javaClass.simpleName}_${e.message}",
                    loadConfig = getStateLoadConfig()
                )
            )

            return
        }

        Timber.d("Waiting for role game to load complete status")

        startSuspenableTimeoutTimer(timeout)

        try {

            // 等待可挂起事件
            waitForRoleGameSuspenable(timeout)
            cancelSuspenableTimeoutTimer()
            val waitSuspenableTimeCost = SystemClock.elapsedRealtime() - loadStartTime

            val renderIsTakenByUser = renderIsTakenByUser(surface)

            Timber.d(
                "Role game suspenable event waiting complete suspendable:%s isReceivedAvatarLoadSuccessEvent:%s renderIsTakenByUser:%s",
                suspendable.value,
                isReceivedAvatarLoadSuccessEvent(),
                renderIsTakenByUser
            )

            // 如果没有收到加载成功事件，则挂起引擎，等待用户自己去Tab或者全屏页接管渲染后才继续往后渲染
            if (suspendable.value && !isReceivedAvatarLoadSuccessEvent() && !renderIsTakenByUser) {
                Timber.d("Role game not loaded yet, suspend engine until user take over render")
                MWBizProxy.suspendAwait()
                Timber.d("Role game suspended")
                MWBizProxy.waitUntil(Lifecycle.Event.ON_RESUME)
                Timber.d("Role game resumed")
                waitRoleGameLoadComplete(timeout)
                Timber.d("Role game loaded complete after user take over render")
            } else {
                val remainTimeoutDuration = timeout - waitSuspenableTimeCost
                Timber.d("Role game not loaded yet, wait for role game load complete remainTimeoutDuration:$remainTimeoutDuration")

                if (remainTimeoutDuration > 0) {
                    waitRoleGameLoadComplete(remainTimeoutDuration)
                }
            }

            Timber.d("Role game to loaded complete")

            if (isReceivedAvatarLoadSuccessEvent()) {
                _gameLoadState.emit(GameLoadState.LoadingProgressUpdated(0.99F, "",
                    loadConfig = getStateLoadConfig()))
                Timber.d("Avatar game loaded ")
                MWBizProxy.suspend()
                // 只有在用户接管渲染后才能继续渲染
                // 其他情况都不继续唤醒，而是等待用户接管渲染，否则会导致引擎空转
                if (waitingSceneReadyLifecycleOwner.lifecycle.currentState.isAtLeast(Lifecycle.State.RESUMED)) {
                    Timber.d("Resume before waitForSceneReady")
                    MWBizProxy.resume()
                }
                waitForSceneReady()
                _gameLoadState.emit(GameLoadState.LoadingProgressUpdated(1.0F, "", loadConfig = getStateLoadConfig()))
                _gameLoadState.emit(GameLoadState.LoadSuccess(loadConfig = getStateLoadConfig()))
                Timber.d("Avatar game loaded[scene ready] ")
            } else {
                Timber.d("Avatar game load task canceled")
            }

        } finally {
            // 游戏提前加载失败，需要取消，不然会导致这个job在下一次开始加载的时候才取消
            cancelSuspenableTimeoutTimer()

            if (!isReceivedAvatarLoadSuccessEvent()) {
                val errorInfo = _receivedMwGameLoadError.value
                if (errorInfo != null) {
                    _gameLoadState.emit(
                        GameLoadState.LoadFailed.GameLoadInternalFailed(
                            code = ERR_CODE_ENGINE_INTERNAL_ERROR,
                            message = errorInfo.message,
                            internalErrorCode = errorInfo.code,
                            internalErrorMessage = errorInfo.message,
                            loadConfig = getStateLoadConfig()
                        )
                    )
                } else {
                    _gameLoadState.emit(
                        GameLoadState.LoadFailed.GameLoadFailed(
                            ERR_CODE_GAME_LOAD_FAILED,
                            app.getString(R.string.editor_role_game_loading_timeout),
                            loadConfig = getStateLoadConfig()
                        )
                    )
                }
            }

            // 没有ready不能释放，会导致引擎崩溃反复重复启动
            if (MWBizProxy.isEngineReady()) {
                MWBizProxy.releaseRender(surface)
                surfaceTexture.release()
                surface.release()
                _surface = null
            }
        }
    }

    private suspend fun connectWithTimeout(connectTimeout: Long): Boolean {
        withTimeoutOrNull(connectTimeout) {
            scope.launch {
                while (!MWBizProxy.isConnected()) {
                    MWBizProxy.connect()
                    delay(500)
                }
            }.join()
        }

        return MWBizProxy.isConnected()
    }

    private fun getStateLoadConfig(): AvatarGameLoadConfig {
        val loadState = _gameLoadState.value
        if (loadState is GameLoadState.LoadConfigOwner) {
            // 在重新加载后需要更新配置
            if(loadState !is GameLoadState.LoadSuccess && loadState !is GameLoadState.LoadFailed){
                return loadState.loadConfig
            }
        }
        return AvatarGameLoadConfig(isPreloadCycle, preloadWaitTime, isEngineValidOnLoad, ++loadingCnt)
    }

    private fun renderIsTakenByUser(backgroundLoadSurface: Surface): Boolean {
        val surface = MWBizProxy.getLastUsedSurface()
        return surface != backgroundLoadSurface
    }

    private suspend fun getUserInfo(): Triple<MetaUserInfo?, Int, String> {
        var userInfo: MetaUserInfo? = null
        var message: String
        var code: Int = ERR_CODE_GET_USER_INFO_FAILED

        var retryCount = 0

        if (PandoraToggle.avatarLoadingStrategy.disablePreNetworkCheck || NetUtil.isNetworkAvailable()) {
            do {
                val info = accountInteractor.accountLiveData.value
                if (info != null && !metaKV.account.accessToken.isNullOrEmpty()) {
                    userInfo = info
                    message = app.getString(R.string.success)
                    code = 0
                    break
                } else {
                    message = app.getString(R.string.editor_role_game_failed_to_get_user_info)
                }

                Timber.d("Retry user token load $retryCount times, message:${message}")

                delay(USER_TOKEN_RETRY_INTERVAL)
                yield()
            } while (retryCount++ < USER_TOKEN_RETRY_MAX_COUNT)
        } else {
            message = app.getString(R.string.editor_role_game_loading_failure_network_unavailable)
            code = ERR_CODE_NETWORK_UNAVAILABLE
        }

        val available = userInfo != null

        Analytics.track(EventConstants.EVENT_ROLE_TOKEN_RESULT) {
            put("result", if (available) "success" else "failed")
            put("show_categoryid", categoryId)
        }

        return Triple(userInfo, code, message)
    }

    private suspend fun getRoleViewGameId(categoryId: Int): Triple<String/*RoleViewGameId*/, Int/*ErroCode*/, String/*Message*/> {
        var roleViewGameId = ""
        var message = ""
        var code = ERR_CODE_GET_GAME_ID_FAILED

        var retryCount = 0


        if (PandoraToggle.avatarLoadingStrategy.disablePreNetworkCheck || NetUtil.isNetworkAvailable()) {
            do {
                if (editorInteractor.gameConfigLiveData.value == null) {
                    editorInteractor.fetchGameConfig().join()
                }
                val dataResult = editorInteractor.gameConfigLiveData.value
                if (dataResult?.succeeded == true) {
                    roleViewGameId = dataResult.data?.roleViewGameId ?: ""
                    message = app.getString(R.string.success)
                    code = 0
                    break
                } else {
                    message = app.getString(R.string.editor_role_game_config_load_failed)
                }

                Timber.d("Retry game config load $retryCount times, message:${message}")

                delay(GAME_CONFIG_RETRY_INTERVAL)
                editorInteractor.fetchGameConfig().join()
                yield()
            } while (retryCount++ < GAME_CONFIG_RETRY_MAX_COUNT)
        } else {
            message = app.getString(R.string.editor_role_game_loading_failure_network_unavailable)
            code = ERR_CODE_NETWORK_UNAVAILABLE
        }

        val isGameConfigLoadSuccess = roleViewGameId.isNotBlank()

        Analytics.track(EventConstants.EVENT_ROLE_GAME_ID_RESULT) {
            put("result", if (isGameConfigLoadSuccess) "success" else "failed")
            if (!isGameConfigLoadSuccess) {
                put("api_info", message)
            }
            put("gameid", roleViewGameId)
            put("show_categoryid", categoryId)
        }

        return Triple(roleViewGameId, code, message)
    }

    private suspend fun waitRoleGameLoadComplete(timeout: Long) {
        val coroutineScope = CoroutineScope(coroutineContext)

        return suspendCancellableCoroutine { continuation ->
            coroutineScope.launch {
                if (!isReceivedAvatarLoadSuccessEvent()) {
                    withTimeout(timeout) {
                        EditorGameInteractHelper.roleUserDataLiveData.asFlow()
                            .takeWhile { it != EditorGameInteractHelper.ROLE_STATE_ROLE_LOAD_END }
                            .collect()
                    }
                }
                Timber.d("waitingRoleGameLoadComplete timeout currentEngineState:${MWBizProxy.getLifecycle().currentState}")
            }.invokeOnCompletion {
                Timber.d("waitingRoleGameLoadComplete coroutineScope complete")
                continuation.resume(Unit)
            }
        }
    }

    /**
     * @return 是否等待超时
     */
    private suspend fun waitForRoleGameSuspenable(timeout: Long): Boolean {
        Timber.d("waitingRoleGameSuspenable start timeout:${timeout}")

        val coroutineScope = CoroutineScope(coroutineContext)

        return suspendCancellableCoroutine { continuation ->
            coroutineScope.launch {
                withTimeout(timeout) {
                    suspendable.takeWhile { !it }.collect()
                }
            }.invokeOnCompletion {
                Timber.d("waitingRoleGameSuspenable complete reason:${it}")
                continuation.resume(it is TimeoutCancellationException)
            }
        }
    }


    /**
     * 预加载接口获取角色游戏GameId
     */
    fun preLoadGameConfig() = scope.launch {
        editorInteractor.fetchGameConfig()
        editorInteractor.gameConfigLiveData.asFlow().collect {
            EditorGameInteractHelper.setRoleGameId(it?.data?.roleViewGameId ?: "")
        }
    }

    /**
     * 创角
     */
    fun chooseRole(item: DefaultRoleInfo): Flow<DataResult<Boolean>> {
        return repository.chooseDefaultRole(item.id).map { dataResult ->
            Analytics.track(EventConstants.EVENT_AVATAR_CHOOSE_CONFIRM_RESULT) {
                put("result", if (dataResult.succeeded) "success" else "failed")
                put(
                    "api_info",
                    if (dataResult.succeeded) "data=${dataResult.data}" else "code=${dataResult.code}, message= ${if (dataResult.code == 0) dataResult.message else "backend self error"}"
                )
                if (dataResult is DataResult.Error) {
                    put("error_msg", dataResult.exception?.cause.toString())
                }
            }

            item.wholeBodyImage2?.let {
                val modifyUserFullBodyImgResult = repository.modifyUserFullBodyImg(it).last()
                if (!modifyUserFullBodyImgResult.succeeded) {
                    throw RuntimeException(modifyUserFullBodyImgResult.message)
                }
            }

            if (dataResult.succeeded && dataResult.data == true) {
                accountInteractor.updateLocalInfo(
                    item.portrait,
                    item.wholeBodyImage,
                    item.wholeBodyImage2,
                    true
                )
                // 成功则向角色游戏发送刷新协议（不关注游戏有没有加载完毕，如果没有加载完毕，则游戏启动后调接口能拿到最新创角数据）
                delay(50)
                UGCProtocolSender.sendProtocol(
                    ProtocolSendConstant.PROTOCOL_CLIENT_GAME_FEATURE,
                    0,
                    AndroidCommonResult(
                        AndroidCommonResult.FEATURE_REFRESH_AVATAR,
                        EditorGameInteractHelper.getRoleGameId(),
                        hashMapOf()
                    ).getDataMapPackedResult()
                )
            } else {
                throw RuntimeException(dataResult.message)
            }
            dataResult
        }.retry(2) {
            it is RuntimeException
        }.catch {
            Timber.e("checkcheck_role retry all failed")
            emit(DataResult.Error(0, it.message ?: ""))
        }
    }

    /**
     * 重新启动角色游戏
     */
    fun reLaunchRoleGame(
        categoryId: Int = this.categoryId,
        timeout: Long = getDefaultTimeoutInMs()
    ): Job {
        this.categoryId = categoryId
        Timber.d("checkcheck_role reLaunchRoleGame categoryId:$categoryId isConnectedToRemote:${MWBizProxy.isConnected()} currentState:${MWBizProxy.getLifecycle().currentState}")

        return scope.launch { manuallyStartGameFlow.emit(ManuallyStartGameEvent(timeout)) }
    }

    /**
     * 获取默认超时时长
     * 开启渐进超时时长策略的时候默认为1分钟
     * 没开启时默认超时30s
     */
    private fun getDefaultTimeoutInMs(): Long {
        return if (PandoraToggle.avatarLoadingStrategy.progressiveBackOff) {
            TimeUnit.MINUTES.toMillis(1)
        } else {
            TimeUnit.SECONDS.toMillis(30)
        }
    }

    private fun isReceivedAvatarLoadSuccessEvent(): Boolean {
        return EditorGameInteractHelper.roleUserDataLiveData.value == EditorGameInteractHelper.ROLE_STATE_ROLE_LOAD_END
    }

    /**
     * 将角色服务绑定到通知上，避免被系统杀死
     */
    private fun bindForegroundNotificationIfNeeded() {
        Timber.d(
            "startForegroundNotificationIfNeeded isOpenAvatarForegroundNotification:%s isConnectedRenderProcess:%s isRunningForeground:%s isBoundForegroundNotification:%s",
            PandoraToggle.isOpenAvatarForegroundNotification,
            _isConnectedRenderProcess.get(),
            ProcessLifecycleOwner.get().lifecycle.currentState.isAtLeast(Lifecycle.State.STARTED),
            MWBizProxy.isBoundForegroundNotification()
        )

        if (!PandoraToggle.isOpenAvatarForegroundNotification) return

        if (MWBizProxy.isBoundForegroundNotification()) return

        val channelId = "avatar"

        val builder = NotificationCompat
            .Builder(app, channelId)
            .setContentTitle(app.getString(R.string.avatar_foreground_notification_title))
            .setContentText(app.getString(R.string.avatar_foreground_notification_content))
            .setWhen(System.currentTimeMillis())
            .setSmallIcon(R.drawable.ic_small_notification)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setContentIntent(getLaunchAvatarIntent(MetaDeepLink.SOURCE_NOTIFICATION_AVATAR_NOTIFICATION_CLICKED))
            .setStyle(NotificationCompat.BigTextStyle())
            .addAction(NotificationCompat.Action.Builder(0, app.getString(R.string.avatar_foreground_notification_stop_avatar), getStopAvatarIntent()).build())
            .setBadgeIconType(NotificationCompat.BADGE_ICON_NONE)
            .addAction(
                NotificationCompat.Action.Builder(
                    0,
                    app.getString(R.string.avatar_foreground_notification_launch_avatar),
                    getLaunchAvatarIntent(MetaDeepLink.SOURCE_NOTIFICATION_AVATAR_NOTIFICATION_LAUNCH_CLICKED)
                ).build()
            )

        // 前台服务notification适配
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = app.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager? ?: return
            val channel = NotificationChannel(channelId, "My Avatar", NotificationManager.IMPORTANCE_LOW)
            channel.setShowBadge(false)
            notificationManager.createNotificationChannel(channel)
        }

        MWBizProxy.bindForegroundNotification(builder.build(), R.integer.avatar_foreground_notification_id)
    }

    private fun getLaunchAvatarIntent(source: Int): PendingIntent? {
        val uri = MetaRouter.Main.buildLocalJumpUri(
            action = MetaDeepLink.ACTION_JUMP_TAB_ANY,
            data = mapOf(
                MetaDeepLink.PARAM_TAB_ID to MainBottomNavigationItem.EDITOR_HOME.itemId,
                MetaDeepLink.PARAM_SOURCE to source,
            )
        )

        val launchIntent =
            runCatching { app.packageManager.getLaunchIntentForPackage(app.packageName) }.getOrNull()
        val pendingIntent = launchIntent?.let {
            it.data = uri
            PendingIntent.getActivity(app, 0, it, PendingIntent.FLAG_IMMUTABLE)
        }

        return pendingIntent
    }

    private fun getStopAvatarIntent(): PendingIntent {
        val stopIntent = Intent(app, StopAvatarService::class.java)
        return PendingIntent.getService(app, 0, stopIntent, PendingIntent.FLAG_IMMUTABLE)
    }

    /**
     * 本地是否有有效的的引擎
     */
    fun isEngineValid(): Boolean {
        return isMwEngineValid
    }

    private class ManuallyStartGameEvent(val timeout: Long)

    private enum class LoadTrigger {
        Manually,
        AccountChanged,
        ConnectionChanged,
        LanguageChange,
    }

    /**
     * 专门用于通知栏杀死角色的
     */
    class StopAvatarService : Service() {
        override fun onBind(intent: Intent?): IBinder? {
            return null
        }

        override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
            GlobalContext.get().get<EditorGameLoadInteractor>().stop("notice_stop")
            runCatching { stopSelf(startId) }
            AvatarAnalytics.notificationClicked("stop")
            return super.onStartCommand(intent, flags, startId)
        }
    }
}