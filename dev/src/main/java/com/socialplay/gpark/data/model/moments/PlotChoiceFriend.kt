package com.socialplay.gpark.data.model.moments

import com.socialplay.gpark.ui.moments.main.SimMultiItem

/**
 * 2023/9/20
 */
data class PlotChoiceFriendTitle(val name: String) : SimMultiItem {

    companion object {
        const val TYPE_TITLE = 0
    }

    override fun viewType(): Int = TYPE_TITLE
}

data class PlotChoiceFriendItem(
    var selectIndex: Int,
    val avatar: String,
    val userName: String,
    val uid: String,
    val isMyAvatar:Boolean,
) : SimMultiItem {

    companion object {
        const val TYPE_ITEM = 1
    }

    override fun viewType(): Int = TYPE_ITEM
}


data class PlotChoiceFriendEmpty(val desc: String) : SimMultiItem {

    companion object {
        const val TYPE_FRIEND_EMPTY = 2
    }

    override fun viewType(): Int = TYPE_FRIEND_EMPTY
}
