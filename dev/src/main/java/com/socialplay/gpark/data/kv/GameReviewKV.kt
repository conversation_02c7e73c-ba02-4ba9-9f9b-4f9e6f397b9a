package com.socialplay.gpark.data.kv

import com.socialplay.gpark.data.model.gamereview.RequestGameReviewsParam.Companion.MODULE_TYPE_AI_BOT
import com.socialplay.gpark.data.model.gamereview.RequestGameReviewsParam.Companion.MODULE_TYPE_GAME_ONLY
import com.tencent.mmkv.MMKV

/**
 * Created by bo.li
 * Date: 2022/7/5
 * Desc: 游戏评论
 */
class GameReviewKV(private val mmkv: MMKV, private val account: AccountKV) {

    companion object {
        private const val KEY_GAME_REVIEW_FIRST_PLAY_TIMES_PREFIX = "key_game_review_first_play_times_"
        private const val KEY_GAME_REVIEW_ADD_UP_TIMES_PREFIX = "key_game_review_add_up_times_"
        private const val KEY_GAME_REVIEWED_PREFIX = "key_game_reviewed_"
    }

    /**
     * 获取对应用户是否评论过该游戏
     */
    fun getIsGameReviewed(gameId: String, modeCode: Int?=MODULE_TYPE_GAME_ONLY): Bo<PERSON>an {
        if (modeCode == MODULE_TYPE_AI_BOT) {
            return mmkv.getBoolean(
                "$KEY_GAME_REVIEWED_PREFIX${gameId}_${account.uuid}" + "aibot",
                false
            )
        } else {
            return mmkv.getBoolean("$KEY_GAME_REVIEWED_PREFIX${gameId}_${account.uuid}", false)
        }

    }

    /**
     * 保存对应用户是否评论过该游戏
     */
    fun saveIsGameReviewed(gameId: String, modeCode: Int ,reviewed: Boolean) {
        if (modeCode == MODULE_TYPE_GAME_ONLY) {
            mmkv.putBoolean("$KEY_GAME_REVIEWED_PREFIX${gameId}_${account.uuid}", reviewed)
        } else if (modeCode == MODULE_TYPE_AI_BOT) {
            mmkv.putBoolean("$KEY_GAME_REVIEWED_PREFIX${gameId}_${account.uuid}"+"aibot", reviewed)
        }

    }

    /**
     * 获取单次时长策略对应日期、用户已弹窗次数
     */
    fun getAddUpTimesPrefix(date: String): Int {
        return mmkv.getInt("$KEY_GAME_REVIEW_ADD_UP_TIMES_PREFIX${date}_${account.uuid}", 0)
    }

    /**
     * 保存单次时长策略对应日期、用户已弹窗次数
     */
    fun saveAddUpTimesPrefix(date: String, times: Int) {
        mmkv.putInt("$KEY_GAME_REVIEW_ADD_UP_TIMES_PREFIX${date}_${account.uuid}", times)
    }

    /**
     * 获取首次策略对应日期、用户已弹窗次数
     */
    fun getFirstPlayTimesPrefix(date: String): Int {
        return mmkv.getInt("$KEY_GAME_REVIEW_FIRST_PLAY_TIMES_PREFIX$date${account.uuid}", 0)
    }

    /**
     * 保存首次策略对应日期、用户已弹窗次数
     */
    fun saveFirstPlayTimesPrefix(date: String, times: Int) {
        mmkv.putInt("$KEY_GAME_REVIEW_FIRST_PLAY_TIMES_PREFIX$date${account.uuid}", times)
    }
}