package com.socialplay.gpark.data.model.unpublished

import com.google.gson.annotations.SerializedName
import com.socialplay.gpark.data.kv.TTaiKV
import java.util.concurrent.TimeUnit

/**
 * 未发布项目提醒弹框配置
 */
data class UnpublishedProjectConfig(
    /**
     * 最后保存时间阈值（天）
     * 默认10天
     */
    @SerializedName("modifyWithinDays")
    val modifyWithinDays: Int = 10,

    /**
     * 项目编辑时长阈值（分钟）
     * 默认5分钟
     */
    @SerializedName("minTime")
    val minTime: Int = 5,

    /**
     * 弹框展示间隔时间（小时）
     * 默认24小时
     */
    @SerializedName("intervalTime")
    val intervalTime: Int = 24
) {
    /**
     * 获取最后保存时间阈值（毫秒）
     */
    fun getMaxSaveTimeAgoMillis(): Long {
        return TimeUnit.DAYS.toMillis(modifyWithinDays.toLong())
    }

    /**
     * 获取项目编辑时长阈值（毫秒）
     */
    fun getMinEditDurationMillis(): Long {
        return TimeUnit.MINUTES.toMillis(minTime.toLong())
    }

    /**
     * 获取弹框展示间隔时间（毫秒）
     */
    fun getDialogShowIntervalMillis(): Long {
        return TimeUnit.HOURS.toMillis(intervalTime.toLong())
    }

    companion object {
        // T台配置ID
        const val TTAI_CONFIG_ID = TTaiKV.ID_UNPUBLISHED_PROJECT_CONFIG
    }
}
