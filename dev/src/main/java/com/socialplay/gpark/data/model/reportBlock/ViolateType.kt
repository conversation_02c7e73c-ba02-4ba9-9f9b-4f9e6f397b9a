package com.socialplay.gpark.data.model.reportBlock

import com.socialplay.gpark.data.model.RESPONSE_CODE_ACCOUNT_BANNED
import com.socialplay.gpark.data.model.RESPONSE_CODE_ACCOUNT_MUTE
import com.socialplay.gpark.data.model.RESPONSE_CODE_ACCOUNT_WARNING

/**
 * Created by bo.li
 * Date: 2024/7/22
 * Desc: 违反规则弹窗（警告、禁言、封禁）
 */
enum class ViolateType(val key: String, val code: Int) {
    Warning("warning", RESPONSE_CODE_ACCOUNT_WARNING), // 警告
    Mute("mute", RESPONSE_CODE_ACCOUNT_MUTE), // 禁言
    Ban("ban", RESPONSE_CODE_ACCOUNT_BANNED), // 封禁
}