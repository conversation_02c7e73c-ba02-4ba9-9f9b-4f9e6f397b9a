package com.socialplay.gpark.data.model.event

import com.bin.cpbus.CpEventBus
import com.socialplay.gpark.data.model.account.ProfileLinkInfo

data class UserDataUpdateEvent(
    val eventType: Int,
    val nickname: String? = null,
    val signature: String? = null,
    val profileLinkInfo: ProfileLinkInfo? = null,
) {
    companion object {
        const val TYPE_NICKNAME = 1
        const val TYPE_SIGNATURE = 2
        const val TYPE_EXTERNAL_LINK_ADD = 3
        const val TYPE_EXTERNAL_LINK_DEL = 4

        fun notifyNickname(nickname: String) {
            CpEventBus.post(UserDataUpdateEvent(TYPE_NICKNAME, nickname = nickname))
        }

        fun notifySignature(signature: String) {
            CpEventBus.post(UserDataUpdateEvent(TYPE_SIGNATURE, signature = signature))
        }

        fun notifyAddProfileLinkInfo(profileLinkInfo: ProfileLinkInfo) {
            CpEventBus.post(
                UserDataUpdateEvent(
                    TYPE_EXTERNAL_LINK_ADD,
                    profileLinkInfo = profileLinkInfo
                )
            )
        }

        fun notifyDeleteProfileLinkInfo(profileLinkInfo: ProfileLinkInfo) {
            CpEventBus.post(
                UserDataUpdateEvent(
                    TYPE_EXTERNAL_LINK_DEL,
                    profileLinkInfo = profileLinkInfo
                )
            )
        }
    }
}