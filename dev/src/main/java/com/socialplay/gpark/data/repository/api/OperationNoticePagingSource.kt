package com.socialplay.gpark.data.repository.api

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.mapper.MetaMapper
import com.socialplay.gpark.data.model.editor.NoticeWrapper
import kotlinx.coroutines.flow.single

class OperationNoticePagingSource(
    private val repository: IMetaRepository,
) : PagingSource<Int, NoticeWrapper>() {

    override fun getRefreshKey(state: PagingState<Int, NoticeWrapper>): Int {
        return 1
    }

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, NoticeWrapper> {
        return try {
            val pageNum = params.key ?: 1
            if (pageNum > 1) {
                // 新的获取IM运营位的接口, 不支持分页获取, 而是全部返回
                return LoadResult.Page(mutableListOf(), null, null)
            }
            val result = repository.getOperationNoticeListV2().single()
            if (result.succeeded) {
                val data = result.data

                return if (data.isNullOrEmpty()) {
                    LoadResult.Page(mutableListOf(), null, null)
                } else {
                    LoadResult.Page(data.map {
                        NoticeWrapper(type = NoticeWrapper.TYPE_SYSTEM, systemNotice = it)
                    }.toList(), null, pageNum + 1)
                }
            } else {
                LoadResult.Error(Exception(result.message))
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }
}