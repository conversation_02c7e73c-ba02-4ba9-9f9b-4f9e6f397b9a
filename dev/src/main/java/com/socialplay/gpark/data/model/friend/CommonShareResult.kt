package com.socialplay.gpark.data.model.friend

/**
 * Created by bo.li
 * Date: 2022/11/29
 * Desc:
 */
data class CommonShareResult (
    // 与web透传内容
    val content: Any?,
    // 图标图片链接：用不到
    val icon: String?,
    // 跳转链接
    val jumpUrl: String?,
    // 唯一id
    val shareId: String?,
    // 副标题：用不到
    val subtitle: String?,
    // 标题：用不到
    val title: String?,
    val shareUser: ShareUser?,
    // 分享场景：hut
    val shareScene: String?,
    val shareQrCode: String?
) {
    data class ShareUser(
        // 用户头像
        val portrait: String?,
        // 用户头部形象地址
        val headImage: String?,
        // 来源用户昵称
        val nickname: String?,
        // 来源用户id
        val uid: String?,
        // 用户全身静态形象
        val wholeBodyImage: String?,
    )
}
