package com.socialplay.gpark.data.model.plot

import com.socialplay.gpark.ui.moments.main.SimMultiItem

/**
 * 2023/9/22
 */
data class PlotTemplateList(val total: Int, val dataList: List<PlotTemplateStyle>)

data class PlotTemplateStyle(
    // 子列表
    val socialPlotTemplateDTOList: List<PlotTemplate>?,
    // 模版类型 id
    val id: Int?,
    // 模板类型icon
    val picon: String?,
    // 模板类型名称
    val pname: String?,
    // 名称
    val pstyle: Int,
) {
    companion object {
        /*样式 0:一行多列 1:多行两列 2:多行三列*/
        const val TYPE_1_ROW_N_COLUMN = 0
        const val TYPE_N_ROW_2_COLUMN = 1
        const val TYPE_N_ROW_3_COLUMN = 2
    }

    fun support(): Boolean {
        return pstyle == TYPE_1_ROW_N_COLUMN || pstyle == TYPE_N_ROW_2_COLUMN || pstyle == TYPE_N_ROW_3_COLUMN
    }
}

data class PlotTemplate(
    val gameId: String,/*游戏id*/
    val extraConfig: String,/*额外配置(json字符串, 例如：{"actionguid": 11, "backgroundguid": 222 })*/
    val materialUrl: String,/*素材缩略图*/
    val templateName: String,/*模板名称*/
    val templateId: Int,/*模板id*/
    val templateType: Int,/*模板类型*/
    val useCount: Int,/*使用量*/
    val createTime: Long,/**/
    val updateTime: Long,/**/
    val isTop: Boolean,/*是否置顶*/
    val weightTop: Long, /*如果是置顶的模板，他的权重 */
    val localPaddingType: Int, /*本地化内间距类型*/
    val localStyle: Int, /*同 com/meta/box/data/model/plot/PlotTemplate.kt:20 pstyle */
) : SimMultiItem {

    override fun viewType(): Int = localStyle
}