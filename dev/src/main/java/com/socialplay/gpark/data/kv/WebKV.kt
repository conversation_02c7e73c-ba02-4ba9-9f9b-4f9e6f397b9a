package com.socialplay.gpark.data.kv

import com.tencent.mmkv.MMKV

/**
 * 用于保存web相关的内容
 * <AUTHOR>
 * @date 2021/06/09
 */
class WebKV(private val webMmkv: MMKV) {

    /**
     * 保存web传入的数据
     */
    fun saveWebData(key: String, text: String): Bo<PERSON>an {
        webMmkv.putString(key, text)
        return true
    }

    /**
     * 获取web存的数据
     */
    fun getWebData(key: String): String {
        return webMmkv.getString(key, "") ?: ""
    }
}