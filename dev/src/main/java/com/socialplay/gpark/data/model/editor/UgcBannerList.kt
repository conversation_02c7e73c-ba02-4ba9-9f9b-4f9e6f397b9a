package com.socialplay.gpark.data.model.editor

/**
 * Created by bo.li
 * Date: 2022/10/28
 * Desc:
 */
data class UgcBannerInfo(
    // banner类型 inner_link 内部web链接 outer_link 浏览器web链接 video 视频
    val type: String?,
    // 展示图片
    val showImage: String?,
    // 资源宽度（不是banner）
    val width: Int,
    // 资源高度（不是banner）
    val height: Int,
    // banner资源链接 跳转链接/媒体资源链接
    val linkUrl: String?,
    // banner名称
    val name: String?,
    // 视频头图
    val videoHeadImage: String?
) {
    companion object {
        const val JUMP_INNER_WEB_TYPE = "inner_link"
        const val JUMP_OUTER_WEB_TYPE = "outer_link"
        const val JUMP_INNER_VIDEO_TYPE = "video"
    }

    fun isJumpInnerVideo() = type == JUMP_INNER_VIDEO_TYPE
    fun isJumpInnerWeb() = type == JUMP_INNER_WEB_TYPE
    fun isJumpOuterWeb() = type == JUMP_OUTER_WEB_TYPE

    fun isLandscapeVideo() = isJumpInnerVideo() && width > height
    fun isPortraitVideo() = isJumpInnerVideo() && width <= height
}