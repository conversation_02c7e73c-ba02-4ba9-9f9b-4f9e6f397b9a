package com.socialplay.gpark.data.model.editor

data class GroupedData<T>(
    val id:String?,
    val title: String?,
    val items: List<T>?,
    val type: Int?,
    val tag: List<T> = emptyList(),
) {
    companion object {
        // T台配置的精选卡片类型 moment
        const val CHOICE_CARD_TYPE_TTAI_MOMENT = 101

        // T台配置的精选卡片类型 popular
        const val CHOICE_CARD_TYPE_TTAI_POPULAR = 102
        // AIBot 卡片类型
        const val  CHOICE_CARD_TYPE_AI_BOT = 103
    }
}