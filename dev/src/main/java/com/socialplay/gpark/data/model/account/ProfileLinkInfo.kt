package com.socialplay.gpark.data.model.account

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/08/16
 *     desc   :
 *
 */
@Parcelize
data class ProfileLinkInfo(
    val icon: String?,
    val title: String?,
    val url: String?,
    val urls: List<ValidationUrl>? = arrayListOf(),
    val id: String?,
    val type: String?
) : Parcelable

@Parcelize
data class ValidationUrl(val url:String?,val isEndMatch:Boolean) : Parcelable


data class UserCreateInfo(val ugcCreatorStatus: Int, val postCreatorStatus: Int) {
    companion object {
        //0-未认证，1-审核中，2-申请失败，4-已认证
        const val IS_CREATE = 4
    }
}

