package com.socialplay.gpark.data.model.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "recent_ugc_game")
data class RecentUgcGameEntity(
    @PrimaryKey
    // ugcId
    val id: String,
    // ugc包名
    var packageName: String,
    // ugc模板gameId
    var gameCode: String,
    // ugc作品名称
    var gameName: String? = null,
    // ugc作品icon
    var gameIcon: String? = null,
    // 创作者名称
    var username: String? = null,
    // 创作者头像
    var userAvatar: String? = null,
    // 点赞数
    var likeCount: Long = 0L,
    // 是否对作品点赞
    var likeIt: Boolean = false,
    // 作品热度
    var popularity: Long = 0L,
    // ugc作品的更新发布时间
    var updateTime: Long = 0L,
    // ugc作品的发布时间
    var releaseTime: Long = 0L,
    // 访问ugc作品的时间戳
    var visitTime: Long = System.currentTimeMillis()
) {
    fun toPlayedEntity():MyPlayedGameEntity{
        return MyPlayedGameEntity(
            id,
            System.currentTimeMillis(),
            gameName?:"",
            gameIcon?:"",
            0,
            null,
            1f,
            packageName,
            visitTime,
            System.currentTimeMillis(),
            null,
            isUgc = 1
        )
    }

    /**
     * 校验是否包含必要展示字段
     * @return true：完整，false：不完整
     */
    fun verifyIntegrity(): Boolean {
        return id.isNotEmpty() && packageName.isNotEmpty() && gameName != null && gameCode.isNotEmpty() && username != null
    }

    override fun equals(other: Any?): Boolean {
        if (other is RecentUgcGameEntity) {
            return this.id == other.id
        }
        return super.equals(other)
    }

    interface Convertor {
        fun toMetaRecentUgcGameEntity(): RecentUgcGameEntity
    }
}