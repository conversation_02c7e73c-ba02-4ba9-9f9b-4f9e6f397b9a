package com.socialplay.gpark.data.model.user

/**
 * @author: ning.wang
 * @date: 2021-09-15 11:42 上午
 * @desc:
 */
data class VisitorInfoApiResult(
//    @Deprecated("use double token")
//    val token: String? = null, // 单token的旧版本使用，客户端已废弃
    val customToken: String? = null, // firebase token，firebase用
    val refreshToken: String?, // refresh token，业务用，双token
    val accessToken: String? = null, // accessToken，业务用，双token
    val accessTokenExpire: Long, // accessToken剩余过期时间
    val userInfo: MetaUserInfo? = null
)