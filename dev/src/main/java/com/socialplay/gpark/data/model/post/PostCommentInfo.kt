package com.socialplay.gpark.data.model.post

/**
 * Created by bo.li
 * Date: 2023/9/15
 * Desc: 帖子评论
 */
data class PostCommentInfo(
    val commentId: String,
    val uid: String,
    val likeCount: Long,
    val content: String?,
    val commentTime: Long,
    // 用户点赞状态：0无感、1点赞
    val opinion: Int,
    val nickname: String?,
    val avatar: String?,
    val floor: Int,
) {

    val opinionLike: Boolean
        get() = opinion == OPTION_LIKE

    companion object {
        const val OPTION_LIKE = 1
        const val OPTION_NO_STATE = 0

        fun getOpinion(like: <PERSON><PERSON><PERSON>): Int {
            return if (like) OPTION_LIKE else OPTION_NO_STATE
        }
    }
}