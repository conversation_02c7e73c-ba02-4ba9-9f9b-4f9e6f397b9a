package com.socialplay.gpark.data.model.reportBlock

/**
 * todo libo 抽出来
 * @describe:[枚举类型] https://meta.feishu.cn/wiki/Cy7kw6zDJiYaSQkM1PBcMMtKnzd
 */
enum class ReportType(val key: String) {
    ReportUser("user"), //举报用户
    PgcReview("pgc_game_review"), //举报pgc评论
    UgcReview("ugc_game_review"), //举报ugc评论
    PgcReply("pgc_game_reply"), // 举报pgc评论回复
    UgcReply("ugc_game_reply"), // 举报ugc评论回复
    ReportRoom("game_room"), //举报房间
    Post("post"), // 帖子
    PostComment("post_comment"), // 帖子评论
    PostReply("post_reply"), // 帖子回复
    UgcClothing("ugc_clothing"), // 帖子回复
    UgcClothingComment("ugc_clothing_review"), // 帖子回复
    UgcClothingReply("ugc_clothing_reply"), // 帖子回复
    ReportGroupChat("chat_group"), //举报群聊
}