package com.socialplay.gpark.data.model.feedback

/**
 * Created by bo.li
 * Date: 2023/8/30
 * Desc:
 */
data class FeedbackRequest(
    val tenant: String = FeedbackRequestWrapper.tenant
)

data class FeedbackConfigItem(
    // 来源code
    val originCode: String?,
    // 反馈内容是否可以为空 true不可空 false可空
    val feedbackTxtNotNull: Boolean,
    // 是否展示反馈内容输入框 true展示 false不展示
    val ifShowTextBox: Boolean,
    // 该来源下的反馈问题的选项列表
    val optionConfigList: List<FeedbackOptionData>?,
    // 选项是否可以为空 true不可空 false可空
    val optionNotNull: Boolean,
    // 选择方式 0单选 1多选
    val chooseWay: Int
) {
    companion object {
        const val CHOOSE_WAY_SINGLE = 0
        const val CHOOSE_WAY_MULTI = 1
    }
}

data class FeedbackOptionData(
    val id: String,
    val desc: String?
)