package com.socialplay.gpark.data.local.migrations

import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/08/20
 *     desc   :
 * </pre>
 */
internal class MetaAppDatabase_Migration_4_5 : Migration(4, 5) {
    override fun migrate(db: SupportSQLiteDatabase) {
        try {
            //language=RoomSql
            db.execSQL(
                "CREATE TABLE IF NOT EXISTS `share_record` (" +
                        "`id` TEXT PRIMARY KEY NOT NULL," +
                        "`platform` TEXT NOT NULL," +
                        "`shareTime` INTEGER NOT NULL" +
                        ")"
            )
            //language=RoomSql
            db.execSQL(
                "ALTER TABLE `game_detail` ADD COLUMN `shareCount` INTEGER NOT NULL DEFAULT 0"
            )
            //language=RoomSql
            db.execSQL(
                "ALTER TABLE `game_detail` ADD COLUMN `hasCommunity` INTEGER NOT NULL DEFAULT 0"
            )
        } catch (ignore: Throwable) {
        }
    }
}