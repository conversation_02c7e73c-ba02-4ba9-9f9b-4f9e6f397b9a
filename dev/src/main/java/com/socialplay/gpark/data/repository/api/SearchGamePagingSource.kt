package com.socialplay.gpark.data.repository.api

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.socialplay.gpark.data.api.MetaApi
import com.socialplay.gpark.data.model.ListItem
import com.socialplay.gpark.data.model.SearchGameInfo
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.search.SearchHelper
import timber.log.Timber

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/09/12
 * desc   :
 * </pre>
 */


class SearchGamePagingSource(
    private val api: MetaApi,
    private val searchKey: String,
    private val isRelate: Boolean,
    private val keywordId:String?,
) : PagingSource<Pair<Int, Long?>, ListItem>() {
    override fun getRefreshKey(state: PagingState<Pair<Int, Long?>, ListItem>): Pair<Int, Long?>? {
        return null
    }

    override suspend fun load(params: LoadParams<Pair<Int, Long?>>): LoadResult<Pair<Int, Long?>, ListItem> {

        val cur = params.key?.first ?: 1
        val last = params.key?.second

        return try {
//            searchKey, cur
            val dataResult = if (isRelate) {
                api.getSearchRelate(
                    hashMapOf(
                        "keyword" to searchKey,
                        "page" to cur,
                        "lastOrderNum" to last,
                        "typeList" to SearchHelper.typeList
                    )
                )
            } else {
                if (keywordId.isNullOrEmpty()){
                    api.getSearchGameInfo(
                        hashMapOf(
                            "keyword" to searchKey,
                            "page" to cur,
                            "lastOrderNum" to last,
                            "typeList" to SearchHelper.typeList
                        )
                    )
                }else{
                    api.getSearchGameInfoById(
                        hashMapOf(
                            "keywordId" to keywordId,
                            "page" to cur,
                            "lastOrderNum" to last,
                            "typeList" to SearchHelper.typeList
                        )
                    )
                }
            }
            val data = dataResult.data
            if (!isRelate&&cur==1){
                Analytics.track(
                    EventConstants.EVENT_SEARCH_RESULT,
                    "hasresult" to (data?.list?.isNotEmpty() == true).toString(),
                    "keyword" to searchKey,
                    "reason" to dataResult.message,
                    "number" to (data?.list?.size?:0),
                    "searchtab" to "game",
                )
            }

            val items = data?.list
            val prevKey = null
            val nextKey = cur + 1
            Timber.d("cur:$cur, prevKey:$prevKey, nextKey:$nextKey, params:$params")

            if (data == null || items.isNullOrEmpty()) {
                if (dataResult.isSuccessful) {
                    LoadResult.Page(emptyList(), prevKey, null)
                } else {
                    LoadResult.Error(NullPointerException())
                }
            } else {
                LoadResult.Page(items, prevKey, if (isRelate) null else nextKey to data.lastOrderNum)
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }
}