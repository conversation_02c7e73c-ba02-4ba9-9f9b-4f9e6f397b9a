package com.socialplay.gpark.data.model.room

import com.socialplay.gpark.data.model.choice.IChoiceItem

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2023/10/12
 *     desc   :
 *
 */
data class CottageRoomList(
    val currentPage: Int,
    val houses: ArrayList<House>,
    val totalPage: Int
)

data class House(
    var description: String? = null,
    val gameId: String,
    var image: String? = null,
    var limitNumber: String? = null,
    var number: String? = null,
    var ownerAvatar: String? = null,
    var ownerNickname: String? = null,
    var ownerUuid: String? = null,
    val roomId: String? = null,
    var sceneId: String? = null,
    var version: String? = null
) : IChoiceItem