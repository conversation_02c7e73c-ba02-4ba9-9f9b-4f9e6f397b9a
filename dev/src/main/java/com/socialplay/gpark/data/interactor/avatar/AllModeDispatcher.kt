package com.socialplay.gpark.data.interactor.avatar

import android.app.Application
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner

class AllModeDispatcher(
    owner: IAvatarLifecycleDispatcherOwner
) : AbstractAvatarLifecycleDispatcher(owner) {


    override fun dispatchEnterEditMode(lifecycleOwner: LifecycleOwner, activity: FragmentActivity) {
        if (isLifecycleOwnerChanged(lifecycleOwner) ) {
            resetLifecycleOwner(lifecycleOwner, activity)
            if (owner.isAvailable) {
                bindLifecycle()
            }
        }
    }

    override fun dispatchEnterViewMode(lifecycleOwner: LifecycleOwner, activity: FragmentActivity) {
        if (isLifecycleOwnerChanged(lifecycleOwner)) {
            resetLifecycleOwner(lifecycleOwner, activity)
            if (owner.isAvailable) {
                bindLifecycle()
            }
        }
    }

    override fun dispatchAvailableChanged(app: Application, isAvailable: Boolean) {
        if (!isAvailable) {
            unbindLifecycle()
            dispatchApplicationEvent(app, ApplicationEvent.DESTROY)
        } else {
            dispatchApplicationEvent(app, ApplicationEvent.CREATED)
            if (!isBoundLifecycle()) {
                bindLifecycle()
            }
        }
    }

    override fun dispatchAvatarGameLoadFinish(isLoaded: Boolean) {
    }

}