package com.socialplay.gpark.data.repository

import com.socialplay.gpark.data.api.MetaApi
import com.socialplay.gpark.data.base.DataSource
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.base.suspendApiNotNull
import com.socialplay.gpark.data.interactor.RecommendApiHeaderWrapper
import com.socialplay.gpark.data.local.SimpleDiskLruCache
import com.socialplay.gpark.data.model.HomeRecommend
import com.socialplay.gpark.data.model.HomeRecommendOperationBody
import com.socialplay.gpark.data.model.choice.RecommendRequestBody
import kotlinx.coroutines.flow.flow

class RecommendRepository(private val api: MetaApi, private val diskLruCache: SimpleDiskLruCache) {
    private val gameSet = hashSetOf<String>()

    fun postRecommend(offset: Int? = null) = suspendApiNotNull {
        if (offset == null) {
            gameSet.clear()
        }
        val headers = RecommendApiHeaderWrapper.getHeaders()
        api.postRecommend3(RecommendRequestBody(offset = offset), headers)
    }.map {
        val list = mutableListOf<HomeRecommend.RecommendList>()
        it.list?.forEach { item ->
            item.reqid = it.requestId
            if (!(item.isGame() && gameSet.contains(item.gameDetail?.code))) {
                gameSet.add(item.gameDetail?.code ?: "")
                list.add(item)
            }
        }
        it.list = list
        it
    }

    fun postCustomRecommend(page: Int, pageExpand: String?, overrideIds: List<String?>? = null) = suspendApiNotNull {
        val map = mutableMapOf<String, Any>()
        map.put("page", page)
        pageExpand?.let {
            map.put("pageExpand", pageExpand)
        }
        overrideIds?.let {
            map.put("overrideIds", overrideIds)
        }
        api.postCustomRecommend(map)
    }

    fun postRecommendFlow(offset: Int? = null) = flow {
        val result = DataSource.getDataResultForApi {
            if (offset == null) {
                gameSet.clear()
            }
            val headers = RecommendApiHeaderWrapper.getHeaders()
            api.postRecommend3(RecommendRequestBody(offset = offset), headers)
        }.map {
            val list = mutableListOf<HomeRecommend.RecommendList>()
            it.list?.forEach { item ->
                item.reqid = it.requestId
                if (!(item.isGame() && gameSet.contains(item.gameDetail?.code))) {
                    gameSet.add(item.gameDetail?.code ?: "")
                    list.add(item)
                }
            }
            it.list = list
            it
        }
        emit(result)
    }

    fun getHomeRecommendOperations() = suspendApiNotNull {
        api.getHomeRecommendOperations(HomeRecommendOperationBody())
    }

    fun postMapsNewest(page: Int, size: Int = 17, orderNum: Long? = null) = suspendApiNotNull {
        api.postMapsNewest(mapOf("page" to page.toLong(), "size" to size.toLong(), "orderNum" to orderNum))
    }
}