package com.socialplay.gpark.data.interactor

import androidx.fragment.app.Fragment
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.model.user.UserRelation
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.ui.dialog.ConfirmDialog
import kotlinx.coroutines.flow.singleOrNull

/**
 * Created by bo.li
 * Date: 2022/9/27
 * Desc: 封禁/屏蔽功能
 */
class BanBlockInteractor(private val metaRepository: IMetaRepository) {

    companion object {
        const val REASON_ROOM_MESSAGE = "room_message"
        const val REASON_IM_MESSAGE = "im_message"
        const val REASON_EDIT_PROFILE = "edit_profile"
        const val REASON_POST_REVIEW = "post_review"
        const val REASON_ADD_FRIEND = "add_friend"
    }

    fun showBanDialog(reason: String, fragment: Fragment) {
        Analytics.track(EventConstants.EVENT_ACCOUNT_IRREGULARITIES_DIALOG_SHOW) {
            put("reason", reason)
        }
        fragment.apply {
            ConfirmDialog.Builder(this)
                .content(resources.getString(R.string.content_account_ban))
                .cancelBtnTxt(isVisible = false)
                .confirmBtnTxt(resources.getString(R.string.dialog_confirm), lightBackground = true)
                .navigate()
        }
    }

    fun showBlockDialog(reason: String, fragment: Fragment) {
        fragment.apply {
            Analytics.track(EventConstants.EVENT_USER_BLOCK_DIALOG_SHOW) {
                put("reason", reason)
            }
            ConfirmDialog.Builder(this)
                .content(resources.getString(R.string.content_account_block))
                .cancelBtnTxt(isVisible = false)
                .confirmBtnTxt(resources.getString(R.string.dialog_confirm), lightBackground = true)
                .navigate()
        }
    }

    suspend fun checkBeingBlocked(otherUuid: String): Boolean {
        val result = metaRepository.getBlockRelation(otherUuid).singleOrNull()?.data
        return result == UserRelation.Other2Me || result == UserRelation.Both
    }
}