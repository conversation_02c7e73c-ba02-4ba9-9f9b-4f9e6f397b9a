package com.socialplay.gpark.data.model.event

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/05/20
 *     desc   :
 * </pre>
 */
@Parcelize
data class InGameOpenWebEvent(
    val url: String,
    val gameId: String,
    val messageId: Int,
    val orientation: Int?,
    val isHost: Boolean,
    val preUnique: String? = null, //预加载唯一键，会用这个值获取预加载好的webview
    val backToClose: Boolean = false, //返回键直接关闭WebDialog
    val reload: Boolean = true, //是否重新加载url指定地址（一般配合preUnique使用）
    val backToRelease: Boolean = true, //返回键是否释放webview（一般配合preUnique使用）
): Parcelable