package com.socialplay.gpark.data.model.post

import android.content.Context
import android.os.Parcelable
import com.luck.picture.lib.config.PictureMimeType
import com.luck.picture.lib.utils.MediaUtils
import com.socialplay.gpark.util.extension.ifNullOrBlank
import kotlinx.parcelize.Parcelize
import org.koin.core.context.GlobalContext
import java.io.File

@Parcelize
data class PostMediaResource(
    /**
     * 资源类型
     * 3 图片
     * 4 视频
     */
    val resourceType: Int,
    // 资源值 url
    val resourceValue: String,
    // 视频首帧图
    val cover: String?,
    val resourceWidth: Int = 0,
    val resourceHeight: Int = 0,
    val localPath: String? = null,
    val itemId: String? = null,
    val gameId: String? = null
): Parcelable {
    companion object {
        const val TYPE_UNKNOWN = 0
        const val TYPE_IMG = 3
        const val TYPE_VIDEO = 4

        fun mimeTypeToResourceType(mimeType: String): Int {
            return if (PictureMimeType.isHasVideo(mimeType)) {
                TYPE_VIDEO
            } else if (PictureMimeType.isHasImage(mimeType)) {
                TYPE_IMG
            } else {
                TYPE_UNKNOWN
            }
        }

        fun isSupported(mediaType: Int): Boolean {
            return mediaType == TYPE_IMG || mediaType == TYPE_VIDEO
        }
    }

    val resPath get() = resourceValue.ifNullOrBlank { localPath.orEmpty() }

    val isUnknown get() = resourceType == TYPE_UNKNOWN
    val isImage get() = resourceType == TYPE_IMG
    val isVideo get() = resourceType == TYPE_VIDEO
    val isSupported get() = isImage || isVideo
    val thumbnail get() = if (isImage) {
        resourceValue.ifNullOrBlank { localPath.orEmpty() }
    } else if (isVideo) {
        cover.ifNullOrBlank { localPath.orEmpty() }
    } else {
        ""
    }
    val isHorizontal get() = resourceWidth >= resourceHeight
    val localExist get() = !localPath.isNullOrBlank() && File(localPath).run { exists() && isFile }
    val isNetResource get() = !resourceValue.isNullOrBlank()

    fun getSize(): Pair<Int, Int> {
        return if (resourceWidth > 0 && resourceHeight > 0) {
             resourceWidth to resourceHeight
        } else if (isSupported && !localPath.isNullOrBlank()) {
            val context: Context = GlobalContext.get().get()
            val mediaExtraInfo = if (isImage) {
                MediaUtils.getImageSize(context, localPath)
            } else {
                MediaUtils.getVideoSize(context, localPath)
            }
            mediaExtraInfo.width to mediaExtraInfo.height
        } else {
            resourceWidth to resourceHeight
        }
    }
}