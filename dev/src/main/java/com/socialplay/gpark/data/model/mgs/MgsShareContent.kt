package com.socialplay.gpark.data.model.mgs

/**
 * @author: ning.wang
 * @date: 2021-12-23 4:45 下午
 * @desc:
 */
data class MgsShareContent(
    val shareId: String,
    // type为好友邀请新加字段，房间邀请无type字段
    val type: String?,
    val time: Long
) {
    companion object {
        // 类型：邀请好友
        // **注意**：房间邀请代码较早，web页没有传type字段，此处的join_room是本地使用的，起标识作用，如果web没传type字段，则认为是加入房间动作
        const val TYPE_JOIN_ROOM = "join_room"
        // 类型：邀请好友 与web约定的type
        const val TYPE_FRIEND_SHARE = "add_friend"
    }
}