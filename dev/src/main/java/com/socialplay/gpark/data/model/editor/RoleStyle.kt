package com.socialplay.gpark.data.model.editor

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/04/01
 *     desc   :
 * </pre>
 */
@Parcelize
data class RoleStyleRefreshEvent(
    val gameId: String
) : Parcelable

@Parcelize
data class RoleStyle(
    val style: Style,
    val customInfo: CustomInfo,
) : Parcelable {

    val isLike get() = customInfo.likeState == 1

    fun switchLike(): RoleStyle {
        val newIsLike = !isLike
        val newLikeState: Int
        val newLikeNumber: Int
        if (newIsLike) {
            newLikeState = 1
            newLikeNumber = style.likeNumber + 1
        } else {
            newLikeState = 0
            newLikeNumber = (style.likeNumber - 1).coerceAtLeast(0)
        }
        return copy(
            style = style.copy(likeNumber = newLikeNumber),
            customInfo = customInfo.copy(likeState = newLikeState)
        )
    }

    @Parcelize
    data class Style(
        val id: Long,
        val styleId: String,
        val userId: String?,
        val privateState: Int,
        val shareState: Int,
        val shareType: String?,
        val collectType: String?,
        val icon: String?,
        val shareId: String?,
        val assets: String?,
        val likeNumber: Int,
        val updateTime: Long,
        val createTime: Long,
    ) : Parcelable

    @Parcelize
    data class CustomInfo(
        val likeState: Int
    ) : Parcelable
}

@Parcelize
data class RoleOtherStyleListRequest(
    val otherUuid: String?,
    val beginIndex: Int,
    val length: Int,
) : Parcelable

@Parcelize
data class RoleStyleListResponse(
    val styleViewList: List<RoleStyle>?
) : Parcelable

@Parcelize
data class LikeRoleStyleRequest(
    val styleId: String,
    val likeType: Int
) : Parcelable {

    companion object {
        const val TYPE_DEFAULT = 0
        const val TYPE_LIKE = 1
    }

    constructor(styleId: String, isLike: Boolean) : this(
        styleId,
        if (isLike) TYPE_LIKE else TYPE_DEFAULT
    )
}

@Parcelize
data class LikeRoleStyleResponse(
    val status: Boolean
) : Parcelable

@Parcelize
data class DeleteRoleStyleRequest(
    val styleId: String
) : Parcelable

@Parcelize
data class DeleteRoleStyleResponse(
    val status: Boolean
) : Parcelable