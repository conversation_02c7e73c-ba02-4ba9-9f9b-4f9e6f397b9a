package com.socialplay.gpark.data.model.editor.cloud

import com.socialplay.gpark.util.GsonUtil.toJsonObject

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/09/23
 *     desc   :
 * </pre>
 */
data class GidPkg(val gameId: String, val platformPackageName: String)
data class ProjectLimit(val maxCloudProject: Long, val maxCloudArchivePerProject: Long)
data class UgcCloudProject(
    val archiveSha1: String,
    val archiveUrl: String,
    val id: Long,
    val projectId: String,
    val projectName: String,
    val templateSceneId: String,
    val thumbnailFileUrl: String,
    val updateTime: Long,
)

data class UgcBackupInfo(
    val archiveId: Long,
    val archiveName: String,
    val createTime: Long,
    val engineVersion: String,
    val fileSha1: String,
    val fileSize: Long,
    val fileUrl: String,
    val slot: Long,
    val updateTime: Long,
    val customExpand: String,
) {
    fun getVersion(): String? {
        return runCatching {
            customExpand.toJsonObject().get("templateVersion").asString
        }.getOrNull()
    }
}