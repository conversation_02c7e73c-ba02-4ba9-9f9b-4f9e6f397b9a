package com.socialplay.gpark.data.model.share

import com.google.gson.annotations.SerializedName

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/08/13
 *     desc   :
 * </pre>
 */
data class DataRelayApiResult(
    val key: String,
    val funId: Int,
    val info: DataRelayInfo,
    val clickTime: Long,
    val acquired: Int,
    val validitySecond: Int
)

data class DataRelayInfo(
    val scheme: String? = null,
    val shareId: String? = null,
    @SerializedName(value = "uniqueId", alternate = ["uniqId"])
    val uniqueId: String? = null,
    val extras: String? = null
)

/**
 * 分享数据中转数据
 */
data class RelayData(
    val shareId: String? = null,
    @SerializedName(value = "uniqueId", alternate = ["uniqId"])
    val uniqId: String? = null,
    val scheme: String? = null,
    val extras: String? = null
)


fun RelayData.toDataRelayInfo(): DataRelayInfo {
    return DataRelayInfo(
        uniqueId = uniqId,
        shareId = shareId,
        scheme = scheme,
        extras = extras
    )
}