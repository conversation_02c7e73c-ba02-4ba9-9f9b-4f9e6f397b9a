package com.socialplay.gpark.data.model.im

/**
 * Created by bo.li
 * Date: 2023/2/7
 * Desc:
 */
data class ReviewTextRiskResult(
    val action: Int,
    val taskId: String?,
    val hits: List<String?>?
) {
    companion object {
        // 通过
        const val ACTION_PASS = 0
        // 疑似
        const val ACTION_SUSPICIOUS = 1
        // 不通过
        const val ACTION_INTERCEPT = 2
    }

    fun checkPass(): <PERSON><PERSON>an {
        return action == ACTION_PASS
    }
}