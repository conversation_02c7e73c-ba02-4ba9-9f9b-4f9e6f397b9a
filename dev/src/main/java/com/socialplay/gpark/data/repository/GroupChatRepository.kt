package com.socialplay.gpark.data.repository

import com.google.gson.reflect.TypeToken
import com.socialplay.gpark.data.api.MetaApi
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.DataSource
import com.socialplay.gpark.data.base.code
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.exception
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.base.suspendApiNotNull
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.local.SimpleDiskLruCache
import com.socialplay.gpark.data.model.groupchat.GroupChatAddMembers
import com.socialplay.gpark.data.model.groupchat.GroupChatApplyInfoList
import com.socialplay.gpark.data.model.groupchat.GroupChatCount
import com.socialplay.gpark.data.model.groupchat.GroupChatDetailInfo
import com.socialplay.gpark.data.model.groupchat.GroupChatInfoPage
import com.socialplay.gpark.data.model.groupchat.GroupChatMemberInfo
import com.socialplay.gpark.data.model.groupchat.GroupChatMembersPage
import com.socialplay.gpark.data.model.groupchat.GroupSimpleInfo
import com.socialplay.gpark.data.model.groupchat.MgsGetSimpleGroupInfoByImIdsRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupApplyPageRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatApplyJoinProcessRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatApplyJoinRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatCountRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatCreateRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatEditNotificationRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatEditRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatIdRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatInviteMembersRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatListRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatMembersRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatRemoveMemberRequest
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.util.LruMap
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock

class GroupChatRepository(
    val metaApi: MetaApi,
    val metaKV: MetaKV,
    private val diskCache: SimpleDiskLruCache
) {
    companion object {
        /**
         * 一次拉取群成员数量
         */
        const val GROUP_MEMBER_PAGE_SIZE = 50
        const val IM_ID_CACHE_PREFIX = "imid_"

        /**
         * 群被解散时的 id
         */
        const val DISBANDED_GROUP_ID = -1L
        const val GROUP_ID_CACHE_PREFIX = "group_id_"
        const val GROUP_MEMBERS_CACHE_PREFIX = "group_members_"
        const val GROUP_CHAT_COUNT_PREFIX = "group_chat_count_"
    }

    fun checkCreateGroupPower(): Flow<Boolean> = flow {
        if (!PandoraToggle.showCreateGroupMenu) {
            emit(false)
            return@flow
        }
        val value = metaKV.groupChatKV.haveCreateGroupPower(metaKV.account.uuid)
        emit(value)
        val result = DataSource.getDataResultForApi {
            metaApi.checkCreateGroupPower()
        }
        if (result.succeeded) {
            val havePower = result.data == true
            if (havePower != value) {
                metaKV.groupChatKV.saveCreateGroupPower(metaKV.account.uuid, havePower)
            }
            emit(havePower)
        }
    }

    suspend fun createGroupChat(request: MgsGroupChatCreateRequest): DataResult<GroupChatDetailInfo> {
        val result = DataSource.getDataResultForApi {
            metaApi.createGroupChat(request)
        }
        if (result.succeeded && result.data != null) {
            val groupDetail = result.data!!
            val groupId = groupDetail.id
            val imId = groupDetail.imId
            val memberLastChange = groupDetail.memberLastChange
            if (groupId != null) {
                if (memberLastChange != null) {
                    updateGroupMembersCurrentVersion(groupId, memberLastChange)
                }
                if (imId != null) {
                    saveImIdAndGroupId(imId, groupId)
                }
            }
        }
        return result
    }

    private fun groupDetailCacheKey(chatGroupId: Long): String {
        return GROUP_ID_CACHE_PREFIX + chatGroupId
    }

    fun getGroupChatDetailInfo(chatGroupId: Long): Flow<DataResult<GroupChatDetailInfo>> =
        flow {
            val groupDetailCache =
                diskCache.get<GroupChatDetailInfo>(groupDetailCacheKey(chatGroupId))
            if (groupDetailCache != null) {
                emit(DataResult.Success(groupDetailCache))
            }
            if (groupDetailCache?.isDisband() == true) {
                // 群已被解散的情况下, 无需去远端获取群详情了
                return@flow
            }
            val result = DataSource.getDataResultForApi {
                metaApi.getGroupChatDetailInfo(
                    MgsGroupChatIdRequest(
                        chatGroupId = chatGroupId
                    )
                )
            }
            if (result.succeeded && result.data != null) {
                diskCache.put(groupDetailCacheKey(chatGroupId), result.data)
                val groupDetail = result.data!!
                val groupId = groupDetail.id
                val memberLastChange = groupDetail.memberLastChange
                if (groupId != null && memberLastChange != null) {
                    updateGroupMembersCurrentVersion(groupId, memberLastChange)
                }
                emit(result)
            } else if (result.code == GroupChatDetailInfo.ERROR_CODE_GROUP_DISBAND
                || result.code == GroupChatDetailInfo.ERROR_CODE_NOT_GROUP_MEMBER
            ) {
                val groupDetail = groupDetailCache?.copy(
                    errorCode = result.code
                ) ?: GroupChatDetailInfo(
                    errorCode = result.code,
                    null,
                    null,
                    null,
                    null,
                    null,
                    chatGroupId,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                )
                diskCache.put(groupDetailCacheKey(chatGroupId), groupDetail)
                emit(DataResult.Success(groupDetail))
            } else {
                // 如果本地有缓存, 但网络获取失败, 就不emit了
                if (groupDetailCache == null) {
                    emit(result)
                }
            }
        }

    suspend fun getGroupChatInfoList(request: MgsGroupChatListRequest): DataResult<GroupChatInfoPage> {
        return DataSource.getDataResultForApi {
            metaApi.getGroupChatInfoList(request)
        }
    }

    suspend fun applyJoinGroupChat(request: MgsGroupChatApplyJoinRequest): DataResult<Boolean> {
        return DataSource.getDataResultForApi {
            metaApi.applyJoinGroupChat(request)
        }
    }

    suspend fun joinGroupChat(request: MgsGroupChatIdRequest): DataResult<GroupChatAddMembers> {
        return DataSource.getDataResultForApi {
            metaApi.joinGroupChat(request)
        }
    }

    suspend fun getGroupChatPendingRequestList(request: MgsGroupApplyPageRequest): DataResult<GroupChatApplyInfoList> {
        return DataSource.getDataResultForApi {
            metaApi.getGroupChatPendingRequestList(request)
        }
    }

    suspend fun editGroupChat(request: MgsGroupChatEditRequest): DataResult<GroupChatDetailInfo> {
        val result = DataSource.getDataResultForApi {
            metaApi.editGroupChat(request)
        }
        if (result.succeeded && result.data != null) {
            val groupDetail = result.data!!
            val groupId = groupDetail.id
            val memberLastChange = groupDetail.memberLastChange
            if (groupId != null && memberLastChange != null) {
                updateGroupMembersCurrentVersion(groupId, memberLastChange)
            }
        }
        return result
    }

    suspend fun leaveGroupChat(request: MgsGroupChatIdRequest): DataResult<Boolean> {
        return DataSource.getDataResultForApi {
            metaApi.leaveGroupChat(request)
        }
    }

    suspend fun disbandGroupChat(request: MgsGroupChatIdRequest): DataResult<Boolean> {
        return DataSource.getDataResultForApi {
            metaApi.disbandGroupChat(request)
        }
    }

    suspend fun editGroupChatNotification(request: MgsGroupChatEditNotificationRequest): DataResult<Boolean> {
        return DataSource.getDataResultForApi {
            metaApi.editGroupChatNotification(request)
        }
    }

    suspend fun processApplyJoinGroupChat(request: MgsGroupChatApplyJoinProcessRequest): DataResult<Boolean> {
        return DataSource.getDataResultForApi {
            metaApi.processApplyJoinGroupChat(request)
        }
    }

    private suspend fun getGroupChatMembersFromRemote(request: MgsGroupChatMembersRequest): DataResult<GroupChatMembersPage> {
        return DataSource.getDataResultForApi {
            metaApi.getGroupChatMembers(request)
        }
    }

    private suspend fun getAllGroupChatMembersFromRemote(chatGroupId: Long): DataResult<Pair<Long, List<GroupChatMemberInfo>>> {
        val result = mutableListOf<GroupChatMemberInfo>()
        var memberLastChange: Long? = null
        var rollId: Long? = null
        while (true) {
            val dataResult = getGroupChatMembersFromRemote(
                MgsGroupChatMembersRequest(
                    memberLastChange = memberLastChange,
                    rollId = rollId,
                    chatGroupId = chatGroupId.toString(),
                    size = GROUP_MEMBER_PAGE_SIZE,
                    reLoading = true,
                )
            )
            if (dataResult.succeeded && dataResult.data != null) {
                val remoteMemberLastChange = dataResult.data!!.memberLastChange
                val remoteRollId = dataResult.data!!.rollId
                val memberList = dataResult.data!!.members ?: emptyList()
                if (remoteMemberLastChange != null) {
                    if (memberLastChange != null && memberLastChange != remoteMemberLastChange) {
                        // 拉取群成员列表过程中, 发现群成员版本号更新了, 则需要重新拉取群成员列表
                        return getAllGroupChatMembersFromRemote(chatGroupId)
                    }
                    memberLastChange = remoteMemberLastChange
                    rollId = remoteRollId
                    result.addAll(memberList)
                    if (remoteRollId == null || memberList.size < GROUP_MEMBER_PAGE_SIZE) {
                        return DataResult.Success(memberLastChange to result)
                    }
                } else {
                    // 服务器没下发 remoteMemberLastChange
                    return DataResult.Error(
                        dataResult.code ?: 0,
                        dataResult.message ?: "",
                        dataResult.exception ?: Exception(),
                        dataResult.duration
                    )
                }
            } else {
                return DataResult.Error(
                    dataResult.code ?: 0,
                    dataResult.message ?: "",
                    dataResult.exception ?: Exception(),
                    dataResult.duration
                )
            }
        }
    }

    private val groupMembersCache = LruMap<Long, List<GroupChatMemberInfo>>(5)
    private val groupMembersMutex = Mutex()

    private fun updateGroupMembersCurrentVersion(chatGroupId: Long, version: Long) {
        metaKV.groupChatKV.updateGroupMemberLatestVersion(chatGroupId, version)
    }

    private fun memberListCacheKey(chatGroupId: Long): String {
        return GROUP_MEMBERS_CACHE_PREFIX + chatGroupId
    }

    private fun isMembersCacheEnable(chatGroupId: Long): Boolean {
        val membersCacheVersion = metaKV.groupChatKV.getGroupMemberCacheVersion(chatGroupId)
        val membersCurrentVersion = metaKV.groupChatKV.getGroupMemberLatestVersion(chatGroupId)
        return membersCacheVersion > 0 && membersCurrentVersion > 0 && membersCacheVersion >= membersCurrentVersion
    }

    suspend fun getAllGroupChatMembersWithCache(
        chatGroupId: Long,
        forceLoad: Boolean?
    ): DataResult<List<GroupChatMemberInfo>> {
        if (forceLoad != true && isMembersCacheEnable(chatGroupId)) {
            val memoryCache = groupMembersCache[chatGroupId]
            if (memoryCache != null) {
                return DataResult.Success(memoryCache)
            }
            // 当前群成员缓存就是最新版本, 直接返回
            val members: List<GroupChatMemberInfo>? = diskCache.get(
                memberListCacheKey(chatGroupId),
                object : TypeToken<List<GroupChatMemberInfo>?>() {
                }.type
            )
            if (members != null) {
                groupMembersCache[chatGroupId] = members
                return DataResult.Success(members)
            }
        }

        val dataResult = getAllGroupChatMembersFromRemote(chatGroupId)
        if (dataResult.succeeded && dataResult.data != null) {
            val remoteMemberLastChange = dataResult.data!!.first
            val memberList = dataResult.data!!.second
            groupMembersMutex.withLock {
                groupMembersCache[chatGroupId] = memberList
                metaKV.groupChatKV.updateGroupMemberCacheVersion(
                    chatGroupId,
                    remoteMemberLastChange
                )
                updateGroupMembersCurrentVersion(
                    chatGroupId,
                    remoteMemberLastChange
                )
                diskCache.put(memberListCacheKey(chatGroupId), memberList)
            }
            return DataResult.Success(memberList)
        } else {
            return DataResult.Error(
                dataResult.code ?: 0,
                dataResult.message ?: "",
                dataResult.exception ?: Exception(),
                dataResult.duration
            )
        }
    }

    suspend fun removeGroupChatMember(request: MgsGroupChatRemoveMemberRequest): DataResult<Boolean> {
        val result = DataSource.getDataResultForApi {
            metaApi.removeGroupChatMember(request)
        }
        if (request.chatGroupId != null && result.succeeded && result.data == true) {
            // 删除群成员成功后, 本地的群成员缓存就过时了
            updateGroupMembersCurrentVersion(request.chatGroupId, -1)
        }
        return result
    }

    suspend fun inviteGroupChatMembers(request: MgsGroupChatInviteMembersRequest): DataResult<GroupChatAddMembers> {
        val result = DataSource.getDataResultForApi {
            metaApi.inviteGroupChatMembers(request)
        }
        if (request.chatGroupId != null && result.succeeded && !result.data!!.success.isNullOrEmpty()) {
            // 删除群成员成功后, 本地的群成员缓存就过时了
            updateGroupMembersCurrentVersion(request.chatGroupId, -1)
        }
        return result
    }

    private suspend fun getGroupSimpleInfoByImIds(imIds: List<String>): DataResult<Map<String, GroupSimpleInfo>> {
        return DataSource.getDataResultForApi {
            metaApi.getGroupSimpleInfoByImIds(MgsGetSimpleGroupInfoByImIdsRequest(imIds = imIds))
        }
    }

    private fun imIdCacheKey(imId: String): String {
        return IM_ID_CACHE_PREFIX + imId
    }

    private suspend fun saveImIdAndGroupId(imId: String, groupId: Long) {
        diskCache.put(imIdCacheKey(imId), groupId.toString())
    }

    suspend fun getGroupIdByImIds(imIds: List<String>): Map<String, Long> {
        val result = mutableMapOf<String, Long>()
        val cacheMiss = mutableSetOf<String>()
        imIds.toSet().forEach { imId ->
            val groupId = diskCache.getString(imIdCacheKey(imId)).toLongOrNull()
            if (groupId == null) {
                cacheMiss.add(imId)
            } else {
                result[imId] = groupId
            }
        }
        if (cacheMiss.isEmpty()) {
            return result
        } else {
            val dataResult = getGroupSimpleInfoByImIds(cacheMiss.toList())
            if (dataResult.succeeded && dataResult.data != null) {
                val data = dataResult.data!!
                data.forEach { entry ->
                    val imId = entry.key
                    val groupId = entry.value.id?.toLongOrNull()
                    if (groupId != null) {
                        saveImIdAndGroupId(imId, groupId)
                        result[imId] = groupId
                    }
                    cacheMiss.remove(imId)
                }
                if (cacheMiss.isNotEmpty()) {
                    // 服务器未返回对应的群信息, 表示群已被解散
                    cacheMiss.forEach { imId ->
                        saveImIdAndGroupId(imId, DISBANDED_GROUP_ID)
                    }
                }
            }
        }
        return result
    }

    private fun groupCountKey(): String {
        return GROUP_CHAT_COUNT_PREFIX + metaKV.account.uuid
    }

    fun getGroupChatCount(): Flow<DataResult<GroupChatCount>> = flow {
        val groupCountCache = diskCache.get<GroupChatCount>(groupCountKey())
        if (groupCountCache != null) {
            emit(DataResult.Success(groupCountCache))
        }
        val result = DataSource.getDataResultForApi {
            metaApi.getGroupChatCount(MgsGroupChatCountRequest(null))
        }
        if (result.succeeded && result.data != null) {
            diskCache.put(groupCountKey(), result.data)
        }
        emit(result)
    }

    suspend fun getGroupChatPendingRequestCount(): DataResult<Int> {
        return DataSource.getDataResultForApi {
            metaApi.getGroupChatPendingRequestCount()
        }
    }

    fun getGroupChatCountByUuid(uuid: String) = suspendApiNotNull {
        metaApi.getGroupChatCount(MgsGroupChatCountRequest(uuid))
    }
}