package com.socialplay.gpark.data.repository.api

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.socialplay.gpark.data.api.MetaApi
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.DataSource
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.friend.FriendSearchInfo
import timber.log.Timber

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/09/12
 * desc   :
 * </pre>
 */
class FriendSearchPagingSource(val clear: Boolean = false, val keyword: String, val metaApi: MetaApi, val metaKV: MetaKV, val pageSize: Int) : PagingSource<Int, FriendSearchInfo>() {

    override fun getRefreshKey(state: PagingState<Int, FriendSearchInfo>): Int? {
        return null
    }

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, FriendSearchInfo> {

        val cur = params.key ?: 1

        return try {
            if (clear) {
                return LoadResult.Page(emptyList(), null, null)
            }
            val data = DataSource.getDataResultForApi { metaApi.searchFriends(keyword, cur, pageSize) }
            val items = data.data?.dataList ?: emptyList()
            if (data is DataResult.Success && !items.isNullOrEmpty()) {
                items.forEach { it.isSelf = it.uid == metaKV.account.uuid }
            }
            val isEnd = data.data?.end == true || items.isNullOrEmpty()
            val prevKey = null
            val nextKey = if (isEnd) null else cur + 1
            Timber.d("FriendSearchPagingSource cur:$cur, prevKey:$prevKey, nextKey:$nextKey, params:$params data:$data")
            if (data is DataResult.Success) {
                LoadResult.Page(items, prevKey, nextKey)
            } else {
                LoadResult.Error(NullPointerException())
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }
}