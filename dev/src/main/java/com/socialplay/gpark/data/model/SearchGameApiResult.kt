package com.socialplay.gpark.data.model

import com.google.gson.annotations.SerializedName
import com.socialplay.gpark.data.model.post.CommunityFeedInfo
import com.socialplay.gpark.data.model.post.PostCardInfo
import com.socialplay.gpark.function.search.SearchHelper

data class SearchGameApiResult(
    @SerializedName("end") val end: Boolean,
    @SerializedName("lastOrderNum") val lastOrderNum: Long?,
    @SerializedName("list") val list: MutableList<ListItem>?
) {

    val validList get() = list?.filter { it.isSupported }
    val validPostCardList get() = validList?.mapNotNull { it.toPostCardInfo() }
}

data class ListItem(
    val contentType: Int?, // 1 pgc 2 ugc
    val contentId: String?, // 内容标识
    val pgc: SearchGameInfo?,
    val ugc: SearchGameInfo?,
    var post: CommunityFeedInfo? = null,
    val id: String?, // 联想接口独有
    val keyword:String?, // 联想接口独有
){
    fun isUgc(): Boolean = contentType == 2 && ugc != null
    fun isPgc(): Boolean = contentType == SearchHelper.PGC_TYPE && pgc != null

    val isSupported get() = isPgc() || isUgc()

    fun toPostCardInfo(): PostCardInfo? {
        val item = pgc ?: ugc ?: return null
        val isPgc = isPgc()
        return PostCardInfo(
            if (isPgc) PostCardInfo.TYPE_PGC else PostCardInfo.TYPE_UGC,
            item.ugid ?: item.gameCode ?: id.orEmpty(),
            item.packageName.orEmpty(),
            item.likeCount ?: 0,
            item.pvCount ?: 0,
            item.authorName ?: item.userName,
            item.iconUrl ?: item.banner,
            null,
            item.gameName,
            0.0f,
            null
        )
    }
}

data class SearchGameInfo(
    val gameCode: String? = "",
    val packageName: String? = "",
    val iconUrl: String? = null,
    val gameName: String? = null,
    val iconId: String? = null,
    var description: String? = null,
    var tagsHighLight: String? = null,
    val authorId: String? = null,
    val authorName: String? = null,
    val ugid: String? = null,
    val likeCount: Long? = null,
    val pvCount: Long? = null,
    val banner:String? = null,
    val userName:String? = null,
    var titleSearchSpan: CharSequence? = null,
)

data class SearchGameItem(
    val id: String = "",
    val packageName: String = "",
    val iconUrl: String? = null,
    var displayName: CharSequence? = null,
    val authorName:String? = null,
    val likeCount: Long? = null,
    val pvCount: Long? = null,
    val isUgc:Boolean = false,
    val itemType:Int = 0, // 0: 搜索结果, 1: 搜索联想词
    val contentId: String? = null
)