package com.socialplay.gpark.data.local.migrations

import androidx.room.migration.Migration

/**
 * @author: ning.wang
 * @date: 2021-07-01 2:33 下午
 * @desc:
 */
internal class MetaAppDatabaseMigrations {

    fun build(): Array<Migration> {
        val list = ArrayList<Migration>()
        list.add(MetaAppDatabase_Migration_1_2())
        list.add(MetaAppDatabase_Migration_2_3())
        list.add(MetaAppDatabase_Migration_3_4())
        list.add(MetaAppDatabase_Migration_4_5())
        list.add(MetaAppDatabase_Migration_5_6())
        list.add(MetaAppDatabase_Migration_6_7())
        list.add(MetaAppDatabase_Migration_7_8())
        list.add(MetaAppDatabase_Migration_8_9())
        list.add(MetaAppDatabase_Migration_9_10())
        list.add(MetaAppDatabase_Migration_10_11())
        list.add(MetaAppDatabase_Migration_11_12())
        return list.toTypedArray()
    }
}