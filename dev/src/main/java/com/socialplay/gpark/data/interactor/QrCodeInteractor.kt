package com.socialplay.gpark.data.interactor

import android.content.Context
import androidx.fragment.app.Fragment
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.qrcode.ScanRequestData
import com.socialplay.gpark.data.model.qrcode.ScanResultData
import com.socialplay.gpark.function.qrcode.*
import timber.log.Timber

/**
 * Created by bo.li
 * Date: 2021/8/18
 * Desc:
 */
class QrCodeInteractor(val metaRepository: com.socialplay.gpark.data.IMetaRepository, val context: Context, val metaKV: MetaKV) {

    private data class QRCoeHandlerWrapper(val handler: QRCodeHandler, val priority: Int)

    private val handlers: MutableList<QRCoeHandlerWrapper> = mutableListOf()

    init {
        addHandler(CommonV2Handler)
        addHandler(MgsHandler)
        addHandler(TrustLinkHandler)
        addHandler(CommonHandler)
        addHandler(WebUrlHand<PERSON>, Int.MAX_VALUE - 1/*使用浏览器打开地址的优先级比较低*/)
        addHandler(Unsupported<PERSON>and<PERSON>, Int.MAX_VALUE/*最低优先级*/)
    }

    fun addHandler(handler: QRCodeHandler, priority: Int = 0) {
        synchronized(handlers) {
            handlers.add(QRCoeHandlerWrapper(handler, priority))
            handlers.sortBy { it.priority }
        }
    }

    fun removeHandler(handler: QRCodeHandler) {
        synchronized(handler) {
            handlers.removeAll { it.handler == handler }
            handlers.sortBy { it.priority }
        }
    }


    /**
     * 分发扫码结果
     * @return 是否有处理器成功处理了此次扫码结果
     */
    suspend fun dispatchQRCodeScanResult(
        fragment: Fragment,
        request: ScanRequestData,
        result: ScanResultData
    ): Boolean {
        val immutableHandlerList = synchronized(handlers) { ArrayList(handlers) }

        immutableHandlerList.forEach {
            if (it.handler.process(context, fragment, request, result)) {
                Timber.d("QRCode content: $result handled by handler:${it.handler}")
                return true
            }
        }
        return false
    }
}
