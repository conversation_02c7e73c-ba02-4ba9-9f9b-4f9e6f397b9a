package com.socialplay.gpark.data.repository.api

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.socialplay.gpark.data.api.MetaApi
import com.socialplay.gpark.data.base.DataSource
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.model.SysActivitiesInfo

class SysMessageListPagingSource(
    val metaApi: MetaApi,
    val pageSize: Int,
    val groupId: Long,
) : PagingSource<Long, SysActivitiesInfo>() {

    override fun getRefreshKey(state: PagingState<Long, SysActivitiesInfo>): Long? {
        return null
    }

    override suspend fun load(params: LoadParams<Long>): LoadResult<Long, SysActivitiesInfo> {

        val cur = params.key

        return try {
            val data = DataSource.getDataResultForApi {
                metaApi.getSysActivitiesInfo(
                    hashMapOf("groupId" to groupId.toString(), "pageSize" to pageSize.toString(), "lastRecordTime" to cur.toString())
                )
            }
            val items: List<SysActivitiesInfo> = data.data ?: emptyList()
            val isEnd = data.data.isNullOrEmpty()
            val nextKey = if (isEnd) null else data.data?.last()?.sendTime
            if (data.succeeded) {
                LoadResult.Page(items, null, nextKey)
            } else {
                LoadResult.Error(NullPointerException())
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }
}