package com.socialplay.gpark.data.model.guide

data class StoreCommentGuideConfig(
    val innerIntervalTime: Int?, // 内部弹框间隔时间（首次不受这个限制） 单位是小时
    val disLikeIntervalTime: Int?, // 点击拒绝后，间隔多久再展示内部弹框 单位是小时
    val storeDialogIntervalTime: Int?, // 距离内部弹框间隔多久之后展示商店弹框 单位是小时
    val keepInnerDialog: Boolean?, // 是否跳过内部弹框
    val innerDialogConfig: List<StoreCommentGuideConfigItem?>?, // 内部弹框配置
    val storeDialogConfig: List<StoreCommentGuideConfigItem?>?, // 商店弹框配置
)

data class StoreCommentGuideConfigItem(
    val configId: String,
    val configData: StoreCommentGuideConfigData?
)

data class StoreCommentGuideConfigData(
    val triggerScene: String?,
    val totalGameTime: Int?,    // 单位是分钟
    val onceGameTime: Int?,    // 单位是分钟
    val appStartCount: Int?,
    val appStartDayCount: Int?,
    val likeCount: Int?,
)