package com.socialplay.gpark.data.local.migrations

import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/10/08
 *     desc   :
 * </pre>
 */
internal class MetaAppDatabase_Migration_8_9: Migration(8, 9) {
    override fun migrate(db: SupportSQLiteDatabase) {
        try {
            db.execSQL("ALTER TABLE `game_detail` ADD COLUMN `extend` TEXT DEFAULT NULL")
        } catch (ignore: Throwable) {
        }
    }
}