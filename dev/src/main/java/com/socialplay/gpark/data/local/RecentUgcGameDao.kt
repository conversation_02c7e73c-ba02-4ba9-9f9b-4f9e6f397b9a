package com.socialplay.gpark.data.local

import androidx.paging.PagingSource
import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.socialplay.gpark.data.model.entity.RecentUgcGameEntity
import timber.log.Timber

/**
 * Created by bo.li
 * Date: 2023/9/14
 * Desc:
 */
@Dao
interface RecentUgcGameDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertInfo(recentUgcGameInfo: RecentUgcGameEntity)

    @Query("SELECT COUNT(id) FROM game_detail WHERE id = :id")
    suspend fun idCount(id: String): Int

    @Query("SELECT * FROM recent_ugc_game ORDER BY visitTime DESC LIMIT :index,:size")
    suspend fun getRecentVisitUgcGames(index: Int, size: Int): List<RecentUgcGameEntity>?

    @Query("SELECT * FROM recent_ugc_game WHERE id = :gameId")
    suspend fun getInfo(gameId: String): RecentUgcGameEntity?

    @Update
    suspend fun updateInfo(infoEntity: RecentUgcGameEntity)
}

suspend fun RecentUgcGameDao.exists(id: String): Boolean {
    return idCount(id) > 0
}

// Target com.socialplay.gpark.data.local.RecentUgcGameDao
// try-catch call target method return default value
// error case Timber.e(e, "$methodName error")

class RecentUgcGameDaoDelegate(private val recentUgcGameDao: RecentUgcGameDao) : RecentUgcGameDao by recentUgcGameDao {
    override suspend fun insertInfo(recentUgcGameInfo: RecentUgcGameEntity) {
        try {
            recentUgcGameDao.insertInfo(recentUgcGameInfo)
        } catch (e: Exception) {
            Timber.e(e, "insertInfo error")
        }
    }

    override suspend fun idCount(id: String): Int {
        return try {
            recentUgcGameDao.idCount(id)
        } catch (e: Exception) {
            Timber.e(e, "idCount error")
            0
        }
    }

    override suspend fun getRecentVisitUgcGames(index: Int, size: Int): List<RecentUgcGameEntity>? {
        return try {
            recentUgcGameDao.getRecentVisitUgcGames(index, size)
        } catch (e: Exception) {
            Timber.e(e, "getRecentVisitUgcGames error")
            null
        }
    }

    override suspend fun getInfo(gameId: String): RecentUgcGameEntity? {
        return try {
            recentUgcGameDao.getInfo(gameId)
        } catch (e: Exception) {
            Timber.e(e, "getInfo error")
            null
        }
    }

    override suspend fun updateInfo(infoEntity: RecentUgcGameEntity) {
        try {
            recentUgcGameDao.updateInfo(infoEntity)
        } catch (e: Exception) {
            Timber.e(e, "updateInfo error")
        }
    }
}