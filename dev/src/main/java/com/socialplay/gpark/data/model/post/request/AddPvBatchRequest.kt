package com.socialplay.gpark.data.model.post.request

/**
 * Created by bo.li
 * Date: 2024/4/25
 * Desc:
 */
data class AddPvBatchRequest(
    val resType: String,
    val resIds: List<String>
)

data class AddPvRequest(
    val resType: String,
    val resId: String
) {
    companion object {
        /**
         * 1:PGC游戏
         * 2:用户
         * 3:樱花校园存档
         * 4:UGC游戏
         * 5:邻居
         * 6:模版游戏
         * 7:穿搭
         * 8:社区标签
         * 9:帖子分享
         * 11:UGC游戏分享
         * 12:PGC游戏分享
         */

        // 话题浏览
        const val RES_TYPE_TOPIC_VIEW = "8"

        // 帖子分享
        const val RES_TYPE_POST_SHARE = "9"

        // UGC分享
        const val RES_TYPE_UGC_SHARE = "11"

        // PGC分享
        const val RES_TYPE_PGC_SHARE = "12"
    }
}