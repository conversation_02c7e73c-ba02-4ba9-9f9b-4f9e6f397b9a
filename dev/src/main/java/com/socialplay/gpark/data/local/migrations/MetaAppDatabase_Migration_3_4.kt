package com.socialplay.gpark.data.local.migrations

import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/06/27
 *     desc   :
 *
 */
internal class MetaAppDatabase_Migration_3_4 : Migration(3, 4) {
    override fun migrate(database: SupportSQLiteDatabase) {
        try {
            //language=RoomSql
            //language=RoomSql
            database.execSQL("CREATE TABLE IF NOT EXISTS `ai_conversation` (`conversationId` INTEGER PRIMARY KEY  AUTOINCREMENT NOT NULL, `aiBotId` TEXT NOT NULL, `uuid` TEXT NOT NULL, `latestMessageId` INTEGER, `messageType` INTEGER NOT NULL , `timestamp` INTEGER NOT NULL , `showName` TEXT NOT NULL, `avatar` TEXT NOT NULL)")
            //language=RoomSql

            //language=RoomSql
            database.execSQL("CREATE TABLE IF NOT EXISTS `ai_message` (`messageId` INTEGER  PRIMARY KEY  AUTOINCREMENT NOT NULL , `senderUserID` TEXT NOT NULL, `receiverUserID` TEXT NOT NULL , `messageType` INTEGER  NOT NULL, `timestamp` INTEGER NOT NULL, `messageDirection` INTEGER NOT NULL, `isRead` INTEGER NOT NULL,  `content` TEXT NOT NULL, `status` INTEGER NOT NULL,  `extra` TEXT , `botInfo` TEXT )")
        } catch (ignore: Throwable) {
        }
    }
}