package com.socialplay.gpark.data.model.community

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/07/01
 *     desc   :
 * </pre>
 */
@Parcelize
data class PostCommentContent(
    val text: String,
    val mediaList: List<PostMedia>?
) : Parcelable {

    val valid: Boolean get() {
        return text.isNotBlank() || !mediaList.isNullOrEmpty()
    }
}

@Parcelize
data class PostMedia(
    val resourceType: Int,
    val resourceValue: String,
    val resourceWidth: Int,
    val resourceHeight: Int,
    val cover: String? = null
) : Parcelable {
    companion object {
        const val TYPE_IMAGE = 3
    }

    val isImage get() = resourceType == TYPE_IMAGE
}

data class AddPostCommentV2Response(
    val commentId: String?,
    val activityUrl: String?,
    val floor: Int?
)