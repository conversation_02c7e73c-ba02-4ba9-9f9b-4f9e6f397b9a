package com.socialplay.gpark.data.repository

import com.socialplay.gpark.data.api.MetaApi
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.base.suspendApi
import com.socialplay.gpark.data.base.suspendApiNotNull
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.local.AppDatabase
import com.socialplay.gpark.data.local.SimpleDiskLruCache
import com.socialplay.gpark.ui.developer.viewmodel.DemoListItem
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.launch
import timber.log.Timber
import kotlin.random.Random

class DemoRepository(
    private val api: MetaApi,
    private val coroutineScope: CoroutineScope,
    private val metaKV: MetaKV,
    private val db: AppDatabase,
    private val diskLruCache: SimpleDiskLruCache
) {

    val _testStateFlow: MutableStateFlow<Int> = MutableStateFlow(0)
    val testStateFlow: StateFlow<Int>
        get() = _testStateFlow

    init {
        coroutineScope.launch {


            repeat(20) {
                delay(5000)
                _testStateFlow.tryEmit(it)
                Timber.d("anxindebug try emit $it ${System.currentTimeMillis()}")
            }
        }
    }

    fun testGetMyGames(pageNum: Int = 0, pageSize: Int = 20) =
        suspendApi { api.getOperationNoticeList(pageNum, pageSize) }
            .map { result ->
                result?.dataList?.map { it.name } ?: emptyList()
            }

    fun getDemoList(key: String, pageSize: Int, startIndex: Int) = flow {
        delay(1000)

        if (Random.nextInt(10) > 5) {
            error("test crash $key $pageSize $startIndex ${System.currentTimeMillis()}")
        }
        emit(buildList {
            for (i in startIndex until (startIndex + pageSize)) {
                add(
                    DemoListItem(
                        "$key - $i${if (i == startIndex) " - Group" else ""}", i, i == startIndex
                    )
                )
            }
        })
    }

    fun getDemoListSuspend(
        key: String, pageSize: Int, startIndex: Int
    ) = suspend {
        delay(1000)
        if (Random.nextInt(10) > 5) {
            error("test crash $key $pageSize $startIndex ${System.currentTimeMillis()}")
        }
        if (startIndex > 160) {
            emptyList()
        } else {
            buildList {
                for (i in startIndex until (startIndex + pageSize)) {
                    add(
                        DemoListItem(
                            "$key - $i${if (i == startIndex) " - Group" else ""}",
                            i,
                            i == startIndex
                        )
                    )
                }
            }
        }
    }
}