package com.socialplay.gpark.di

import android.app.Application
import androidx.room.Room
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.local.AiConversationDao
import com.socialplay.gpark.data.local.AiMessageDao
import com.socialplay.gpark.data.local.AppDatabase
import com.socialplay.gpark.data.local.GameDetailDao
import com.socialplay.gpark.data.local.MetaCacheDao
import com.socialplay.gpark.data.local.MyPlayedGameDao
import com.socialplay.gpark.data.local.PlayGameTimeDao
import com.socialplay.gpark.data.local.RecentUgcGameDao
import com.socialplay.gpark.data.local.ShareRecordDao
import com.socialplay.gpark.data.local.migrations.MetaAppDatabaseMigrations
import org.koin.android.ext.koin.androidApplication
import org.koin.dsl.module

/**
 * Created by yaqi.liu on 2021/5/7
 */
val databaseModule = module {

    fun provideDatabase(application: Application): AppDatabase {

        return Room.databaseBuilder(application, AppDatabase::class.java, "gpark.db")
            //            .allowMainThreadQueries() // 允许主线程直接调用
            .fallbackToDestructiveMigration() // 破坏性升级
            .fallbackToDestructiveMigrationOnDowngrade() // 破坏性降级
            .addMigrations(*MetaAppDatabaseMigrations().build())
            .build()
    }


    fun provideGameDetailDao(database: AppDatabase): GameDetailDao {
        return database.gameDetailDao()
    }

    fun provideMyPlayedGameDao(database: AppDatabase): MyPlayedGameDao {
        return database.myPlayedGameDao()
    }

    fun provideMetaCacheDao(database: AppDatabase): MetaCacheDao {
        return database.metaCacheDao()
    }

    fun provideRecentUgcGameDao(database: AppDatabase): RecentUgcGameDao {
        return database.recentUgcGameDao()
    }

    fun provideAiConversationDao(database: AppDatabase): AiConversationDao {
        return database.aiConversationDao()
    }

    fun provideAiMessageDao(database: AppDatabase): AiMessageDao {
        return database.aiMessageDao()
    }

    fun provideShareRecordDao(database: AppDatabase): ShareRecordDao {
        return database.shareRecordDao()
    }

    fun providePlayGameTimeDao(database: AppDatabase): PlayGameTimeDao {
        return database.playGameTimeDao()
    }

    single { provideDatabase(androidApplication()) }
    single { provideGameDetailDao(get()) }
    single { provideMyPlayedGameDao(get()) }
    single { provideMetaCacheDao(get()) }
    single { provideRecentUgcGameDao(get()) }
    single { provideAiConversationDao(get()) }
    single { provideAiMessageDao(get()) }
    single { provideShareRecordDao(get()) }
    single { providePlayGameTimeDao(get()) }
    single { MetaKV() }
}