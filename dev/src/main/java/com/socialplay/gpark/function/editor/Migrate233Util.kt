package com.socialplay.gpark.function.editor

import android.content.Context
import com.meta.biz.ugc.local.EditorLocalHelper
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.kv.MetaKV
import com.meta.biz.ugc.model.EditorConfigJsonEntity
import com.socialplay.gpark.function.download.DownloadFileProvider
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.Md5Util.md5
import com.meta.biz.ugc.util.Zipper
import com.socialplay.gpark.util.extension.toast
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.singleOrNull
import kotlinx.coroutines.withContext
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import org.koin.core.context.GlobalContext
import timber.log.Timber
import java.io.File

/**
 * Created by bo.li
 * Date: 2023/4/6
 * Desc:
 */
object Migrate233Util: KoinComponent {

    private val metaKV: MetaKV by inject()
    private val repository: IMetaRepository by inject()
    private const val TAG = "checkcheck_migrate"

    /**
     * 处理233迁移工程
     */
    suspend fun handle233Migrate(getRootFile: File): String {
        Timber.tag(TAG).d("handle233Migrate")
        return withContext(Dispatchers.IO) {
            val sb = StringBuilder()
            getRootFile.listFiles()?.apply {
                    val zipList = filter {
                        it.name.endsWith(".zip")
                    }
                    Timber.tag(TAG).d("zipListSize:${zipList.size}")
                    zipList.forEach { zip ->
                        val target = runCatching {
                            Timber.tag(TAG).d("name:${zip.name}, path:${zip.path}")
                            val target = File(DownloadFileProvider.getEditorUserUnzipLocal(), zip.nameWithoutExtension)
                            if (!target.exists()) {
                                target.mkdirs()
                            }
                            target
                        }.getOrElse {
                            withContext(Dispatchers.Main) {
                                GlobalContext.get().get<Context>().toast("${zip.name} mkdirs error", true)
                            }
                            sb.append("${zip.name} get target error: ${it}\n\n")
                            Timber.tag(TAG).e("forEach ${zip.name} error: $it")
                            null
                        }
                        target?.let {
                            val result = migrate233UnZipFile(zip, target)
                            if (!result.succeeded) {
                                sb.append("${zip.name} migrate233UnZipFile error: ${result.message}\n\n")
                            }
                        }
                    }
                    zipList.forEach { zip ->
                        runCatching {
                            zip.delete()
                        }.getOrElse {
                            withContext(Dispatchers.Main) {
                                GlobalContext.get().get<Context>().toast("${zip.name} delete error", true)
                            }
                            sb.append("${zip.name} delete zip error: $it\n\n")
                            Timber.tag(TAG).e("forEach ${zip.name} error: $it")
                        }
                    }
                }
            return@withContext sb.toString()
        }
    }

    /**
     * 更换信息为新的
     */
    private suspend fun migrate233UnZipFile(
        file: File,
        target: File,
    ): DataResult<File> {
        return withContext(Dispatchers.IO) {
            val context = GlobalContext.get().get<Context>()
            kotlin.runCatching {
                Zipper.unzip(file).to(target)
                val jsonFile = EditorLocalHelper.getJsonFile(target)
                val originJsonInfo = EditorLocalHelper.getEditorConfigEntity(jsonFile)
                originJsonInfo?.gid ?: throw NullPointerException(context.getString(R.string.debug_migrate_gid_empty))
                val newGameId = getMigrateGameId(originJsonInfo.gid!!) ?: throw NullPointerException(context.getString(R.string.debug_migrate_no_ttai_id, originJsonInfo.gid))
                val newTemplateInfo = repository.fetchTemplateInfoByCode(newGameId).singleOrNull()
                if (newTemplateInfo?.succeeded != true || newTemplateInfo.data == null) {
                    throw IllegalArgumentException("${context.getString(R.string.debug_migrate_error_api)}, msg：${newTemplateInfo?.message}，data:${newTemplateInfo?.data}")
                }
                val newThumbPath: String? = if (originJsonInfo.thumb == null || originJsonInfo.thumb!!.startsWith("http")) {
                    originJsonInfo.thumb
                } else {
                    val newPicFile = File(File(target, "Pictures"), File(originJsonInfo.thumb!!).name)
                    if (newPicFile.exists()) {
                        newPicFile.absolutePath
                    } else {
                        originJsonInfo.thumb
                    }
                }
                val newJsonInfo = EditorConfigJsonEntity(
                    fileId = (metaKV.account.uuid + System.currentTimeMillis()).md5(),
                    gid = newGameId,
                    parentPackageName = newTemplateInfo.data?.packageName,
                    name = originJsonInfo.name,
                    parentId = newTemplateInfo.data?.gameIdentity,
                    thumb = newThumbPath,
                    version = newTemplateInfo.data?.version,
                    createFileTime = System.currentTimeMillis()
                )
                jsonFile.writeText(GsonUtil.gson.toJson(newJsonInfo))
                return@withContext DataResult.Success(target)
            }.getOrElse {
                it.printStackTrace()
                kotlin.runCatching {
                    target.deleteRecursively()
                }
                withContext(Dispatchers.Main) {
                    GlobalContext.get().get<Context>().toast(context.getString(R.string.debug_migrate_unzip_failed, it))
                }
                Timber.tag(TAG).e(context.getString(R.string.debug_migrate_unzip_failed, it))
                return@withContext DataResult.Error(0, it.message ?:"", null)
            }
        }
    }

    private fun getMigrateGameId(gameId233: String): String? = GsonUtil.gsonSafeParseCollection<Map<String, String>>(metaKV.tTaiKV.editorMigrateMap)?.get(gameId233)
}