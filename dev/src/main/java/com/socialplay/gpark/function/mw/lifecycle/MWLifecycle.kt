package com.socialplay.gpark.function.mw.lifecycle

import android.app.Activity
import android.app.Application
import android.content.ComponentName
import android.os.Bundle
import android.util.SparseArray

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/05/14
 * desc   :
 * </pre>
 */


open class MWLifecycle {

    companion object {

        private val applicationEventMap = SparseArray<ApplicationEvent>().apply {
            put(ApplicationEvent.ALL.status, ApplicationEvent.ALL)
            put(ApplicationEvent.BEFORE_CREATED.status, ApplicationEvent.BEFORE_CREATED)
            put(ApplicationEvent.AFTER_CREATED.status, ApplicationEvent.AFTER_CREATED)
        }

        private val activityEventMap = SparseArray<ActivityEvent>().apply {
            put(ActivityEvent.ANY.status, ActivityEvent.ANY)
            put(ActivityEvent.CREATE.status, ActivityEvent.CREATE)
            put(ActivityEvent.START.status, ActivityEvent.START)
            put(ActivityEvent.STOP.status, ActivityEvent.STOP)
            put(ActivityEvent.RESUME.status, ActivityEvent.RESUME)
            put(ActivityEvent.PAUSE.status, ActivityEvent.PAUSE)
            put(ActivityEvent.DESTROY.status, ActivityEvent.DESTROY)
            put(ActivityEvent.SAVE_INSTANCE_STATE.status, ActivityEvent.SAVE_INSTANCE_STATE)
        }

        fun applicationEvent(status: Int): ApplicationEvent {
            return applicationEventMap.get(status, ApplicationEvent.ALL)
        }

        fun activityEvent(status: Int): ActivityEvent {
            return activityEventMap.get(status, ActivityEvent.ANY)
        }
    }

    open fun onApplicationAll(app: Application, event: ApplicationEvent) {}

    open fun onBeforeApplicationCreated(app: Application) {}

    open fun onAfterApplicationCreated(app: Application) {}

    open fun onApplicationDestroy(app: Application) {}

    open fun onActivityAll(activity: Activity, event: ActivityEvent) {}

    open fun onActivityCreated(activity: Activity) {}

    open fun onActivityStarted(activity: Activity) {}

    open fun onActivityResumed(activity: Activity) {}

    open fun onActivityPaused(activity: Activity) {}

    open fun onActivityStopped(activity: Activity) {}

    open fun onActivityDestroyed(activity: Activity) {}

    open fun onActivitySaveInstanceState(activity: Activity, bundle: Bundle?) {}

    open fun onActivityCreatedCreatedWithBundle(activity: Activity, bundle: Bundle?) {}

    open fun onBroadcastIntent(action: String?, component: ComponentName?, bundle: Bundle?) {}

    enum class ApplicationEvent(val status: Int, val keyWord: String) {
        ALL(0, "all"),
        BEFORE_CREATED(1, "before"),
        AFTER_CREATED(2, "after")
    }

    enum class ActivityEvent(val status: Int, val keyWord: String) {
        ANY(0, "all"),
        CREATE(3, "create"),
        START(4, "start"),
        STOP(5, "stop"),
        RESUME(6, "resume"),
        PAUSE(7, "pause"),
        DESTROY(8, "destroy"),
        SAVE_INSTANCE_STATE(9, "saveInstanceState")
    }
}