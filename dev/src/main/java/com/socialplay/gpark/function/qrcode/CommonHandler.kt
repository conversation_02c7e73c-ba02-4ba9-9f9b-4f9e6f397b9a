package com.socialplay.gpark.function.qrcode

import android.content.Context
import androidx.fragment.app.Fragment
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.LoginState
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.LoginStatusEvent
import com.socialplay.gpark.data.model.LoginType
import com.socialplay.gpark.data.model.LoginWay
import com.socialplay.gpark.data.model.qrcode.CreatorQrLoginData
import com.socialplay.gpark.data.model.qrcode.QrCodeResolveApiRequest
import com.socialplay.gpark.data.model.qrcode.QrCodeResolveApiResponse
import com.socialplay.gpark.data.model.qrcode.ScanRequestData
import com.socialplay.gpark.data.model.qrcode.ScanResultData
import com.socialplay.gpark.util.extension.toast
import kotlinx.coroutines.flow.singleOrNull
import org.koin.core.context.GlobalContext

object CommonHandler : QRCodeHandler {

    private val metaRepository: IMetaRepository = GlobalContext.get().get()

    override suspend fun process(
        context: Context,
        fragment: Fragment,
        request: ScanRequestData,
        result: ScanResultData
    ): Boolean {
        if (result.content.isEmpty()) return false
        val response = metaRepository.resolveQrCode(QrCodeResolveApiRequest(result.content))
            .singleOrNull()
        val data = response?.data ?: return false
        when (data.type) {
            QrCodeResolveApiResponse.TYPE_CREATOR_QR_LOGIN -> {
                val loginData = data.data as? CreatorQrLoginData
                if (loginData == null) {
                    fragment.toast(R.string.login_fail)
                    return true
                }
                val loginResult = metaRepository.processAuthLogin(
                    DataResult.Success(loginData.toAuthInfoApiResult()),
                    LoginType.ExistAccount,
                    LoginWay.QrCode,
                    ""
                ).singleOrNull()
                when (loginResult) {
                    is LoginState.Succeeded -> {
                        val accountInteractor: AccountInteractor = GlobalContext.get().get()
                        accountInteractor.postLoginStatusAndUserInfo(
                            loginResult.userInfo,
                            LoginStatusEvent.LOGIN_SUCCESS
                        )
                        fragment.toast(R.string.login_succeed)
                        accountInteractor.afterSwitchAccount()
                    }

                    is LoginState.Failed -> {
                        fragment.toast(loginResult.message)
                    }

                    else -> {
                        fragment.toast(R.string.login_fail)
                    }
                }
                return true
            }

            else -> {
                return false
            }
        }
    }
}
