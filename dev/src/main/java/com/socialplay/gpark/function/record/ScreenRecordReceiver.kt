package com.socialplay.gpark.function.record

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Context.RECEIVER_EXPORTED
import android.content.Context.RECEIVER_NOT_EXPORTED
import android.content.Intent
import android.content.IntentFilter
import android.net.Uri
import android.os.Build
import android.text.TextUtils
import timber.log.Timber

/**
 * 录屏权限广播
 */
class ScreenRecordReceiver : BroadcastReceiver() {
    var gamePackageName: String? = null
    var recordActionCallback: RecordActionCallback? = null

    companion object {
        const val META_APP_X_SCREEN_RECORD = "META_APP_X_SCREEN_RECORD"
        const val EXTRA_PACKAGE_NAME = "EXTRA_PACKAGE_NAME"
        const val EXTRA_ACTION_TYPE = "EXTRA_ACTION_TYPE"
        const val EXTRA_FILE_PATH = "EXTRA_FILE_PATH"
        const val EXTRA_FILE_URI = "EXTRA_FILE_URI"
        const val EXTRA_SHOW_END_DIALOG = "EXTRA_SHOW_END_DIALOG"
        const val ACTION_TYPE_START_COUNT_DOWN = 0
        const val ACTION_TYPE_BEFORE_START = 1
        const val ACTION_TYPE_STARTED = 2
        const val ACTION_TYPE_START_FAILED = 3
        const val ACTION_TYPE_END_RECORD = 4
        const val ACTION_TYPE_AFTER_SAVE = 5
        const val ACTION_TYPE_PAUSE = 6
        const val ACTION_TYPE_RESUME = 7

        /**
         * 游戏进程必须注册广播
         */
        fun registerRecordReceiver(context: Context, gamePackageName: String, recordActionCallback: RecordActionCallback): ScreenRecordReceiver {
            val receiver = ScreenRecordReceiver()
            receiver.gamePackageName = gamePackageName
            receiver.recordActionCallback = recordActionCallback
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                context.registerReceiver(
                    receiver,
                    IntentFilter(META_APP_X_SCREEN_RECORD),
                    RECEIVER_EXPORTED
                )
            } else {
                context.registerReceiver(receiver, IntentFilter(META_APP_X_SCREEN_RECORD))
            }
            return receiver
        }

        fun sendRecordReceiver(
            context: Context,
            actionType: Int,
            gamePackageName: String,
            filePath: String? = null,
            fileUri: Uri? = null,
            showEndDialog: Boolean = true
        ) {
            val intent = Intent(META_APP_X_SCREEN_RECORD).apply {
                putExtra(EXTRA_PACKAGE_NAME, gamePackageName)
                putExtra(EXTRA_ACTION_TYPE, actionType)
                if (filePath != null) {
                    putExtra(EXTRA_FILE_PATH, filePath)
                }
                if (fileUri != null) {
                    putExtra(EXTRA_FILE_URI, fileUri)
                }
                putExtra(EXTRA_SHOW_END_DIALOG, showEndDialog)
            }
            context.sendBroadcast(intent)
        }
    }

    override fun onReceive(context: Context, intent: Intent) {
        val action = intent.action
        val packageName = intent.getStringExtra(EXTRA_PACKAGE_NAME)
        val actionType = intent.getIntExtra(EXTRA_ACTION_TYPE, ACTION_TYPE_BEFORE_START)
        Timber.e("my_record game process onReceive action:$action,resultCode:$actionType,packageName:$packageName")
        if (TextUtils.equals(action, META_APP_X_SCREEN_RECORD)) {
            recordActionCallback?.onRecordAction(actionType, intent)
        }
    }

    interface RecordActionCallback {
        fun onRecordAction(actionType: Int, intent: Intent)
    }

}
