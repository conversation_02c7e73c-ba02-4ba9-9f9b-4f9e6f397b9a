package com.socialplay.gpark.function.mw.lifecycle

import android.app.Activity
import android.app.Application
import androidx.fragment.app.FragmentActivity
import com.meta.biz.ugc.protocol.constants.ProtocolReceiveConstants
import com.meta.biz.ugc.protocol.listener.SingleProtocolListener
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.umw.UMW
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import timber.log.Timber
import java.lang.ref.WeakReference
import com.meta.biz.ugc.model.AvatarSaveShareDialogShowMsg
import com.socialplay.gpark.function.umw.impl.ext.addProtocolObserver
import com.socialplay.gpark.ui.editor.share.AvatarSaveShareDialog

/**
 * 角色保存分享
 * https://meta.feishu.cn/wiki/CK1Vw8d7SisvolkCsd3cZxjvnVg
 * https://project.feishu.cn/androidcc/story/detail/4478760819
 */
class AvatarSaveShareLifecycle(
    private val umw: UMW
) : MWLifecycle() {

    private var activityRef: WeakReference<Activity>? = null

    private val scope by lazy { MainScope() }

    private val listener by lazy {
        object : SingleProtocolListener<AvatarSaveShareDialogShowMsg>(ProtocolReceiveConstants.PROTOCOL_SHOW_AVATAR_SAVE_SHARE_DIALOG) {
            override fun handleProtocol(message: AvatarSaveShareDialogShowMsg?, messageId: Int) {
                message?.images?.let {
                    scope.launch {
                        if(message.status == 0){
                            showShareDialog(it)
                        }else if(message.status == 1){
                            updateShareDialog(it)
                        }
                    }
                }
            }
        }
    }

    override fun onAfterApplicationCreated(app: Application) {
        super.onAfterApplicationCreated(app)
        Timber.d("AvatarSaveShareLifecycle onAfterApplicationCreated")
    }

    override fun onActivityStarted(activity: Activity) {
        super.onActivityStarted(activity)
        activityRef = WeakReference(activity)
        umw.addProtocolObserver(listener)
        Timber.d("AvatarSaveShareLifecycle onActivityStarted")
    }

    override fun onActivityStopped(activity: Activity) {
        umw.removeProtocolObserver(listener)
        activityRef = null
        Timber.d("AvatarSaveShareLifecycle onActivityStopped")
        super.onActivityStopped(activity)
    }

    private fun showShareDialog(images: List<String>) {
        Analytics.track(EventConstants.ROLE_SAVE_SHARE)
        val activity = activityRef?.get() as? FragmentActivity ?: return
        AvatarSaveShareDialog.show(activity.supportFragmentManager, images, umw.avatar?.isStandalone == true)
    }

    private fun updateShareDialog(images: List<String>) {
        val activity = activityRef?.get() as? FragmentActivity ?: return
        AvatarSaveShareDialog.update(activity.supportFragmentManager, images)
    }

}