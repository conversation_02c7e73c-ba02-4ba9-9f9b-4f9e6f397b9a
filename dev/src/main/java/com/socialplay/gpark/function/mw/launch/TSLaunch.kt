package com.socialplay.gpark.function.mw.launch

import android.content.Context
import com.meta.lib.mwbiz.MWBiz
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.GameDetailInfo
import com.socialplay.gpark.data.model.ResourceInfo
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.mw.MWGamePreDownload
import com.socialplay.gpark.function.mw.launch.bean.TSLaunchParams
import com.socialplay.gpark.function.mw.launch.exception.BaseTSLaunchException
import com.socialplay.gpark.function.mw.launch.setp.ITSGameLaunchWrapStep
import com.socialplay.gpark.util.simMsg
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 * <AUTHOR>
 * @date 2022/11/22
 */
class TSLaunch : BaseTSLaunch() {

    //启动游戏协程任务
    private var launchJob: Job? = null
    private val metaKV: MetaKV = GlobalContext.get().get()

    //启动普通TS游戏
    fun launch(context: Context, launchParams: TSLaunchParams) {
        launchParams.resIdBean
            .setTsTypeIfUnset(ResIdBean.TS_TYPE_NORMAL)
            .setIconTypeIfEmpty(ResIdBean.ICON_TYPE_PGC)
        gameLaunch(context, launchParams, TSGameLaunchManager.tsLaunch)
    }

    //启动TS UGC游戏
    fun launchUgc(context: Context, launchParams: TSLaunchParams) {
        defScope.launch(Dispatchers.IO) {
            val metaRepository: IMetaRepository = GlobalContext.get().get()
            metaRepository.reportLaunchUgcGame(launchParams.gameId)
        }
        launchParams.resIdBean
            .setTsTypeIfUnset(ResIdBean.TS_TYPE_UCG)
            .setIconTypeIfEmpty(ResIdBean.ICON_TYPE_UGC)
        launchParams.isUgcGame = true
        //UGC游戏不需要扩展参数缓存
        launchParams.expandCache = false
        gameLaunch(context, launchParams, TSGameLaunchManager.tsUGCLaunch)
    }

    //启动TS Local游戏
    fun launchLocal(context: Context, launchParams: TSLaunchParams) {
        launchParams.isLocalGame = true
        launchParams.resIdBean
            .setTsTypeIfUnset(ResIdBean.TS_TYPE_LOCAL)
        gameLaunch(context, launchParams, TSGameLaunchManager.tsLocalLaunch)
    }

    //启动TS view游戏
    fun launchView(context: Context, launchParams: TSLaunchParams) {
        launchParams.isViewGame = true
        gameLaunch(context, launchParams, TSGameLaunchManager.tsViewLaunch)
    }

    //启动剧情创作游戏
    fun launchPlot(context: Context, launchParams: TSLaunchParams) {
        launchParams.resIdBean
            .setTsTypeIfUnset(ResIdBean.TS_TYPE_NORMAL)
            .setIconTypeIfEmpty(ResIdBean.ICON_TYPE_PGC)
        launchParams.isPlotGame = true
        gameLaunch(context, launchParams, TSGameLaunchManager.tsPlotLaunch)
    }

    //启动游戏
    private fun gameLaunch(ctx: Context, p: TSLaunchParams, step: ITSGameLaunchWrapStep) {
        launchJob?.cancel() //取消上次拉起
        launchJob = defScope.launch(Dispatchers.IO) { launchFlow(ctx, p, step) }
    }

    private suspend fun launchFlow(
        ctx: Context,
        params: TSLaunchParams,
        step: ITSGameLaunchWrapStep,
    ) {
        checkCondition(params)
        if (!MWBiz.isAvailable() && isDownloadingWithId(params.gameId)) {
            call { onPauseDownload(params) }
        } else {
            if (!params.isViewGame) {
                MWGamePreDownload.sendStartGameState()
            }
            requireLaunchParamsFlow(params).onStart {
                step.check(ctx, params)
            }.map { mwLaunchParams ->
                //配置启动游戏的参数
                Timber.tag("TSLaunch").d("process 开始 ${params.gameId}")
                params.setMWLaunchParams(mwLaunchParams)
                step.process(ctx, params)
                call { onLaunchGame(params) }
                params
            }.catch { e ->
                //prepare 失败
                Timber.tag("TSLaunch").d("catch prepare 失败 $e")
                params.setPrepareThrowable(e)
                launchPrepareEndEvent(params)//发送prepare失败埋点
            }.map {
                //prepare成功
                Timber.tag("TSLaunch").d("launch ${params.gameId}")
                launchPrepareEndEvent(params)//发送prepare成功埋点
                step.launch(ctx, params)//启动游戏
                launchGameEndEvent(params)//发送启动成功埋点
                if (step != TSGameLaunchManager.tsViewLaunch) metaKV.playGame.playGameTimes ++
            }.catch { e ->
                //启动游戏异常
                Timber.tag("TSLaunch").d("catch launch 失败 $e")
                params.setLaunchThrowable(e)
                launchGameEndEvent(params)
            }.onCompletion {
                Timber.tag("TSLaunch").d("onCompletion $it")
                call { onLaunchGameEnd(params, params.getThrowable()) }
                updateCurrentState(LaunchStatus.NONE)
            }.collect()
        }
    }

    //拉起准备结束的埋点（成功或者失败）
    private fun launchPrepareEndEvent(params: TSLaunchParams) {
        val error = params.getPrepareThrowable()
        val eventMap = mutableMapOf<String, Any>()
        if (error == null) {
            eventMap["result"] = "success"
        } else {
            eventMap["result"] = "failed"
            eventMap["reason"] = "${error.simMsg()}"
            eventMap["error_type"] = if (error is BaseTSLaunchException) {
                error.getErrorType()
            } else {
                BaseTSLaunchException.ERROR_TYPE_OTHER_FAILED
            }
        }
        sendEvent(EventConstants.EVENT_MW_ENV_PREPARE, params, eventMap)
    }

    //拉起游戏结束埋点（成功或者失败）
    private fun launchGameEndEvent(params: TSLaunchParams) {
        val error = params.getLaunchThrowable()
        val eventMap = mutableMapOf<String, Any>()
        if (error == null) {
            eventMap["result"] = "success"
        } else {
            eventMap["result"] = "failed"
            eventMap["reason"] = "${error.simMsg()}"
            eventMap["error_type"] = if (error is BaseTSLaunchException) {
                error.getErrorType()
            } else {
                BaseTSLaunchException.ERROR_TYPE_OTHER_FAILED
            }
        }
        sendEvent(EventConstants.EVENT_LAUNCH_GAME_RESULT, params, eventMap)
    }

    private fun checkCondition(params: TSLaunchParams) {
        if (params.resIdBean.getCategoryID() <= 0) {
            val ex = IllegalArgumentException("you must set a resId before you launch!!")
            if (BuildConfig.DEBUG) throw ex else ex.printStackTrace(System.err)
        }
    }

    fun createTSGameDetailInfo(
        gameId: String,
        packageName: String,
        gameName: String,
        startupExtension: String? = null,
        icon: String = "",
    ): GameDetailInfo {
        val resourceInfo = ResourceInfo(null, 0, 0, "", packageName, 0, 0, null)
        return GameDetailInfo(
            gameId, gameName, icon, "", GameDetailInfo.GAME_TYPE_TS,
            startupExtension, null, null, null, null, resourceInfo,
            listOf(GameDetailInfo.GAME_TYPE_MGS)
        )
    }

}