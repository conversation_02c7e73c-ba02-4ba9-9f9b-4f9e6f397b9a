package com.socialplay.gpark.function.editor

import android.os.Parcelable
import androidx.annotation.Keep
import com.meta.biz.ugc.model.GameTransform
import kotlinx.parcelize.Parcelize

/**
 * xingxiu.hou
 * 2023/2/22
 */

/**
 * 角色游戏试穿
 */
@Keep
@Parcelize
data class RoleGameTryOn(
    val tryOnUserId: String,//试穿的用户Id
    val from: String, //来源
    val changePhoto: Int,
    val roleId: String,
    val allowTryOn: Boolean,
    val roleData: String,
    val clothesItemId: String,
    val itemId: String,
    val pgcClothItemId: String
) : Parcelable {
    companion object {
        const val FROM_UNKNOWN = "unknown"
        const val FROM_MY_PROFILE = "my_profile"
        const val FROM_OTHER_PROFILE = "other_profile"
        const val FROM_PROFILE_PHOTO = "profile_photo"
        const val FROM_REC_USER = "rec_user"
        const val FROM_COMMUNITY_POST = "community_post"
        const val FROM_HOME_PAGE_CLOTHES = "home_page_clothes"
        const val FROM_PROFILE_CURRENT_OUTFIT = "profile_current_outfit"

        fun create(
            tryOnUserId: String = "",
            from: String = FROM_UNKNOWN,
            changePhoto: Int = 0,
            roleId: String = "",
            allowTryOn: Boolean = false,
            roleData: String = "",
            clothesItemId: String = "",
            itemId: String = "",
            pgcClothItemId: String = "",
        ): RoleGameTryOn {
            return RoleGameTryOn(
                tryOnUserId,
                from,
                changePhoto,
                roleId,
                allowTryOn,
                roleData,
                clothesItemId,
                itemId,
                pgcClothItemId
            )
        }
    }

    val sendWithTransform get() = from == FROM_OTHER_PROFILE
            || from == FROM_MY_PROFILE
            || from == FROM_COMMUNITY_POST
            || from == FROM_PROFILE_CURRENT_OUTFIT

    val hasTryOnData: Boolean
        get() = !tryOnUserId.isNullOrBlank()
                || !roleId.isNullOrBlank()
                || !roleData.isNullOrBlank()
                || !clothesItemId.isNullOrBlank()
                || !itemId.isNullOrBlank()
                || !pgcClothItemId.isNullOrBlank()

    fun isPoseStatus(): Boolean {
        return from == FROM_PROFILE_PHOTO && changePhoto == 1
    }

    fun getTransformStatus(): String {
        return if (isPoseStatus()) {
            GameTransform.STATUS_ROLE_POSE
        } else if (hasTryOnData && sendWithTransform) {
            GameTransform.STATUS_TRY_ON
        } else {
            GameTransform.STATUS_ROLE_EDIT
        }
    }
}