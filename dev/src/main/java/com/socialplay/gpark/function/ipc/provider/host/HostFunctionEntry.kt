package com.socialplay.gpark.function.ipc.provider.host

import com.meta.lib.mwbiz.MWBizProxy
import com.meta.lib.mwbiz.bean.bridge.SendMsg
import com.socialplay.gpark.function.ipc.provider.ts.ITSFunctionEntry
import timber.log.Timber

/**
 * 主进程回调MW函数
 */
object HostFunctionEntry : ITSFunctionEntry {

    override fun call(action: String, data: Map<String, Any>?) {
        Timber.i("HostFunctionEntry call ts action:$action, data:$data")
        MWBizProxy.callUE(SendMsg().apply {
            this.action = action
            this.data = data ?: emptyMap()
        })
    }
}