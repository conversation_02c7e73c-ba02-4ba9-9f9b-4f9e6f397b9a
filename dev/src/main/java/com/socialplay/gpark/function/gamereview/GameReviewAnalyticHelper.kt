package com.socialplay.gpark.function.gamereview

import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.OnLifecycleEvent
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewbinding.ViewBinding
import com.socialplay.gpark.ui.base.adapter.BasePagingDataAdapter
import java.util.concurrent.atomic.AtomicBoolean

/**
 * Created by bo.li
 * Date: 2022/4/25
 * Desc:
 */
class GameReviewAnalyticHelper <T: Any, VB: ViewBinding>(
    private val screenHeight: Int,
    private var owner: LifecycleOwner?,
    private var recyclerView: RecyclerView?,
    private var headerCount: Int,
    private var footerCount: Int,
    private var adapter: BasePagingDataAdapter<T, VB>?,
    private var reportAnalytic: ((T?) -> Unit)?
) : LifecycleObserver {

    private var layoutManager: LinearLayoutManager? = null

    // 控制onResume发埋点的时机，onPause后才发
    private var checkWhenResume = AtomicBoolean(false)
    // 控制第一个item加载出来前都不要发埋点
    private var isMapInit = AtomicBoolean(false)

    // 目前可见的position（滑动到位置/onPause后生效）
    private var visiblePositionArray = intArrayOf(-1, -1)

    private val location = IntArray(2)
    private val rvLocation = IntArray(2)

    init {
        init()
    }

    private fun init() {
        this.layoutManager = recyclerView?.layoutManager as? LinearLayoutManager
        this.owner?.lifecycle?.addObserver(this)
        this.adapter?.setOnItemShowListener { view, position ->
            layoutManager?.findViewByPosition(position)?.post {
                if (!isMapInit.get()) {
                    isMapInit.set(true)
                    checkRangeMap(false)
                }
            }
        }
    }

    fun updateHeaderCount(headerCount: Int) {
        this.headerCount = headerCount
    }

    private fun checkRangeMap(checkInit: Boolean) {
        layoutManager?.let {
            val nowVisiblePositionArray = ListAnalyticUtil.getVisiblePositionArray(it, location, rvLocation, screenHeight) ?: return
            if (!checkInit || ListAnalyticUtil.isVisibleArrayInit(visiblePositionArray)) {
                reportRangeTopPosition(nowVisiblePositionArray, true)
            }
            visiblePositionArray = nowVisiblePositionArray
        }
    }

    fun onScrolled() {
        if (!isMapInit.get() || checkWhenResume.get() || owner?.lifecycle?.currentState != Lifecycle.State.RESUMED) {
            return
        }
        checkRangeMap(true)
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_RESUME)
    fun onResume() {
        if (checkWhenResume.get()) {
            checkWhenResume.set(false)
            checkRangeMap(false)
        }
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_PAUSE)
    fun onPause() {
        checkWhenResume.set(true)
        visiblePositionArray = intArrayOf(-1, -1)
    }

    private fun reportRangeTopPosition(positionArray: IntArray, checkRepeat: Boolean) {
        for (position in positionArray[0] .. positionArray[1]) {
            if (position < 0) {
                continue
            }
            if (!checkRepeat || position !in visiblePositionArray[0] .. visiblePositionArray[1]) {
                val dataPosition = position - headerCount
                if (dataPosition in 0 until (adapter?.itemCount ?:0)) {
                    val item = adapter?.snapshot()?.getOrNull(dataPosition) ?: return
                    reportAnalytic?.invoke(item)
                }
            }
        }
    }

    fun clear() {
        owner?.lifecycle?.removeObserver(this)
        owner = null
        recyclerView = null
        layoutManager = null
        adapter = null
        reportAnalytic = null
        isMapInit.set(false)
        visiblePositionArray = intArrayOf(-1, -1)
        checkWhenResume.set(false)
    }
}