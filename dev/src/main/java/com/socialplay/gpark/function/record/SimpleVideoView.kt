package com.socialplay.gpark.function.record

import android.content.Context
import android.graphics.Color
import android.net.Uri
import android.util.AttributeSet
import android.util.Log
import android.view.LayoutInflater
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.VideoView
import com.socialplay.gpark.databinding.LayoutSimpleVideoViewBinding
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible
import timber.log.Timber

/**
 * @des:
 * @author: lijunjia
 * @date: 2022/9/23 15:06
 */
class SimpleVideoView : RelativeLayout {
    private lateinit var ivPlay: ImageView
    lateinit var ivCover: ImageView
    lateinit var videoView: VideoView
    private var mVideoPath: String? = null
    var clickCallback: ((<PERSON>olean) -> Unit)? = null

    constructor(context: Context) : super(context) {
        initAttrs(context, null)
    }

    constructor(context: Context, attrs: AttributeSet) : super(context, attrs) {
        initAttrs(context, attrs)
    }

    constructor(context: Context, attrs: AttributeSet, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        initAttrs(context, attrs)
    }

    private fun initAttrs(context: Context, attrs: AttributeSet?) {
        val binding = LayoutSimpleVideoViewBinding.inflate(LayoutInflater.from(context), this)
        ivPlay = binding.ivPlay
        ivCover = binding.ivVideoCover
        videoView = binding.videoView
        setOnAntiViolenceClickListener {
            if (mVideoPath == null) {
                return@setOnAntiViolenceClickListener
            }
            clickCallback?.invoke(videoView.isPlaying)
            togglePlay()
        }
        videoView.setOnPreparedListener {
            it.isLooping = true
        }
        videoView.setOnErrorListener { _, what, extra ->
            Timber.d("play error what:$what extra:$extra")
            true
        }
    }


    fun initVideoPath(videoPath: String) {
        mVideoPath = videoPath
        videoView.setVideoPath(videoPath)
    }

    fun pauseAndShowCover() {
        ivCover.visible()
        if (videoView.isPlaying) {
            ivPlay.visible()
            videoView.pause()
        }
    }

    private fun togglePlay() {
        if (videoView.isPlaying) {
            ivPlay.visible()
            videoView.pause()
        } else {
            ivCover.gone()
            ivPlay.gone()
            videoView.start()
        }
    }

    fun release(){
        videoView.setOnPreparedListener(null)
        videoView.setOnErrorListener(null)
    }

}