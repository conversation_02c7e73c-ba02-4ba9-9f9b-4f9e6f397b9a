package com.socialplay.gpark.function.videocompression;

import android.annotation.SuppressLint;
import android.annotation.TargetApi;
import android.media.MediaCodec;
import android.media.MediaCodecInfo;
import android.media.MediaCodecList;
import android.media.MediaExtractor;
import android.media.MediaFormat;
import android.media.MediaMetadataRetriever;

import java.io.File;
import java.nio.ByteBuffer;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

import timber.log.Timber;

/**
 * 视频压缩
 */
@SuppressLint("NewApi")
public class MediaController {

    public static  File   cachedFile;
    private        String path;

    private final static    String          MIME_TYPE              = "video/avc";
    private final static    int             PROCESSOR_TYPE_OTHER   = 0;
    private static volatile MediaController Instance               = null;
    private                 boolean         videoConvertFirstWrite = true;

    //Default values
    private final static int DEFAULT_VIDEO_WIDTH   = 640;
    private final static int DEFAULT_VIDEO_HEIGHT  = 360;
    private final static int DEFAULT_VIDEO_BITRATE = 450000;

    public static MediaController getInstance() {
        MediaController localInstance = Instance;
        if (localInstance == null) {
            synchronized (MediaController.class) {
                localInstance = Instance;
                if (localInstance == null) {
                    Instance = localInstance = new MediaController();
                }
            }
        }
        return localInstance;
    }

    @SuppressLint("NewApi")
    public static int selectColorFormat(MediaCodecInfo codecInfo, String mimeType) {
        MediaCodecInfo.CodecCapabilities capabilities = codecInfo.getCapabilitiesForType(mimeType);
        int lastColorFormat = 0;
        for (int i = 0; i < capabilities.colorFormats.length; i++) {
            int colorFormat = capabilities.colorFormats[i];
            if (isRecognizedFormat(colorFormat)) {
                lastColorFormat = colorFormat;
                if (!(codecInfo.getName().equals("OMX.SEC.AVC.Encoder") && colorFormat == 19)) {
                    return colorFormat;
                }
            }
        }
        return lastColorFormat;
    }

    private static boolean isRecognizedFormat(int colorFormat) {
        switch (colorFormat) {
            case MediaCodecInfo.CodecCapabilities.COLOR_FormatYUV420Planar:
            case MediaCodecInfo.CodecCapabilities.COLOR_FormatYUV420PackedPlanar:
            case MediaCodecInfo.CodecCapabilities.COLOR_FormatYUV420SemiPlanar:
            case MediaCodecInfo.CodecCapabilities.COLOR_FormatYUV420PackedSemiPlanar:
            case MediaCodecInfo.CodecCapabilities.COLOR_TI_FormatYUV420PackedSemiPlanar:
                return true;
            default:
                return false;
        }
    }

    public native static int convertVideoFrame(ByteBuffer src, ByteBuffer dest, int destFormat, int width, int height, int padding, int swap);

    private void didWriteData(final boolean last, final boolean error) {
        final boolean firstWrite = videoConvertFirstWrite;
        if (firstWrite) {
            videoConvertFirstWrite = false;
        }
    }

    public static class VideoConvertRunnable implements Runnable {

        private final String videoPath;
        private final File   destDirectory;

        private VideoConvertRunnable(String videoPath, File dest) {
            this.videoPath = videoPath;
            this.destDirectory = dest;
        }

        public static void runConversion(final String videoPath, final File dest) {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    try {
                        VideoConvertRunnable wrapper = new VideoConvertRunnable(videoPath, dest);
                        Thread th = new Thread(wrapper, "VideoConvertRunnable");
                        th.start();
                        th.join();
                    } catch (Exception e) {
                        Timber.e("tmessages" + e.getMessage());
                    }
                }
            }).start();
        }

        @Override
        public void run() {
            MediaController.getInstance().convertVideo(videoPath, destDirectory);
        }
    }

    public static MediaCodecInfo selectCodec(String mimeType) {
        int numCodecs = MediaCodecList.getCodecCount();
        MediaCodecInfo lastCodecInfo = null;
        for (int i = 0; i < numCodecs; i++) {
            MediaCodecInfo codecInfo = MediaCodecList.getCodecInfoAt(i);
            if (!codecInfo.isEncoder()) {
                continue;
            }
            String[] types = codecInfo.getSupportedTypes();
            for (String type : types) {
                if (type.equalsIgnoreCase(mimeType)) {
                    lastCodecInfo = codecInfo;
                    if (!lastCodecInfo.getName().equals("OMX.SEC.avc.enc")) {
                        return lastCodecInfo;
                    } else if (lastCodecInfo.getName().equals("OMX.SEC.AVC.Encoder")) {
                        return lastCodecInfo;
                    }
                }
            }
        }
        return lastCodecInfo;
    }

    /**
     * Background conversion for queueing tasks
     *
     * @param path source file to compress
     * @param dest destination directory to put result
     */

    public void scheduleVideoConvert(String path, File dest) {
        startVideoConvertFromQueue(path, dest);
    }

    private void startVideoConvertFromQueue(String path, File dest) {
        VideoConvertRunnable.runConversion(path, dest);
    }

    @TargetApi(16)
    private long readAndWriteTrack(MediaExtractor extractor, MP4Builder mediaMuxer, MediaCodec.BufferInfo info, long start, long end, File file, boolean isAudio) throws Exception {
        int trackIndex = selectTrack(extractor, isAudio);
        Timber.d("readAndWriteTrack:" + "isAudio" + isAudio + "trackIndex:" + trackIndex);
        if (trackIndex >= 0) {
            extractor.selectTrack(trackIndex);
            MediaFormat trackFormat = extractor.getTrackFormat(trackIndex);
            int muxerTrackIndex = mediaMuxer.addTrack(trackFormat, isAudio);
            int maxBufferSize = trackFormat.getInteger(MediaFormat.KEY_MAX_INPUT_SIZE);
            boolean inputDone = false;
            if (start > 0) {
                extractor.seekTo(start, MediaExtractor.SEEK_TO_PREVIOUS_SYNC);
            } else {
                extractor.seekTo(0, MediaExtractor.SEEK_TO_PREVIOUS_SYNC);
            }
            ByteBuffer buffer = ByteBuffer.allocateDirect(maxBufferSize);
            long startTime = -1;

            while (!inputDone) {

                boolean eof = false;
                int index = extractor.getSampleTrackIndex();
                if (index == trackIndex) {
                    info.size = extractor.readSampleData(buffer, 0);

                    if (info.size < 0) {
                        info.size = 0;
                        eof = true;
                    } else {
                        info.presentationTimeUs = extractor.getSampleTime();
                        if (start > 0 && startTime == -1) {
                            startTime = info.presentationTimeUs;
                        }
                        if (end < 0 || info.presentationTimeUs < end) {
                            info.offset = 0;
                            info.flags = extractor.getSampleFlags();
                            if (mediaMuxer.writeSampleData(muxerTrackIndex, buffer, info, isAudio)) {
                                // didWriteData(messageObject, file, false, false);
                            }
                            extractor.advance();
                        } else {
                            eof = true;
                        }
                    }
                } else if (index == -1) {
                    eof = true;
                }
                if (eof) {
                    inputDone = true;
                }
            }

            extractor.unselectTrack(trackIndex);
            return startTime;
        }
        return -1;
    }

    @TargetApi(16)
    private int selectTrack(MediaExtractor extractor, boolean audio) {
        int numTracks = extractor.getTrackCount();
        for (int i = 0; i < numTracks; i++) {
            MediaFormat format = extractor.getTrackFormat(i);
            String mime = format.getString(MediaFormat.KEY_MIME);
            if (audio) {
                if (mime.startsWith("audio/")) {
                    return i;
                }
            } else {
                if (mime.startsWith("video/")) {
                    return i;
                }
            }
        }
        return -5;
    }

    /**
     * Perform the actual video compression. Processes the frames and does the magic
     * Width, height and bitrate are now default
     *
     * @param sourcePath the source uri for the file as per
     * @param destDir    the destination directory where compressed video is eventually saved
     * @return
     */
    public boolean convertVideo(final String sourcePath, File destDir) {
        return convertVideo(sourcePath, destDir, 0, 0, 0);
    }

    /**
     * Perform the actual video compression. Processes the frames and does the magic
     *
     * @param sourcePath the source uri for the file as per
     * @param destDir    the destination directory where compressed video is eventually saved
     * @param outWidth   the target width of the converted video, 0 is default
     * @param outHeight  the target height of the converted video, 0 is default
     * @param outBitrate the target bitrate of the converted video, 0 is default
     * @return
     */
    @TargetApi(16)
    public boolean convertVideo(final String sourcePath, File destDir, int outWidth, int outHeight, int outBitrate) {
        this.path = sourcePath;
        MediaMetadataRetriever retriever = new MediaMetadataRetriever();
        retriever.setDataSource(path);

        String width = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH);
        String height = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT);
        String rotation = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_ROTATION);
        String rate = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_BITRATE);

        long startTime = -1;
        long endTime = -1;

        int resultWidth = outWidth > 0 ? outWidth : DEFAULT_VIDEO_WIDTH;
        int resultHeight = outHeight > 0 ? outHeight : DEFAULT_VIDEO_HEIGHT;

        int rotationValue = Integer.valueOf(rotation);
        int originalWidth = Integer.valueOf(width);
        int originalHeight = Integer.valueOf(height);
        int originBitRate = Integer.valueOf(rate);
        Timber.e("MediaController" + "originalWidth:" + originalWidth + ", originalHeight: " + originalHeight);
        int bitrate = outBitrate > originBitRate ? originBitRate : outBitrate;
        Timber.d("video compress rate:" + rate + "bitrate:" + bitrate);

        File cacheFile = new File(destDir, "VIDEO_" + new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.US).format(new Date()) + ".mp4");
        if (rotationValue == 90) {
            rotationValue = 0;
        } else if (rotationValue == 180) {
            rotationValue = 0;
        } else if (rotationValue == 270) {
            rotationValue = 0;
        }
        Timber.e("MediaController" + "resultWidth:" + resultWidth + ", resultHeight: " + resultHeight);

        File inputFile = new File(path);
        if (!inputFile.canRead()) {
            didWriteData(true, true);
            return false;
        }
        videoConvertFirstWrite = true;
        boolean error = false;

        MP4Builder mediaMuxer = null;
        MediaExtractor extractor = null;

        try {
            MediaCodec.BufferInfo info = new MediaCodec.BufferInfo();
            Mp4Movie movie = new Mp4Movie();
            movie.setCacheFile(cacheFile);
            movie.setRotation(rotationValue);
            movie.setSize(resultWidth, resultHeight);
            mediaMuxer = new MP4Builder().createMovie(movie);
            extractor = new MediaExtractor();
            extractor.setDataSource(inputFile.toString());
            Timber.d("video compress resultWidth:" + resultWidth + "originalWidth:" + originalWidth + "resultHeight" + resultHeight + "originalHeight:" + originalHeight);
            int videoIndex;
            videoIndex = selectTrack(extractor, false);
            if (videoIndex >= 0) {
                MediaCodec decoder = null;
                MediaCodec encoder = null;
                InputSurface inputSurface = null;
                OutputSurface outputSurface = null;

                try {
                    boolean outputDone = false;
                    boolean inputDone = false;
                    boolean decoderDone = false;
                    int videoTrackIndex = -5;
                    int colorFormat;
                    colorFormat = MediaCodecInfo.CodecCapabilities.COLOR_FormatSurface;
                    extractor.selectTrack(videoIndex);
                    extractor.seekTo(0, MediaExtractor.SEEK_TO_PREVIOUS_SYNC);
                    MediaFormat inputFormat = extractor.getTrackFormat(videoIndex);
                    MediaFormat outputFormat = MediaFormat.createVideoFormat(MIME_TYPE, resultWidth, resultHeight);
                    outputFormat.setInteger(MediaFormat.KEY_COLOR_FORMAT, colorFormat);
                    outputFormat.setInteger(MediaFormat.KEY_BIT_RATE, bitrate != 0 ? bitrate : 921600);
                    outputFormat.setInteger(MediaFormat.KEY_FRAME_RATE, 25);
                    outputFormat.setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, 10);

                    encoder = MediaCodec.createEncoderByType(MIME_TYPE);
                    encoder.configure(outputFormat, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE);
                    inputSurface = new InputSurface(encoder.createInputSurface());
                    inputSurface.makeCurrent();
                    encoder.start();

                    decoder = MediaCodec.createDecoderByType(inputFormat.getString(MediaFormat.KEY_MIME));
                    outputSurface = new OutputSurface();
                    decoder.configure(inputFormat, outputSurface.getSurface(), null, 0);
                    decoder.start();

                    final int TIMEOUT_USEC = 2500;
                    while (!outputDone) {
                        if (!inputDone) {
                            boolean eof = false;
                            int index = extractor.getSampleTrackIndex();
                            if (index == videoIndex) {
                                int inputBufIndex = decoder.dequeueInputBuffer(TIMEOUT_USEC);
                                if (inputBufIndex >= 0) {
                                    ByteBuffer inputBuf;
                                    inputBuf = decoder.getInputBuffer(inputBufIndex);
                                    int chunkSize = extractor.readSampleData(inputBuf, 0);
                                    if (chunkSize < 0) {
                                        decoder.queueInputBuffer(inputBufIndex, 0, 0, 0L, MediaCodec.BUFFER_FLAG_END_OF_STREAM);
                                        inputDone = true;
                                    } else {
                                        decoder.queueInputBuffer(inputBufIndex, 0, chunkSize, extractor.getSampleTime(), 0);
                                        extractor.advance();
                                    }
                                }
                            } else if (index == -1) {
                                eof = true;
                            }
                            if (eof) {
                                int inputBufIndex = decoder.dequeueInputBuffer(TIMEOUT_USEC);
                                if (inputBufIndex >= 0) {
                                    decoder.queueInputBuffer(inputBufIndex, 0, 0, 0L, MediaCodec.BUFFER_FLAG_END_OF_STREAM);
                                    inputDone = true;
                                }
                            }
                        }

                        boolean decoderOutputAvailable = true;
                        while (decoderOutputAvailable) {
                            int encoderStatus = encoder.dequeueOutputBuffer(info, TIMEOUT_USEC);
                            if (encoderStatus == MediaCodec.INFO_TRY_AGAIN_LATER) {
                            } else if (encoderStatus == MediaCodec.INFO_OUTPUT_BUFFERS_CHANGED) {
                            } else if (encoderStatus == MediaCodec.INFO_OUTPUT_FORMAT_CHANGED) {
                                MediaFormat newFormat = encoder.getOutputFormat();
                                if (videoTrackIndex == -5) {
                                    videoTrackIndex = mediaMuxer.addTrack(newFormat, false);
                                }
                            } else if (encoderStatus < 0) {
                                throw new RuntimeException("unexpected result from encoder.dequeueOutputBuffer: " + encoderStatus);
                            } else {
                                ByteBuffer encodedData;
                                encodedData = encoder.getOutputBuffer(encoderStatus);
                                if (encodedData == null) {
                                    throw new RuntimeException("encoderOutputBuffer " + encoderStatus + " was null");
                                }
                                if (info.size > 1) {
                                    if ((info.flags & MediaCodec.BUFFER_FLAG_CODEC_CONFIG) == 0) {
                                        if (mediaMuxer.writeSampleData(videoTrackIndex, encodedData, info, false)) {
                                            didWriteData(false, false);
                                        }
                                    } else if (videoTrackIndex == -5) {
                                        byte[] csd = new byte[info.size];
                                        encodedData.limit(info.offset + info.size);
                                        encodedData.position(info.offset);
                                        encodedData.get(csd);
                                        ByteBuffer sps = null;
                                        ByteBuffer pps = null;
                                        for (int a = info.size - 1; a >= 0; a--) {
                                            if (a > 3) {
                                                if (csd[a] == 1 && csd[a - 1] == 0 && csd[a - 2] == 0 && csd[a - 3] == 0) {
                                                    sps = ByteBuffer.allocate(a - 3);
                                                    pps = ByteBuffer.allocate(info.size - (a - 3));
                                                    sps.put(csd, 0, a - 3).position(0);
                                                    pps.put(csd, a - 3, info.size - (a - 3)).position(0);
                                                    break;
                                                }
                                            } else {
                                                break;
                                            }
                                        }

                                        MediaFormat newFormat = MediaFormat.createVideoFormat(MIME_TYPE, resultWidth, resultHeight);
                                        if (sps != null && pps != null) {
                                            newFormat.setByteBuffer("csd-0", sps);
                                            newFormat.setByteBuffer("csd-1", pps);
                                        }
                                        videoTrackIndex = mediaMuxer.addTrack(newFormat, false);
                                    }
                                }
                                outputDone = (info.flags & MediaCodec.BUFFER_FLAG_END_OF_STREAM) != 0;
                                encoder.releaseOutputBuffer(encoderStatus, false);
                            }
                            if (encoderStatus != MediaCodec.INFO_TRY_AGAIN_LATER) {
                                continue;
                            }

                            int decoderStatus = decoder.dequeueOutputBuffer(info, TIMEOUT_USEC);
                            if (decoderStatus == MediaCodec.INFO_TRY_AGAIN_LATER) {
                                decoderOutputAvailable = false;
                            } else if (decoderStatus == MediaCodec.INFO_OUTPUT_BUFFERS_CHANGED) {

                            } else if (decoderStatus == MediaCodec.INFO_OUTPUT_FORMAT_CHANGED) {
                                MediaFormat newFormat = decoder.getOutputFormat();
                                Timber.e("tmessages" + "newFormat = " + newFormat);
                            } else if (decoderStatus < 0) {
                                throw new RuntimeException("unexpected result from decoder.dequeueOutputBuffer: " + decoderStatus);
                            } else {
                                boolean doRender;
                                doRender = info.size != 0;
                                decoder.releaseOutputBuffer(decoderStatus, doRender);
                                if (doRender) {
                                    boolean errorWait = false;
                                    try {
                                        outputSurface.awaitNewImage();
                                    } catch (Exception e) {
                                        errorWait = true;
                                        e.printStackTrace();
                                    }
                                    if (!errorWait) {
                                        outputSurface.drawImage(false);
                                        inputSurface.setPresentationTime(info.presentationTimeUs * 1000);
                                        inputSurface.swapBuffers();
                                    }
                                }
                                if ((info.flags & MediaCodec.BUFFER_FLAG_END_OF_STREAM) != 0) {
                                    decoderOutputAvailable = false;
                                    encoder.signalEndOfInputStream();
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    error = true;
                }

                extractor.unselectTrack(videoIndex);

                if (outputSurface != null) {
                    outputSurface.release();
                }
                if (inputSurface != null) {
                    inputSurface.release();
                }
                if (decoder != null) {
                    decoder.stop();
                    decoder.release();
                }
                if (encoder != null) {
                    encoder.stop();
                    encoder.release();
                }
            }
            if (!error) {
                readAndWriteTrack(extractor, mediaMuxer, info, startTime, endTime, cacheFile, true);
            }
        } catch (Exception e) {
            error = true;
        } finally {
            if (extractor != null) {
                extractor.release();
            }
            if (mediaMuxer != null) {
                try {
                    mediaMuxer.finishMovie(false);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        didWriteData(true, error);
        cachedFile = cacheFile;
        return true;
    }

}