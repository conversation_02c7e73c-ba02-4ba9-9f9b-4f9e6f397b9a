package com.socialplay.gpark.function.ipc.provider.account

import com.socialplay.gpark.data.interactor.AccountInteractor
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

/**
 * Created by bo.li
 * Date: 2023/11/6
 * Desc:
 */
class UserAccountProvider: IUserAccountProvider, KoinComponent {

    private val accountInteractor by inject<AccountInteractor>()
    override fun refreshAccessToken(currentAccessToken: String?): String? {
        return accountInteractor.refreshAccessToken(currentAccessToken)
    }
}