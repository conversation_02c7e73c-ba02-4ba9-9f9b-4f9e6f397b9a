package com.socialplay.gpark.function.analytics.handle

import android.app.Activity
import android.app.Application
import android.os.Build
import android.os.Bundle
import android.os.SystemClock
import android.util.Log
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.apm.page.IPageMonitor
import com.socialplay.gpark.ui.main.MainActivity
import com.socialplay.gpark.util.PackageUtil

/**
 * Created by bo.li
 * Date: 2021/9/14
 * Desc: app启动相关埋点
 */
object AppLaunchAnalytics {


    private var appAttachBefore = -1L
    private var appAttachAfter = 0L
    private var appCreateAfter = 0L
    private var appCreateBefore = 0L
    var mainStartTime = 0L
    var hasPaused = false

    private var isSend = false
    private var isFirstStartMainActivity: Boolean? = null
    private var isFirstStartMainActivityFromSaveState: Boolean = false
    private var isFirstStartMainFragment: Boolean? = null
    private var application: Application? = null

    private val callback = object : Application.ActivityLifecycleCallbacks {
        override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
            if (isFirstStartMainActivity == null) {
                isFirstStartMainActivity = activity is MainActivity
                if (isFirstStartMainActivity == true) {
                    isFirstStartMainActivityFromSaveState = savedInstanceState != null
                }
            }
        }

        override fun onActivityStarted(activity: Activity) {
        }

        override fun onActivityResumed(activity: Activity) {
        }

        override fun onActivityPaused(activity: Activity) {
        }

        override fun onActivityStopped(activity: Activity) {
        }

        override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
        }

        override fun onActivityDestroyed(activity: Activity) {
        }
    }

    fun init(application: Application) {
        this.application = application
        application.registerActivityLifecycleCallbacks(callback)
    }

    fun handleFragmentCreated(pageMonitor: IPageMonitor) {
        if (isSend) {
            return
        }
        if (isFirstStartMainFragment == null) {
            isFirstStartMainFragment = pageMonitor.isFirstPage()
        }
    }

    fun handleAppOnAttachBefore() {
        Log.v("AppLaunchAnalytics","handleAppOnAttachBefore")
        appAttachBefore = SystemClock.elapsedRealtime()
    }

    fun handleAppOnAttachAfter() {
        Log.v("AppLaunchAnalytics","handleAppOnAttachAfter")
        appAttachAfter = SystemClock.elapsedRealtime()
    }

    fun handleAppOnCreateBefore() {
        Log.v("AppLaunchAnalytics","handleAppOnCreateBefore")
        appCreateBefore = SystemClock.elapsedRealtime()
    }

    fun handleAppOnCreateAfter() {
        Log.v("AppLaunchAnalytics","handleAppOnCreateAfter")
        appCreateAfter = SystemClock.elapsedRealtime()
    }

    /**
     * 计算并发送启动APP时间埋点
     * 发送条件：
     * 1、启动过程没有onPause
     * 2、SDK >= 22（Android 5.1.1）
     * 3、从安装界面/桌面启动
     * 4、启动时间 > 0
     */
    fun handleAppBootTime(activity: Activity) {
        if (hasPaused || appAttachBefore < 0) {
            release()
            return
        }
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP_MR1) {
            release()
            return
        }
        if (isFirstStartMainActivity != true || isFirstStartMainActivityFromSaveState || isFirstStartMainFragment != true) {
            release()
            return
        }
        val referrerString = activity.referrer?.toString()
        if (referrerString.isNullOrEmpty() || !PackageUtil.isLauncher(referrerString, activity)) {
            release()
            return
        }
        val currentTimeMillis = SystemClock.elapsedRealtime()
        val mainTime = currentTimeMillis - mainStartTime
        val bootCostTime = currentTimeMillis - appAttachBefore
        val appInitTime = appCreateAfter - appAttachBefore
        val appAttachTime = appAttachAfter - appAttachBefore
        val appCreateTime = appCreateAfter - appCreateBefore
        Analytics.track(EventConstants.EVENT_APP_BOOT_COST_TIME) {
            put("bootCostTime", bootCostTime)
            put("splashBootCostTime", 0)
            put("mainBootCostTime", mainTime)
            put("app_init_time", appInitTime)
            put("app_attach_time", appAttachTime)
            put("app_create_time", appCreateTime)
            if (bootCostTime <= (appAttachTime + appCreateTime + appInitTime + mainTime) * 1.5f) {
                put("bootcosttimev2", bootCostTime)
            }
        }
        release()
    }

    private fun release() {
        isSend = true
        appAttachBefore = -1L
        application?.unregisterActivityLifecycleCallbacks(callback)
    }

}