package com.socialplay.gpark.function.intermodal.base;

import android.app.Activity;
import android.content.Context;
import android.graphics.PixelFormat;
import android.os.Build;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;

import com.socialplay.gpark.util.ScreenUtil;

import timber.log.Timber;

/**
 * <pre>
 *     <AUTHOR> yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2021/03/02
 *     desc   :游戏内的View展示前view的宽高设置工具类
 * </pre>
 */
public final class GameViewShowHelper {

    private static final String TAG = "MOD_PAY";
    //游戏里view的最大宽高
    public static final float MAX = 403F;
    // 默认类型
    public static final int VIEW_DEFAULT_PAGE = -1;
    //mgs展开高度
    public static final int VIEW_MGS_MESSAGE_EXPAND = 2;

    private GameViewShowHelper() {
    }

    public static GameViewShowHelper getInstance() {
        return SingletonHolder.instance;
    }

    private static class SingletonHolder {
        private static final GameViewShowHelper instance = new GameViewShowHelper();
    }

    public boolean displayView(Context context, Activity activity, View view, int type) {
        Timber.d("%s displayView", TAG);
        if (activity == null || view == null) {
            return false;
        }
        boolean isHorizontal = ScreenUtil.INSTANCE.isHorizontalScreen(activity);
        WindowManager.LayoutParams layoutParams = getWindowLayoutParams(activity, context, type, isHorizontal);
        int flag =  View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION;

        view.setSystemUiVisibility(flag);
        return setLayoutParams(activity, view, layoutParams);
    }

    public boolean displayView(Context context, Activity activity, BaseGamePage gamePage) {
        Timber.d("%s displayView", TAG);
        if (activity == null || gamePage == null || gamePage.getCurrentView() == null) {
            return false;
        }
        boolean isHorizontal = ScreenUtil.INSTANCE.isHorizontalScreen(activity);
        WindowManager.LayoutParams layoutParams = getWindowLayoutParams(activity, gamePage);
        return setLayoutParams(activity, gamePage.getCurrentView(), layoutParams);
    }

    public WindowManager.LayoutParams getWindowLayoutParams(Activity gameActivity, Context context, int type, boolean isHorizontal) {
        WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams();
        switch (type) {
            case VIEW_DEFAULT_PAGE:
                layoutParams.y = 0;
                layoutParams.gravity = Gravity.CENTER_VERTICAL;
                layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT;
                layoutParams.height = WindowManager.LayoutParams.MATCH_PARENT;
                layoutParams.flags = WindowManager.LayoutParams.FLAG_FULLSCREEN;
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    layoutParams.layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_ALWAYS;
                }
                break;
            case VIEW_MGS_MESSAGE_EXPAND:
                layoutParams.y = 0;
                layoutParams.x = 0;
                layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT;
                layoutParams.height = WindowManager.LayoutParams.MATCH_PARENT;
                layoutParams.flags = WindowManager.LayoutParams.FLAG_FULLSCREEN;
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P && gameActivity != null) {
                    layoutParams.layoutInDisplayCutoutMode = gameActivity.getWindow().getAttributes().layoutInDisplayCutoutMode;
                }
                break;
            default:
                break;
        }
        layoutParams.format = PixelFormat.RGBA_8888;
        return layoutParams;
    }

    public WindowManager.LayoutParams getWindowLayoutParams(Activity gameActivity, BaseGamePage gamePage) {
        WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams();
        layoutParams.x = gamePage.getX();
        layoutParams.y = gamePage.getY();
        layoutParams.gravity = gamePage.getGravity();
        layoutParams.width = gamePage.getWidth();
        layoutParams.height = gamePage.getHeight();
        layoutParams.flags = WindowManager.LayoutParams.FLAG_FULLSCREEN;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P && gameActivity != null) {
            layoutParams.layoutInDisplayCutoutMode = gameActivity.getWindow().getAttributes().layoutInDisplayCutoutMode;
        }
        layoutParams.format = PixelFormat.RGBA_8888;
        return layoutParams;
    }

    private Boolean setLayoutParams(Activity activity, View view, WindowManager.LayoutParams layoutParams) {
        try {
            Timber.d("%s displayView%s", TAG, activity);
            if (view.isAttachedToWindow()) {
                activity.getWindowManager().updateViewLayout(view, layoutParams);
            } else {
                activity.getWindowManager().addView(view, layoutParams);
            }
            return true;
        } catch (Throwable e) {
            Timber.e("%s displayView %s", TAG, e);
        }
        return false;
    }

    public boolean dismissView(Activity activity, View view) {
        Timber.d("%s dismissView%s", TAG, activity);
        if (activity == null || view == null) {
            return false;
        }
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                if (view.isAttachedToWindow()) {
                    activity.getWindowManager().removeViewImmediate(view);
                    return true;
                }
            } else {
                activity.getWindowManager().removeViewImmediate(view);
                return true;
            }
        } catch (Throwable e) {
            //            Timber.d(TAG, "dismissView", e);
        }
        return false;
    }

}
