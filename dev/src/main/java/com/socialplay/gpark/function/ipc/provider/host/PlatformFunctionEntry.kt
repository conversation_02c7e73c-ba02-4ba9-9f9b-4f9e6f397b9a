package com.socialplay.gpark.function.ipc.provider.host

import android.app.Application
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.function.ad.AdProxy
import com.socialplay.gpark.function.ad.AdToggleCtrl
import com.socialplay.gpark.function.ipc.ReceivedActionRegistry
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.ad.AdType
import timber.log.Timber

/**
 * 平台函数调用入口
 * 该接口仅在主进程中注册，其他进程通过IPC调用回来
 */
class PlatformFunctionEntry(private val application: Application) : IPlatformFunctionEntry {

    /**
     * TS调用入口
     */
    override fun call(
        processName: String,
        action: String,
        gameId: String,
        gamePackage: String,
        data: Map<String, Any>
    ) {
        Timber.d("action call: $processName, $action, $data, gameId:$gameId, gamePackage: $gamePackage")
        val callbackAction = data["action"] as? String ?: ""
        val rewardedAction = data["action1"] as? String ?: ""
        Timber.d("callbackAction: $callbackAction")
        when (action) {
            ReceivedActionRegistry.AD_IS_ACTIVATED.value -> { // 获取乐园信息
                val isActivated = when (data["adType"] as? Int ?: -1) {
                    AdType.REWARDED -> {
                        AdToggleCtrl.isActivatedRewardedAd(gameId, gamePackage)
                    }

                    else -> {
                        false
                    }
                }
                val param = linkedMapOf(
                    "isActivated" to isActivated
                )
                AdProxy.callAction(processName, callbackAction, param)
                Timber.d("result: $param")
            }

            ReceivedActionRegistry.AD_GET_META_APP_INFO.value -> { // 获取乐园信息
                val param = linkedMapOf(
                    "meta_version_code" to BuildConfig.VERSION_CODE,
                    "app_pkg_name" to application.packageName
                )
                AdProxy.callAction(processName, callbackAction, param)
                Timber.d("result: $param")
            }

            ReceivedActionRegistry.AD_IS_REWARDED_AD_READY.value -> { //激励广告是否准备好
                val param = linkedMapOf(
                    "isReady" to AdProxy.isRewardedAdReady(gameId, gamePackage)
                )
                AdProxy.callAction(processName, callbackAction, param)
                Timber.d("isRewardedAdReady result: $param")
            }

            ReceivedActionRegistry.AD_SHOW_REWARDED_AD.value -> { //显示激励广告
                val result = AdProxy.startShowRewardedAdResult(
                    application.applicationContext,
                    processName,
                    callbackAction,
                    rewardedAction,
                    gameId,
                    gamePackage,
                    data
                )
                if (!result) {
                    AdProxy.doAdFailFake(processName, callbackAction)
                }
                Timber.d("showRewardedAd result: $result")
            }
        }
    }

    override fun canPreloadAd(gameId: String, gamePkg: String): Boolean {
        return AdToggleCtrl.isActivatedRewardedAd(gameId, gamePkg)
                && !AdProxy.isLaunchFromDev(gameId)
                && PandoraToggle.adShowAmount > 0
                && AdToggleCtrl.isWithinAdCountLimit
    }


}