package com.socialplay.gpark.function.ipc

import android.app.Application
import com.socialplay.gpark.function.ipc.provider.ts.IInGameIntentStarter
import com.socialplay.gpark.function.ipc.provider.host.IPlatformFunctionEntry
import com.socialplay.gpark.function.ipc.provider.ts.ITSFunctionEntry
import com.socialplay.gpark.function.ipc.provider.host.PlatformFunctionEntry
import com.socialplay.gpark.function.ipc.provider.ts.TSFunctionEntry
import com.socialplay.gpark.function.startup.StartupProcessType
import com.socialplay.gpark.function.startup.core.ProcessType
import com.socialplay.gpark.function.mw.lifecycle.InGameLifecycle
import com.socialplay.gpark.util.extension.get
import com.socialplay.gpark.util.extension.provide
import com.meta.ipc.IPC
import com.meta.lib.mwbiz.MWBizBridge
import com.socialplay.gpark.function.ipc.provider.account.IUserAccountProvider
import com.socialplay.gpark.function.ipc.provider.account.UserAccountProvider
import com.socialplay.gpark.function.ipc.provider.host.MWIPCFuncProviderImpl
import com.socialplay.gpark.function.ipc.provider.host.MW_FUNC_PROVIDER_NAME
import timber.log.Timber

/**
 *     author : wei.zhu
 *     e-mail : <EMAIL>
 *     time   : 2022/04/20
 *     desc   :
 */
object IPCFunctionRegister {
    val ipc = IPC.getInstance()

    fun register(app: Application, processType: ProcessType) {
        when (processType) {
            StartupProcessType.H -> {
                ipc.provide(MW_FUNC_PROVIDER_NAME, MWIPCFuncProviderImpl())
                ipc.provide(IPlatformFunctionEntry, PlatformFunctionEntry(app))
                ipc.provide(IUserAccountProvider, UserAccountProvider())

            }
            StartupProcessType.M -> {
                ipc.provide(ITSFunctionEntry, TSFunctionEntry(app))
                ipc.provide(IInGameIntentStarter, InGameLifecycle)

                forwardMWMessageToIPCChannel()
            }
        }
    }

    /**
     * 通过IPC通道转发MW的消息
     */
    private fun forwardMWMessageToIPCChannel() {
        ReceivedActionRegistry.entries.forEach {
            Timber.d("Register MWMessage. action: ${it.value}")
            MWBizBridge.registerMWMsgAction(it.value, "MessageChannel")
        }
        MWBizBridge.addOnMWMsgCallback {
            Timber.d("MWBizBridge Received mw message. action: ${it.action}, ${MWBizBridge.currentGameId()}, ${MWBizBridge.currentGamePkg()} ,data: ${it.data}")
            kotlin.runCatching {
                ipc.get(IPlatformFunctionEntry)
                    .call(StartupProcessType.M.desc, it.action, MWBizBridge.currentGameId(), MWBizBridge.currentGamePkg(), it.data)
            }.getOrElse { ex ->
                Timber.w(ex, "MWBizBridge Failed to dispatch mw message via ipc channel.")
            }
        }
    }
}