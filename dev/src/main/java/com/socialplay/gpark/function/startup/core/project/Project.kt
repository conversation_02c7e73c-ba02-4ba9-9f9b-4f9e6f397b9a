package com.socialplay.gpark.function.startup.core.project

import android.app.Application
import com.socialplay.gpark.function.startup.core.ProcessType
import com.socialplay.gpark.function.startup.core.Startup
import com.socialplay.gpark.function.startup.core.task.Task
import com.socialplay.gpark.function.startup.dsl.ProjectLauncher
import kotlinx.coroutines.*
import timber.log.Timber
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.coroutines.CoroutineContext

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/06/11
 * desc   :
 * </pre>
 */


class Project(val name: String) : CoroutineScope {

    private lateinit var launcher: ProjectLauncher
    lateinit var startup: Startup

    val application: Application
        get() = startup.application

    val processName: String
        get() = startup.processName

    val processType: ProcessType
        get() = startup.processType

    override val coroutineContext: CoroutineContext
        get() = Dispatchers.IO

    val isMainProcess: Boolean
        get() = processName == application.packageName

    private lateinit var launcherDeferred: Deferred<Any>
    private val isStarted = AtomicBoolean(false)

    fun setLauncher(launcher: ProjectLauncher) {
        this.launcher = {
            onProjectStart()
            val startTime = System.currentTimeMillis()
            launcher.invoke(this)
            launcherDeferred = async(this.coroutineContext) {}
            val cost = System.currentTimeMillis() - startTime
            onProjectFinish(cost)
        }
    }

    suspend fun start() {
        if (!isStarted.getAndSet(true)) {
            launcher.invoke(this)
        }
    }

    fun startRunBlocking() {
        runBlocking {
            start()
        }
    }

    fun matchProcess(type: ProcessType): Boolean {
        return startup.processType == type || type.set.contains(startup.processType)
    }

    private fun onProjectStart() {
        Timber.tag(Startup.TAG).d("project:%s, process:%s onProjectStart threadName:%s", name, processType, curThreadName())
    }

    private fun onProjectFinish(cost: Long) {
        Timber.tag(Startup.TAG).d("project:%s, process:%s onProjectFinish cost:%s, threadName:%s", name, processType, cost, curThreadName())
    }

    fun onTaskStart(task: Task) {
        Timber.tag(Startup.TAG).d("project:%s, process:%s onTaskStart task:%s, type:%s, threadName:%s", name, processType, task.name, task.type, curThreadName())
    }

    fun onTaskFinished(task: Task, cost: Long) {
        Timber.tag(Startup.TAG).d("project:%s, process:%s onTaskFinished task:%s, type:%s, cost:%s, threadName:%s", name, processType, task.name, task.type, cost, curThreadName())
    }

    private fun curThreadName() = Thread.currentThread().name

    suspend fun await() {
        if (!isStarted.get()) {
            error("project not start $name")
        }
        if (!launcherDeferred.isCompleted) {
            launcherDeferred.await()
        }
    }
}