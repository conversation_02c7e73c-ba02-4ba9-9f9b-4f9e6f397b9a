package com.socialplay.gpark.function.deeplink.linkhandler

import com.socialplay.gpark.data.interactor.FloatNoticeInteractor
import com.socialplay.gpark.data.model.mgs.MgsShareContent
import com.socialplay.gpark.function.deeplink.DispatchMgsShare
import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.deeplink.LinkHandleResult
import com.socialplay.gpark.function.deeplink.LinkHandler
import com.socialplay.gpark.function.deeplink.LinkHandlerChain
import org.koin.core.context.GlobalContext

/**
 * @author: bo.li
 * @date: 2022-8-31 11:00
 * @desc: 添加好友邀请
 */
class FriendShareLinkHandler : LinkHandler {
    override fun handle(chain: LinkHandlerChain, data: LinkData): LinkHandleResult {
        val shareId: String? = data.uri.getQueryParameter("share_id")
        val type: String? = data.uri.getQueryParameter("type")
        if (shareId.isNullOrEmpty()) {
            return chain.handle(data)
        }
        return when (type) {
            MgsShareContent.TYPE_FRIEND_SHARE -> {
                DispatchMgsShare.setProcessedShareIdByDeepLink(shareId)
                GlobalContext.get().get<FloatNoticeInteractor>().showFloatNotice(GlobalContext.get().get(), data.activity, data.navHost, shareId, type)
                LinkHandleResult.Success
            }

            else -> {
                LinkHandleResult.Failed("no type matched")
            }
        }
    }
}