package com.socialplay.gpark.function.share.screenshot

import android.app.Activity
import android.content.Context
import android.database.ContentObserver
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.provider.MediaStore
import com.meta.lib.mwbiz.MWBizBridge
import com.socialplay.gpark.data.interactor.LifecycleInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.share.ShareRawData
import com.socialplay.gpark.function.editor.EditorGameInteractHelper
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.permission.Permission
import com.socialplay.gpark.ui.permission.PermissionRequest
import com.socialplay.gpark.ui.share.GlobalShareDialog
import com.socialplay.gpark.ui.share.ScreenshotTipsDialog
import com.socialplay.gpark.util.LruSet
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.extension.fragmentManagerForDialog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.koin.core.context.GlobalContext
import java.io.File

/**
 * Created by Debin Kong on 2024/3/25
 * <AUTHOR> Kong
 */
class ScreenshotMonitor : CoroutineScope by CoroutineScope(Dispatchers.IO) {

    companion object {

        const val CLS_MAIN = "com.socialplay.gpark.ui.main.MainActivity"
        const val CLS_ROLE = "com.socialplay.gpark.ui.editor.tab.FullScreenEditorActivity"
        const val CLS_GAME = "com.epicgames.ue4.GameActivity"
        const val CLS_P_GAME = "com.epicgames.ue4.PortraitGameActivity"

        val screenshotWhitelist = setOf(
            CLS_MAIN,
            CLS_ROLE,
            CLS_GAME,
            CLS_P_GAME
        )

        var inRoleTabFull = false

        private val monitor: ScreenshotMonitor by lazy { ScreenshotMonitor() }

        /**
         * ### INSTANCE
         * @return [ScreenshotMonitor]
         */
        val instance get() = monitor

        fun inScreenshotWhitelist(activity: Activity?): Boolean {
            activity ?: return false
            return screenshotWhitelist.contains(activity.componentName.className)
        }
    }

    private var isHasScreenshotListen = false

    private var internalObserver: MediaContentObserver? = null

    private var externalObserver: MediaContentObserver? = null

    private val uiHandler = Handler(Looper.getMainLooper())

    private var startListenTime: Long = 0

    @Volatile
    private var hasPermission = false
    private var enabled = false

    private var inited = false

    var isSharing = false

    private val screenshotListener by lazy {
        object : IScreenshotListener {
            private val images: LruSet<String> = LruSet(10)

            override fun onShot(imagePath: String?) {
                if (!PandoraToggle.enableShareScreenshot
                    || isSharing
                    || imagePath.isNullOrBlank()
                    || images.contains(imagePath)
                    || !File(imagePath).exists()
                ) return
                val activity = LifecycleInteractor.fragmentActivity ?: return
                images.add(imagePath)
                val className = activity.componentName?.className
                val inNormalGame = className == CLS_GAME || className == CLS_P_GAME
                val inRoleGame = className == CLS_ROLE || (className == CLS_MAIN && inRoleTabFull)
                val isHor = ScreenUtil.isHorizontalScreen(activity)
                val gameId = if (inNormalGame) {
                    MWBizBridge.currentGameId()
                } else if (inRoleGame) {
                    EditorGameInteractHelper.getRoleGameId()
                } else {
                    null
                }
                val fm = if (className == CLS_MAIN) {
                    activity.fragmentManagerForDialog
                } else {
                    activity.supportFragmentManager
                }
                val rawData = ShareRawData.screenshot(imagePath, gameId)
                val isInGame = inNormalGame || inRoleGame
                if (isHor) {
                    ScreenshotTipsDialog.show(fm, rawData, isPortrait = false, isInGame = isInGame)
                } else {
                    GlobalShareDialog.show(fm, rawData, isPortrait = true, isInGame = isInGame)
                }
            }
        }
    }

    fun update(enable: Boolean = this.enabled, hasPermission: Boolean = this.hasPermission) {
        this.enabled = enable
        this.hasPermission = hasPermission
        val activity = LifecycleInteractor.activity ?: return
        if (enable && hasPermission && inScreenshotWhitelist(activity)) {
            startListen(activity)
        } else {
            stopListen(activity)
        }
    }

    fun checkIsReadStoragePermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            PermissionRequest.checkSelfPermission(
                context,
                *Permission.LOCAL_IMAGE.permissions
            )
        } else {
            PermissionRequest.checkSelfPermission(
                context,
                *Permission.READ_STORAGE.permissions
            )
        }
    }

    private fun initScreenshotToggleState(activity: Activity) {
        if (!inited) {
            inited = true
            val metaKV: MetaKV = GlobalContext.get().get()
            hasPermission = checkIsReadStoragePermission(activity)
            if (PandoraToggle.enableShareScreenshot && !hasPermission) {
                metaKV.appKV.iShowScreenshotSettingRedHot = !metaKV.appKV.iShowScreenshotSettingRedHotClicked
                metaKV.appKV.iShowScreenshotPrivacyRedHot = !metaKV.appKV.iShowScreenshotPrivacyRedHotClicked
            }
            // 没有主动关闭过 有权限就默认开启
            if (!metaKV.appKV.userCloseScreenshotShare && hasPermission) {
                metaKV.appKV.enableScreenshotShare = hasPermission
            }
            enabled = metaKV.appKV.enableScreenshotShare
        }
    }

    fun startListen(activity: Activity) {
        initScreenshotToggleState(activity)
        if (!enabled || !hasPermission) return

        if (!isHasScreenshotListen) {
            try {
                launch {
                    registerObserver(activity)
                }
            } catch (exception: Exception) {
                exception.printStackTrace()
            }
        }
    }

    fun stopListen(activity: Activity) {
        if (isHasScreenshotListen) {
            launch {
                unregisterObserver(activity)
            }
        }
    }

    private fun registerObserver(activity: Activity) {
        internalObserver =
            MediaContentObserver(MediaStore.Images.Media.INTERNAL_CONTENT_URI, uiHandler)
        externalObserver =
            MediaContentObserver(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, uiHandler)
        startListenTime = System.currentTimeMillis()
        internalObserver?.let {
            activity.applicationContext.contentResolver.registerContentObserver(
                MediaStore.Images.Media.INTERNAL_CONTENT_URI,
                Build.VERSION.SDK_INT > Build.VERSION_CODES.P, it,
            )
        }
        externalObserver?.let {
            activity.applicationContext.contentResolver.registerContentObserver(
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                Build.VERSION.SDK_INT > Build.VERSION_CODES.P, it,
            )
        }
        isHasScreenshotListen = true
        ScreenshotEventDispatcher.setListener(screenshotListener)
    }

    private fun unregisterObserver(activity: Activity) {
        try {
            internalObserver?.let {
                activity.contentResolver.unregisterContentObserver(it)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        internalObserver = null

        try {
            externalObserver?.let {
                activity.contentResolver.unregisterContentObserver(it)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        externalObserver = null

        isHasScreenshotListen = false
        startListenTime = 0
        ScreenshotEventDispatcher.unsetListener()
    }

    private inner class MediaContentObserver(
        private val contentUri: Uri,
        handler: Handler?
    ) : ContentObserver(handler) {

        override fun onChange(selfChange: Boolean) {
            super.onChange(selfChange)
            if (!PandoraToggle.enableShareScreenshot || isSharing) return
            launch {
                ScreenshotEventDispatcher.handleMediaContentChange(
                    contentUri,
                    LifecycleInteractor.activity,
                    startListenTime
                )
            }
        }
    }
}