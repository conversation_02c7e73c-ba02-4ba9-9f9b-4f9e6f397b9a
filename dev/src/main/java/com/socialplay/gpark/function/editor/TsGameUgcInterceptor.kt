package com.socialplay.gpark.function.editor

import android.os.Bundle
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.model.ResourceInfo
import com.socialplay.gpark.data.model.entity.GameDetailEntity
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.interceptor.IPlayGameInterceptor
import com.socialplay.gpark.function.interceptor.InterceptorController
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

class TsGameUgcInterceptor : IPlayGameInterceptor,
    KoinComponent {

    private val metaRepository: IMetaRepository by inject()

    override suspend fun onIntercept(pkgName: String, gameId: String, bundle: Bundle, resIdBean: ResIdBean?): Pair<Boolean, Bundle> {
        val gameName = bundle.getString(InterceptorController.KEY_GAME_NAME)
        val isUgc = bundle.getBoolean(InterceptorController.KEY_IS_UGC)
        if (isUgc) {
            val gameInfo = GameDetailEntity(
                id = gameId,
                name = gameName ?: "",
                icon = "",
                description = "",
                type = 4,
                startupExtension = "",
                images = null,
                videos = null,
                author = null,
                sns = null,
                resource = ResourceInfo(null, 0, 0, "", pkgName, 2, 2, null),
                gameTags = listOf(1),
                shareCount = 0,
                hasCommunity = false,
                createTime = 0,
                releaseTime = 0,
                updateDescription = null,
                labelInfo = null,
                extend = null
            )
            metaRepository.insertGameDetailCache(gameInfo)
        }
        return false to bundle
    }
}