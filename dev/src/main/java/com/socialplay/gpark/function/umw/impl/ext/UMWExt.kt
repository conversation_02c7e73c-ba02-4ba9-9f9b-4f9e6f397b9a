package com.socialplay.gpark.function.umw.impl.ext

import com.meta.biz.ugc.model.IMWMsg
import com.meta.biz.ugc.protocol.listener.SingleProtocolListener
import com.socialplay.gpark.function.umw.GameType
import com.socialplay.gpark.function.umw.UMW


/**
 * 增加协议回调监听
 * T必须继承IMWMsg
 */
@Throws
inline fun <reified T : IMWMsg> UMW.addProtocolObserver(listener: SingleProtocolListener<T>) {
    this.addProtocolObserver(T::class.java, listener)
}

val UMW.isAvatarGame: Boolean get() = this.getGameType() == GameType.Avatar

val UMW.isTsGame: Boolean get() = this.getGameType() == GameType.TS
