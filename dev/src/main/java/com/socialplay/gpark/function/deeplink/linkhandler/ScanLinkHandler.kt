package com.socialplay.gpark.function.deeplink.linkhandler

import android.os.Bundle
import androidx.fragment.app.FragmentActivity
import com.socialplay.gpark.data.model.mgs.MgsScanQrLaunchGameParameter
import com.socialplay.gpark.data.model.qrcode.ScanEntry
import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.deeplink.LinkHandleResult
import com.socialplay.gpark.function.deeplink.LinkHandler
import com.socialplay.gpark.function.deeplink.LinkHandlerChain
import com.socialplay.gpark.function.deeplink.MetaDeepLink
import com.socialplay.gpark.function.qrcode.LaunchGameHandler
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.qrcode.QRCodeScanFragment

/**
 * 处理跳转扫一扫
 */
class ScanLinkHandler : LinkHandler {

    override fun handle(chain: LinkHandlerChain, data: LinkData): LinkHandleResult {
        return handleQrCode(data)
    }

    private fun handleQrCode(data: LinkData): LinkHandleResult {
        val dataString = data.uri.getQueryParameter("data") ?: return LinkHandleResult.Failed("no data")
        val arguments = BasicNavigateLinkHandler.decodeBundle(dataString)

        val gamePkgName = arguments.getString(MetaDeepLink.PARAM_FROM_GAME_PACKAGE_NAME)
        val gameId = arguments.getString(MetaDeepLink.PARAM_FROM_GAME_ID)
        val gameType = arguments.getString(MetaDeepLink.PARAM_FROM_GAME_TYPE)
        val navFragment = data.navHost
        val activity = kotlin.runCatching { data.activity as FragmentActivity }.getOrNull() ?: return LinkHandleResult.Failed("no activity")

        var customData: Bundle = Bundle.EMPTY
        if(gamePkgName != null && gameId != null && gameType != null){
            val parameter = MgsScanQrLaunchGameParameter(gameId, gamePkgName, gameType)
            customData = Bundle().apply {
                putParcelable(LaunchGameHandler.KEY_LAUNCH_GAME_PARAMETER, parameter)
            }
        }

        MetaRouter.IM.goQRCodeScan(activity, navFragment, QRCodeScanFragment.KEY_REQUEST_KEY_GAME_TO_QR_CODE, ScanEntry.Unknown, customData = customData,gamePkgName, gameId, gameType)
        return LinkHandleResult.Success
    }
}