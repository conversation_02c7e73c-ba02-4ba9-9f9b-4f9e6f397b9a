package com.socialplay.gpark.function.mw.feature

import android.app.Application
import com.bin.cpbus.CpEventBus
import com.meta.biz.ugc.model.GameCommonFeature
import com.meta.biz.ugc.model.MWGameWebViewResultMsg
import com.meta.biz.ugc.protocol.UGCProtocolSender
import com.meta.biz.ugc.protocol.constants.ProtocolSendConstant
import com.meta.web.preload.WebCoreCache
import com.socialplay.gpark.data.model.event.MWTransWebEvent
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.web.WebViewCache
import timber.log.Timber
import java.util.concurrent.atomic.AtomicReference

object GameWebViewFeature {

    private val loaderConfig = AtomicReference("" to "")

    fun init(app: Application) {
        WebViewCache.init(app)
    }

//    fun onShowWebView(request: GameCommonFeature, messageId: Int, isHost: <PERSON>olean) {
//        val url = request.params?.get("url")?.toString() ?: ""
//        loaderConfig.set("${System.currentTimeMillis()}" to url)
//        val orientation = when ((request.params?.get("orientation") as? String) ?: "h") {
//            "h" -> ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
//            else -> ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
//        }
//        val gameId =
//            request.gameId.takeIf { it.isNotEmpty() } ?: MetaVerseCore.bridge().currentGameId()
//        CpEventBus.post(InGameOpenWebEvent(url, gameId, messageId, orientation, isHost))
//
//    }

    fun onPreLoadWebView(request: GameCommonFeature) {
        val url = request.params?.get("url") as? String
        val preUnique = request.params?.get("preUnique") as? String
        Timber.d("WebView.Preload:$preUnique $url")
        if (preUnique.isNullOrEmpty()) return
        if (PandoraToggle.isOpenBabelWeb) {
            WebCoreCache.preload(preUnique, url)
        } else {
            WebViewCache.preLoad(preUnique, url)
        }
    }

    fun mwTransToWeb(request: GameCommonFeature) {
        CpEventBus.post(
            MWTransWebEvent(loaderConfig.get().first, request)
        )
    }

    fun featGameWebViewMessage(messageId: Int) {
        UGCProtocolSender.sendProtocol(
            ProtocolSendConstant.PROTOCOL_ACTION_GAME_WEBVIEW_RESULT,
            messageId,
            MWGameWebViewResultMsg(true)
        )
    }

}