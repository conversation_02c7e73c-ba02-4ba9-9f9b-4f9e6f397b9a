package com.socialplay.gpark.function.videocompression

import android.content.Context
import java.net.URISyntaxException

object ISiliCompressor {
    /**
     * videoFilePath源路径
     * destinationDir缓存路径
     * outWidth输出宽度（一定是视频的宽高）小于0的时候有默认值
     * outHeight输出高度小于0的时候有默认值
     * bitrate码率 控制视频质量
     */
    fun compressVideo(context: Context, videoFilePath: String, destinationDir: String, outWidth: Int, outHeight: Int, bitrate: Int): String? {
        var compressedFilePath: String? = null
        try {
            compressedFilePath = SiliCompressor.with(context)?.compressVideo(videoFilePath, destinationDir, outWidth, outHeight, bitrate)
        } catch (e: URISyntaxException) {
            e.printStackTrace()
        }
        return compressedFilePath
    }
}
