package com.socialplay.gpark.function.apm

import android.os.SystemClock
import androidx.fragment.app.Fragment
import com.socialplay.gpark.function.apm.page.ApiMonitorMessage
import com.socialplay.gpark.function.apm.page.DrawMonitorMessage
import com.socialplay.gpark.function.apm.page.IPageMonitor
import com.socialplay.gpark.function.apm.page.PageLifeMonitorMessage
import com.socialplay.gpark.function.apm.page.PageMonitorCore
import com.socialplay.gpark.function.apm.page.PageMonitorDeque
import com.socialplay.gpark.function.apm.page.data.PageConfig
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.ui.core.PageExposureView
import org.koin.ext.getFullName
import timber.log.Timber

/**
 * 2024/1/12
 */
object PageMonitor {

    private val pageConfigs = mutableMapOf<String, PageConfig>()
    internal fun putAPMPageConfig(config: PageConfig) {
        check(!pageConfigs.containsKey(config.pageClassName)) { IllegalArgumentException("PageMonitor: check ${config.pageClassName} is already exists!") }
        pageConfigs[config.pageClassName] = config
    }

    fun getConfig(pageClassName: String): PageConfig? {
        return if (pageConfigs.containsKey(pageClassName)) {
            pageConfigs[pageClassName]
        } else null
    }

    fun init() {
        pageMonitorConfig()
        PageMonitorCore.init()
    }

    /*页面打开*/
    fun pageLife(pageClassName: String, pageKey: Int, pageName: String, life: Int, tag: String) {
        val currentTime = currentTime()
        PageMonitorDeque.put(
            PageLifeMonitorMessage(pageClassName, pageKey, currentTime, tag, pageName, life)
        )
    }

    /*页面绘制完成一次*/
    fun onPageDrawEnd(pageClassName: String, pageKey: Int, tag: String) {
        val currentTime = currentTime()
        PageMonitorDeque.put(DrawMonitorMessage(pageClassName, currentTime, pageKey, tag))

    }

    /*接口请求开始*/
    fun apiRequestStart(pageClassName: String, pageKey: Int, tag: String) {
        val time = currentTime()
        PageMonitorDeque.put(
            ApiMonitorMessage(pageClassName, pageKey, time, tag, ApiMonitorMessage.API_STATUS_START)
        )
    }

    /*接口请求结束*/
    fun apiRequestEnd(pageClassName: String, pageKey: Int, tag: String) {
        val time = currentTime()
        PageMonitorDeque.put(
            ApiMonitorMessage(pageClassName, pageKey, time, tag, ApiMonitorMessage.API_STATUS_END)
        )
    }

    /*接口请求出错*/
    fun apiRequestError(pageClassName: String, pageKey: Int, tag: String) {
        val time = currentTime()
        PageMonitorDeque.put(
            ApiMonitorMessage(pageClassName, pageKey, time, tag, ApiMonitorMessage.API_STATUS_ERROR)
        )
    }

    private fun currentTime(): Long {
        return SystemClock.elapsedRealtime()
    }
}

/*==========扩展函数==========*/

fun PageMonitor.page(key: String, onPageConfig: PageConfig.() -> Unit = {}) {
    val pageConfig = PageConfig(key)
    onPageConfig.invoke(pageConfig)
    putAPMPageConfig(pageConfig)
}

//页面打开之后的数据准备
fun Fragment.onPageCreate(tag: String = "") {
    val tag: String = getPageMonitorTag()
    Timber.tag("PageMonitor").d("onPageCreate ${this.hashCode()} ${this::class.getFullName()} $tag")
    PageMonitor.pageLife(
        this::class.getFullName(), this.hashCode(), getPageName(),
        PageLifeMonitorMessage.LIFE_CREATE, tag,
    )
}

fun Fragment.onPageViewCreated(tag: String = "") {
    val tag: String = getPageMonitorTag()
    Timber.tag("PageMonitor")
        .d("onPageViewCreated ${this.hashCode()} ${this::class.getFullName()} $tag")
    PageMonitor.pageLife(
        this::class.getFullName(), this.hashCode(), getPageName(),
        PageLifeMonitorMessage.LIFE_VIEW_CREATED, tag,
    )
}

fun Fragment.onPageStart(tag: String = "") {
    val tag: String = getPageMonitorTag()
    Timber.tag("PageMonitor").d("onPageStart ${this.hashCode()} ${this::class.getFullName()} $tag")
    PageMonitor.pageLife(
        this::class.getFullName(), this.hashCode(), getPageName(),
        PageLifeMonitorMessage.LIFE_START, tag,
    )
}

fun Fragment.onPageResume(tag: String = "") {
    val tag: String = getPageMonitorTag()
    Timber.tag("PageMonitor").d("onPageResume ${this.hashCode()} ${this::class.getFullName()} $tag")
    PageMonitor.pageLife(
        this::class.getFullName(), this.hashCode(), getPageName(),
        PageLifeMonitorMessage.LIFE_RESUME, tag,
    )
}

//页面打开之后的数据准备
fun Fragment.onPageDestroyView() {
    val tag: String = getPageMonitorTag()
    Timber.tag("PageMonitor")
        .d("onPageDestroyView ${this.hashCode()} ${this::class.getFullName()} $tag")
    PageMonitor.pageLife(
        this::class.getFullName(), this.hashCode(), getPageName(),
        PageLifeMonitorMessage.LIFE_DESTROY_VIEW, tag,
    )
}

//接口请求开始
fun Fragment.apiStart() {
    val tag: String = getPageMonitorTag()
    Timber.tag("PageMonitor").d("apiStart ${this.hashCode()} ${this::class.getFullName()} $tag")
    PageMonitor.apiRequestStart(this::class.getFullName(), this.hashCode(), tag)
}

//接口请求结束
fun Fragment.apiEnd() {
    val tag: String = getPageMonitorTag()
    Timber.tag("PageMonitor").d("apiEnd ${this.hashCode()} ${this::class.getFullName()} $tag")
    PageMonitor.apiRequestEnd(this::class.getFullName(), this.hashCode(), tag)
}

//接口请求失败
fun Fragment.apiError() {
    val tag: String = getPageMonitorTag()
    Timber.tag("PageMonitor").d("apiError ${this.hashCode()} ${this::class.getFullName()} $tag")
    PageMonitor.apiRequestError(this::class.getFullName(), this.hashCode(), tag)
}

fun Fragment.getPageMonitorTag(): String {
    return if (this is IPageMonitor) pageMonitorTag() else ""
}

fun Fragment.getPageName(): String {
    return when (this) {
        is BaseFragment<*> -> getFragmentName()
        is PageExposureView -> getPageName()
        else -> this::class.getFullName()
    }
}