package com.socialplay.gpark.function.deeplink;


import android.os.Bundle;
import android.util.Base64;

import com.google.gson.reflect.TypeToken;
import com.socialplay.gpark.util.GsonUtil;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.Serializable;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Set;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

public class BundleConverter {

    @Keep
    private static class SerializableData {
        private String key;
        private Object data;
        private int    type;
        private String extra;

        public SerializableData() {
        }

        public SerializableData(String key, Object data, int type, String extra) {
            this.key = key;
            this.data = data;
            this.type = type;
            this.extra = extra;
        }
    }


    public static final int Boolean = 1;
    public static final int Byte    = Boolean + 1;
    public static final int Char    = Byte + 1;
    public static final int Short   = Char + 1;
    public static final int Int     = Short + 1;
    public static final int Long    = Int + 1;
    public static final int Float   = Long + 1;
    public static final int Double  = Float + 1;
    public static final int String  = Double + 1;

    public static final int Bundle = String + 1;

    public static final int Serializable = Bundle + 1;
    public static final int Parcelable   = Serializable + 1;
    public static final int BooleanArray = Parcelable + 1;
    public static final int ByteArray    = BooleanArray + 1;
    public static final int CharArray    = ByteArray + 1;
    public static final int ShortArray   = CharArray + 1;
    public static final int IntArray     = ShortArray + 1;
    public static final int LongArray    = IntArray + 1;
    public static final int FloatArray   = LongArray + 1;
    public static final int DoubleArray  = FloatArray + 1;
    public static final int StringArray  = DoubleArray + 1;


    private static final HashMap<Class<?>, Integer> TYPE_MAP = new HashMap<>();

    static {
        TYPE_MAP.put(boolean.class, Boolean);
        TYPE_MAP.put(Boolean.class, Boolean);

        TYPE_MAP.put(byte.class, Byte);
        TYPE_MAP.put(Byte.class, Byte);

        TYPE_MAP.put(short.class, Short);
        TYPE_MAP.put(Short.class, Short);

        TYPE_MAP.put(char.class, Char);
        TYPE_MAP.put(Character.class, Char);

        TYPE_MAP.put(int.class, Int);
        TYPE_MAP.put(Integer.class, Int);

        TYPE_MAP.put(long.class, Long);
        TYPE_MAP.put(Long.class, Long);

        TYPE_MAP.put(float.class, Float);
        TYPE_MAP.put(Float.class, Float);

        TYPE_MAP.put(double.class, Double);
        TYPE_MAP.put(Double.class, Double);

        TYPE_MAP.put(String.class, String);
        TYPE_MAP.put(Bundle.class, Bundle);

        TYPE_MAP.put(boolean[].class, BooleanArray);
        TYPE_MAP.put(byte[].class, ByteArray);
        TYPE_MAP.put(char[].class, CharArray);
        TYPE_MAP.put(short[].class, ShortArray);
        TYPE_MAP.put(int[].class, IntArray);
        TYPE_MAP.put(long[].class, LongArray);
        TYPE_MAP.put(float[].class, FloatArray);
        TYPE_MAP.put(double[].class, DoubleArray);
        TYPE_MAP.put(String[].class, StringArray);
    }


    private static byte[] compress(byte[] bytes) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try (GZIPOutputStream gzip = new GZIPOutputStream(out)) {
            gzip.write(bytes);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return out.toByteArray();
    }

    private static byte[] uncompress(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return null;
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ByteArrayInputStream in = new ByteArrayInputStream(bytes);
        try {
            GZIPInputStream ungzip = new GZIPInputStream(in);
            byte[] buffer = new byte[256];
            int n;
            while ((n = ungzip.read(buffer)) >= 0) {
                out.write(buffer, 0, n);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return out.toByteArray();
    }

    public static String encode(Bundle bundle) {
        List<SerializableData> result = encodeInternal(bundle);
        final String json = GsonUtil.INSTANCE.getGson().toJson(result);
        final byte[] compressed = compress(json.getBytes(StandardCharsets.UTF_8));
        return Base64.encodeToString(compressed, Base64.DEFAULT);
    }

    @NonNull
    private static List<SerializableData> encodeInternal(android.os.Bundle bundle) {
        List<SerializableData> result = new ArrayList<>();
        final Set<String> keySet = bundle.keySet();

        for (String key : keySet) {
            final Object value = bundle.get(key);
            if (value != null) {
                final Class<?> valueTypeClass = value.getClass();
                final Integer type = TYPE_MAP.get(valueTypeClass);
                if (type != null) {
                    if (type == Bundle) {
                        result.add(new SerializableData(key, GsonUtil.INSTANCE.getGson().toJson(encodeInternal((android.os.Bundle) value)), type, null));
                    } else {
                        result.add(new SerializableData(key, value, type, null));
                    }
                } else {
                    //TODO Process Collection Map ,Parcelable Serializable
                    if (value instanceof Serializable) {
                        result.add(new SerializableData(key, GsonUtil.INSTANCE.getGson().toJson(value), Serializable, valueTypeClass.getName()));
                    }
                }
            }
        }

        return result;
    }

    public static Bundle decode(String str) {
        final byte[] uncompressed = uncompress(Base64.decode(str, Base64.DEFAULT));
        final String json = new String(uncompressed, StandardCharsets.UTF_8);
        return decodeInternal(json);
    }

    private static Bundle decodeInternal(String str) {
        final Bundle bundle = new Bundle();
        final List<SerializableData> data = GsonUtil.INSTANCE.getGson().fromJson(str, new TypeToken<List<SerializableData>>() {
        }.getType());

        for (SerializableData datum : data) {
            switch (datum.type) {
                case Boolean:
                    bundle.putBoolean(datum.key, (Boolean) datum.data);
                    break;
                case Byte:
                    bundle.putByte(datum.key, ((Number) datum.data).byteValue());
                    break;
                case Char:
                    bundle.putChar(datum.key, (char) ((Number) datum.data).byteValue());
                    break;
                case Short:
                    bundle.putShort(datum.key, (short) ((Number) datum.data).shortValue());
                    break;
                case Int:
                    bundle.putInt(datum.key, ((Number) datum.data).intValue());
                    break;
                case Long:
                    bundle.putLong(datum.key, ((Number) datum.data).longValue());
                    break;
                case Float:
                    bundle.putFloat(datum.key, ((Number) datum.data).floatValue());
                    break;
                case Double:
                    bundle.putDouble(datum.key, ((Number) datum.data).doubleValue());
                    break;
                case String:
                    bundle.putString(datum.key, (String) datum.data);
                    break;
                case Bundle:
                    bundle.putBundle(datum.key, decodeInternal((String) datum.data));
                    break;
                case Serializable:
                    try {
                        bundle.putSerializable(datum.key, (Serializable) GsonUtil.INSTANCE.getGson().fromJson(datum.data.toString(), Class.forName(datum.extra)));
                    } catch (ClassNotFoundException e) { }
                    break;
                case Parcelable:
                    break;
                case BooleanArray:
                    break;
                case ByteArray:
                    break;
                case CharArray:
                    break;
                case ShortArray:
                    break;
                case IntArray:
                    break;
                case LongArray:
                    break;
                case FloatArray:
                    break;
                case DoubleArray:
                    break;
                case StringArray:
                    break;
            }
        }

        return bundle;
    }
}
