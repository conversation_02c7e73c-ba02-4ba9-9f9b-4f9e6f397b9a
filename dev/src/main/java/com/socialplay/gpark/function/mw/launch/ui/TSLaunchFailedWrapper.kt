package com.socialplay.gpark.function.mw.launch.ui

import androidx.fragment.app.Fragment
import com.socialplay.gpark.R
import com.socialplay.gpark.function.analytics.kernel.PandoraInit
import com.socialplay.gpark.function.mw.launch.bean.TSLaunchParams
import com.socialplay.gpark.function.mw.launch.exception.BaseTSLaunchException
import com.socialplay.gpark.function.mw.launch.exception.TSEngineException
import com.socialplay.gpark.function.mw.launch.exception.TSEngineVersionNotMatchException
import com.socialplay.gpark.function.mw.launch.exception.TSOfflineException
import com.socialplay.gpark.function.mw.launch.exception.TSVersionCompatibleException
import com.socialplay.gpark.util.extension.toast
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 2023/9/18
 */
object TSLaunchFailedWrapper {

    fun show(fragment: Fragment, params: TSLaunchParams, e: Throwable?): Bo<PERSON>an {
        if (!fragment.isVisible) return false
        val hasError = AtomicBoolean(true)
        when (e) {
            is TSEngineVersionNotMatchException -> {
                TSEngineNotMatchDialog.show(fragment, params.gameInfo.icon)
            }

            is TSVersionCompatibleException -> {
                val img = params.gameInfo.images?.firstOrNull()?.url ?: params.gameInfo.icon
                MWVersionCompatibleDialog.show(fragment.childFragmentManager, e.showDialogText, img)
            }

            is TSOfflineException -> fragment.toast(e.toastMsg)

            is BaseTSLaunchException -> {
                if (e is TSEngineException && !PandoraInit.checkEsVersion(fragment.requireContext())) {
                    fragment.toast(R.string.opengl_es_tips)
                } else {
                    fragment.toast(R.string.launch_failed_click_to_retry)
                }
            }
            else -> {
                hasError.set(e != null)
                if (e != null) fragment.toast(R.string.launch_failed_click_to_retry)
            }
        }
        return hasError.get()
    }

}