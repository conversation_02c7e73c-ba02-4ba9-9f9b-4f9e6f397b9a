package com.socialplay.gpark.function.locale

import android.app.Application
import android.app.LocaleManager
import android.content.Context
import android.content.res.Configuration
import android.content.res.Resources
import android.os.Build
import android.os.LocaleList
import androidx.appcompat.app.AppCompatDelegate
import androidx.core.os.LocaleListCompat
import com.socialplay.gpark.ui.locale.LanguageSettingFragment
import timber.log.Timber
import java.util.Locale

/**
 * Created by bo.li
 * Date: 2024/4/11
 * Desc:
 */
object LocaleUtil {
    fun getLocale(context: Context): Locale {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            context.resources.configuration.locales.get(0) ?: Locale.getDefault() ?: Locale.ENGLISH
        } else {
            context.resources.configuration.locale ?: Locale.getDefault() ?: Locale.ENGLISH
        }
    }

    fun getLocale(configuration: Configuration): Locale {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            configuration.locales.get(0) ?: Locale.getDefault() ?: Locale.ENGLISH
        } else {
            configuration.locale ?: Locale.getDefault() ?: Locale.ENGLISH
        }
    }

    private fun setLocale(config: Configuration, locale: Locale) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            val localeList = LocaleList(locale)
            config.setLocales(localeList)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                val android14OrAboveLocale = LocaleListCompat.forLanguageTags(locale.language)
                AppCompatDelegate.setApplicationLocales(android14OrAboveLocale)
            }
        } else {
            config.setLocale(locale)
        }
    }

    /**
     * 获取系统的语种对象
     */
    fun getSystemLocale(context: Context): Locale {
        val locale = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // 在 Android 13 上，不能用 Resources.getSystem() 来获取系统语种了
            // Android 13 上面新增了一个 LocaleManager 的语种管理类
            // 因为如果调用 LocaleManager.setApplicationLocales 会影响获取到的结果不准确
            // 所以应该得用 LocaleManager.getSystemLocales 来获取会比较精准
            runCatching {
                context.getSystemService(LocaleManager::class.java)?.systemLocales?.get(0)
            }.getOrElse {
                Timber.tag(LanguageSettingFragment.TAG).e("getSystemLocale $it")
                null
            } ?: getLocale(Resources.getSystem().configuration)
        } else {
            getLocale(Resources.getSystem().configuration)
        }
        Timber.d("getSystemLocale ${locale}")
        return locale
    }

    /**
     * 绑定当前 App 的语种
     * context是application/activity时，作用范围是不同的
     */
    fun attachLanguages(context: Context, locale: Locale): Context {
        val resources = context.resources
        val config = Configuration(resources.configuration)
        setLocale(config, locale)
        resources.updateConfiguration(config, resources.displayMetrics)
        Timber.tag(LanguageSettingFragment.TAG).d("attachLanguages, isApplication:${context is Application} locale:${locale}")
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            context
        } else {
            context.createConfigurationContext(config)
        }
    }

    /**
     * 更新 Resources 语种
     */
    fun updateLanguages(resources: Resources, locale: Locale) {
        val config = resources.configuration
        setLocale(config, locale)
        resources.updateConfiguration(config, resources.displayMetrics)
        Timber.tag(LanguageSettingFragment.TAG).d("updateLanguages ${locale}")
    }

    /**
     * 更新 Resources 语种
     * context是application/activity时，作用范围是不同的
     */
    fun updateConfigurationChanged(resources: Resources, newConfig: Configuration, locale: Locale) {
        val config = Configuration(newConfig)
        setLocale(config, locale)
        resources.updateConfiguration(config, resources.displayMetrics)
        Timber.tag(LanguageSettingFragment.TAG).d("updateConfigurationChanged ${locale}")
    }

    /**
     * 设置默认的语种环境（日期格式化会用到）
     */
    fun setDefaultLocale(context: Context) {
        val configuration = context.resources.configuration
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            LocaleList.setDefault(configuration.locales)
        } else {
            Locale.setDefault(configuration.locale)
        }
    }
}

