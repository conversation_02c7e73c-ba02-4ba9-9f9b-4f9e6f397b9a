package com.socialplay.gpark.function.account

import android.view.View
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.user.Gender
import com.socialplay.gpark.ui.view.pickerview.builder.OptionsPickerBuilder
import com.socialplay.gpark.ui.view.pickerview.builder.TimePickerBuilder
import com.socialplay.gpark.ui.view.pickerview.view.OptionsPickerView
import com.socialplay.gpark.ui.view.pickerview.view.TimePickerView
import com.socialplay.gpark.ui.view.wheelview.view.WheelView
import com.socialplay.gpark.util.DateUtil
import com.socialplay.gpark.util.DateUtilWrapper
import java.util.*

/**
 * Created by bo.li
 * Date: 2023/2/9
 * Desc:
 */
class TimePickerObserver(val fragment: Fragment): LifecycleEventObserver {
    private var timePickerView: TimePickerView? = null
    private var genderPickerView: OptionsPickerView<String>? = null
    private var mTimeCallback: ((date: Date) -> Unit)? = null
    private var mGenderCallback: ((gender: Gender) -> Unit)? = null
    private var mGenderDismissCallback: (() -> Unit)? = null
    private var mTimeDismissCallback: (() -> Unit)? = null

    init {
        fragment.lifecycle.addObserver(this)
    }

    fun showDatePicker(
        defaultDate: Calendar = DateUtilWrapper.getDefaultBirthCalendar(),
        callback: (date: Date) -> Unit,
        dismissCallback: (() -> Unit)? = null
    ) {
        val startDate = DateUtilWrapper.getEarliestBirthCalendar()

        val endDate = Calendar.getInstance()

        mTimeCallback = callback
        mTimeDismissCallback = dismissCallback
        timePickerView = TimePickerBuilder(fragment.requireContext()) { date, _ ->
            mTimeCallback?.invoke(date)
        }.setLayoutRes(R.layout.view_sign_up_birthday_picker) { v ->

            v.findViewById<View>(R.id.tv_finish).setOnClickListener {
                timePickerView?.returnData()
                timePickerView?.dismiss()
            }
        }.setLabel(
            DateUtilWrapper.getLabel4Year(fragment.requireContext()),
            "",
            DateUtilWrapper.getLabel4Day(fragment.requireContext()),
            "",
            "",
            ""
        )
        .setRangDate(startDate, endDate)
        .setDate(defaultDate)
        .setLineSpacingMultiplier(2.9F)
        .setDividerColor(ContextCompat.getColor(fragment.requireContext(), R.color.color_F6F6F6))
        .setDividerType(WheelView.DividerType.GPARK_STYLE)
        .setTextColorCenter(ContextCompat.getColor(fragment.requireContext(), R.color.textColorPrimary))
        .setTextColorOut(ContextCompat.getColor(fragment.requireContext(), R.color.color_666666))
        .setCenterTypeface(ResourcesCompat.getFont(fragment.requireContext(), R.font.poppins_bold_700))
        .setOuterTypeface(ResourcesCompat.getFont(fragment.requireContext(), R.font.poppins_medium_500))
        .build()
        timePickerView?.setOnDismissListener {
            mTimeDismissCallback?.invoke()
        }

        timePickerView?.show()
    }

    fun showGenderPicker(
        selectGender: Gender?,
        callback: (gender: Gender) -> Unit,
        dismissCallback: (() -> Unit)? = null
    ) {
        val genderList = listOf(Gender.Female, Gender.Male, Gender.Other)
        mGenderDismissCallback = dismissCallback
        mGenderCallback = callback

        genderPickerView = OptionsPickerBuilder(
            fragment.requireContext()
        ) { options1, _, _, _ ->
            mGenderCallback?.invoke(genderList[options1])
        }.setLayoutRes(R.layout.view_sign_up_gender_picker) { v ->
            v.findViewById<View>(R.id.tv_finish).setOnClickListener {
                genderPickerView?.returnData()
                genderPickerView?.dismiss()
            }
        }.setLineSpacingMultiplier(2.9F)
        .setSelectOptions(genderList.indexOf(selectGender ?: Gender.Other))
        .setDividerColor(ContextCompat.getColor(fragment.requireContext(), R.color.color_F6F6F6))
        .setDividerType(WheelView.DividerType.GPARK_STYLE)
        .setTextColorCenter(ContextCompat.getColor(fragment.requireContext(), R.color.textColorPrimary))
        .setTextColorOut(ContextCompat.getColor(fragment.requireContext(), R.color.color_666666))
        .setCenterTypeface(ResourcesCompat.getFont(fragment.requireContext(), R.font.poppins_bold_700))
        .setOuterTypeface(ResourcesCompat.getFont(fragment.requireContext(), R.font.poppins_medium_500))
        .build()
        genderPickerView?.setOnDismissListener {
            mGenderDismissCallback?.invoke()
        }

        genderPickerView?.setPicker(genderList.map { getGenderText(it) })
        genderPickerView?.show()
    }

    private fun getGenderText(gender: Gender): String {
        return fragment.getString(
            when (gender) {
                Gender.Male   -> R.string.male
                Gender.Female -> R.string.female
                Gender.Other  -> R.string.other
            }
        )
    }

    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
        if (event == Lifecycle.Event.ON_DESTROY) {
            timePickerView?.returnData()
            timePickerView?.dismiss()
            timePickerView = null
            mTimeCallback = null

            genderPickerView?.returnData()
            genderPickerView?.dismiss()
            genderPickerView = null
            mGenderCallback = null
            mGenderDismissCallback = null
            mTimeDismissCallback = null
        }
    }
}