package com.socialplay.gpark.function.ipc.provider.ts

import android.app.Application
import com.meta.lib.mwbiz.MWBizBridge
import com.meta.lib.mwbiz.bean.bridge.SendMsg
import timber.log.Timber

/**
 * TS函数调用入口
 * 该接口仅在TS游戏进程中注册，用于平台调用TS的方法
 */
class TSFunctionEntry(private val app: Application) : ITSFunctionEntry {

    override fun call(action: String, data: Map<String, Any>?){
        Timber.i("TSFunctionEntry call ts action:$action data:$data",)
        MWBizBridge.callUE(SendMsg().apply {
            this.action = action
            this.data = data ?: emptyMap()
        })
    }
}