package com.socialplay.gpark.function.deeplink.linkhandler

import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.deeplink.LinkHandleResult
import com.socialplay.gpark.function.deeplink.LinkHandler
import com.socialplay.gpark.function.deeplink.LinkHandlerChain
import com.socialplay.gpark.function.deeplink.MetaDeepLink
import com.socialplay.gpark.function.router.MetaRouter

/**
 * 处理跳转到游戏详情页
 */
class GameDetailFromGameLinkHandler : LinkHandler {

    override fun handle(chain: LinkHandlerChain, data: LinkData): LinkHandleResult {
        val dataString = data.uri.getQueryParameter("data") ?: return LinkHandleResult.Failed("no data")
        val arguments = BasicNavigateLinkHandler.decodeBundle(dataString)
        val fromGamePkgName = arguments.getString(MetaDeepLink.PARAM_FROM_GAME_PACKAGE_NAME)
        val fromGameId = arguments.getString(MetaDeepLink.PARAM_FROM_GAME_ID)
        val autoDownload = arguments.getBoolean(MetaDeepLink.PARAM_GAME_AUTO_DOWNLOAD, false)
        val extraBundle = arguments.getBundle(MetaDeepLink.PARAM_EXTRA_BUNDLE)

        MetaRouter.GameDetail.navigate(
            data.navHost,
            extraBundle?.getString("gId").orEmpty(),
            extraBundle?.getSerializable("resIdBean") as? ResIdBean ?: ResIdBean(),
            extraBundle?.getString("packageName").orEmpty(),
            fromGameId,
            fromGamePkgName,
            autoDownload,
            extraBundle?.getString("type").orEmpty(),
        )
        return LinkHandleResult.Success
    }
}