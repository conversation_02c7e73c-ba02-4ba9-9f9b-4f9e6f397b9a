package com.socialplay.gpark.function.deeplink.linkhandler

import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.deeplink.LinkHandleResult
import com.socialplay.gpark.function.deeplink.LinkHandler
import com.socialplay.gpark.function.deeplink.LinkHandlerChain
import com.socialplay.gpark.function.deeplink.MetaDeepLink
import com.socialplay.gpark.function.deeplink.MetaDeepLink.KEY_FROM_GAME_ID
import com.socialplay.gpark.function.deeplink.MetaDeepLink.KEY_NEED_BACK_GAME
import com.socialplay.gpark.function.deeplink.MetaDeepLink.KEY_NEED_BACK_ROLE
import com.socialplay.gpark.function.router.MetaRouter

/**
 * Created by bo.li
 * Date: 2024/4/11
 * Desc: 反馈页面
 */
class FeedbackLinkHandler: LinkHandler {
    override fun handle(chain: LinkHandlerChain, data: LinkData): LinkHandleResult {
        val feedbackSource = data.uri.getQueryParameter(MetaDeepLink.PARAM_SOURCE) ?: return LinkHandleResult.Failed("source is null")
        val feedbackGameId = data.uri.getQueryParameter(MetaDeepLink.PARAM_FEEDBACK_GAME_ID)
        val feedbackDefaultType = data.uri.getQueryParameter(MetaDeepLink.PARAM_DEFAULT_TYPE)
        val needBackGame = data.uri.getQueryParameter(KEY_NEED_BACK_GAME).toBoolean()
        val needBackRole = data.uri.getQueryParameter(KEY_NEED_BACK_ROLE).toBoolean()
        val fromGameId = data.uri.getQueryParameter(KEY_FROM_GAME_ID)
        MetaRouter.Feedback.feedback(data.navHost, feedbackGameId, feedbackSource, feedbackDefaultType, needBackRole, needBackGame, fromGameId)
        return LinkHandleResult.Success
    }
}