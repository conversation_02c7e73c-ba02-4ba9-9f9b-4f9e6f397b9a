package com.socialplay.gpark.function.analytics.kernel

import android.app.ActivityManager
import android.app.Application
import android.content.Context
import android.content.pm.ConfigurationInfo
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.meta.lib.mwbiz.MWBizBridge
import com.meta.pandora.Pandora
import com.meta.pandora.PandoraConfig
import com.meta.pandora.data.entity.CommonParams
import com.meta.pandora.data.entity.Event
import com.meta.pandora.data.entity.Params
import com.meta.pandora.function.crash.CrashParams
import com.meta.pandora.function.crash.CrashType
import com.meta.pandora.function.event.preview.PandoraEventPreview
import com.meta.pandora.setSubProcessEventParamsInterceptor
import com.meta.verse.lib.MVConstant
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.app.initialize.AppTimeInit
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.DevEnvType
import com.socialplay.gpark.di.CommonParamsProvider
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventDesc
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.mw.MWInitialize
import com.socialplay.gpark.function.startup.StartupProcessType
import com.socialplay.gpark.function.startup.core.ProcessType
import com.socialplay.gpark.ui.dialog.DialogShowManager
import org.koin.core.context.GlobalContext
import timber.log.Timber


fun ProcessType.toPandoraType(): com.meta.pandora.ProcessType {
    return when (this) {
        StartupProcessType.H -> com.meta.pandora.ProcessType.H
        StartupProcessType.M -> com.meta.pandora.ProcessType.M
        StartupProcessType.R -> com.meta.pandora.ProcessType.R
        StartupProcessType.AUTO -> com.meta.pandora.ProcessType.AUTO
        else -> com.meta.pandora.ProcessType.O
    }
}

object PandoraInit {

    private val _abTestConfigFetchedLiveData = MutableLiveData<Boolean>()
    val abTestConfigFetchedLiveData: LiveData<Boolean> = _abTestConfigFetchedLiveData

    private val _abTestConfigUpdatedLiveData = MutableLiveData<Int>()
    val abTestConfigUpdatedLiveData: LiveData<Int> = _abTestConfigUpdatedLiveData

    private val commonParamsProvider by lazy { GlobalContext.get().get<CommonParamsProvider>() }
    private val developerKV by lazy { GlobalContext.get().get<MetaKV>().developer }
    private const val KIND_PANDORA_APP_LAUNCH = "pandora_app_launch" //pandora sdk内部埋点
    private lateinit var context: Application

    private var esVer: Int = 0

    private val eventMap = if (BuildConfig.DEBUG) {
        hashMapOf<String, String>().apply {
            EventConstants.javaClass.declaredFields.forEach { field ->
                kotlin.runCatching {
                    field.isAccessible = true
                    val e = field.get(EventConstants) as? Event
                    val desc = (field.declaredAnnotations.firstOrNull { it is EventDesc } as? EventDesc)?.desc
                    if (e != null && desc != null) {
                        put(e.kind, desc)
                    }
                }
            }
        }
    } else null


    fun preInit(context: Application, processType: ProcessType) {
        this.context = context

        val env = when (BuildConfig.PD_ENV_TYPE) {
            DevEnvType.Pre.name -> PandoraConfig.Env.PRE
            DevEnvType.Test.name -> PandoraConfig.Env.TEST
            else -> PandoraConfig.Env.ONLINE
        }
        val config = PandoraConfig.Builder(BuildConfig.PANDORA_APP_KEY)
            .buildId(BuildConfig.BUILD_ID)
            .server(PandoraInitWrapper.getServer())
            .env(env)
            .processType(processType.toPandoraType())
            .debug(BuildConfig.DEBUG)
            .enableABTest()
            .enableHttpResponseTimeMonitor()
            .enableFeatureFlag()
            .setCommonParamsInterceptor(::addPublicParams)
            .setEventParamsInterceptor(::addEventParams)
            .setSubProcessEventParamsInterceptor(::addSubProcessEventParams)
            .setAbTestParamsInterceptor(::addABCommonParams)
            .setCrashParamsInterceptor(::addCrashParams)
            .setOnAbTestConfigFetchedListener {
                Timber.tag(DialogShowManager.TAG).d("setOnAbTestConfigFetchedListener")
                _abTestConfigFetchedLiveData.value = true
            }
            .setOnAbTestConfigUpdatedListener {
                _abTestConfigUpdatedLiveData.value = (_abTestConfigUpdatedLiveData.value ?: 0) + 1
            }
            .setFlavorConfig()
            .build()
        Pandora.preInit(context, config)
    }

    fun init(context: Application, isMainProcess: Boolean, processType: ProcessType) {

        if (isMainProcess) {
            if (developerKV.isShowEvent) {
                PandoraEventPreview.open(context)
            }
            PandoraEventPreview.setCloseListener {
                developerKV.isShowEvent = false
            }
            Pandora.setEventPreview(PandoraEventPreview)

            PandoraInitWrapper.init(context)
        }
        //内核进程在lifecycle回调启用崩溃收集
        if (processType != StartupProcessType.M) {
            Pandora.enableCrashHandler(context, true)
        }
        Pandora.enablePageLogger(context, listOf("meta"))

        Pandora.init()
        if (processType == StartupProcessType.M || processType == StartupProcessType.R) {
            listenerMWEvent()
        }
    }

    private val MWGraphicEventParams = mutableMapOf<String, Any>()
    private fun listenerMWEvent() {
        MWBizBridge.addMWCoreCallback(MVConstant.BRIDGE_MW_GRAPHIC_EVENT) { key, value ->
            when (key) {
                MVConstant.BRIDGE_MW_GRAPHIC_EVENT -> {
                    MWGraphicEventParams.clear()
                    val params = value.getOrNull(0) as? Map<String, Any>
                    MWGraphicEventParams.putAll(params ?: mapOf())
                    MWGraphicEventParams.onEach { entry ->
                        Timber.d("${entry.key}  ${entry.value}")
                    }
                }
            }
        }
    }

    fun setCrashParams(context: Context, processType: com.meta.pandora.ProcessType, params: CrashParams) {
        var packageName = context.packageName
        var gameVersion: String? = null

        params.put(MWGraphicEventParams)
        if (processType == com.meta.pandora.ProcessType.M) {
            val metaKV: MetaKV = GlobalContext.get().get()
            packageName = MWBizBridge.currentGamePkg()
            val gameId = MWBizBridge.currentGameId()
            gameVersion = MWInitialize.getTSGameVersion(gameId)

            params.gameId(gameId)
            val resIdBean = metaKV.analytic.getLaunchResIdBean(MWBizBridge.currentGameId())
                ?: metaKV.analytic.getLaunchResIdBean(MWBizBridge.currentGamePkg())
                ?: ResIdBean()
            val ugcType = resIdBean.getTsType()
                .let { if (it == -1L) ResIdBean.TS_TYPE_NORMAL else it }
            val ugcParentId = resIdBean.getGameCode() ?: ""
            params.put("ugc_type", ugcType.toString())
            params.put("ugc_parent_id", ugcParentId)
            val attribute = MWBizBridge.gameAttribute()
            params.put("mwc_scene_id", attribute["mwGameId"] ?: "")
        }
        if (processType == com.meta.pandora.ProcessType.R) {
            packageName = MWBizBridge.currentGamePkg()
            val gameId = MWBizBridge.currentGameId()
            gameVersion = MWInitialize.getTSGameVersion(gameId)
            params.gameId(gameId)
        }
        params.packageName(packageName)
        if (!gameVersion.isNullOrEmpty()) {
            params.gameVersion(gameVersion)
        }
    }

    private fun addCrashParams(processType: com.meta.pandora.ProcessType, crashType: CrashType, params: CrashParams) {
        if (processType == com.meta.pandora.ProcessType.H && !crashType.isAnr) {
            AppTimeInit.onCrash()
        }
        setCrashParams(context, processType, params)

        if (crashType != CrashType.MW_TS_ERROR && !crashType.isAnr) {
            Timber.e(
                "%s %s %s %s",
                params.get("processName"),
                processType.name,
                " duration:--  ",
                params.get("errorStack")
            )
        }

    }

    private fun logEvent(event: Event, params: Params) {
        val desc = eventMap?.get(event.kind)
        Timber.tag("Pandora-event").e("Event(kind=%s,desc=%s,params=%s)", event.kind, desc ?: event.desc, params.toString())
    }

    private fun addABCommonParams(params: CommonParams) {
        commonParamsProvider.let {
            params.uid(it.uid)
            params.deviceId(it.deviceId)
            params.put("android_id", it.androidId)
            params.put("metaverse_version", it.metaverseVersion)
            params.put("metaverse_engine_version", it.metaverseEngineVersion)
            params.put("region", it.region)
            PandoraInitWrapper.getABPublicParams(commonParamsProvider).forEach { it2 ->
                params.put(it2.key, it2.value)
            }
        }
    }


    private fun addPublicParams(params: CommonParams) {
        commonParamsProvider.let {
            params.deviceId(it.deviceId)
            params.put("android_id", it.androidId)
            params.put("region", it.region)
            params.put("is_tablet", it.isTablet)
            PandoraInitWrapper.getPublicParams(commonParamsProvider).forEach { it2 ->
                params.put(it2.key, it2.value)
            }
        }
    }

    private fun addEventParams(event: Event, params: CommonParams): Boolean {
        logEvent(event, params)

        commonParamsProvider.let {
            params.uid(it.uid)
            params.put("metaverse_version", it.metaverseVersion)
            params.put("metaverse_engine_version", it.metaverseEngineVersion)
        }
        if (event.kind == KIND_PANDORA_APP_LAUNCH) {
            params.put("abi", Pandora.getABI())
            params.put("gles", getOpenGlVersion(context))
        }
        return true
    }

    private fun addSubProcessEventParams(processType: com.meta.pandora.ProcessType, event: Event, params: Params) {
        when (processType) {
            com.meta.pandora.ProcessType.M, com.meta.pandora.ProcessType.R -> {
                params.put("metaverse_engine_version", commonParamsProvider.metaverseEngineVersion)
            }
        }
    }

    fun getOpenGlVersion(context: Context): String {
        val am: ActivityManager? = kotlin.runCatching { context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager? }.getOrNull()
        val info: ConfigurationInfo = am?.deviceConfigurationInfo ?: return ""
        esVer = info.reqGlEsVersion
        return info.glEsVersion
    }

    fun checkEsVersion(context: Context): Boolean {
        if (esVer == 0) {
            getOpenGlVersion(context)
        }
        return esVer >= 196609
    }
}


