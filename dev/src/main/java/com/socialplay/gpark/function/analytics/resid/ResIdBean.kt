package com.socialplay.gpark.function.analytics.resid

import androidx.core.graphics.drawable.IconCompat.IconType
import com.socialplay.gpark.util.KeepClass
import java.io.Serializable


/**
 * create by: bin on 2020-02-26
 */
@KeepClass
class ResIdBean : Serializable {


    /**
     * 用于从intent获取该对象
     */

    private var categoryID: Int = 0 //推荐位类型，其实也可以是字符串，不过之前已经编码了就整数吧

    private var param1: Long = 0 //两个参数，比如一堆图标的游戏库，这两个参数可以是第几行第几列；比如分类，这两个参数可以使分类id和在这个分类里面第几个位置。

    private var param2: Long = 0

    private var paramExtra: String? = null //还有其他信息不够的就编码到这字符串里面

    private var source: Int = 1  // 这个资源位这次是被谁填充的，来源是推荐还是运营还是广告强插位的标示flag。0=推荐；1=运营；2=广告强插；待扩充

    private var isSpec: Int = 0

    private var gameId: String? = null // 游戏id

    private var reqId: String? = null

    private var type: Int = 0

    private var iconId: Int = -1

    private var typeID: String? = null

    // 重排方法
    private var reRankMethod: String = ""

    //需要携带的参数对
    private var extras: Map<String, Any>? = null

    private var tsType: Long = -1

    // 父模板游戏id，普通ts游戏用不着
    private var gameCode: String? = null

    private var gameVersionName: String? = null

    // 点击游戏按钮时间
    private var clickGameTime: Long = System.currentTimeMillis()
    private var fileId: String? = null
    private var path: String? = null
    private var iconType: String? = null

    private var isModuleGuide: Boolean = false

    constructor()

    constructor(resid: ResIdBean) {
        this.categoryID = resid.categoryID
        this.param1 = resid.param1
        this.param2 = resid.param2
        this.paramExtra = resid.paramExtra
        this.source = resid.source
        this.isSpec = resid.isSpec
        this.gameId = resid.gameId
        this.reqId = resid.reqId
        this.type = resid.type
        this.iconId = resid.iconId
        this.typeID = resid.typeID
        this.reRankMethod = resid.reRankMethod
        this.extras = resid.extras?.toMutableMap()
        this.tsType = resid.tsType
        this.gameCode = resid.gameCode
        this.gameVersionName = resid.gameVersionName
        this.clickGameTime = resid.clickGameTime
        this.isModuleGuide = resid.isModuleGuide
    }

    fun getCategoryID(): Int {
        return categoryID
    }

    fun setCategoryID(categoryID: Int): ResIdBean {
        this.categoryID = categoryID
        return this
    }

    fun getParam1(): Long {
        return param1
    }

    fun setParam1(param1: Long): ResIdBean {
        this.param1 = param1
        return this
    }

    fun setParam1(param1: Int): ResIdBean {
        this.param1 = param1.toLong()
        return this
    }

    fun getParam2(): Long {
        return param2
    }

    fun setParam2(param2: Long): ResIdBean {
        this.param2 = param2
        return this
    }

    fun setParam2(param2: Int): ResIdBean {
        this.param2 = param2.toLong()
        return this
    }

    fun getParamExtra(): String? {
        return paramExtra
    }

    fun setParamExtra(paramExtra: String?): ResIdBean {
        this.paramExtra = paramExtra
        return this
    }

    fun getSource(): Int {
        return source
    }

    fun setSource(source: Int): ResIdBean {
        this.source = source
        return this
    }

    fun getGameId(): String? {
        return gameId
    }

    fun setGameId(gameId: String?): ResIdBean {
        this.gameId = gameId
        return this
    }

    fun getReqId(): String? {
        return reqId
    }

    fun setReqId(reqId: String?): ResIdBean {
        this.reqId = reqId
        return this
    }

    fun setIsSpec(isSpec: Int): ResIdBean {
        this.isSpec = isSpec
        return this
    }

    fun getIsSpec(): Int {
        return isSpec
    }

    fun setType(type: Int): ResIdBean {
        this.type = type
        return this
    }

    fun getType(): Int {
        return this.type
    }

    fun setTypeID(typeID: String?): ResIdBean {
        this.typeID = typeID
        return this
    }

    fun getTypeID(): String? {
        return this.typeID
    }

    fun setIconID(iconId: Int): ResIdBean {
        this.iconId = iconId
        return this
    }

    fun getIconID(): Int {
        return this.iconId
    }

    fun setReRankMethod(reRankMethod: String?): ResIdBean {
        this.reRankMethod = reRankMethod ?: ""
        return this
    }

    fun getReRankMethod(): String {
        return this.reRankMethod
    }


    fun setExtras(extras: Map<String, Any>): ResIdBean {
        this.extras = extras
        return this
    }

    fun getExtras():Map<String, Any>? {
        return extras
    }

    fun setTsType(tsType: Long): ResIdBean {
        this.tsType = tsType
        return this
    }

    fun getTsType(): Long {
        return tsType
    }

    fun setTsTypeIfUnset(tsType: Long): ResIdBean {
        if (this.tsType == -1L) {
            this.tsType = tsType
        }
        return this
    }

    fun setGameCode(gameCode: String?): ResIdBean {
        this.gameCode = gameCode
        return this
    }

    fun getGameCode(): String? {
        return gameCode
    }

    fun setGameVersionName(gameVersionName: String?): ResIdBean {
        this.gameVersionName = gameVersionName
        return this
    }

    fun getGameVersionName(): String? {
        return gameVersionName
    }

    fun setClickGameTime(time: Long): ResIdBean {
        this.clickGameTime = time
        return this
    }

    fun getClickGameTime(): Long {
        return clickGameTime
    }

    fun setFileId(fileId: String): ResIdBean {
        this.fileId = fileId
        return this
    }

    fun getFileId(): String? {
        return fileId
    }

    fun setPath(path: String): ResIdBean {
        this.path = path
        return this
    }

    fun getPath(): String? {
        return path
    }

    fun setIconType(iconType:String?): ResIdBean {
        this.iconType = iconType
        return this
    }

    fun getIconType(): String? {
        return iconType
    }

    fun setIconTypeIfEmpty(iconType: String?): ResIdBean {
        if (this.iconType.isNullOrEmpty()) {
            this.iconType = iconType
        }
        return this
    }

    fun setIsModuleGuide(isGuide: Boolean): ResIdBean {
        this.isModuleGuide = isGuide
        return this
    }

    fun isModuleGuide(): Boolean {
        return isModuleGuide
    }

    companion object {

        val EXTRA_RES_ID = "EXTRA_RES_ID"

        val TS_TYPE_NORMAL = 3L
        val TS_TYPE_LOCAL = 0L
        val TS_TYPE_UCG = 2L

        val ICON_TYPE_PGC = "PGC"
        val ICON_TYPE_UGC = "UGC"

        fun newInstance(): ResIdBean {
            return ResIdBean()
        }

        fun newInstance(resid: ResIdBean?): ResIdBean {
            return if (resid == null) {
                ResIdBean()
            } else {
                ResIdBean(resid)
            }
        }
    }

}
