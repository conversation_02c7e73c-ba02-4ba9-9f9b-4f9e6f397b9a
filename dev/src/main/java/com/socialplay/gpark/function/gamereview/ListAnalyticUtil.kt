package com.socialplay.gpark.function.gamereview

import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager

/**
 * Created by bo.li
 * Date: 2022/4/26
 * Desc:
 */
object ListAnalyticUtil {
    private const val INVISIBLE_POSITION = -2
    private const val UNINITIALIZED_POSITION = -1

    fun isVisibleArrayInit(visiblePositionArray: IntArray): Boolean {
        val first = visiblePositionArray.firstOrNull() ?: return false
        val second = visiblePositionArray.lastOrNull() ?: return false
        return  first != UNINITIALIZED_POSITION && second != UNINITIALIZED_POSITION
    }

    fun getVisiblePositionArray(
        layoutManager: LinearLayoutManager,
        itemLocation: IntArray,
        rvLocation: IntArray,
        screenHeight: Int
    ): IntArray? {
        val firstVisiblePosition = getRealVisibleFirstPosition(layoutManager, itemLocation, rvLocation, screenHeight)
        val lastVisiblePosition = getRealVisibleLastPosition(layoutManager, itemLocation, rvLocation, screenHeight)
        if (firstVisiblePosition < 0 || lastVisiblePosition < 0 || firstVisiblePosition > lastVisiblePosition) {
            return null
        }
        return intArrayOf(firstVisiblePosition, lastVisiblePosition)
    }

    private fun getRealVisibleFirstPosition(
        layoutManager: LinearLayoutManager,
        itemLocation: IntArray,
        rvLocation: IntArray,
        screenHeight: Int
    ): Int {
        var firstVisiblePosition = layoutManager.findFirstVisibleItemPosition()
        var view = findLocationByPosition(firstVisiblePosition, itemLocation, layoutManager)?: return INVISIBLE_POSITION
        while (itemLocation[1] !in (rvLocation[1] - view.height)..screenHeight) {
            firstVisiblePosition ++
            view = findLocationByPosition(firstVisiblePosition, itemLocation, layoutManager)?: return INVISIBLE_POSITION
        }
        return firstVisiblePosition
    }

    private fun getRealVisibleLastPosition(
        layoutManager: LinearLayoutManager,
        itemLocation: IntArray,
        rvLocation: IntArray,
        screenHeight: Int
    ): Int {
        var lastVisiblePosition = layoutManager.findLastVisibleItemPosition()
        var view = findLocationByPosition(lastVisiblePosition, itemLocation, layoutManager)?: return INVISIBLE_POSITION
        while (itemLocation[1] !in (rvLocation[1] - view.height)..screenHeight) {
            lastVisiblePosition --
            view = findLocationByPosition(lastVisiblePosition, itemLocation, layoutManager)?: return INVISIBLE_POSITION
        }
        return lastVisiblePosition
    }

    private fun findLocationByPosition(firstVisiblePosition: Int, itemLocation: IntArray, layoutManager: LinearLayoutManager): View? {
        val view = layoutManager.findViewByPosition(firstVisiblePosition)
        view?.getLocationOnScreen(itemLocation)
        return view
    }
}