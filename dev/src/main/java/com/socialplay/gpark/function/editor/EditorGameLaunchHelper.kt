package com.socialplay.gpark.function.editor

import android.app.Application
import androidx.annotation.StringRes
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import com.meta.biz.ugc.local.EditorLocalHelper
import com.meta.biz.ugc.model.EditorTemplate
import com.meta.lib.mwbiz.MWBiz
import com.meta.lib.mwbiz.MWLifeCallback
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.GameDetailInfo
import com.socialplay.gpark.data.model.entity.RecentUgcGameEntity
import com.socialplay.gpark.function.analytics.kernel.PandoraInit
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.meta.biz.ugc.local.EditorLocalHelper.getTemplateUnzipFile
import com.meta.biz.ugc.model.EditorConfigJsonEntity
import com.socialplay.gpark.data.model.editor.UgcFormWorkArchiveData
import com.socialplay.gpark.function.mw.LoadGameResourceEventCallback
import com.socialplay.gpark.function.mw.launch.TSLaunch
import com.socialplay.gpark.function.mw.launch.bean.TSLaunchParams
import com.socialplay.gpark.function.mw.launch.exception.TSUserCancelledException
import com.socialplay.gpark.function.mw.launch.ui.TSLaunchFailedWrapper
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.util.extension.safeCancel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.koin.core.context.GlobalContext
import timber.log.Timber
import java.util.concurrent.atomic.AtomicBoolean

/**
 * Created by bo.li
 * Date: 2022/3/9
 * Desc: 编辑器游戏启动
 */
class EditorGameLaunchHelper(private var launchCallback: IEditorLaunchCallback?): LoadGameResourceEventCallback {
    private val metaRepository: IMetaRepository = GlobalContext.get().get()
    private val tsLaunch: TSLaunch by lazy { TSLaunch() }
    private var isHandlingClickStart = AtomicBoolean(false)
    private var launchingGame = AtomicBoolean(false)

    val context: Application = GlobalContext.get().get()
    var owner: LifecycleOwner? = null
    val observe = LifecycleEventObserver { _, event ->
        if (event == Lifecycle.Event.ON_DESTROY) {
            onDestroyHelper()
        }
    }
    val scope = MainScope()

    private var ugcTemplateJob: Job? = null

    override fun onCancel() {
        if (ugcTemplateJob?.isActive == true) {
            launchCallback?.onLaunchOver(
                LaunchOverResult(
                    null,
                    null,
                    null,
                    "",
                    false,
                    TSUserCancelledException(),
                    false
                )
            )
        }
        ugcTemplateJob?.safeCancel()
        ugcTemplateJob = null
        EditorLocalHelper.cancelLastCall()
    }

    fun init(owner: LifecycleOwner) {
        this.owner = owner
        owner.lifecycle.addObserver(observe)
    }

//    /**
//     * 启动普通TS游戏：角色编辑器、开放广场
//     */
//    fun startNormalTsGame(fragment: Fragment, gameInfo: GameDetailInfo, resId: ResIdBean) {
//        callBackOnCheck(gameInfo, null, null)
//        commonStartGame { mwReady, msg ->
//            if (mwReady) {
//                launchNormalTsGame(fragment, gameInfo, resId)
//            } else {
//                onLaunchEnd(gameInfo, null, null, msg, false)
//            }
//        }
//    }

    /**
     * 启动模板游戏
     */
    fun startTemplateGame(
        fragment: Fragment,
        item: EditorTemplate,
        resIdBean: ResIdBean
    ) {
        ugcTemplateJob?.safeCancel()
        ugcTemplateJob = scope.launch(Dispatchers.IO) {
            callBackOnCheckSuspendable(null, null, null, TYPE_TEMPLATE)
            if (item.gid.isNullOrEmpty() || item.fileUrl == null || item.gameIdentity.isNullOrEmpty()) {
                onLaunchEndSuspendable(null, null, null, R.string.template_information_error, false)
                ugcTemplateJob = null
                return@launch
            }
            if (PandoraToggle.isUgcBackup || item.type == EditorConfigJsonEntity.TYPE_MODULE) {
                val data = metaRepository.checkMaxCloud(item.type)
                if (data.succeeded) {
                    if (data.data == true) {
                        onLaunchEndSuspendable(
                            null,
                            null,
                            null,
                            R.string.ugc_work_amount_reach_limit,
                            false,
                            null,
                            true
                        )
                        ugcTemplateJob = null
                        return@launch
                    }
                } else {
                    onLaunchEndSuspendable(
                        null,
                        null,
                        null,
                        R.string.failed_get_ugc_work_upper_limit,
                        false,
                        null,
                        true
                    )
                    ugcTemplateJob = null
                    return@launch
                }
            }
            val file = EditorLocalHelper.downloadProjectFile(item)
            if (file != null) {
                val uuid = GlobalContext.get().get<MetaKV>().account.uuid
                val copyProjectFiles = withContext(Dispatchers.IO) {
                    EditorLocalHelper.generateProjectFile(
                        file,
                        EditorLocalHelper.getLocalUnzipFile(file.name),
                        uuid
                    )
                }
                if (copyProjectFiles == null) {
                    onLaunchEndSuspendable(
                        null,
                        null,
                        null,
                        R.string.launch_failed_click_to_retry,
                        false
                    )
                    ugcTemplateJob = null
                } else {
                    val editorConfigEntity = EditorLocalHelper.getEditorConfigEntity(
                        EditorLocalHelper.getJsonFile(copyProjectFiles)
                    )
                    // 判空迁移国内fix: 修复读取本地工程配置文件失败导致崩溃
                    if (editorConfigEntity != null) {
                        ugcTemplateJob = null
                        withContext(Dispatchers.Main) {
                            launchCallback?.onUgcTemplateCreated()
                            startLocalGame(
                                fragment,
                                editorConfigEntity.gid,
                                copyProjectFiles.path,
                                item.packageName ?: "",
                                editorConfigEntity.fileId ?: "",
                                resIdBean
                            )
                        }
                    } else {
                        onLaunchEndSuspendable(
                            null,
                            null,
                            copyProjectFiles.path,
                            R.string.failed_to_get_configuration_file,
                            false
                        )
                        ugcTemplateJob = null
                    }
                }
            } else {
                onLaunchEndSuspendable(null, null, null, R.string.download_failure, false)
                ugcTemplateJob = null
            }
        }
    }
    /**
     * 启动云存档游戏
     */
    fun startArchiveGame(
        fragment: Fragment,
        item: UgcFormWorkArchiveData?,
        resIdBean: ResIdBean
    ) {
        ugcTemplateJob?.safeCancel()
        ugcTemplateJob = scope.launch(Dispatchers.IO) {
            callBackOnCheckSuspendable(null, null, null, TYPE_TEMPLATE)
            if (item?.checkRes != true || item.archiveUrl.isNullOrEmpty() || item.sha1.isNullOrEmpty()) {
                onLaunchEndSuspendable(null, null, null, R.string.template_information_error, false)
                ugcTemplateJob = null
                return@launch
            }
            if (PandoraToggle.isUgcBackup) {
                val data = metaRepository.checkMaxCloud(item.type)
                if (data.succeeded) {
                    if (data.data == true) {
                        onLaunchEndSuspendable(
                            null,
                            null,
                            null,
                            R.string.ugc_work_amount_reach_limit,
                            false,
                            null,
                            true
                        )
                        ugcTemplateJob = null
                        return@launch
                    }
                } else {
                    onLaunchEndSuspendable(
                        null,
                        null,
                        null,
                        R.string.failed_get_ugc_work_upper_limit,
                        false,
                        null,
                        true
                    )
                    ugcTemplateJob = null
                    return@launch
                }
            }

            val file = EditorLocalHelper.checkAndDownloadArchiveFile(item.archiveUrl, item.sha1)
            Timber.d("startArchiveGame file:${file?.path}")
            if (file != null) {
                Timber.d("startArchiveGame getTemplateUnzipFile folder:${getTemplateUnzipFile(file).path}")
                val copyProjectFiles = withContext(Dispatchers.IO) {
                    EditorLocalHelper.copyProjectFiles(
                        file,
                        EditorLocalHelper.getLocalUnzipFile(file.name)
                    )
                }
                Timber.d("startArchiveGame getLocalUnzipFile:${EditorLocalHelper.getLocalUnzipFile(file.name).path}")
                Timber.d("startArchiveGame copyProjectFiles:${copyProjectFiles?.path}")
                if (copyProjectFiles == null) {
                    onLaunchEnd(null, null, null, R.string.launch_failed_click_to_retry, false)
                    ugcTemplateJob = null
                } else {
                    val editorConfigEntity = EditorLocalHelper.getEditorConfigEntity(
                        EditorLocalHelper.getJsonFile(copyProjectFiles)
                    )
                    Timber.d("startArchiveGame editorConfigEntity:${editorConfigEntity}")
                    // 判空迁移国内fix: 修复读取本地工程配置文件失败导致崩溃
                    if (editorConfigEntity != null) {
                        ugcTemplateJob = null
                        withContext(Dispatchers.Main) {
                            launchCallback?.onUgcTemplateCreated()
                            startLocalGame(
                                fragment,
                                editorConfigEntity.gid,
                                copyProjectFiles.path,
                                editorConfigEntity.packageName ?: "",
                                editorConfigEntity.fileId ?: "",
                                resIdBean,
                                templateArchiveId = item.archiveId
                            )
                        }
                    } else {
                        onLaunchEnd(
                            null,
                            null,
                            copyProjectFiles.path,
                            R.string.failed_to_get_configuration_file,
                            false
                        )
                        ugcTemplateJob = null
                    }
                }
            } else {
                onLaunchEnd(null, null, null, R.string.download_failure, false)
                ugcTemplateJob = null
            }
        }
    }

    /**
     * 启动本地游戏
     * @param parentPackageName 父模板的packageName
     */
    fun startLocalGame(
        fragment: Fragment,
        gid: String?,
        path: String?,
        parentPackageName: String,
        fileId: String,
        resIdBean: ResIdBean,
        ugcSlot: Long? = null,
        archiveId: String? = null,
        templateArchiveId: String? = null
    ) {
        callBackOnCheck(null, null, path)
        if (gid.isNullOrEmpty() || path.isNullOrEmpty()) {
            onLaunchEnd(null, null, path, R.string.editor_role_game_config_load_failed, false)
            return
        }
        commonStartGame { mwReady, msg ->
            if (mwReady) {
                launchLocalGame(
                    fragment,
                    gid,
                    path,
                    parentPackageName,
                    fileId,
                    resIdBean,
                    ugcSlot,
                    archiveId,
                    templateArchiveId
                )
                onLaunchEnd(null, null, path, null, true)
            } else {
                onLaunchEnd(null, null, path, msg, false)
            }
        }
    }

    /**
     * 启动UGC游戏
     * 从接口拿到较全信息可使用此方法提前保存数据库
     * @param convertor UGC游戏实体转换器
     */
    fun startUgcGame(
        fragment: Fragment,
        convertor: RecentUgcGameEntity.Convertor,
        resIdBean: ResIdBean
    ) {
        val entity = convertor.toMetaRecentUgcGameEntity()
        GlobalContext.get().get<IMetaRepository>().insertUgcPlayedGame(entity, true)
        startUgcGame(fragment, EditorUGCLaunchParams(entity.id, entity.packageName, entity.gameName ?:"", entity.gameIcon ?:"", entity.gameCode), resIdBean)
    }

    /**
     * 启动UGC游戏
     */
    fun startUgcGame(
        fragment: Fragment,
        ugcId: String,
        packageName: String?,
        gameName: String,
        gameCode: String?,
        resIdBean: ResIdBean,
    ) {
        startUgcGame(fragment, EditorUGCLaunchParams(ugcId, packageName, gameName, "", gameCode), resIdBean)
    }

    fun startUgcGame(fragment: Fragment, params: EditorUGCLaunchParams, resIdBean: ResIdBean) {
        callBackOnCheck(null, params.ugcId, null)
        if (params.packageName.isNullOrEmpty()) {
            onLaunchEnd(null, params.ugcId, null, R.string.editor_role_game_config_load_failed, false)
            return
        }
        commonStartGame { mwReady, msg ->
            if (mwReady) {
                launchUgcGame(fragment, params, resIdBean)
            } else {
                onLaunchEnd(null, params.ugcId, null, msg, false)
            }
        }
    }

    /**
     * 统一启动
     */
    private fun commonStartGame(callback: (Boolean, Int?) -> Unit) {
        if (isHandlingClickStart.get()) {
            callback(false, R.string.mgs_game_is_launching)
            return
        }
        onLaunchStart()
        val isMwReady = MWBiz.isAvailable()
        if (isMwReady) {
            // 引擎已经下载
            callback(true, null)
        } else {
            // 引擎未下载完
            observeMWDownload { success ->
                if (success) {
                    callback(true, null)
                } else if (!PandoraInit.checkEsVersion(context)) {
                    callback(false, R.string.opengl_es_tips)
                } else {
                    callback(false, R.string.engine_download_failure)
                }
            }
        }
    }

    /**
     * 监听MW下载进度
     */
    private fun observeMWDownload(callback: (success: Boolean) -> Unit) {
        owner?.let {
            MWLifeCallback.available.removeObserver(it)
            MWLifeCallback.available.observe(it, true) { data ->
                callback(data.first)
            }
        }
    }
//
//    /**
//     * 启动普通TS游戏
//     */
//    private fun launchNormalTsGame(fragment: Fragment, gameInfo: GameDetailInfo, resId: ResIdBean) {
//        if (launchingGame.get()) {
//            onLaunchEnd(gameInfo, null, null, R.string.launching, false)
//            return
//        }
//        launchingGame.set(true)
//
//        tsLaunch.onLaunchListener {
//            onLaunchGame {
//                callBackOnCheck(gameInfo, null, null)
//            }
//
//            onLaunchGameEnd { params, e ->
//                TSLaunchFailedWrapper.show(fragment, params, e)
//                onLaunchEnd(gameInfo, null, null, R.string.launch_failed_click_to_retry, e == null, e)
//            }
//        }
//        val resIdBean = resId.setGameId(gameInfo.id).setTsType(ResIdBean.TS_TYPE_NORMAL)
//        val params = TSLaunchParams(gameInfo, resIdBean)
//        tsLaunch.launch(context, params)
//    }

    /**
     * MW启动Ugc游戏
     */
    private fun launchUgcGame(fragment: Fragment, ugcParams: EditorUGCLaunchParams, resIdBean: ResIdBean) {
        if (launchingGame.get()) {
            onLaunchEnd(null, ugcParams.ugcId, null, R.string.launching, false)
            return
        }
        launchingGame.set(true)
        tsLaunch.onLaunchListener {
            onLaunchGameEnd { params, e ->
                TSLaunchFailedWrapper.show(fragment, params, e)
                onLaunchEnd(params.gameInfo, ugcParams.ugcId, null, R.string.launch_failed_click_to_retry, e==null, e)
            }
        }
        val gameInfo = tsLaunch.createTSGameDetailInfo(ugcParams.ugcId, ugcParams.packageName?:"", ugcParams.gameName, icon = ugcParams.icon)
        val resId = resIdBean.setGameId(ugcParams.ugcId).setTsType(ResIdBean.TS_TYPE_UCG).setGameCode(ugcParams.gameCode)
        val params = TSLaunchParams(gameInfo, resId).apply { isUgcGame = true }
        tsLaunch.launchUgc(context, params)
    }

    /**
     * MW启动本地游戏
     */
    private fun launchLocalGame(
        fragment: Fragment,
        gameId: String,
        path: String,
        packageName: String,
        fileId: String,
        resIdBean: ResIdBean,
        ugcSlot: Long? = null,
        archiveId: String? = null,
        templateArchiveId: String? = null
    ) {
        if (launchingGame.get()) {
            onLaunchEnd(null, null, path, R.string.launching, false)
            return
        }
        launchingGame.set(true)
        tsLaunch.onLaunchListener {
            onLaunchGameEnd { params, e ->
                TSLaunchFailedWrapper.show(fragment, params, e)
                onLaunchEnd(null, null, path, R.string.launch_failed_click_to_retry, e == null, e)
            }
        }
        //本地游戏，没有游戏名，使用包名作为游戏名称
        val gameInfo = tsLaunch.createTSGameDetailInfo(gameId, packageName, packageName)
        val resId = resIdBean.setGameId(gameId).setTsType(ResIdBean.TS_TYPE_LOCAL).setFileId(fileId).setPath(path)
        val params = TSLaunchParams(gameInfo, resId).apply {
            tsLocalPath = path
            ugcSlot?.let {
                custom["UGCSlot"] = it
            }
            custom["fileId"] = fileId
            archiveId?.let {
                custom["archiveId"] = it
            }
            templateArchiveId?.let {
                custom["templateArchiveId"] = it
            }
        }
        tsLaunch.launchLocal(context, params)
    }

    private fun onLaunchStart() {
        isHandlingClickStart.set(true)
    }

    private fun callBackOnCheck(gameInfo: GameDetailInfo?, ugcId: String?, path: String?, type: String? = null) {
        scope.launch(Dispatchers.Main) {
            launchCallback?.onChecking(gameInfo, ugcId, path, type)
        }
    }

    private suspend fun callBackOnCheckSuspendable(
        gameInfo: GameDetailInfo?,
        ugcId: String?,
        path: String?,
        type: String? = null
    ) {
        withContext(Dispatchers.Main) {
            launchCallback?.onChecking(gameInfo, ugcId, path, type)
        }
    }

    private fun onLaunchEnd(
        gameInfo: GameDetailInfo?,
        ugcId: String?,
        path: String?,
        @StringRes msgRes: Int?,
        success: Boolean,
        e: Throwable? = null,
        needGoMine: Boolean = false
    ) {
        isHandlingClickStart.set(false)
        launchingGame.set(false)
        scope.launch(Dispatchers.Main) {
            val msg = msgRes?.let { context.getString(it) }
            launchCallback?.onLaunchOver(
                LaunchOverResult(
                    gameInfo,
                    ugcId,
                    path,
                    msg,
                    success,
                    e,
                    needGoMine
                )
            )
        }
    }

    private suspend fun onLaunchEndSuspendable(
        gameInfo: GameDetailInfo?,
        ugcId: String?,
        path: String?,
        @StringRes msgRes: Int?,
        success: Boolean,
        e: Throwable? = null,
        needGoMine: Boolean = false
    ) {
        isHandlingClickStart.set(false)
        launchingGame.set(false)
        withContext(Dispatchers.Main) {
            val msg = msgRes?.let { context.getString(it) }
            launchCallback?.onLaunchOver(
                LaunchOverResult(
                    gameInfo,
                    ugcId,
                    path,
                    msg,
                    success,
                    e,
                    needGoMine
                )
            )
        }
    }

    companion object {
        const val TYPE_TEMPLATE = "type_template"
        const val TYPE_NORMAL = "type_normal"
    }

    fun onDestroyHelper() {
        launchCallback = null
        owner?.let {
            MWLifeCallback.available.removeObserver(it)
        }
        <EMAIL> = null
        owner?.lifecycle?.removeObserver(observe)
    }
}

data class EditorUGCLaunchParams(
    val ugcId: String,
    val packageName: String?,
    val gameName: String,
    val icon: String,
    val gameCode: String?,
)