package com.socialplay.gpark.function.deeplink.linkhandler

import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.deeplink.LinkHandleResult
import com.socialplay.gpark.function.deeplink.LinkHandler
import com.socialplay.gpark.function.deeplink.LinkHandlerChain
import com.socialplay.gpark.function.deeplink.MetaDeepLink
import com.socialplay.gpark.function.mw.lifecycle.AvatarGameTime

class GameReviewLinkHandler: LinkHandler {
    override fun handle(chain: LinkHandlerChain, data: LinkData): LinkHandleResult {
        val dataString = data.uri.getQueryParameter("data") ?: return LinkHandleResult.Failed("data is null")
        val arguments = BasicNavigateLinkHandler.decodeBundle(dataString)
        val gameId = arguments.getString(MetaDeepLink.PARAM_FROM_GAME_ID) ?: return LinkHandleResult.Failed("gameId is null")
        AvatarGameTime.check(gameId)
        return LinkHandleResult.Success
    }
}