package com.socialplay.gpark.function.im

import android.view.View
import android.widget.TextView
import com.meta.box.biz.friend.internal.model.FriendStatus
import com.meta.box.biz.friend.internal.model.toLocalStatus
import com.socialplay.gpark.R
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setTextWithArgs
import com.socialplay.gpark.util.extension.visible

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/12/04
 *     desc   :
 * </pre>
 */
object FriendStatusHelper {

    const val SOURCE_CONTACT_LIST = 1
    const val SOURCE_CHAT = 2

    fun updateStatus(
        uuid: String?,
        status: FriendStatus?,
        tvStatus: TextView,
        source: Int,
        vOnline: View
    ) {
        if (status == null) {
            vOnline.gone()
            tvStatus.gone()
        } else {
            when (status.toLocalStatus()) {
                FriendStatus.OFFLINE_NOT_PLAY_GAME_IN_RECENTLY,
                FriendStatus.OFFLINE_PLAYED_GAME_IN_RECENTLY -> {
                    vOnline.visible()
                    vOnline.setBackgroundResource(R.drawable.sp_offline_dot)
                    tvStatus.visible()
                    tvStatus.setText(R.string.offline_cap)
                    when (source) {
                        SOURCE_CONTACT_LIST -> {
                            Analytics.track(
                                EventConstants.FRIENDS_LIST_USER_STATE,
                                EventParamConstants.KEY_STATE to 2,
                                EventParamConstants.KEY_USERID to uuid.orEmpty()
                            )
                        }

                        SOURCE_CHAT -> {
                            Analytics.track(
                                EventConstants.IM_CHAT_USER_STATE,
                                EventParamConstants.KEY_STATE to 2,
                                EventParamConstants.KEY_USERID to uuid.orEmpty()
                            )
                        }
                    }
                }

                FriendStatus.ONLINE -> {
                    vOnline.visible()
                    vOnline.setBackgroundResource(R.drawable.sp_online_dot)
                    tvStatus.visible()
                    tvStatus.setText(R.string.online_status)
                    when (source) {
                        SOURCE_CONTACT_LIST -> {
                            Analytics.track(
                                EventConstants.FRIENDS_LIST_USER_STATE,
                                EventParamConstants.KEY_STATE to 1,
                                EventParamConstants.KEY_USERID to uuid.orEmpty()
                            )
                        }

                        SOURCE_CHAT -> {
                            Analytics.track(
                                EventConstants.IM_CHAT_USER_STATE,
                                EventParamConstants.KEY_STATE to 1,
                                EventParamConstants.KEY_USERID to uuid.orEmpty()
                            )
                        }
                    }
                }

                FriendStatus.PLAYING_GAME -> {
                    vOnline.visible()
                    vOnline.setBackgroundResource(R.drawable.sp_online_dot)
                    tvStatus.visible()
                    tvStatus.setTextWithArgs(
                        R.string.playing_formatted,
                        status.gameStatus?.gameName
                    )
                    when (source) {
                        SOURCE_CONTACT_LIST -> {
                            Analytics.track(
                                EventConstants.FRIENDS_LIST_USER_STATE,
                                EventParamConstants.KEY_STATE to 3,
                                EventParamConstants.KEY_USERID to uuid.orEmpty()
                            )
                        }

                        SOURCE_CHAT -> {
                            Analytics.track(
                                EventConstants.IM_CHAT_USER_STATE,
                                EventParamConstants.KEY_STATE to 3,
                                EventParamConstants.KEY_USERID to uuid.orEmpty()
                            )
                        }
                    }
                }

                else -> {
                    vOnline.gone()
                    tvStatus.gone()
                }
            }
        }
    }
}