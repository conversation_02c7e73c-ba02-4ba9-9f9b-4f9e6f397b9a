package com.socialplay.gpark.function.umw.impl.ts

import com.meta.biz.ugc.UGCProtocolBiz
import com.meta.biz.ugc.listener.IMWFunction
import com.meta.lib.mwbiz.MWBizBridge
import com.meta.lib.mwbiz.bean.bridge.SendMsg
import com.meta.verse.lib.MetaVerseCore
import com.socialplay.gpark.function.umw.GameType
import com.socialplay.gpark.function.umw.UMW
import com.socialplay.gpark.function.umw.impl.AbsUMWImpl

/**
 * TS游戏的UMW实现
 */
abstract class UMWTSImpl : AbsUMWImpl() {

    init {
        defineProtocolBasicFunc()
    }

    override val ts: UMW.ITsInterface get() = TsInterfaceImpl(this)

    override val avatar: UMW.IAvatarInterface? get() = null

    private fun defineProtocolBasicFunc() {
        UGCProtocolBiz.setFuncListener(object : IMWFunction {
            override fun callUE(json: String): String {
                return MetaVerseCore.bridge().callUE(json)
            }

            override fun registerAction(action: String) {
                MWBizBridge.registerMWMsgAction(action)
            }
        })
    }

    override fun getGameType(): GameType {
        return GameType.TS
    }

    override fun callUE(msg: SendMsg) {
        MWBizBridge.callUE(msg)
    }

    override fun currentGameId(): String {
        return MWBizBridge.currentGameId()
    }

    override fun currentGameName(): String {
        return MWBizBridge.currentGameName()
    }

    override fun currentGamePkg(): String {
        return MWBizBridge.currentGamePkg()
    }
}