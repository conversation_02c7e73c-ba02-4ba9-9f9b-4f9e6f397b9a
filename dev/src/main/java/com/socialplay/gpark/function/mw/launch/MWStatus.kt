package com.socialplay.gpark.function.mw.launch

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import kotlinx.coroutines.flow.Flow

/**
 * <AUTHOR>
 * @date 2022/11/22
 */
object MWStatus {

    //引擎是否可用
    private val engineAvailable = MutableLiveData<Boolean>()

    fun setEngineAvailable(available: Boolean) {
        engineAvailable.postValue(available)
    }

    fun engineAvailableFlow(): Flow<Boolean> = engineAvailable.asFlow()

}