package com.socialplay.gpark.function.deeplink

class LinkHandlerChain {
    private val handlers: MutableList<LinkHandler> = mutableListOf()

    private var currentIndex: Int = 0

    internal fun reset() {
        this.currentIndex = 0
    }

    internal fun addHandler(linkHandler: LinkHandler) {
        this.handlers.add(linkHandler)
    }

    internal fun removeHandler(linkHandler: LinkHandler) {
        this.handlers.remove(linkHandler)
    }

    /**
     * @param [source] 来源 [com.socialplay.gpark.function.deeplink.LinkData]
     * @return [Pair<Boolean, String?>] [处理成功, 原因]
     */
    fun handle(data: LinkData): LinkHandleResult {
        val index = currentIndex++
        if (index >= handlers.size) {
            return LinkHandleResult.Failed("no fit handler")
        }

        val linkHandler = handlers[index]
        return linkHandler.handle(this, data)
    }
}

sealed class LinkHandleResult {
    data object Success: LinkHandleResult()
    data class Failed(val reason: String?): LinkHandleResult()

    val succeeded: Boolean
        get() = this is Success

    val failedReason: String?
        get() = (this as? Failed)?.reason

}