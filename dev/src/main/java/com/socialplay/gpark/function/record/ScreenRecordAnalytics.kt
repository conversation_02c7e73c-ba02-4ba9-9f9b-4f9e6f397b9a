package com.socialplay.gpark.function.record

import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants

/**
 * @des:悬浮球游戏助手游戏录屏功能统计
 * @author: li<PERSON><PERSON><PERSON>
 * @date: 2022/1/12 15:41
 */
object ScreenRecordAnalytics {
    const val PARAM_GRANTED = "granted"
    const val PARAM_DENIED = "denied"
    const val PARAM_STORAGE_FROM_PERMISSION_APPLY = "storage_permission"
    const val PARMA_VOICE_RECORD_FROM_PERMISSION_APPLY = "voice_record_permission"

    /**
     *开始 申请权限（sd卡，录制音频)
     */
    fun eventStartApplyPermission(gameId: String, from: String) {
        Analytics.track(EventConstants.EVENT_GAME_RECORD_START_APPLY_PERMISSION) {
            put("gameid", gameId)
            put("apply_from", from)
        }
    }

    /**
     * 申请权限（sd卡，录制音频)结果
     */
    fun eventApplyPermissionResult(gameId: String, from: String, result: String) {
        Analytics.track(EventConstants.EVENT_GAME_RECORD_APPLY_PERMISSION_RESULT) {
            put("gameid", gameId)
            put("result", result)
            put("apply_from", from)
        }
    }

    /**
     *开始 申请用户录屏授权
     */
    fun eventStartApplyUserPermit(gameId: String) {
        Analytics.track(EventConstants.EVENT_GAME_RECORD_START_APPLY_USER_PERMIT) {
            put("gameid", gameId)
        }
    }

    /**
     * 申请录屏用户授权结果
     */
    fun eventApplyUserPermitResult(gameId: String, result: String) {
        Analytics.track(EventConstants.EVENT_GAME_RECORD_APPLY_USER_PERMIT_RESULT) {
            put("gameid", gameId)
            put("result", result)
        }
    }

    /**
     * 录屏倒计时弹窗的显示
     */
    fun eventCountDownDialogShow(gameId: String) {
        Analytics.track(EventConstants.EVENT_GAME_RECORD_COUNT_DOWN_SHOW) {
            put("gameid", gameId)
        }
    }


    fun eventRecordViewClick(gameId: String) {
        Analytics.track(EventConstants.EVENT_GAME_RECORD_RECORD_VIEW_CLICK) {
            put("gameid", gameId)
        }
    }

    fun eventRecordFinishDialogShow(gameId: String) {
        Analytics.track(EventConstants.EVENT_GAME_RECORD_FINISH_DIALOG_SHOW) {
            put("gameid", gameId)
        }
    }

    fun eventRecordFinishDialogClose(gameId: String) {
        Analytics.track(EventConstants.EVENT_GAME_RECORD_FINISH_DIALOG_CLOSE) {
            put("gameid", gameId)
        }
    }


    fun eventStartRecordFailed(reason: String) {
        Analytics.track(EventConstants.EVENT_GAME_RECORD_START_RECORD_FAILED) {
            put("reason", reason)
        }
    }

    fun eventStopRecordFailed(reason: String) {
        Analytics.track(EventConstants.EVENT_GAME_RECORD_STOP_RECORD_FAILED) {
            put("reason", reason)
        }
    }

    fun eventStartRecordSuccess(gameId: String) {
        Analytics.track(EventConstants.EVENT_GAME_RECORD_START_RECORD_SUCCESS) {
            put("gameid", gameId)
        }
    }

    fun eventStopRecordSuccess(gameId: String) {
        Analytics.track(EventConstants.EVENT_GAME_RECORD_STOP_RECORD_SUCCESS) {
            put("gameid", gameId)
        }
    }

    fun eventSaveRecordFailed(reason: String) {
        Analytics.track(EventConstants.EVENT_GAME_RECORD_SAVE_RECORD_FILE_FAILED) {
            put("reason", reason)
        }
    }

    fun eventSaveRecordSuccess() {
        Analytics.track(EventConstants.EVENT_GAME_RECORD_SAVE_RECORD_FILE_SUCCESS)
    }

    fun eventRecordVoiceOpen(gameId: String) {
        Analytics.track(EventConstants.EVENT_GAME_RECORD_VOICE_OPEN) {
            put("gameid", gameId)
        }
    }

    fun eventRecordVoiceClose(gameId: String) {
        Analytics.track(EventConstants.EVENT_GAME_RECORD_VOICE_CLOSE) {
            put("gameid", gameId)
        }
    }

    fun eventClickShareRecordEndDialog(gameId: String, platform: String, success: Boolean) {
        val map = mapOf(
            "gameid" to gameId,
            "platform" to platform,
            "result" to (if (success) "1" else "0")
        )
        Analytics.track(EventConstants.EVENT_GAME_RECORD_FINISH_DIALOG_SHARE_CLICK, map)
    }

    fun eventClickShareMyRecord(gameId: String, platform: String, success: Boolean) {
        val map = mapOf(
            "gameid" to gameId,
            "platform" to platform,
            "result" to (if (success) "1" else "0")
        )
        Analytics.track(EventConstants.EVENT_VIDEO_SHARE_DIALOG_CLICK, map)
    }
}