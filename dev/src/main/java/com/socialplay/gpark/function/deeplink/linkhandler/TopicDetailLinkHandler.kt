package com.socialplay.gpark.function.deeplink.linkhandler

import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.deeplink.LinkHandleResult
import com.socialplay.gpark.function.deeplink.LinkHandler
import com.socialplay.gpark.function.deeplink.LinkHandlerChain
import com.socialplay.gpark.function.deeplink.MetaDeepLink
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.util.extension.ifNullOrBlank
import timber.log.Timber

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/10/09
 *     desc   :
 * </pre>
 */
class TopicDetailLinkHandler : LinkHandler {
    override fun handle(chain: LinkHandlerChain, data: LinkData): LinkHandleResult {
        Timber.d("metadeeplink TopicDetailLinkHandler handle uri:%s", data.uri)

        val tagId = data.uri.getQueryParameter(MetaDeepLink.PARAM_TOPIC_ID)?.toLongOrNull()
            ?: return LinkHandleResult.Failed("invalid topic id")
        val source = data.uri.getQueryParameter(MetaDeepLink.PARAM_SOURCE)
        val tab = data.uri.getQueryParameter(MetaDeepLink.PARAM_TAB)?.toIntOrNull() ?: -1

        MetaRouter.Post.topicDetail(
            data.navHost,
            PostTag(tagId, null),
            source.ifNullOrBlank { data.source.ifNullOrBlank { "scheme" } },
            null,
            initTab = tab
        )
        return LinkHandleResult.Success
    }
}