package com.socialplay.gpark.function.mw

import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.socialplay.gpark.function.mw.launch.TSLaunch
import com.socialplay.gpark.function.mw.launch.bean.TSLaunchParams
import kotlinx.coroutines.launch

class TSLaunchWrapper(private val fragment: Fragment) {

    private val tsLaunch by lazy { TSLaunch() }

    private val gameScenes: MWGameStartScenes by lazy { MWGameStartScenes(fragment) }
    private var onLaunchSuccess: ((TSLaunchParams) -> Unit)? = null
    private var onLaunchFailed: ((TSLaunchParams, Throwable) -> Unit)? = null

    init {
        initTSLaunch(fragment)
    }

    fun listenLaunchSuccess(block: (TSLaunchParams) -> Unit) {
        onLaunchSuccess = block
    }

    fun listenLaunchFailed(block: (TSLaunchParams, Throwable) -> Unit) {
        onLaunchFailed = block
    }

    fun callLaunch(block: TSLaunch.() -> Unit) {
        block.invoke(tsLaunch)
    }

    private fun initTSLaunch(fragment: Fragment) {
        tsLaunch.onLaunchListener(fragment.viewLifecycleOwner) {
            onLaunchGame {
                gameScenes.show()
            }
            onLaunchGameEnd { params, e ->
                gameScenes.hide()
                if (e != null) {
                    fragment.viewLifecycleOwner.lifecycleScope.launch {
                        onLaunchFailed?.invoke(params, e)
                    }
                } else {
                    onLaunchSuccess?.invoke(params)
                }
            }
        }
    }

}