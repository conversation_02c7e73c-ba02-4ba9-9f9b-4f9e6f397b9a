package com.socialplay.gpark.function.mw.launch.setp

import android.content.Context
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.function.mw.launch.bean.TSLaunchParams
import org.koin.core.context.GlobalContext

/**
 * xingxiu.hou
 * 2022/10/25
 */
class TSGameProcessConfigStep : ITSGameProcessStep {

    private val metaKV: MetaKV by lazy { GlobalContext.get().get() }
    private val metaRepository by lazy { GlobalContext.get().get<IMetaRepository>() }

    override suspend fun process(context: Context, params: TSLaunchParams) {
        val gameId = params.gameId
        val packageName = params.packageName
        if (params.save2PlayedDB()) { //某些游戏只需要保存资源位信息就可以
            if (metaKV.download.getLastDownloadStateTimeStamp(gameId, packageName) <= 0) {
                metaKV.download.saveLastDownloadStateTimestamp(gameId, packageName)
            }
            metaRepository.updateMyGameInfo(params.gameInfo, 1.0F)
        } else {
            metaRepository.deleteMyPlayedGame(gameId)
        }
        if (params.resIdBean.getGameVersionName().isNullOrEmpty()) {
            params.resIdBean.setGameVersionName(params.getGameVersionName())
        }
        metaKV.analytic.saveLaunchResIdBean(params.packageName, params.resIdBean)
        metaKV.analytic.saveLaunchResIdBeanWithId(params.gameId, params.resIdBean)
        metaKV.tsKV.saveTsGameExpand(params.gameId, params.getGameExpand())
    }

}
