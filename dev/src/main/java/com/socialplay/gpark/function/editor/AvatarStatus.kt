package com.socialplay.gpark.function.editor

import com.meta.biz.ugc.model.GameTransform

/**
 *     author : wei.zhu
 *     e-mail : <EMAIL>
 *     time   : 2023/06/08
 *     desc   :
 */
data class AvatarStatus(
    val fakeProgressNeeded: Boolean,
    val status: String,
    val opacityData: String? = null,) {

    fun isPortrait(): <PERSON>ole<PERSON> {
        return this.status == GameTransform.STATUS_ROLE_VIEW
    }

    fun isLandscape(): Boolean {
        return !isPortrait()
    }
}