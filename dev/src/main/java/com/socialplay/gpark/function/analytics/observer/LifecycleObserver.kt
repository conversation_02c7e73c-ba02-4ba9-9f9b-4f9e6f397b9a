package com.socialplay.gpark.function.analytics.observer

import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.OnLifecycleEvent
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import org.koin.core.context.GlobalContext
import java.util.*


/**
 *  <AUTHOR> taiyuan
 *  time   : 2021/5/25
 *  desc   : apptime时长统计相关
 */
class LifecycleObserver(owner: LifecycleOwner, private val pageName: String) : LifecycleObserver {
    private var resumeTime = 0L

    init {
        owner.lifecycle.addObserver(this)
    }

    private val metaKV by lazy { GlobalContext.get().get<MetaKV>() }

    @OnLifecycleEvent(Lifecycle.Event.ON_RESUME)
    fun onResume() {
        resumeTime = System.currentTimeMillis()
        //页面展示
        Analytics.track(EventConstants.EVENT_SHOW_PAGE){
            put("pageName", pageName)
        }
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_PAUSE)
    fun onPause() {
        metaKV.analytic.setFirstAppOpenTime()

        val duration = System.currentTimeMillis() - resumeTime
        Analytics.track(EventConstants.EVENT_APP_PAGE_TIME){
            put("playtime", duration)
            put("pagename", pageName)
        }

        // 上报 次留 3日留存 7日留存
        val appDayCount = metaKV.analytic.getAppDayCount()

        val analytics = listOf(
            2 to EventConstants.EVENT_AF_APP_TIME_1,
            3 to EventConstants.EVENT_AF_APP_TIME_2,
            4 to EventConstants.EVENT_AF_APP_TIME_3,
            8 to EventConstants.EVENT_AF_APP_TIME_7,
        )

        analytics.forEach {
            val day = it.first
            val event = it.second

            if (appDayCount == day.toLong() && !metaKV.analytic.isDayCountTracked(day)) {
                Analytics.track(event)
                metaKV.analytic.setDayCountTracked(day)
            }
        }
    }
}