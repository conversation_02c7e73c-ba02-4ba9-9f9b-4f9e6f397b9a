package com.socialplay.gpark.function.mw

import android.app.Activity
import android.content.Context
import android.content.pm.ActivityInfo
import android.os.Build
import android.os.SystemClock
import com.bin.cpbus.CpEventBus
import com.google.gson.reflect.TypeToken
import com.luck.picture.lib.config.PictureMimeType
import com.meta.biz.ugc.local.EditorLocalHelper
import com.meta.biz.ugc.model.AndroidCommonResult
import com.meta.biz.ugc.model.DuplicateImageCallbackMsg
import com.meta.biz.ugc.model.DuplicateImageMsg
import com.meta.biz.ugc.model.GameCommonFeature
import com.meta.biz.ugc.model.SaveImage2Gallery
import com.meta.biz.ugc.model.SaveImage2GalleryCallbackMsg
import com.meta.biz.ugc.protocol.UGCProtocolSender
import com.meta.biz.ugc.protocol.constants.ProtocolSendConstant
import com.meta.biz.ugc.util.simJson
import com.meta.lib.mwbiz.MWBizBridge
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.code
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.BaseAccountInteractor
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor
import com.socialplay.gpark.data.interactor.LifecycleInteractor
import com.socialplay.gpark.data.interactor.PublishPostInteractor
import com.socialplay.gpark.data.interactor.UploadFileInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.mapper.MetaMapper
import com.socialplay.gpark.data.model.account.UpdatePrivacySettingsEvent
import com.socialplay.gpark.data.model.editor.CommonToggleInfo
import com.socialplay.gpark.data.model.event.InGameOpenWebEvent
import com.socialplay.gpark.data.model.editor.RoleStyleRefreshEvent
import com.socialplay.gpark.data.model.guide.UgcModuleGuideEvent
import com.socialplay.gpark.data.model.post.CommonPostPublishReceiveEvent
import com.socialplay.gpark.data.model.post.CommonPostPublishSendEvent
import com.socialplay.gpark.data.model.post.MomentLocalTSStartUp
import com.socialplay.gpark.data.model.post.PostMomentCard
import com.socialplay.gpark.data.model.share.DirectSharePlatforms
import com.socialplay.gpark.data.model.share.ShareRawData
import com.socialplay.gpark.data.model.share.ShareRoleScreenshotData
import com.socialplay.gpark.data.model.share.ShareRoleScreenshotEvent
import com.socialplay.gpark.data.model.share.UserShareInfo
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.download.DownloadFileProvider
import com.socialplay.gpark.function.exoplayer.SharedVideoPlayerControllerInteractor
import com.socialplay.gpark.function.mw.feature.GameWebViewFeature
import com.socialplay.gpark.function.mw.lifecycle.PlotChoiceFriendsLifecycle
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.function.share.platform.ShareHelper
import com.socialplay.gpark.function.share.platform.SystemShare
import com.socialplay.gpark.ui.main.MainActivity
import com.socialplay.gpark.ui.permission.TSGamePermission
import com.socialplay.gpark.usecase.FinishShareOCVideoTaskUseCase
import com.socialplay.gpark.ui.share.GlobalShareDialog
import com.socialplay.gpark.ui.share.role.ShareRoleScreenshotsDialogV2
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.MediaStoreUtils
import com.socialplay.gpark.util.UriUtil
import com.socialplay.gpark.util.WebUtil
import com.socialplay.gpark.util.extension.registerHermes
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.ifNullOrEmpty
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.single
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.Subscribe
import org.koin.core.context.GlobalContext
import timber.log.Timber
import java.io.File

/**
 * Created by bo.li
 * Date: 2022/11/22
 * Desc: 处理 游戏->客户端的协议
 */
object GameCommonFeatureResolver : CoroutineScope by CoroutineScope(Dispatchers.IO) {
    private val metaRepository: com.socialplay.gpark.data.IMetaRepository by lazy {
        GlobalContext.get().get()
    }
    private val metaMapper: MetaMapper by lazy { GlobalContext.get().get() }
    private val publishPostInteractor: PublishPostInteractor by lazy { GlobalContext.get().get() }
    private val metaKV: MetaKV by lazy { GlobalContext.get().get() }

    private val requestId = "GameCommonFeatureResolver_${SystemClock.elapsedRealtime()}"
    private var job: Job? = null
    private var suspendJumpPost: (() -> Unit)? = null

    init {
        registerHermes()
    }

    /**
     * 主进程
     */
    fun addHostGameCommonReceiver(
        context: Context,
        message: GameCommonFeature,
        messageId: Int,
        currentGameId: String,
        currentGamePkg: String
    ) {
        when (message.feature) {
            GameCommonFeature.FEAT_SHARE -> {
                message.share(context, messageId, currentGameId, currentGamePkg, true)
            }

            GameCommonFeature.FEATURE_GET_ROLE_TOGGLE -> {
                message.handleRoleToggle(messageId)
            }

            GameCommonFeature.FEATURE_UPLOAD_FILE -> {
                uploadMWFile(
                    message.gameId,
                    messageId,
                    runCatching { message.params?.get("filePath") as? String }.getOrNull(),
                    message.params?.get("bizCode") as? String
                )
            }

            MWFeatureSupport.PUBLISH_POST_GPARK -> {
                sendPublishPostEventRequest(message, messageId, true)
            }

            GameCommonFeature.FEATURE_UPDATE_PRIVACY_SETTINGS -> {
                message.updatePrivacySettings()
            }

            GameCommonFeature.FEATURE_JUMP_WEB -> {
                jumpWeb(context, message.gameId, messageId, message.params, true)
            }

            GameCommonFeature.FEATURE_STYLE_COMMUNITY_OUTFIT_UPDATE -> {
                message.styleCommunityOutfitUpdate()
            }

            GameCommonFeature.FEATURE_GET_SHARE_PLATFORMS -> {
                message.getSharePlatforms(messageId)
            }

            GameCommonFeature.FEATURE_JUMP_WEB_PRE_LOAD -> {
                GameWebViewFeature.onPreLoadWebView(message)
            }

            GameCommonFeature.FEATURE_MW_TRANS_WEB -> {
                GameWebViewFeature.mwTransToWeb(message)
            }

            GameCommonFeature.FEATURE_COPY_ROLE_SCREENSHOT -> {
                message.copyRoleScreenshot(messageId)
            }
        }
    }

    /**
     * 游戏进程
     */
    fun addGameCommonReceiver(
        context: Context,
        message: GameCommonFeature,
        messageId: Int,
        currentGameId: String,
        currentGamePkg: String,
        plotLifecycle: PlotChoiceFriendsLifecycle,
        resIdBean: ResIdBean,
        exitCaller: MWGameExitCaller
    ) {
        when (message.feature) {
            GameCommonFeature.FEAT_SHARE -> {
                message.share(context, messageId, currentGameId, currentGamePkg, false)
            }

            MWFeatureSupport.PUBLISH_POST_GPARK -> {
                sendPublishPostEventRequest(message, messageId, false)
            }

            GameCommonFeature.FEAT_LOCAL_GAME_CONFIG -> {
                fetchLocalGameConfig(message.gameId, resIdBean, messageId)
            }

            GameCommonFeature.FEATURE_GALLERY_SAVE -> {
                launch {
                    val canSave = checkSave2Gallery(context)
                    if (canSave) {
                        message.gallerySave(context, messageId)
                    }
                }
            }

            GameCommonFeature.FEATURE_PLOT_CHOOSE_FRIEND -> {
                message.plotChooseFriend(plotLifecycle, messageId)
            }

            GameCommonFeature.FEATURE_PLOT_RECORD_SHOW_LOADING -> {
                message.showRecordLoading(plotLifecycle)
            }

            GameCommonFeature.FEATURE_PLOT_RECORD_CLOSE_LOADING -> {
                message.closeRecordLoading(plotLifecycle)
            }
            GameCommonFeature.FEATURE_UPLOAD_FILE -> {
                uploadMWFile(
                    message.gameId,
                    messageId,
                    runCatching { message.params?.get("filePath") as? String }.getOrNull(),
                    message.params?.get("bizCode") as? String
                )
            }

            GameCommonFeature.FEATURE_JUMP_WEB -> {
                jumpWeb(context, message.gameId, messageId, message.params, false)
            }

            GameCommonFeature.FEATURE_CHOOSE_IMAGE -> {
                message.plotChooseImage(messageId, plotLifecycle)
            }

            GameCommonFeature.FEATURE_STYLE_COMMUNITY_OUTFIT_UPDATE -> {
                message.styleCommunityOutfitUpdate()
            }

            GameCommonFeature.FEATURE_GET_SHARE_PLATFORMS -> {
                message.getSharePlatforms(messageId)
            }

            GameCommonFeature.FEATURE_JUMP_WEB_PRE_LOAD -> {
                GameWebViewFeature.onPreLoadWebView(message)
            }

            GameCommonFeature.FEATURE_MW_TRANS_WEB -> {
                GameWebViewFeature.mwTransToWeb(message)
            }

            GameCommonFeature.FEATURE_NOTIFY_EVENT -> {
                message.notifyEvent(messageId, exitCaller)
            }
        }
    }

    fun GameCommonFeature.handleRoleToggle(messageId: Int) {
        MainScope().launch {
            callUECommonResult(
                gameId,
                messageId,
                AndroidCommonResult.FEATURE_GET_ROLE_TOGGLE,
                hashMapOf(
                    "shareShot" to CommonToggleInfo(PandoraToggle.avatarShare)
                )
            )
        }
    }

    fun callUECommonResult(
        gameId: String,
        messageId: Int,
        feature: String,
        params: HashMap<String, Any?>,
    ) {
        UGCProtocolSender.sendProtocol(
            ProtocolSendConstant.PROTOCOL_CLIENT_GAME_RESULT,
            messageId,
            AndroidCommonResult(
                feature,
                gameId,
                params
            ).getDataMapPackedResult()
        )
    }

    private fun uploadMWFile(gameId: String, messageId: Int, filePath: String?, bizCode: String?) {
        GlobalScope.launch(Dispatchers.IO) {
            runCatching {
                filePath ?: throw CommonFeatureException(
                    1001,
                    "src file not exist or path is empty"
                )
                val srcFile = File(filePath)
                if (filePath.isEmpty() || !srcFile.exists()) {
                    throw CommonFeatureException(1001, "src file not exist or path is empty")
                }
                val result = GlobalContext.get().get<UploadFileInteractor>()
                    .uploadSingle(bizCode?:UploadFileInteractor.BIZ_CODE_COMMUNITY, srcFile).single()
                if (result.succeeded && !result.data?.url.isNullOrEmpty()) {
                    val data = mapOf<String, String>(
                        "first" to (result.data?.filePath ?: ""),
                        "second" to (result.data?.url ?: "")
                    )
                    // 发协议
                    callUECommonResult(
                        gameId,
                        messageId,
                        GameCommonFeature.FEATURE_UPLOAD_FILE,
                        hashMapOf(
                            "code" to 200,
                            "data" to data
                        )
                    )
                } else {
                    throw CommonFeatureException(
                        result.code ?: 1004,
                        result.message ?: "invoke uploadSingle error"
                    )
                }
            }.getOrElse {
                // 发协议
                val code = if (it is CommonFeatureException) it.errorCode else 1000
                callUECommonResult(
                    gameId,
                    messageId,
                    GameCommonFeature.FEATURE_UPLOAD_FILE,
                    hashMapOf(
                        "code" to code,
                        "data" to null,
                        "errMsg" to it.message
                    )
                )
            }
        }
    }

    private fun sendPublishPostEventRequest(message: GameCommonFeature, messageId: Int, isHost: Boolean) {
        if (!MWFeatureSupport.isSupportFeature(MWFeatureSupport.PUBLISH_POST_GPARK, messageId, isHost)) return
        val params = message.params ?: return
        val method = params["method"] as? String
        if (method.isNullOrBlank()) return
        val requestId = params["requestId"] as? String ?: ""
        val gameId = message.gameId
        CpEventBus.post(
            CommonPostPublishSendEvent(
                requestId,
                method,
                gameId,
                publishPostInteractor.processName,
                data = params["data"]
            )
        )
    }

    fun relayPublishPostEventResult(event: CommonPostPublishReceiveEvent) {
        callUECommonResult(
            event.gameId,
            0,
            GameCommonFeature.FEATURE_PUBLISH_POST,
            hashMapOf(
                "code" to event.code,
                "requestId" to event.requestId,
                "method" to event.method,
                "data" to event.data,
                "msg" to event.msg
            )
        )
    }

    //分享
    private fun GameCommonFeature.share(
        context: Context,
        messageId: Int,
        currentGameId: String,
        currentGamePkg: String,
        isHost: Boolean
    ) {
        val shareModule = params?.get("shareModule").toString()
        val shareScene = params?.get("shareScene").toString()
        val templateId = params?.get("templateid")?.toString()?.toFloat()?.toInt()
        val extra = params?.get("extrafig")?.toString()
        val tsLocalStartUp = MomentLocalTSStartUp.fromJson(extra ?: "")
        when (shareModule) {
            "community" -> {
                if (isHost) return
                when (shareScene) {
                    "video", "image" -> {
                        val customCacheKey = if (shareScene == "video") prepareVideoCache() else null
                        val enableOutfitShare = params?.get("enableOutfitShare")?.toString()?.toBoolean() == true
                        communityShareSingleMedia(
                            context,
                            currentGameId,
                            currentGamePkg,
                            templateId,
                            customCacheKey,
                            enableOutfitShare,
                            templateId != null,
                            listOf(
                                PostMomentCard(
                                    resourceType = PostMomentCard.RES_TYPE_MOMENT,
                                    resourceValue = tsLocalStartUp.listTemplateId,
                                    templateId = tsLocalStartUp.listTemplateId,
                                    gameId = currentGameId,
                                    extendParams = extra
                                )
                            )
                        )
                    }
                }
            }
            "system" -> {
                if (isHost) return
                Analytics.track(EventConstants.EVENT_MOMENTS_SHARE) {
                    templateId?.also { put("templateid", it) }
                }
                when (shareScene) {
                    "video" -> systemShareWithPlot(context, true)
                    "image" -> systemShareWithPlot(context, false)
                }
            }

            "ts" -> {
                if (isHost) return
                when (shareScene) {
                    "ocMoment" -> {
                        shareOcMoment(context)
                    }
                }
            }

            "common" -> {
                when (shareScene) {
                    "direct" -> {
                        LifecycleInteractor.activity?.let {
                            launch {
                                ShareHelper.directShare(it, this@share, messageId, currentGameId)
                            }
                        }
                    }
                }
            }

            "role" -> {
                when (shareScene) {
                    "screenshot" -> {
                        runCatching {
                            if (!isHost) return
                            val activity = LifecycleInteractor.fragmentActivity ?: return
                            if (activity.isDestroyed || activity.isFinishing) return
                            val params = params?.simJson() ?: return
                            val data = GsonUtil.gsonSafeParseCollection<ShareRoleScreenshotData>(params) ?: return
                            if (data.screenshots.isNullOrEmpty()) return
                            if (data.screenshots.any { it !is String }) {
                                Timber.e(params)
                                return
                            }
                            val screenshotEvent = ShareRoleScreenshotEvent(
                                gameId,
                                data.screenshots,
                                data.roleId,
                                data.isShowUuid,
                                activity.javaClass != MainActivity::class.java
                            )
                            ShareRoleScreenshotsDialogV2.show(
                                activity,
                                screenshotEvent,
                                UserShareInfo.new() ?: return
                            )
                        }
                    }
                }

            }
        }
    }

    private fun prepareVideoCache(): String {
        val customCacheKey = "${System.currentTimeMillis()}"
        val last = metaKV.appKV.tempVideoCacheKey
        if (last.isNotEmpty()) {
            launch(Dispatchers.Main) {
                val playerController =
                    GlobalContext.get().get<SharedVideoPlayerControllerInteractor>()
                withContext(Dispatchers.IO) {
                    playerController.clearCache(last)
                }
            }
        }
        metaKV.appKV.tempVideoCacheKey = customCacheKey
        return customCacheKey
    }

    /*分享单张图片/视频到发帖页*/
    private fun GameCommonFeature.communityShareSingleMedia(
        context: Context,
        currentGameId: String,
        currentGamePkg: String,
        templateId: Int?,
        customCacheKey: String?,
        enableOutfitShare: Boolean,
        isFromMoment: Boolean,
        moments: List<PostMomentCard>?
    ) {
        val filePath = params?.get("filePath")?.toString() ?: return
        suspendJumpPost = {
            launch(Dispatchers.Main) {
                MetaRouter.Main.openPostPublishFromGame(
                    context = GlobalContext.get().get(),
                    content = null,
                    briefMedias = arrayListOf(filePath),
                    completeCardList = null,
                    tagIdList = null,
                    clearTopBackFeed = true,
                    fromGameId = currentGameId,
                    fromPkgName = currentGamePkg,
                    showCategoryId = CategoryId.PLOT_IN_GAME,
                    templateId = templateId,
                    customCacheKey = customCacheKey,
                    enableOutfitShare = enableOutfitShare,
                    isFromMoment = isFromMoment,
                    moments = moments
                )
            }
        }
        if (publishPostInteractor.isMainProcess) {
            suspendJumpPost?.invoke()
            suspendJumpPost = null
        } else {
            CpEventBus.post(
                CommonPostPublishSendEvent(
                    requestId,
                    PublishPostInteractor.METHOD_CAN_PUBLISH,
                    currentGameId,
                    publishPostInteractor.processName
                )
            )
            job?.cancel()
            job = launch(Dispatchers.Main) {
                delay(1_000)
                suspendJumpPost?.invoke()
                suspendJumpPost = null
                job = null
            }
        }
    }

    @Subscribe
    fun onEvent(event: CommonPostPublishReceiveEvent) {
        if (event.processName != publishPostInteractor.processName) return
        if (event.requestId != requestId) return
        if (event.method != PublishPostInteractor.METHOD_CAN_PUBLISH) return
        val result = event.data as? Boolean ?: return
        job?.cancel()
        job = null
        if (result) {
            suspendJumpPost?.invoke()
            suspendJumpPost = null
        } else {
            GlobalContext.get().get<Context>().toast(R.string.post_publishing_tip)
        }
    }

    /*剧情创作分享*/
    private fun GameCommonFeature.systemShareWithPlot(context: Context, isVideo: Boolean) = launch(Dispatchers.Main) {
        val filePath = params?.get("filePath")?.toString()
        if (!filePath.isNullOrEmpty() && File(filePath).exists()) {
            trickShareOCVideoForDailyTask(isVideo)
            val ext = File(filePath).extension.ifNullOrEmpty { if (isVideo) "mp4" else "png" }
            val tempFile = copyShareFile(context, ext, filePath)
            withContext(Dispatchers.IO) { File(filePath).copyTo(tempFile, true) }
            if (isVideo) {
                SystemShare.shareVideoFileBySystem(context, tempFile)
            } else {
                SystemShare.shareImageFileBySystem(context, tempFile)
            }
        }
    }

    private fun trickShareOCVideoForDailyTask(isVideo: Boolean) = launch {
        if (!isVideo) return@launch
//        if (to != TO_DY && to != TO_XHS && to != TO_KS) return
        val finishDailyTaskShareOcVideoUseCase: FinishShareOCVideoTaskUseCase by GlobalContext.get()
            .inject()
        finishDailyTaskShareOcVideoUseCase()
    }

    private suspend fun checkSave2Gallery(context: Context): Boolean {
        //目前只测试到一个三星Android 9 的设备需要权限，只处理Android 10以下的设备需要权限
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
            if (TSGamePermission.requestExternalStoragePermissions(context)) {
                return true
            } else {
                Timber.d("requestFilePermissions failed")
                return false
            }
        } else {
            return true
        }
    }

    private suspend fun copyShareFile(context: Context, ext: String, filePath: String): File {
        //考虑到外部存储可能出现的问题,这里用内部存储路径兜底处理一下
        val tempFiles = listOf(
            File(context.externalCacheDir, "${context.getString(R.string.app_name)}-Moments.${ext}"),
            File(context.cacheDir, "${context.getString(R.string.app_name)}-Moments.${ext}")
        )
        return withContext(Dispatchers.IO) {
            tempFiles.first {
                runCatching { File(filePath).copyTo(it, true).exists() }
                    .getOrElse { false }
            }
        }
    }

    private suspend fun gallerySave(
        context: Context,
        fileType: String?,
        filePath: String?
    ): Pair<Boolean, String> = withContext(Dispatchers.IO) {
        val result = when {
            filePath.isNullOrEmpty() -> false to "parameter cannot be null"
            !File(filePath).exists() -> false to "file not found"
            else -> {
                val uri = MediaStoreUtils.saveFileToAlbum(context, File(filePath))
                (uri != null) to (uri.toString())
            }
        }
        return@withContext result
    }

    /*剧情创作保存视频*/
    private fun GameCommonFeature.gallerySave(context: Context, messageId: Int) = launch {
        val fileType = params?.get("fileType")?.toString()
        val filePath = params?.get("filePath")?.toString()
        val templateId = params?.get("templateid")?.toString()?.toFloat()?.toInt()
        Analytics.track(EventConstants.EVENT_MOMENTS_SAVE) {
            templateId?.also { put("templateid", it) }
        }
        val result = gallerySave(context, fileType, filePath)
        callUECommonResult(
            gameId,
            messageId,
            AndroidCommonResult.FEATURE_GALLERY_SAVE,
            hashMapOf(
                "code" to if (result.first) 200 else 500,
                "data" to "${result.first}",
                "errMsg" to result.second
            )
        )
    }

    /**
     * 提供给引擎，保存单个图片
     */
    private suspend fun SaveImage2Gallery.gallerySave(context: Context, messageId: Int) {
        val result = gallerySave(context, "image", filePath)
        UGCProtocolSender.sendProtocol(
            ProtocolSendConstant.BRIDGE_ACTION_SAVE_IMAGE_TO_GALLERY,
            messageId,
            SaveImage2GalleryCallbackMsg(
                code = if (result.first) 200 else 500,
                data = result.first,
                errMsg = if (result.first) null else result.second,
            )
        )
    }

    /*剧情创作选择好友*/
    private fun GameCommonFeature.plotChooseFriend(lifecycle: PlotChoiceFriendsLifecycle, messageId: Int) = launch {
        withContext(Dispatchers.Main) {
            val selectCount = params?.get("roleNum")?.toString()?.toDouble()?.toInt() ?: 1
            lifecycle.showChooseFriend(selectCount) {
                val result = mutableMapOf<String, Any>().apply {
                    put(
                        "roleList",
                        it.map { item ->
                            mutableMapOf(
                                "uuid" to item.first,
                                "role" to item.second
                            )
                        })
                }
                Timber.tag("AndroidCommonResult").d("chooseFriend:${result}")
                callUECommonResult(
                    gameId,
                    messageId,
                    AndroidCommonResult.FEATURE_PLOT_CHOOSE_FRIEND,
                    hashMapOf(
                        "code" to 200,
                        "data" to result,
                        "errMsg" to ""
                    )
                )
            }
        }
    }

    /*显示录制Loading*/
    private fun GameCommonFeature.showRecordLoading(lifecycle: PlotChoiceFriendsLifecycle) =
        launch {
            withContext(Dispatchers.Main) {
                val recordTime = params?.get("record_time")?.toString()?.toDouble()?.toLong() ?: 1000L
                val hint = params?.get("hint")?.toString() ?: ""
                lifecycle.showRecordLoading(recordTime, hint)
            }
        }

    /*关闭录制Loading*/
    private fun GameCommonFeature.closeRecordLoading(lifecycle: PlotChoiceFriendsLifecycle) =
        launch { withContext(Dispatchers.Main) { lifecycle.closeRecordLoading() } }


    private fun fetchLocalGameConfig(gameId: String, resIdBean: ResIdBean, messageId: Int) {
        GlobalScope.launch(Dispatchers.IO) {
            runCatching {
                if (resIdBean.getTsType() != ResIdBean.TS_TYPE_LOCAL) throw CommonFeatureException(1005, "current ts type is not a local game, current value: ${resIdBean.getTsType()}")
                if (resIdBean.getPath().isNullOrEmpty()) throw CommonFeatureException(1004, "game path is empty")
                val projectFile = File(resIdBean.getPath() ?: "")
                if (!projectFile.exists()) throw CommonFeatureException(1001, "project file not exist")
                val jsonFile = EditorLocalHelper.getJsonFile(projectFile)
                if (!jsonFile.exists()) throw CommonFeatureException(1002, "json file not exist")
                val data = GsonUtil.mapGson.fromJson<Map<String, Any?>>(jsonFile.readText(), object : TypeToken<Map<String, Any?>>() {}.type) ?: throw CommonFeatureException(1003, "json data is empty")
                callUECommonResult(
                    gameId,
                    messageId,
                    AndroidCommonResult.FEAT_LOCAL_GAME_CONFIG,
                    hashMapOf(
                        "code" to 200,
                        "data" to data
                    )
                )
            }.getOrElse {
                Timber.e("checkcheck_local_config, fetchLocalGameConfig ${it}")
                val code = if (it is CommonFeatureException) it.errorCode else 1000
                callUECommonResult(
                    gameId,
                    messageId,
                    AndroidCommonResult.FEAT_LOCAL_GAME_CONFIG,
                    hashMapOf("code" to code, "errMsg" to "caught an error: ${it}", "data" to null)
                )
            }
        }
    }

    private fun GameCommonFeature.updatePrivacySettings() {
        val ootdPrivateSwitch = params?.get("ootdPrivateSwitch")?.toString()?.toBoolean() ?: return
        CpEventBus.post(UpdatePrivacySettingsEvent(ootdPrivateSwitch))
    }

    fun jumpWeb(
        context: Context,
        gameId: String,
        messageId: Int,
        params: Map<String?, Any?>?,
        isHost: Boolean
    ) {
        params ?: return
        val url = params["url"] as? String
        val code = params["code"] as? String
        if (url.isNullOrBlank() && code == null) return
        val actualUrl = if (!url.isNullOrBlank() && WebUtil.isHttpOrHttpsScheme(url)) {
            url
        } else if (!code.isNullOrBlank()) {
            kotlin.runCatching {
                GlobalContext.get().get<H5PageConfigInteractor>().getH5PageUrl(code.toLong())
            }.getOrElse {
                return
            }
        } else {
            return
        }
        val paramUrl = UriUtil.addParam(actualUrl, "source", "mw")
        val inGame = (params["inGame"] as? Boolean) == true
        val orientation = when ((params["orientation"] as? String) ?: "v") {
            "h" -> ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
            "follow" -> null
            else -> ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        }
//        val exitGame = (params["exitGame"] as? Boolean) == true
        val preUnique = params["preUnique"] as? String
        val backToClose = params["backToClose"] as? Boolean == true
        val reload = params["reload"] as? Boolean == true
        val backToRelease = params["backToRelease"] as? Boolean == true
        if (inGame) {
            CpEventBus.post(
                InGameOpenWebEvent(
                    paramUrl, gameId, messageId, orientation, isHost,
                    preUnique, backToClose, reload, backToRelease
                )
            )
        } else {
            MetaRouter.Web.navigate(
                context = context,
                fragment = null,
                url = paramUrl,
                showTitle = false,
                showStatusBar = false
            )
        }
    }

    fun notifyWebClose(gameId: String, messageId: Int, url: String, release: Boolean = true, preUnique: String? = null) {
        callUECommonResult(
            gameId,
            messageId,
            GameCommonFeature.FEATURE_JUMP_WEB,
            hashMapOf(
                "code" to 200,
                "url" to url,
                "release" to release,
                "preUnique" to (preUnique ?: ""),
            )
        )
    }

    /**
     * 选择图片
     */
    private fun GameCommonFeature.plotChooseImage(
        messageId: Int,
        lifecycle: PlotChoiceFriendsLifecycle
    ) = launch {
        withContext(Dispatchers.Main) {
            val rawRatio = params?.get("ratio")
            if (rawRatio == null) {
                lifecycle.goChooseImage(0, 0, false, messageId, this@plotChooseImage)
                return@withContext
            }
            val ratioList = rawRatio.toString().split(":")
            val ratioWidth: Int? = kotlin.runCatching { ratioList[0].toIntOrNull() }.getOrNull()?.takeIf { it > 0 }
            val ratioHeight: Int? = kotlin.runCatching { ratioList[1].toIntOrNull() }.getOrNull()?.takeIf { it > 0 }
            if (ratioWidth == null || ratioHeight == null) {
                callUECommonResult(
                    gameId,
                    messageId,
                    feature,
                    hashMapOf(
                        "code" to 2001,
                        "data" to null,
                        "errMsg" to "ratio parsing failed, ratioWidth:${ratioWidth}, ratioHeight:${ratioHeight}"
                    )
                )
                return@withContext
            }
            lifecycle.goChooseImage(ratioWidth, ratioHeight, true, messageId, this@plotChooseImage)
        }
    }

    private fun GameCommonFeature.styleCommunityOutfitUpdate() {
        val type = params?.get("type")?.toString() ?: return
        if (type == "refresh") {
            CpEventBus.post(RoleStyleRefreshEvent(gameId))
        }
    }

    fun DuplicateImageMsg.duplicateImage(activity: Activity?, messageId: Int) {
        runCatching {
            if (activity == null) {
                throw CommonFeatureException(1002, "current activity not detected")
            }
            if (folderPath.isNullOrEmpty() && bizCode.isNullOrEmpty()) {
                throw CommonFeatureException(1004)
            }
            val path: String = if (folderPath == null) "" else runCatching {
                val file = File(folderPath ?: "")
                if (!file.exists() || !file.startsWith(DownloadFileProvider.appFilesDir)) {
                    throw CommonFeatureException(1004)
                }
                file.path
            }.getOrElse {
                throw CommonFeatureException(
                    1004,
                    "folderPath parsing failed, folderPath:${folderPath}"
                )
            }
            var ratioWidth = 0
            var ratioHeight = 0
            if (!cropAspectRatio.isNullOrEmpty()) {
                // 指定了像素比
                val ratioList = cropAspectRatio.toString().split(":")
                val tempRatioWidth: Int? =
                    kotlin.runCatching { ratioList[0].toIntOrNull() }.getOrNull()?.takeIf { it > 0 }
                val tempRatioHeight: Int? =
                    kotlin.runCatching { ratioList[1].toIntOrNull() }.getOrNull()?.takeIf { it > 0 }
                if (tempRatioWidth == null || tempRatioHeight == null) {
                    throw CommonFeatureException(
                        1003,
                        "cropAspectRatio parsing failed,cropAspectRatio:${cropAspectRatio},tempRatioWidth:${tempRatioWidth},tempRatioHeight:${tempRatioHeight}"
                    )
                }
                ratioWidth = tempRatioWidth
                ratioHeight = tempRatioHeight
            }
            val mimeList: List<String>? =
                mimeType?.filter { !it.isNullOrEmpty() && (PictureMimeType.isHasImage(it)||PictureMimeType.isHasVideo(it)) }
                    ?.filterNotNull()
            val maxSize = if (maxFileSize <= 0) 0 else maxFileSize
            MetaRouter.MobileEditor.duplicateImage(
                activity,
                ratioWidth,
                ratioHeight,
                mimeList,
                maxSize,
                path,
                messageId,
                bizCode,
                compressVideo, maxVideoSecond, minVideoSecond
            )
        }.getOrElse {
            if (it is CommonFeatureException) {
                UGCProtocolSender.sendProtocol(
                    ProtocolSendConstant.BRIDGE_ACTION_DUPLICATE_IMAGE,
                    messageId,
                    DuplicateImageCallbackMsg(
                        message = it.message,
                        code = it.errorCode,
                        imagePath = null,
                    )
                )
            } else {
                UGCProtocolSender.sendProtocol(
                    ProtocolSendConstant.BRIDGE_ACTION_DUPLICATE_IMAGE,
                    messageId,
                    DuplicateImageCallbackMsg(
                        message = it.cause?.message ?: it.message,
                        code = 999,
                        imagePath = null,
                    )
                )
            }
        }
    }


    fun SaveImage2Gallery.saveImage2Gallery(activity: Activity?, messageId: Int) = launch {
        runCatching {
            if (activity == null || activity.isFinishing) {
                throw CommonFeatureException(1002, "current activity not detected")
            }
            val canSave = checkSave2Gallery(activity)
            if (canSave) {
                gallerySave(activity, messageId)
            } else {
                throw CommonFeatureException(1003, "can not access")
            }
        }.getOrElse {
            Timber.e("check_duplicate ${it}")
            UGCProtocolSender.sendProtocol(
                ProtocolSendConstant.BRIDGE_ACTION_SAVE_IMAGE_TO_GALLERY,
                messageId,
                SaveImage2GalleryCallbackMsg(
                    errMsg = if (it is CommonFeatureException) {
                        it.message
                    } else {
                        it.cause?.message ?: it.message
                    },
                    code = if (it is CommonFeatureException) {
                        it.errorCode
                    } else {
                        999
                    },
                    data = false,
                )
            )
        }
    }

    private fun GameCommonFeature.shareOcMoment(context: Context) {
        val paths = params?.get("paths") as? List<String>
        val mode = params?.get("mode") as? String
        if (paths.isNullOrEmpty() || mode.isNullOrBlank()) return
        val isVideo = when (mode) {
            "image" -> {
                false
            }

            "video" -> {
                true
            }

            else -> {
                return
            }
        }
        val gameId = MWBizBridge.currentGameId()
        LifecycleInteractor.fragmentActivity?.let {
            GlobalShareDialog.show(
                it.supportFragmentManager,
                ShareRawData.ocMoment(
                    if (isVideo) null else paths,
                    if (isVideo) paths else null,
                    gameId
                )
            )
        }
    }

    private fun GameCommonFeature.getSharePlatforms(messageId: Int) {
        callUECommonResult(
            gameId,
            messageId,
            GameCommonFeature.FEATURE_GET_SHARE_PLATFORMS,
            hashMapOf(
                "json" to GsonUtil.safeToJson(DirectSharePlatforms())
            )
        )
    }

    private fun GameCommonFeature.copyRoleScreenshot(messageId: Int) {
        launch {
            kotlin.runCatching {
                val params = params ?: throw CommonFeatureException(1000, "empty params")
                val srcPath = params["srcPath"] as? String
                if (srcPath.isNullOrBlank()) throw CommonFeatureException(1000, "invalid srcPath")
                val targetFilename = params["targetFilename"] as? String
                if (targetFilename.isNullOrBlank()) throw CommonFeatureException(
                    1000,
                    "invalid targetFilename"
                )
                val srcFile = File(srcPath)
                if (!srcFile.startsWith(DownloadFileProvider.appFilesDir)) throw CommonFeatureException(
                    1000,
                    "invalid srcPath"
                )
                if (!srcFile.exists() || !srcFile.isFile) throw CommonFeatureException(
                    1000,
                    "invalid source file"
                )
                if (!DownloadFileProvider.roleScreenshotsCacheDir.exists()) {
                    DownloadFileProvider.roleScreenshotsCacheDir.mkdirs()
                }
                val targetFile = File(DownloadFileProvider.roleScreenshotsCacheDir, targetFilename)
                if (!targetFile.startsWith(DownloadFileProvider.roleScreenshotsCacheDir)) throw CommonFeatureException(
                    1000,
                    "invalid targetFilename"
                )
                val copiedTargetFile = srcFile.copyTo(targetFile, true)
                callUECommonResult(
                    gameId,
                    messageId,
                    GameCommonFeature.FEATURE_COPY_ROLE_SCREENSHOT,
                    hashMapOf(
                        "code" to 200,
                        "data" to copiedTargetFile.absolutePath
                    )
                )
            }.getOrElse {
                val code = if (it is CommonFeatureException) it.errorCode else 1000
                callUECommonResult(
                    gameId,
                    messageId,
                    GameCommonFeature.FEATURE_COPY_ROLE_SCREENSHOT,
                    hashMapOf(
                        "code" to code,
                        "data" to null,
                        "errMsg" to it.message
                    )
                )
            }
        }
    }

    private fun GameCommonFeature.notifyEvent(messageId: Int, exitCaller: MWGameExitCaller) {
        val params = params ?: throw CommonFeatureException(1000, "empty params")
        val eventId = params["eventId"]?.toString()?.toIntOrNull()
        when (eventId) {
            1 -> {
                if (!isCurrentGameModuleGuide()) return
                CpEventBus.post(UgcModuleGuideEvent(BaseAccountInteractor.MODULE_GUIDE_STATUS_GAME))
            }

            2 -> {
                if (!isCurrentGameModuleGuide()) return
                exitCaller.onFloatExitClick().invoke()
            }
        }
    }

    fun isCurrentGameModuleGuide(): Boolean {
        val resIdBean = metaKV.analytic.getLaunchResIdBeanWithId(MWBizBridge.currentGameId())
            ?: metaKV.analytic.getLaunchResIdBean(MWBizBridge.currentGamePkg()) ?: return false
        return resIdBean.isModuleGuide()
    }
}

class CommonFeatureException(val errorCode: Int, override val message: String = "") : Exception()