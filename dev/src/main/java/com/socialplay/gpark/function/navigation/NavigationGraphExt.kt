package com.socialplay.gpark.function.navigation

import androidx.navigation.NavController
import androidx.navigation.NavGraph
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.R
import timber.log.Timber

/**
 *     author : wei.zhu
 *     e-mail : <EMAIL>
 *     time   : 2021/09/26
 *     desc   :
 */
object NavigationGraphExt {

    fun merge(navController: NavController, graph: NavGraph, mainGraph: Int) {
        R.navigation::class.java.fields.forEach {
            val graphId = it.getInt(null)
            if (graphId != mainGraph) {
                graph.addAll(navController.navInflater.inflate(graphId))
            }
        }
    }

    /**
     * 打印页面跳转日志，便于排查当前页面
     */
    fun printCNavigateChange(navController: NavController?) {
        if (BuildConfig.DEBUG) {
            navController?.addOnDestinationChangedListener { _, destination, _ ->
                Timber.d("page_route_change,destination=${destination}")
            }
        }
    }
}