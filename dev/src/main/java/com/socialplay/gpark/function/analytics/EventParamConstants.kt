package com.socialplay.gpark.function.analytics

/**
 * Created by bo.li
 * Date: 2023/11/20
 * Desc:
 */
object EventParamConstants {

    // 查看大图，来源
    const val IMG_PRE_FROM_EDIT_PROFILE = "edit_profile"
    const val IMG_PRE_FROM_UGC_DETAIL = "ugc_detail"
    const val IMG_PRE_FROM_UGC_DETAIL_HALF = "ugc_detail_half"
    const val IMG_PRE_FROM_SYSTEM_DETAIL_LIST = "system_detail_list"
    const val IMG_PRE_FROM_COMMUNITY_FEED = "community_feed"
    const val IMG_PRE_FROM_PUBLISH_POST = "publish_post"
    const val IMG_PRE_FROM_POST_DETAIL = "post_detail"
    const val IMG_PRE_FROM_PROFILE = "profile"
    const val IMG_PRE_FROM_GAME_DETAIL_COMMENT = "game_detail_comment"

    // 发帖类型
    const val COMMUNITY_POST_TYPE = "type"

    // 置顶/普通帖子
    const val ANALYTIC_COMMUNITY_TYPE_NORMAL = "1"
    const val ANALYTIC_COMMUNITY_TYPE_PIN = "2"
    // 帖子点赞
    const val V_LIKE = "1"
    const val V_UNLIKE = "2"
    // 帖子点赞来源
    const val LOCATION_LIKE_FEED = "1"
    const val LOCATION_LIKE_POST_DETAIL = "2"
    const val LOCATION_LIKE_PROFILE = "3"
    const val LOCATION_LIKE_VIDEO_FEED = "4"
    const val LOCATION_LIKE_TOPIC_DETAIL = "5"
    const val LOCATION_LIKE_TOPIC_SQUARE = "6"
    // 帖子评论
    const val LOCATION_COMMENT_POST_DETAIL = "1"
    const val LOCATION_COMMENT_VIDEO_FEED = "2"
    // 评论位置
    const val TYPE_COMMENT_COMMENT = "1"
    const val TYPE_COMMENT_REPLY = "2"
    // 发帖入口
    const val CLICK_PUBLISH_SOURCE_FEED = "1"
    const val CLICK_PUBLISH_SOURCE_TOPIC = "2"
    // 进入话题页来源
    const val SOURCE_TOPIC_PROFILE = "1"
    const val SOURCE_TOPIC_FEED = "2"
    const val SOURCE_TOPIC_POST_DETAIL = "3"
    const val SOURCE_TOPIC_TOPIC = "4"
    const val SOURCE_TOPIC_SQUARE = "5"
    const val SOURCE_TOPIC_IM = "6"
    // 话题关注
    const val V_FOLLOW = "1"
    const val V_UNFOLLOW = "2"
    // 话题榜单
    const val V_NOT_HOT = "0"
    const val V_HOT = "1"
    // 举报
    const val SOURCE_REPORT_PROFILE = "1"
    const val SOURCE_REPORT_CHAT = "2"
    const val SOURCE_REPORT_GAME = "3"
    const val SOURCE_TOPIC_SEARCH: String = "topic_search"

    // 关注
    const val LOCATION_FOLLOW_PROFILE = "1"
    const val LOCATION_FOLLOW_POST_DETAIL = "2"
    const val LOCATION_KOL_TAB_STAR_CREATOR = "tab_star"
    const val LOCATION_KOL_MORE_CREATOR = "more_discoveries"
    const val LOCATION_KOL_MORE_STAR_CREATOR = "more_star"
    const val TYPE_FOLLOW = "1"
    const val TYPE_UNFOLLOW = "2"

    // key
    const val KEY_TAG = "tag"
    const val KEY_TAG_ID = "tag_id"
    const val KEY_TAG_LIST = "tag_list"
    const val KEY_TAG_PAGE = "tag_page"
    const val KEY_SOURCE = "source"
    const val KEY_POSTID = "postid"
    const val KEY_ACT = "act"
    const val KEY_HOT = "hot"
    const val KEY_RK = "rk"
    const val KEY_TYPE = "type"
    const val KEY_LOCATION = "location"
    const val KEY_PLAYTIME = "playtime"
    const val KEY_EVENT_ID = "event_id"
    const val KEY_UGCID = "ugcid"
    const val KEY_UGCNAME = "ugcname"
    const val KEY_RESULT = "result"
    const val KEY_RANK = "rank"
    const val KEY_USERID = "userid"
    const val KEY_SHOW_CATEGORYID = "show_categoryid"
    const val KEY_GAMEID = "gameid"
    const val KEY_PACKAGENAME = "packagename"
    const val KEY_OPERATION_ID = "operation_id"
    const val KEY_TITLE = "title"
    const val KEY_TAGID = "tagid"
    const val KEY_STATE = "state"
    const val KEY_REVIEW_TAG = "review_tag"

    const val SRC_MESSAGE_LIST_ENTRANCE_UNKNOWN = "0"
    const val SRC_MESSAGE_LIST_ENTRANCE_MAPS = "1"
    const val SRC_MESSAGE_LIST_ENTRANCE_AVATAR = "2"
}