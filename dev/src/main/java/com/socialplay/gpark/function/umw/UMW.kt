package com.socialplay.gpark.function.umw

import com.meta.biz.ugc.model.IMWMsg
import com.meta.biz.ugc.model.IPlatformMsg
import com.meta.biz.ugc.model.MWProtocol
import com.meta.biz.ugc.protocol.listener.SingleProtocolListener
import com.meta.lib.mwbiz.bean.bridge.SendMsg
import com.socialplay.gpark.data.interactor.avatar.IAvatarNavigationInterceptor
import com.socialplay.gpark.function.mw.lifecycle.MWLifecycle
import org.json.JSONObject

/**
 * MW 统一调用入口类，使用这个类不用区分是角色游戏还是普通TS游戏
 */
interface UMW {

    companion object {

        /**
         * 当前的UMW对象，会在[UMWLifecycleRegistry.bindCurrentUMW]中通过设置
         * 其他地方请通过[UMW.current]来获取当前的UMW对象
         */
        @Deprecated("Don't use this field directly, use UMW.current instead.", level = DeprecationLevel.WARNING, replaceWith = ReplaceWith("UMW.current"))
        var _current: UMW? = null

        /**
         * 获取当前的UMW对象，这个仅用于在其他地方想通过静态方式访问当前环境内的UMW信息
         * 非必要尽量不要通过这种方式来访问 UMW
         *
         * 这个对象有可能为空，比如下面这些情况：
         * １、调用过早，进程创建了但是UE尚未初始化
         * ２、角色进程死亡到重启的空白阶段或死亡后未重启
         */
        val current: UMW? get() = _current
    }

    /**
     * 获取角色相关的专用操作接口，如果当前运行环境不是角色游戏，会返回null
     * 如果一些操作是角色游戏专有的则放到这个接口里面，如非必要，尽量不要放到这个里面而是提供统一的方法
     */
    val avatar: IAvatarInterface?

    /**
     * 获取TS游戏相关的专用操作接口，如果当前运行环境不是TS游戏，会返回null
     * 如果一些操作是TS游戏专有的则放到这个接口里面，如非必要，尽量不要放到这个里面而是提供统一的方法
     */
    val ts: ITsInterface?

    /**
     * 获取当前所处环境的游戏类型
     */
    fun getGameType(): GameType

    /**
     * 注册Lifecycle
     */
    fun registerLifecycle(lifecycle: MWLifecycle)

    /**
     * 注册单个协议监听器,监听TS游戏发送给客户端的协议
     */
    fun registerProtocol(protocol: MWProtocol)

    /**
     * 增加协议回调监听，用于监听TS游戏发送给客户端的协议
     * T必须继承IMWMsg
     */
    @Throws
    fun <T : IMWMsg> addProtocolObserver(clazz: Class<T>, listener: SingleProtocolListener<T>)


    /**
     * 移除协议回调监听
     * T必须继承IMWMsg
     */
    @Throws
    fun <T : IMWMsg> removeProtocolObserver(listener: SingleProtocolListener<T>)


    /**
     * 发送协议，用于给TS游戏发送协议消息
     * @param useMessageChannel 是否使用信息通道（黏性）
     * @param params 协议中的data
     */
    fun sendProtocol(
        protocol: MWProtocol,
        params: String,
        messageId: Int = 0,
        useMessageChannel: Boolean = true
    )

    /**
     * 发送协议，用于给TS游戏发送协议消息
     * @param platformMsg 协议中的data的封装类
     */
    fun <T : IPlatformMsg> sendProtocol(protocol: MWProtocol, platformMsg: T, messageId: Int = 0)

    /**
     * 发送协议
     * @param useMessageChannel 是否使用信息通道（黏性）
     * @param jsonObject 协议中的data
     */
    fun sendProtocol(
        protocol: MWProtocol,
        jsonObject: JSONObject,
        messageId: Int = 0,
        useMessageChannel: Boolean = true
    )

    /**
     * 发送协议
     * @param useMessageChannel 是否使用信息通道（黏性）
     * @param map 协议中的data
     */
    fun sendProtocol(
        protocol: MWProtocol,
        map: Map<String, Any?>,
        messageId: Int = 0,
        useMessageChannel: Boolean = true
    )

    /**
     * 直接调用调用UE，如果可以，优先使用 [sendProtocol]系列方法
     */
    fun callUE(msg: SendMsg)

    /**
     * 获取当前运行环境的游戏ID
     */
    fun currentGameId(): String

    /**
     * 获取当前运行环境的游戏名称
     */
    fun currentGameName(): String

    /**
     * 获取当前运行环境的游戏包名
     */
    fun currentGamePkg(): String


    interface IAvatarInterface {

        /**
         * 为指定的声明周期监听器设置生命周期分发策略
         * @param lifecycle 生命周期监听器
         * @param strategy 生命周期分发策略，具体策略见[LifecycleDispatchStrategy]
         */
        fun setLifecycleDispatchStrategy(
            lifecycle: MWLifecycle,
            strategy: Int,
        )

        /**
         * 注册一个导航拦截器，用于拦截角色的导航操作
         */
        fun registerNavigationInterceptor(interceptor: IAvatarNavigationInterceptor)

        /**
         * 注销一个导航拦截器
         */
        fun unregisterNavigationInterceptor(interceptor: IAvatarNavigationInterceptor)


        /**
         * 角色是否运行在独立Activity模式下，角色编辑态有两种显示模式
         * 1、MainActivity中以Fragment的形式显示
         * 2、FullScreenEditorActivity独占显示
         */
        val isStandalone: Boolean

        /**
         * 角色是否运行在嵌入在MainActivity的状态
         */
        val isEmbedded: Boolean

        /**
         * 是否是编辑态
         */
        val isEditMode: Boolean

        /**
         * 是否是展示态
         */
        val isViewMode: Boolean

        /**
         * 角色游戏是否已经加载完成
         */
        val isLoaded: Boolean

        companion object LifecycleDispatchStrategy {
            /**
             *仅在角色编辑状态下分发生命周期
             */
            const val EditOnly = 1

            /**
             * 所有模式下（展示和编辑）都分发生命周期
             */
            const val AllMode = 2

            /**
             * 仅在角色编辑态下分发生命周期，但是必须游戏是处于运行状态(未加载成功不分发)
             */
            const val EditActiveOnly = 3


            /**
             * 所有模式下（展示和编辑）都分发生命周期，但是必须是游戏是处于运行状态(未加载成功不分发)
             */
            const val AllActiveOnly = 4


            /**
             * 默认的生命周期分发策略，默认为[EditOnly]
             */
            const val Default = EditOnly
        }
    }

    interface ITsInterface {

    }
}

