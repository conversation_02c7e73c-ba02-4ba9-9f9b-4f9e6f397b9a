package com.socialplay.gpark.function.mgs

import android.app.Activity
import android.app.Application
import android.app.Dialog
import android.content.Context
import android.content.pm.ActivityInfo
import android.net.Uri
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentActivity
import com.meta.biz.mgs.data.model.Member
import com.meta.biz.mgs.data.model.MgsPlayerInfo
import com.meta.biz.mgs.data.model.request.MgsShareScreenshot
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.BaseAccountInteractor
import com.socialplay.gpark.data.interactor.MgsInteractor
import com.socialplay.gpark.data.model.GameDetailInfo
import com.socialplay.gpark.data.model.event.InGameOpenWebEvent
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.mw.GameCommonFeatureResolver
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.mgs.dialog.BanBlockDialog
import com.socialplay.gpark.ui.mgs.dialog.MgsDanmuDurationDesDialog
import com.socialplay.gpark.ui.mgs.dialog.MgsInputDialog
import com.socialplay.gpark.ui.mgs.dialog.MgsPlayerInfoDialog
import com.socialplay.gpark.ui.mgs.dialog.MgsQrCodeDialog
import com.socialplay.gpark.ui.mgs.dialog.MgsQuitGameDialog
import com.socialplay.gpark.ui.mgs.dialog.MgsScreenshotDialog
import com.socialplay.gpark.ui.mgs.dialog.MgsOptimizeCardDialog
import com.socialplay.gpark.ui.mgs.dialog.MgsReportDialog
import com.socialplay.gpark.ui.mgs.dialog.MgsReportSuccessDialog
import com.socialplay.gpark.ui.mgs.dialog.QuitAssetGuideDialog
import com.socialplay.gpark.ui.mgs.listener.OnMgsPlayerInfoDialogListener
import com.socialplay.gpark.ui.mgs.listener.OnMgsQuitGameDialogListener
import com.socialplay.gpark.ui.mgs.record.GameRecordCountDownDialog
import com.socialplay.gpark.ui.mgs.record.GameScreenRecordEndDialog
import com.socialplay.gpark.ui.web.GameWebDialog
import com.socialplay.gpark.util.extension.dismissSafely
import org.koin.core.context.GlobalContext

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2021/08/15
 *     desc   : mgs游戏里弹窗展示管理
 *
 */
object MgsDialogManager {

    private var playerInfoDialog: Dialog? = null
    private var qrCodeDialog: Dialog? = null
    private var quitGameDialog: Dialog? = null
    private var banBlockDialog: Dialog? = null
    private var startRecordCountDownDialog: Dialog? = null
    private var recordEndDialog: Dialog? = null
    private var ageLimitDialog: Dialog? = null
    private var showCustomDialog = false
    fun anyDialogShowing(): Boolean {
        return playerInfoDialog?.isShowing == true || qrCodeDialog?.isShowing == true
                || quitGameDialog?.isShowing == true || banBlockDialog?.isShowing == true
                || startRecordCountDownDialog?.isShowing == true || recordEndDialog?.isShowing == true
                || ageLimitDialog?.isShowing == true
                || inputDialog?.isShowing == true
                || reportDialog?.isShowing == true
                || inputDialog?.isShowing == true
                ||mgsDanmuDesDialog?.isShowing == true
                || showCustomDialog

    }
    private var screenshotDialog: Dialog? = null
    private var ugcCardDialog: Dialog? = null
    private var reportDialog: Dialog? = null
    private var inputDialog: MgsInputDialog? = null
    private var mgsDanmuDesDialog: Dialog? = null

    private var webDialog: DialogFragment? = null

    fun destroyAllDialog(activity: Activity) {
        if(!activity.isFinishing){
            playerInfoDialog?.dismiss()
        }
        playerInfoDialog = null
        qrCodeDialog?.dismiss()
        qrCodeDialog = null
        if (!activity.isFinishing) {
            quitGameDialog?.dismiss()
        }
        quitGameDialog = null
        if (!activity.isFinishing) {
            banBlockDialog?.dismiss()
        }
        banBlockDialog = null
        if (!activity.isFinishing) {
            startRecordCountDownDialog?.dismiss()
        }
        startRecordCountDownDialog = null
        if (!activity.isFinishing) {
            recordEndDialog?.dismiss()
        }
        recordEndDialog = null
        if (!activity.isFinishing) {
            ageLimitDialog?.dismiss()
        }
        ageLimitDialog = null
        if (!activity.isFinishing) {
            reportDialog?.dismiss()
        }
        reportDialog = null
        if (!activity.isFinishing) {
            inputDialog?.dismissDialog()
        }
        inputDialog = null
        if (!activity.isFinishing) {
            mgsDanmuDesDialog?.dismiss()
        }
        mgsDanmuDesDialog = null
        dismissGameWebDialog(activity)
    }

    /**
     * 展示退出游戏弹窗
     */
    fun showQuitGame(
        activity: Activity,
        metaApp: Context,
        listener: OnMgsQuitGameDialogListener,
    ) {
        if (activity.isFinishing) return
        val gameInfo = GlobalContext.get().get<MgsInteractor>().getMgsGameInfo()
        Analytics.track(EventConstants.EVENT_CLICK_MGS_GAME_QUIT) {
            putAll(
                mapOf(
                    "gameid" to gameInfo?.id.toString(),
                    "gamepkg" to gameInfo?.packageName.toString()
                )
            )
        }
        quitGameDialog?.dismiss()
        val accountInteractor: AccountInteractor = GlobalContext.get().get()
        quitGameDialog = if (GameCommonFeatureResolver.isCurrentGameModuleGuide()
            && accountInteractor.moduleGuideStatus == BaseAccountInteractor.MODULE_GUIDE_STATUS_TODO
        ) {
            QuitAssetGuideDialog(metaApp, activity, listener)
        } else {
            MgsQuitGameDialog(
                metaApp,
                activity,
                listener,
                true,
                true,
                metaApp.getString(R.string.mgs_quit_game_title),
                metaApp.getString(R.string.meta_mgs_quit_game_ok),
                metaApp.getString(R.string.mgs_quit_game_cancel)
            )
        }
        quitGameDialog?.show()
    }
    fun showAgeLimitDialog(activity: Activity,
                            metaApp: Context,
                            age: Int,
                            listener: OnMgsQuitGameDialogListener){
        if (activity.isFinishing) return
        ageLimitDialog?.dismiss()
        ageLimitDialog = MgsQuitGameDialog(
            metaApp, activity, listener, false, true,
            metaApp.getString(R.string.age_limit_title, age),
            metaApp.getString(R.string.mgs_quit_game_cancel),
            metaApp.getString(R.string.age_limit_check)
        )
        ageLimitDialog?.show()
    }

    /**
     * 展示用户资料卡片窗
     */
    fun showPlayerInfoDialog(
        data: MgsPlayerInfo,
        activity: Activity,
        metaApp: Context,
        gameInfo: GameDetailInfo?,
        listener: OnMgsPlayerInfoDialogListener
    ) {
        if (activity.isFinishing) return
        // 用户资料卡片
        playerInfoDialog?.dismiss()
        playerInfoDialog = MgsPlayerInfoDialog(metaApp, activity, gameInfo, data, listener)
        playerInfoDialog?.show()
    }

    /**
     * 用户资料卡片弹窗静音状态回调
     */
    fun playerInfoDialogMute(member: Member) {
        (playerInfoDialog as? MgsPlayerInfoDialog)?.onReceivedVoiceChange(member)
    }

    /**
     * 用户资料卡片弹窗申请好友回调
     */
    fun playerInfoDialogApplied() {
        (playerInfoDialog as? MgsPlayerInfoDialog)?.onAppliedFriend()
    }

    /**
     * 展示二维码弹窗
     */
    fun showQrCodeDialog(
        url: String,
        roomShowNum: String,
        activity: Activity,
        metaApp: Context,
        gameInfo: GameDetailInfo?
    ) {
        if (activity.isFinishing) return
        qrCodeDialog?.dismiss()
        qrCodeDialog = MgsQrCodeDialog(metaApp, activity, gameInfo, url, roomShowNum)
        qrCodeDialog?.show()
    }

    fun showBanBlockDialog(
        metaApp: Context,
        activity: Activity,
        type: String,
        reason: String,
    ) {
        if (activity.isFinishing) return
        banBlockDialog?.dismiss()
        banBlockDialog = BanBlockDialog(metaApp, activity, type, reason)
        banBlockDialog?.show()
    }

    fun showStartRecordCountDownDialog(metaApp: Context, activity: Activity, finishAction: () -> Unit) {
        if (activity.isFinishing) return
        startRecordCountDownDialog?.dismiss()
        startRecordCountDownDialog = GameRecordCountDownDialog(metaApp, activity, 3, finishAction)
        startRecordCountDownDialog?.show()
    }

    fun showRecordEndDialog(videoPath: String, videoUri: Uri, gameId: String, gamePackageName: String, gameName:String , activity: Activity, metaApp: Application, dismissCallback: (() -> Unit)?) {
        if (activity.isFinishing) return
        recordEndDialog?.dismiss()
        recordEndDialog = GameScreenRecordEndDialog(videoPath, videoUri, gameId, gamePackageName, gameName, activity, metaApp, dismissCallback)
        recordEndDialog?.show()
    }

    /**
     * 展示分享截图弹窗
     */
    fun showScreenshotDialog(activity: Activity, metaApp: Context, shareScreenshot: MgsShareScreenshot, jumpUrl: String?, userNumber: String?) {
        if (activity.isFinishing) return
        screenshotDialog?.dismiss()
        screenshotDialog = null
        screenshotDialog = MgsScreenshotDialog(metaApp, activity, shareScreenshot, userNumber, jumpUrl)
        screenshotDialog?.show()
    }
    /**
     * ugc用户名片
     */
    fun showOptimizeDialog(
        data: MgsPlayerInfo,
        activity: Activity,
        metaApp: Context,
        gameInfo: GameDetailInfo,
        listener: OnMgsPlayerInfoDialogListener
    ) {
        if (activity.isFinishing) return
        // 用户资料卡片
        ugcCardDialog?.dismiss()
        ugcCardDialog = MgsOptimizeCardDialog(metaApp, activity, gameInfo, data, listener)
        ugcCardDialog?.show()
    }

    fun showReportDialog(
        metaApp: Context,
        activity: Activity,
        uuid: String?,
        nickname: String?,
        gameId: String
    ) {
        if (activity.isFinishing) return
        Analytics.track(EventConstants.EVENT_USER_REPORT_CLICK, EventParamConstants.KEY_SOURCE to EventParamConstants.SOURCE_REPORT_GAME)
        reportDialog?.dismiss()
        reportDialog = MgsReportDialog(metaApp, activity, uuid ?: "", nickname ?: "", gameId)
        reportDialog?.show()
        Analytics.track(EventConstants.EVENT_MGS_CARD_REPORT_REPORT_CLICK)
    }
    fun showInputDialog(metaApp: Context, activity: Activity, hintText: String, text: String?, maxEms: Int, listener: MgsInputDialog.OnInputDialogListener){
        if (activity.isFinishing) return
        inputDialog?.dismiss()
        inputDialog = MgsInputDialog(activity, metaApp)
        inputDialog?.show()
        inputDialog?.setInputMessage(hintText, text, maxEms, listener)
    }

    fun showReportSuccessDialog(metaApp: Context, activity: Activity) {
        if (activity.isFinishing) return
        val reportSuccessDialog = MgsReportSuccessDialog(metaApp, activity)
        reportSuccessDialog.show()
    }
    fun showDanmuDialog(metaApp: Context, activity: Activity, duration: Long){
        if (activity.isFinishing) return
        mgsDanmuDesDialog?.dismiss()
        mgsDanmuDesDialog = MgsDanmuDurationDesDialog(activity, metaApp, duration)
        mgsDanmuDesDialog?.show()
    }

    fun isShowInputDialog(): Boolean {
        return inputDialog?.isShowing ?: false

    }
    fun showCustomInputDialog(isShow: Boolean) {
        showCustomDialog = isShow
    }

    fun showWebDialog(activity: Activity?, inGameOpenWebEvent: InGameOpenWebEvent) {
        if (activity == null || activity.isFinishing || activity !is FragmentActivity) return

        dismissGameWebDialog(activity)

        val previousOrientation = if (inGameOpenWebEvent.orientation != null) {
            val preOrientation = activity.requestedOrientation
            if (preOrientation != inGameOpenWebEvent.orientation) {
                activity.requestedOrientation = inGameOpenWebEvent.orientation
            }
            preOrientation
        } else null
        webDialog = GameWebDialog.show(
            activity,
            inGameOpenWebEvent
        ) {
            if (it == webDialog) {
                webDialog = null
            }
            if (!PandoraToggle.isOpenBabelWeb) {
                if (!activity.isFinishing && previousOrientation != null && activity.requestedOrientation != previousOrientation) {
                    activity.requestedOrientation = previousOrientation
                }
            }
        }
    }

    private fun dismissGameWebDialog(activity: Activity) {
        webDialog?.dismissSafely()
        webDialog = null
    }
}