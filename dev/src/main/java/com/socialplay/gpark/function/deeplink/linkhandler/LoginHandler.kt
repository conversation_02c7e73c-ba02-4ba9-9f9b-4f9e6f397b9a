package com.socialplay.gpark.function.deeplink.linkhandler

import com.socialplay.gpark.data.model.LoginSource
import com.socialplay.gpark.data.model.user.ContinueAccountInfo
import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.deeplink.LinkHandleResult
import com.socialplay.gpark.function.deeplink.LinkHandler
import com.socialplay.gpark.function.deeplink.LinkHandlerChain
import com.socialplay.gpark.function.deeplink.MetaDeepLink
import com.socialplay.gpark.function.deeplink.MetaDeepLink.PARAM_TAB_ID
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.main.MainBottomNavigationItem
import com.socialplay.gpark.util.extension.getParameterAsInt
import com.socialplay.gpark.util.fromJSON

/**
 * 登录
 */
class LoginHandler : LinkHandler {

    companion object {
        const val PARAM_ONLY_LOGIN = "onlyLogin"
        const val PARAM_CONTINUE_ACCOUNT_INFO = "continueAccountInfo"
        const val PARAM_SUCCESS_TO_MAIN = "successToMain"
    }

    override fun handle(chain: LinkHandlerChain, data: LinkData): LinkHandleResult {

        val dataString = data.uri.getQueryParameter("data")

        if (dataString != null) {
            val arguments = BasicNavigateLinkHandler.decodeBundle(dataString)
            val fromGameId = arguments.getString(MetaDeepLink.PARAM_FROM_GAME_ID)
            // [com.socialplay.gpark.data.model.LoginSource]
            val source = arguments.getString(MetaDeepLink.PARAM_SOURCE_FROM)
            MetaRouter.Login.login(
                data.navHost,
                if (source.isNullOrEmpty()) LoginSource.SchemeUnknown.source else source,
                gid = fromGameId
            )
        } else {
            // Scheme模式跳转
            val gId = data.uri.getQueryParameter(MetaDeepLink.PARAM_FROM_GAME_ID)
            val source = data.uri.getQueryParameter(MetaDeepLink.PARAM_SOURCE_FROM)
            val onlyLogin = data.uri.getQueryParameter(PARAM_ONLY_LOGIN)?.toBoolean()?:false
            val continueAccountInfo = data.uri.getQueryParameter(PARAM_CONTINUE_ACCOUNT_INFO)
            val successToMain = data.uri.getQueryParameter(PARAM_SUCCESS_TO_MAIN)?.toBoolean() ?: false

            MetaRouter.Login.login(
                data.navHost,
                if (source.isNullOrEmpty()) LoginSource.SchemeUnknown.source else source,
                gid = gId,
                onlyLogin = onlyLogin,
                continueAccountInfo = runCatching { continueAccountInfo?.fromJSON<ContinueAccountInfo>() }.getOrNull(),
                successToMain = successToMain
            )
        }
        return LinkHandleResult.Success
    }

}