package com.socialplay.gpark.function.pay.way

import android.app.Activity
import com.socialplay.gpark.data.model.Product
import com.socialplay.gpark.data.model.pay.IAPConstants
import com.socialplay.gpark.data.model.pay.IAPConstants.PAY_CHANNEL_SIMULATE
import com.socialplay.gpark.data.model.pay.PayParams

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2021/06/02
 *     desc   : 模拟支付
 *
 */
class SimulationPlatform : BasePayPlatform<PayParams>() {
    override fun platformType(): Int = PAY_CHANNEL_SIMULATE

    override fun startPay(activity: Activity, params : PayParams) {
        if (getAgentPayParams()?.currentIapScene == IAPConstants.IAP_SCENE_VIP_PLUS_RENEW) {
            //恢复订阅商品
            onRenewPaySuccess()
        }else{
            onPaySuccess()
        }

    }

    override suspend fun consumeGoods(type: String, purchaseToken: String) {

    }
}