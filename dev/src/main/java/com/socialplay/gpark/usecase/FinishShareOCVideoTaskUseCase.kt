package com.socialplay.gpark.usecase

import com.socialplay.gpark.data.kv.DailyTaskKV
import com.socialplay.gpark.data.kv.MetaKV
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.koin.core.context.GlobalContext

class FinishShareOCVideoTaskUseCase(
    val finishDailyTaskUseCase: FinishDailyTaskUseCase,
    private val dailyTaskKV: DailyTaskKV
) {
    suspend operator fun invoke() {
        withContext(Dispatchers.IO) {
            launch {
                val metaKV by lazy { GlobalContext.get().get<MetaKV>() }
                val pair = metaKV.dailyTaskKV.getShareOCVideoInfo() ?: return@launch
                val (actId, logicId) = pair
                finishDailyTaskUseCase(actId, logicId)
                dailyTaskKV.resetDailyTasKShareOcVideo()
            }
        }
    }
}