package com.socialplay.gpark.util

import android.annotation.SuppressLint
import android.content.Context
import android.os.Build
import android.system.ErrnoException
import android.system.Os
import android.system.OsConstants
import java.io.File
import java.io.IOException
import java.io.RandomAccessFile
import java.nio.channels.FileLock
import android.os.Process
import android.util.Log


/**
 *     author : wei.zhu
 *     e-mail : <EMAIL>
 *     time   : 2021/07/29
 *     desc   : 仿照Chrome中写的 https://chromium.googlesource.com/chromium/src/+/refs/heads/main/android_webview/java/src/org/chromium/android_webview/AwDataDirLock.java
 *     主要用于监测进程是否占用了WebView的数据目录，如果占用了就会切换为使用其他的目录
 *
 */
@SuppressLint("LogNotTimber")
object WebViewDataDirFixer {

    private class WebViewDataDirLocker(private val processName: String) {

        companion object {

            private const val TAG = "WebViewDataDirLocker"
            private const val EXCLUSIVE_LOCK_FILE = "webview_data_fake.lock"

            // This results in a maximum wait time of 1.5s
            private const val LOCK_RETRIES = 16
            private const val LOCK_SLEEP_MS = 100
        }

        var exclusiveFileLock: FileLock? = null

        fun lock(dataDir: String) {
            val lockFile = File(dataDir, EXCLUSIVE_LOCK_FILE)

            val rafLockFile: RandomAccessFile = try {
                RandomAccessFile(lockFile, "rw")
            } catch (e: IOException) {
                throw RuntimeException("Failed to create lock file $lockFile", e)
            }

            for (attempts in 1..LOCK_RETRIES) {
                try {
                    exclusiveFileLock = rafLockFile.channel.tryLock()
                } catch (e: IOException) {
                }

                if (exclusiveFileLock != null) {
                    writeCurrentProcessInfo(rafLockFile)
                    return
                }

                if (attempts == LOCK_RETRIES) {
                    break
                }

                try {
                    Thread.sleep(LOCK_SLEEP_MS.toLong())
                } catch (e: InterruptedException) {
                }
            }

            throw RuntimeException(getLockFailureReason(rafLockFile))
        }

        private fun writeCurrentProcessInfo(file: RandomAccessFile) {
            try {
                file.setLength(0)
                file.writeInt(Process.myPid())
                file.writeUTF(processName)
            } catch (e: IOException) {
                Log.w(TAG, "Failed to write info to lock file", e)
            }
        }

        private fun getLockFailureReason(file: RandomAccessFile): String {
            val error = StringBuilder("Using WebView from more than one process at "
                    + "once with the same data directory is not supported. https://crbug.com/558377 "
                    + ": Current process ")

            error.append(processName)
            error.append(" (pid ").append(Process.myPid()).append("), lock owner ")
            try {
                val pid = file.readInt()
                val processName = file.readUTF()
                error.append(processName).append(" (pid ").append(pid).append(")")

                try {
                    Os.kill(pid, 0)
                } catch (e: ErrnoException) {
                    when (e.errno) {
                        OsConstants.ESRCH -> {
                            error.append(" doesn't exist!")
                        }
                        OsConstants.EPERM -> {
                            error.append(" pid has been reused!")
                        }
                        else              -> {
                            error.append(" status unknown!")
                        }
                    }
                }
            } catch (e: IOException) {
                error.append(" unknown")
            }
            return error.toString()
        }
    }

    //静态持有Locker对象，使其不被释放掉，防止锁被释放
    private var locker: WebViewDataDirLocker? = null

    fun fix(context: Context, packageName: String, processName: String?, setWebViewDataDirBlock: (dataDir: String) -> Unit) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.P || locker != null) {
            return
        }

        val processNameOrUnknown = processName ?: "unknown"

        locker = WebViewDataDirLocker(processNameOrUnknown)
        var dataDirToUse = processNameOrUnknown

        try {

            //如果出现文件被占用的情况，使用20个不同的目录尝试
            for (i in 0 until 20) {
                try {
                    locker?.lock(context.getDir("webview_$dataDirToUse", Context.MODE_PRIVATE).canonicalPath)
                    break
                } catch (e: Exception) {
                    Log.w("WebViewDataDirFixer", "Failed to set web directory processName:$processName packageName:$packageName dataDirToUse:$dataDirToUse times:$i")
                    dataDirToUse = "${processNameOrUnknown}_${System.nanoTime()}"
                }
            }

            setWebViewDataDirBlock(dataDirToUse)

            Log.w("WebViewDataDirFixer", "Set web data directory to $dataDirToUse on process $processName,PackageName:$packageName")
        } catch (e: Exception) {
            Log.w("WebViewDataDirFixer", "Failed to set web directory processName:$processName packageName:$packageName dataDirToUse:$dataDirToUse")
        }
    }
}