package com.socialplay.gpark.util

import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonObject
import com.google.gson.ToNumberPolicy
import com.google.gson.reflect.TypeToken
import timber.log.Timber
import java.io.File
import java.lang.reflect.Type

/**
 * create by: bin on 2021/5/17
 */
object GsonUtil {

    val gson = Gson()
    val gsonDisableHtml by lazy { GsonBuilder().disableHtmlEscaping().create() }
    val hasNullGson by lazy { GsonBuilder().serializeNulls().create() }
    // 防止long被转换为double
    val mapGson by lazy {
        GsonBuilder().
        setObjectToNumberStrategy(ToNumberPolicy.LAZILY_PARSED_NUMBER)
            .disableHtmlEscaping()
            .serializeNulls().create()
    }

    inline fun <reified T : Any> gsonSafeParse(jsonString: String?): T? {
        return try {
            if (jsonString.isNullOrBlank()) {
                null
            } else {
                gson.fromJson(jsonString, T::class.java)
            }
        } catch (e: Exception) {
            Timber.e(e)
            null
        }
    }

    /**
     * 安全的转换集合类型、携带泛型的类
     */
    inline fun <reified T : Any> gsonSafeParseCollection(jsonString: String?): T? {
        return try {
            if (jsonString.isNullOrBlank()) {
                null
            } else {
                gson.fromJson<T>(jsonString, object : TypeToken<T>() {}.type)
            }
        } catch (e: Exception) {
            Timber.e(e)
            null
        }
    }

    fun safeToJson(src: Any, def: String = ""): String {
        return kotlin.runCatching {
            gson.toJson(src)
        }.getOrElse {
            Timber.e(it, "GsonUtil safeToJson")
            def
        }
    }

    inline fun <reified T: Any> File.fromJson(): T = gson.fromJson(readText(), T::class.java)
    inline fun <reified T: Any> String.fromJson(): T = gson.fromJson(this, T::class.java)

    fun File.toJsonElement(): JsonElement = fromJson()
    fun File.toJsonObject(): JsonObject = fromJson()
    fun String.toJsonElement(): JsonElement = fromJson()
    fun String.toJsonObject(): JsonObject = fromJson()
}


fun Any?.toJSON(): String? {
    return GsonUtil.gson.toJson(this)
}

inline fun <reified T> String.fromJSON(): T {
    return GsonUtil.gson.fromJson(this, object : TypeToken<T>() {}.type)
}