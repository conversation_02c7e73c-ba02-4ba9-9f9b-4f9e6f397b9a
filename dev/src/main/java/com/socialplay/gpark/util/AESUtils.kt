package com.socialplay.gpark.util

import android.util.Base64
import timber.log.Timber
import java.io.InputStream
import java.io.OutputStream
import java.security.InvalidParameterException
import java.security.NoSuchAlgorithmException
import java.text.MessageFormat
import javax.crypto.Cipher
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec

object AESUtils {
    /**
     * AES加解密
     */
    private const val ALGORITHM = "AES"

    /**
     * 默认的初始化向量值
     */
    private const val IV_DEFAULT = "234ji39dj49du4d8"

    /**
     * 默认加密的KEY
     */
    private const val KEY_DEFAULT = "8H6A9K13SI67ABG9"

    /**
     * 工作模式：CBC
     */
    private const val TRANSFORM_CBC_PKCS5 = "AES/CBC/PKCS5Padding"

    /**
     * 工作模式：ECB
     */
    private const val TRANSFORM_ECB_PKCS5 = "AES/ECB/PKCS5Padding"

    /**
     * 基于CBC工作模式的AES加密数据
     * @param value 待加密数据
     * @param key 秘钥，如果不填则使用默认值
     * @param iv 初始化向量值，如果不填则使用默认值
     * @return ByteArray
     */
    fun encryptCbcMode(value: ByteArray, key: String = KEY_DEFAULT, iv: String = IV_DEFAULT): ByteArray? {
        if (key.length != 16 || iv.length != 16) {
            throw InvalidParameterException("key/iv length must is 16")
        }
        //密码
        val keySpec = getSecretKey(key)
        //初始化向量器
        val ivParameterSpec = IvParameterSpec(iv.toByteArray())
        try {
            val encipher = Cipher.getInstance(TRANSFORM_CBC_PKCS5)
            //加密模式
            encipher.init(Cipher.ENCRYPT_MODE, keySpec, ivParameterSpec)
            //使用AES加密
            return encipher.doFinal(value)
        } catch (e: Exception) {
            Timber.d(MessageFormat.format("基于CBC工作模式的AES加密失败,VALUE:{0},KEY:{1}", value, key))
            e.printStackTrace()
        }
        return null
    }


    /**
     * 基于CBC工作模式的AES加密字符串
     * @param value 待加密字符串
     * @param key 秘钥，如果不填则使用默认值
     * @param iv 初始化向量值，如果不填则使用默认值
     * @return java.lang.String
     */
    fun encryptCbcMode(value: String, key: String = KEY_DEFAULT, iv: String = IV_DEFAULT): String? {
        if (key.length != 16 || iv.length != 16) {
            throw InvalidParameterException("key/iv length must is 16")
        }
        //密码
        val keySpec = getSecretKey(key)
        //初始化向量器
        val ivParameterSpec = IvParameterSpec(iv.toByteArray())
        try {
            val encipher = Cipher.getInstance(TRANSFORM_CBC_PKCS5)
            //加密模式
            encipher.init(Cipher.ENCRYPT_MODE, keySpec, ivParameterSpec)
            //使用AES加密
            val encrypted = encipher.doFinal(value.toByteArray())
            //然后转成BASE64返回
            return String(encrypted)
        } catch (e: Exception) {
            Timber.d(MessageFormat.format("基于CBC工作模式的AES加密失败,VALUE:{0},KEY:{1}", value, key))
            e.printStackTrace()
        }
        return null
    }

    /**
     * 基于CBC工作模式的AES解密数据
     * @param encryptedBytes AES加密之后的数据
     * @param key 秘钥，如果不填则使用默认值
     * @param iv 初始化向量值，如果不填则使用默认值
     * @return ByteArray
     */
    fun decryptCbcMode(encryptedBytes: ByteArray, key: String = KEY_DEFAULT, iv: String = IV_DEFAULT): ByteArray? {
        if (key.length != 16 || iv.length != 16) {
            throw InvalidParameterException("key/iv length must is 16")
        }
        // 密码
        val keySpec = getSecretKey(key)
        Timber.d("AES解密时的秘钥 %s", keySpec)
        // 初始化向量器
        val ivParameterSpec = IvParameterSpec(iv.toByteArray())
        try {
            val encipher = Cipher.getInstance(TRANSFORM_CBC_PKCS5)

            // 设置加密模式
            encipher.init(Cipher.DECRYPT_MODE, keySpec, ivParameterSpec)
            // 然后再AES解密返回
            return encipher.doFinal(encryptedBytes)
        } catch (e: Exception) {
            Timber.d(MessageFormat.format("基于CBC工作模式的AES解密失败,encryptedStr:{0},KEY:{1}", encryptedBytes, key))
            e.printStackTrace()
        }
        return null
    }

    /**
     * 基于CBC工作模式的AES解密数据
     * @param encryptedStream AES加密之后的数据
     * @param key 秘钥，如果不填则使用默认值
     * @param iv 初始化向量值，如果不填则使用默认值
     * @return ByteArray
     */
    fun decryptCbcMode(encryptedStream: InputStream, decryptedStream: OutputStream, key: String = KEY_DEFAULT, iv: String = IV_DEFAULT) {
        if (key.length != 16 || iv.length != 16) {
            throw InvalidParameterException("key/iv length must is 16")
        }
        // 密码
        val keySpec = getSecretKey(key)
        Timber.d("AES解密时的秘钥 %s", keySpec)
        // 初始化向量器
        val ivParameterSpec = IvParameterSpec(iv.toByteArray())
        try {
            val encipher = Cipher.getInstance(TRANSFORM_CBC_PKCS5)

            // 设置加密模式
            encipher.init(Cipher.DECRYPT_MODE, keySpec, ivParameterSpec)
            // 然后再AES解密返回

            val buffer = ByteArray(1024 * 1024*4)
            var len = -1
            while (true) {
                len = encryptedStream.read(buffer)
                if (len == -1) {
                    break
                }

                decryptedStream.write(encipher.update(buffer,0,len))
            }

            decryptedStream.write(encipher.doFinal())
        } catch (e: Exception) {
            throw IllegalStateException(MessageFormat.format("基于CBC工作模式的AES解密失败,encryptedStr:{0},KEY:{1}", encryptedStream, key), e)
        }
    }


    /**
     * 基于CBC工作模式的AES解密字符串
     * @param encryptedStr AES加密之后的字符串
     * @param key 秘钥，如果不填则使用默认值
     * @param iv 初始化向量值，如果不填则使用默认值
     * @return ByteArray
     */
    fun decryptCbcMode(encryptedStr: String, key: String = KEY_DEFAULT, iv: String = IV_DEFAULT): String? {
        if (key.length != 16 || iv.length != 16) {
            throw InvalidParameterException("key/iv length must is 16")
        }
        // 密码
        val keySpec = getSecretKey(key)
        // 初始化向量器
        val ivParameterSpec = IvParameterSpec(iv.toByteArray())
        try {
            val encipher = Cipher.getInstance(TRANSFORM_CBC_PKCS5)
            // 设置加密模式
            encipher.init(Cipher.DECRYPT_MODE, keySpec, ivParameterSpec)
            // 然后再AES解密返回
            val doFinal = encipher.doFinal(encryptedStr.toByteArray())
            return String(doFinal)
        } catch (e: Exception) {
            Timber.d(MessageFormat.format("基于CBC工作模式的AES解密失败,encryptedStr:{0},KEY:{1}", encryptedStr, key))
            e.printStackTrace()
        }
        return null
    }


    /**
     * 基于ECB工作模式的AES加密数据
     * @param value 待加密数据
     * @param key 秘钥，如果不填则使用默认值
     * @return ByteArray
     */
    fun encryptEcbMode(value: ByteArray, key: String = KEY_DEFAULT): ByteArray? {
        if (key.length != 16 && key.length != 32) {
            throw InvalidParameterException("key length must is 16 or 32")
        }
        //密码
        val keySpec = getSecretKey(key)
        try {
            val encipher = Cipher.getInstance(TRANSFORM_ECB_PKCS5)
            //加密模式
            encipher.init(Cipher.ENCRYPT_MODE, keySpec)
            //使用AES加密
            return encipher.doFinal(value)
        } catch (e: Exception) {
            Timber.d(MessageFormat.format("基于ECB工作模式的AES加密失败,VALUE:{0},KEY:{1}", value, key))
            e.printStackTrace()
        }
        return null
    }

    /**
     * 基于ECB工作模式的AES加密字符串
     * @param value 待加密字符串
     * @param key 秘钥，如果不填则使用默认值
     * @return java.lang.String
     */
    fun encryptEcbMode(value: String, key: String = KEY_DEFAULT): String? {
        if (key.length != 16 && key.length != 32) {
            throw InvalidParameterException("key length must is 16 or 32")
        }
        //密码
        val keySpec = getSecretKey(key)
        try {
            val encipher = Cipher.getInstance(TRANSFORM_ECB_PKCS5)
            //加密模式
            encipher.init(Cipher.ENCRYPT_MODE, keySpec)
            //使用AES加密
            val encrypted = encipher.doFinal(value.toByteArray())
            //然后转成BASE64返回
            return String(Base64.encode(encrypted, Base64.NO_WRAP))
        } catch (e: Exception) {
            Timber.d(MessageFormat.format("基于ECB工作模式的AES加密失败,VALUE:{0},KEY:{1}", value, key))
            e.printStackTrace()
        }
        return null
    }

    /**
     * 基于ECB工作模式的AES解密数据
     * @param encryptedBytes AES加密之后的数据
     * @param key 秘钥，如果不填则使用默认值
     * @return java.lang.String
     */
    fun decryptEcbMode(encryptedBytes: ByteArray, key: String = KEY_DEFAULT): ByteArray? {
        if (key.length != 16 && key.length != 32) {
            throw InvalidParameterException("key length must is 16 or 32")
        }
        //密码
        val keySpec = getSecretKey(key)
        try {
            val encipher = Cipher.getInstance(TRANSFORM_ECB_PKCS5)
            //加密模式
            encipher.init(Cipher.DECRYPT_MODE, keySpec)
            //然后再AES解密
            return encipher.doFinal(encryptedBytes)
        } catch (e: Exception) {
            Timber.d(MessageFormat.format("基于ECB工作模式的AES解密失败,encryptedStr:{0},KEY:{1}", keySpec, key))
            e.printStackTrace()
        }
        return null
    }

    /**
     * 基于ECB工作模式的AES解密字符串
     * @param encryptedStr AES加密之后的字符串
     * @param key 秘钥，如果不填则使用默认值
     * @return java.lang.String
     */
    fun decryptEcbMode(encryptedStr: String, key: String = KEY_DEFAULT): String? {
        if (key.length != 16 && key.length != 32) {
            throw InvalidParameterException("key length must is 16 or 32")
        }
        //密码
        val keySpec = getSecretKey(key)
        try {
            //Base64解码
            val decodedBytes: ByteArray = Base64.decode(encryptedStr, Base64.NO_WRAP)

            val encipher = Cipher.getInstance(TRANSFORM_ECB_PKCS5)
            //加密模式
            encipher.init(Cipher.DECRYPT_MODE, keySpec)
            //然后再AES解密
            val originalBytes = encipher.doFinal(decodedBytes)
            //返回字符串
            return String(originalBytes)
        } catch (e: Exception) {
            Timber.d(MessageFormat.format("基于ECB工作模式的AES解密失败,encryptedStr:{0},KEY:{1}", encryptedStr, key))
            e.printStackTrace()
        }
        return null
    }

    /**
     * 生成加密秘钥
     * @param key 明文秘钥
     * @return SecretKeySpec
     */
    private fun getSecretKey(key: String): SecretKeySpec? {
        //生成指定算法密钥
        try {
            return SecretKeySpec(key.toByteArray(), ALGORITHM)
        } catch (ex: NoSuchAlgorithmException) {
            Timber.d(MessageFormat.format("生成加密秘钥失败,KEY:{0}", key))
            ex.printStackTrace()
        }
        return null
    }

    fun initAESCipher(key: String, iv: String): Cipher? {
        var cipher: Cipher?
        try {
            val ivParameterSpec = IvParameterSpec(iv.toByteArray())
            val keySpec = getSecretKey(key)
            cipher = Cipher.getInstance(TRANSFORM_CBC_PKCS5)
            cipher?.init(Cipher.DECRYPT_MODE, keySpec, ivParameterSpec)
        } catch (e: Exception) {
            cipher = null
            e.printStackTrace()
        }
        return cipher
    }
}