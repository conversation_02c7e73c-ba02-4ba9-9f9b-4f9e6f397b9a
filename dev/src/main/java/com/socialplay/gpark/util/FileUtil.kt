package com.socialplay.gpark.util

import android.content.Context
import android.graphics.Bitmap
import android.os.Environment
import android.os.StatFs
import com.luck.picture.lib.config.SelectMimeType
import timber.log.Timber
import java.io.*
import java.util.*

object FileUtil {
    //视频剪裁目录
    private const val DEFAULT_DIR = "/sdcard/233LeYuanRecorded/"
    private val mTmpFileSubFix = "" //后缀,
    private val mTmpFilePreFix = "" //前缀;
    private val mLock = Any()
    private const val mBufferSize = 524288
    private val TAG: String = FileUtil::class.java.simpleName

    /**
     * SD卡存在并可以使用
     */
    fun isSDExists(): Boolean {
        return Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED)
    }

    //创建文件
    fun createFile(path: String?): File {
        val file = File(path)
        kotlin.runCatching {
            if (file.exists()) {
                deleteFile(file)
            }
            file.createNewFile()
        }.getOrElse { Timber.e(it) }
        return file
    }

    /**
     * 删除文件目录以及目录下的文件
     *
     * @param file
     */
    fun deleteFileDirectory(file: File) {
        deleteFile(file)
        file.delete()
    }

    /**
     * delete dir or files
     *
     * @param file
     */
    fun deleteFile(file: File?) {
        if (file == null || !file.exists()) {
            return
        }
        if (file.isDirectory) {
            val files = file.listFiles() ?: return
            for (subFile in files) {
                deleteFile(subFile)
            }
        } else {
            file.delete()
        }
    }

    /**
     * 删除文件(通过路径)
     * @param fileDirectory 文件夹路径
     * @param fileName 文件名
     */
    fun delFile(fileDirectory: String, fileName: String) {
        val file: File = File(fileDirectory + fileName)
        if (file.isFile) {
            file.delete()
        }
    }

    /**
     * 删除文件夹和文件夹里面的文件
     *
     * @param
     * @return
     */
    fun deleteDirWithFile(tempFile: File) {
        kotlin.runCatching {
            if (tempFile.exists()) {
                //里面的文件删除
                tempFile.deleteRecursively()
                //外层文件删除
                tempFile.delete()
            }
        }.getOrElse { Timber.e(it) }
    }

    /**
     * 判断文件是否存在
     *
     * @param filePath 文件路径
     *
     * @return
     */
    fun isFileExist(filePath: String?): Boolean {
        kotlin.runCatching {
            val file = File(filePath)
            return file.exists()
        }.getOrDefault(false)
        return false
    }

    /**
     * 判断文件夹是否存在,不存在就创建
     *
     * @param path
     * @return true
     */
    fun hasFileDir(path: String?): Boolean {
        val file = File(path)
        if (!file.exists()) {
            file.mkdirs()
        }
        return file.exists()
    }


    private fun createOrExistsDir(file: File?): Boolean {
        return file != null && if (file.exists()) file.isDirectory else file.mkdirs()
    }

    fun isFileExists(originFile: File): Boolean {
        return originFile.exists() && originFile.length() > 0
    }


    fun createOrExistsFile(file: File?): Boolean {
        if (file == null) return false
        if (file.exists()) return file.isFile
        return if (!createOrExistsDir(file.parentFile)) false else try {
            file.createNewFile()
        } catch (e: IOException) {
            e.printStackTrace()
            false
        }
    }

    /**
     * 通过路径获取文件
     */
    fun getFile(filePath: String?): File {
        val dir = File(filePath)
        if (!dir.parentFile.exists()) {
            dir.parentFile.mkdirs()
        }
        val file = File(filePath)
        if (!file.exists()) {
            try {
                val flag = file.createNewFile()
                if (!flag) {
                    Timber.d(TAG, "create new file fail")
                }
            } catch (e: java.lang.Exception) {
                Timber.d(e.message)
            }
        }
        return file
    }

    /**
     * 在box目录下生成一个mp4的文件,并返回名字的路径.(视频剪裁处使用)
     *
     * @return
     */
    fun createMp4FileInBox(): String {
        return createFileWithSuffix(DEFAULT_DIR, ".mp4")
    }

    /**
     * 在指定的文件夹里创建一个文件名字, 名字是当前时间,指定后缀.
     * 创建带有后缀的文件
     */
    fun createFileWithSuffix(dir: String, suffix: String): String {
        synchronized(mLock) {
            val c = Calendar.getInstance()
            val hour = c[Calendar.HOUR_OF_DAY]
            val minute = c[Calendar.MINUTE]
            var year = c[Calendar.YEAR]
            val month = c[Calendar.MONTH] + 1
            val day = c[Calendar.DAY_OF_MONTH]
            val second = c[Calendar.SECOND]
            val millisecond = c[Calendar.MILLISECOND]
            year = year - 2000
            var dirPath = dir
            val d = File(dirPath)
            if (!d.exists()) d.mkdirs()
            if (dirPath.endsWith("/") == false) {
                dirPath += "/"
            }
            var name: String = mTmpFilePreFix
            name += year.toString()
            name += month.toString()
            name += day.toString()
            name += hour.toString()
            name += minute.toString()
            name += second.toString()
            name += millisecond.toString()
            name += mTmpFileSubFix
            if (suffix.startsWith(".") == false) {
                name += "."
            }
            name += suffix
            try {
                Thread.sleep(1) // 保持文件名的唯一性.
            } catch (e: InterruptedException) {
                // TODO Auto-generated catch block
                e.printStackTrace()
            }
            val retPath = dirPath + name
            createFile(retPath)
            return retPath
        }
    }

    /**
     * 将内容写进文件
     */
    fun writeToFile(filePath: String?, content: String?): Boolean {
        val file: File = getFile(filePath)
        kotlin.runCatching {
            val fw = FileWriter(file, false)
            val bw = BufferedWriter(fw)
            bw.write(content)
            bw.close()
            fw.close()
            return true
        }.getOrElse {
            Timber.d(TAG, "write file", it.message)
        }
        return false
    }

    /**
     * 读取文件内容()通过路径
     */
    fun readFile(filePath: String?): String {
        val file = File(filePath)
        val stringBuilder = StringBuilder()
        val buf = CharArray(64)
        var count = 0
        kotlin.runCatching {
            val fileInputStream = FileInputStream(file)
            val reader = InputStreamReader(fileInputStream, "UTF-8")
            while (reader.read(buf).also { count = it } != -1) {
                stringBuilder.appendRange(buf, 0, count)
            }
        }.getOrElse {
            Timber.d(TAG, "readFile", it.message)
        }
        return stringBuilder.toString()
    }

    /**
     * 读取文件（通过文件）
     */
    fun readFile(targetFile: File?): String {
        val result = java.lang.StringBuilder()
        if (targetFile == null || !targetFile.exists()) return result.toString()
        targetFile.run {
            kotlin.runCatching {
                val fileReader = FileReader(targetFile)
                val buf = CharArray(1024)
                var len: Int
                while (fileReader.read(buf, 0, 1024).also { len = it } != -1) {
                    result.append(String(buf, 0, len))
                }
            }.getOrElse { Timber.d(it) }
        }
        return result.toString()
    }


    /**
     * 获取SD卡的剩余容量，单位是Byte
     *
     * @return
     */
    fun getSDFreeMemory(): Long {
        kotlin.runCatching {
            if (isSDExists()) {
                val pathFile = Environment.getExternalStorageDirectory()
                // Retrieve overall information about the space on a filesystem.
                // This is a Wrapper for Unix statfs().
                val statfs = StatFs(pathFile.path)
                // 获取SDCard上每一个block的SIZE
                val nBlockSize = statfs.blockSize.toLong()
                // 获取可供程序使用的Block的数量
                // long nAvailBlock = statfs.getAvailableBlocksLong();
                val nAvailBlock = statfs.availableBlocks.toLong()
                // 计算SDCard剩余大小Byte
                return nAvailBlock * nBlockSize
            }
        }.getOrDefault(0)
        return 0
    }

    /**
     * 获取指定文件大小
     */
    fun getFileSize(file: File): Long {
        return if (file.exists()) {
            file.length()
        } else 0
    }

    /**
     * 获取指定文件夹大小
     */
    fun getDirectorySize(file: File): Long {
        var size: Long = 0
        val fileList = file.listFiles() ?: return 0
        for (file in fileList) {
            if (file.isDirectory) {
                size = size + getDirectorySize(file)
            } else {
                size = size + getFileSize(file)
            }
        }
        return size
    }

    /**
     *
     * 判断文件是否可以写入
     */
    fun write(str: String, file: File?): Boolean {
        return write(str.toByteArray(), file)
    }

    fun write(bytes: ByteArray?, file: File?): Boolean {
        if (!preWrite(file)) return false
        var output: FileOutputStream? = null
        return try {
            output = FileOutputStream(file)
            output.write(bytes)
            output.flush()
            true
        } catch (err: Throwable) {
            err.printStackTrace()
            false
        } finally {
            close(output)
        }
    }

    fun write(input: InputStream, file: File?, bufferSize :Int = 1024 * 8): Boolean {
        if (!preWrite(file)) return false
        var output: FileOutputStream? = null
        return try {
            output = FileOutputStream(file)
            val buff = ByteArray(bufferSize)
            var len: Int
            while (input.read(buff).also { len = it } != -1) {
                output.write(buff, 0, len)
            }
            output.flush()
            true
        } catch (err: Throwable) {
            err.printStackTrace()
            false
        } finally {
            close(output)
        }
    }

    fun readStrings(file: File?): Array<String?>? {
        if (!preRead(file)) return null
        var `in`: InputStream? = null
        return try {
            `in` = FileInputStream(file)
            readStrings(`in`)
        } catch (e: Throwable) {
            e.printStackTrace()
            null
        } finally {
            close(`in`)
        }
    }

    fun readStrings(`in`: InputStream?): Array<String?>? {
        kotlin.runCatching {
            val reader = BufferedReader(InputStreamReader(`in`))
            val list: MutableList<String?> = ArrayList()
            var line: String?
            while (reader.readLine().also { line = it } != null) {
                list.add(line)
            }
            return list.toTypedArray()
        }.getOrNull()
        return null
    }

    fun preWrite(file: File?): Boolean {
        return preDir(file?.parentFile)
    }

    private fun preRead(file: File?): Boolean {
        if (file == null) {
            return false
        }
        return file.length() != 0L
    }

    fun preDir(dir: File?): Boolean {
        if (dir == null) {
            return false
        }
        return !(!dir.exists() && !dir.mkdirs())
    }

    fun close(vararg closeables: Closeable?) {
        for (closeable in closeables) {
            if (closeable == null) continue
            try {
                closeable.close()
            } catch (ignore: Throwable) {
            }
        }
    }


    /**
     * 移动文件
     *
     * @param src
     * @param tar
     * @return
     * @throws Exception
     */
    @Throws(java.lang.Exception::class)
    fun moveFile(src: File?, tar: File?): Boolean {
        if (copyFile(src, tar)) {
            deleteFile(src)
            return true
        }
        return false
    }

    /**
     * 复制文件
     *
     * @param src
     * @param tar
     * @return
     * @throws Exception
     */
    @Throws(java.lang.Exception::class)
    fun copyFile(src: File?, tar: File?): Boolean {
        if (src?.isFile == true) {
            val `is`: InputStream = FileInputStream(src)
            val op: OutputStream = FileOutputStream(tar)
            val bis = BufferedInputStream(`is`)
            val bos = BufferedOutputStream(op)
            val bt = ByteArray(1024 * 8)
            var len = bis.read(bt)
            while (len != -1) {
                bos.write(bt, 0, len)
                len = bis.read(bt)
            }
            bis.close()
            bos.close()
        }
        if (src?.isDirectory == true) {
            val f = src.listFiles()
            tar?.mkdir()
            for (i in f.indices) {
                copyFile(
                    f[i].absoluteFile,
                    File(tar?.absoluteFile.toString() + File.separator + f[i].name)
                )
            }
        }
        return true
    }

    /**
     * 获取最后的‘/’后的文件名
     *
     * @param name
     * @return
     */
    fun getLastName(name: String): String {
        var lastIndexOf = 0
        try {
            lastIndexOf = name.lastIndexOf('/')
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
        return if (name != "") name.substring(lastIndexOf + 1) else ""
    }

    /**
     * @param url 保存文件的文字
     * @return 文件名
     */
    fun getFileName(url: String?): String? {
        var fileName: String? = null
        if (url != null && url.contains("/")) {
            val data = url.split("/").toTypedArray()
            fileName = data[data.size - 1]
        }
        return fileName
    }

    /**
     * 写入字符串到文件 (同步模式，不另创线程）
     *
     * @param log   需要写入的字符串
     * @param file  写入的文件
     * @param isAdd 是否是追加，true:追加 false:覆盖
     */
    fun writeTextSync(log: String?, file: File, isAdd: Boolean): Boolean {
        try {
            if (!file.parentFile.exists()) {
                file.parentFile.mkdirs()
            }
            if (!file.exists()) {
                file.createNewFile()
            }
            var fos: FileOutputStream? =
                null //FileOutputStream会在BufferWriter close的时候自动调用底层的close()方法，不用关闭
            var bw: BufferedWriter? = null
            try {
                fos = FileOutputStream(file, isAdd) //这里的第二个参数代表追加还是覆盖，true为追加，flase为覆盖
                bw = BufferedWriter(OutputStreamWriter(fos, "UTF-8"))
                bw.write(log)
            } catch (e: java.lang.Exception) {
                e.printStackTrace()
                return false
            } finally {
                try {
                    bw?.close()
                } catch (e: java.lang.Exception) {
                    e.printStackTrace()
                }
            }
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
            return false
        }
        return true
    }

    /**
     * 删除目录下所有文件
     */
    fun deleteAllInDir(dir: File?): Boolean {
        return deleteFilesInDirWithFilter(dir) { pathname: File? -> true }
    }

    private fun deleteFilesInDirWithFilter(dir: File?, filter: FileFilter): Boolean {
        if (dir == null) return false
        if (!dir.exists()) return true
        if (!dir.isDirectory) return false
        val files = dir.listFiles()
        if (files != null && files.size != 0) {
            for (file in files) {
                if (filter.accept(file)) {
                    if (file.isFile) {
                        if (!file.delete()) return false
                    } else if (file.isDirectory) {
                        if (!deleteDirectory(file)) return false
                    }
                }
            }
        }
        return true
    }

    /**
     * 删除文件目录
     */
    private fun deleteDirectory(dir: File?): Boolean {
        if (dir == null) return false
        // dir doesn't exist then return true
        if (!dir.exists()) return true
        // dir isn't a directory then return false
        if (!dir.isDirectory) return false
        val files = dir.listFiles()
        if (files != null && files.size != 0) {
            for (file in files) {
                if (file.isFile) {
                    if (!file.delete()) return false
                } else if (file.isDirectory) {
                    if (!deleteDirectory(file)) return false
                }
            }
        }
        return dir.delete()
    }

    /**
     * 压缩视频本地缓存目录路径
     */
    fun getCompressVideoDir(context: Context, dirName: String): String {
        var dirPath = ""
        dirPath = if (Environment.MEDIA_MOUNTED == Environment.getExternalStorageState()) {
            (context.externalCacheDir.toString() + File.separator
                    + dirName)
        } else {
            (context.cacheDir.toString() + File.separator
                    + dirName)
        }
        val file = File(dirPath)
        if (!file.exists()) {
            file.mkdirs()
        }
        return dirPath
    }


    fun readFile2String(file: File?): String? {
        val bytes = readFile2BytesByStream(file)
        return bytes?.let { String(it) }
    }

    private fun readFile2BytesByStream(file: File?): ByteArray? {
        if (file == null || !file.exists()) {
            return null
        }
        var os: ByteArrayOutputStream? = null
        var `is`: BufferedInputStream? = null
        return try {
            `is` = BufferedInputStream(FileInputStream(file))
            os = ByteArrayOutputStream()
            val b = ByteArray(mBufferSize)
            var len: Int
            while (`is`.read(b, 0, mBufferSize)
                    .also { len = it } != -1
            ) {
                os.write(b, 0, len)
            }
            os.toByteArray()
        } catch (e: IOException) {
            e.printStackTrace()
            null
        } finally {
            close(`is`)
            close(os)
        }
    }

    fun writeFileFromIS(file: File?, `is`: InputStream?): Boolean {
        if (`is` == null || !createOrExistsFile(file)) {
            return false
        }
        var bw: OutputStream? = null
        return try {
            bw = BufferedOutputStream(FileOutputStream(file))
            val data = ByteArray(mBufferSize)
            var len: Int
            while (`is`.read(data).also { len = it } != -1) {
                bw.write(data, 0, len)
            }
            true
        } catch (e: IOException) {
            e.printStackTrace()
            false
        } finally {
            close(bw)
            close(`is`)
        }
    }

    /**
     * 获取指定文件夹大小
     */
    fun getFileSizes(f: File): Long {
        var size: Long = 0
        val flist = f.listFiles() ?: return 0
        for (file in flist) {
            size = if (file.isDirectory) {
                size + getFileSizes(file)
            } else {
                size + getFileSize(file)
            }
        }
        return size
    }

    fun havePhotoPath(context: Context, type: Int):Boolean{
        return when (type) {
            SelectMimeType.ofImage() -> {
                context.getExternalFilesDir(Environment.DIRECTORY_PICTURES) != null
            }
            SelectMimeType.ofVideo() -> {
                context.getExternalFilesDir(Environment.DIRECTORY_MOVIES) != null
            }
            else                     -> {
                false
            }
        }
    }
    fun saveImageToFile(bitmap: Bitmap, imageFile: File):Boolean {
        try {
            createOrExistsFile(imageFile)
            val fos = FileOutputStream(imageFile)
            bitmap.compress(Bitmap.CompressFormat.JPEG, 100, fos)
            fos.flush()
            fos.close()
            return true
        } catch (e: IOException) {
            e.printStackTrace()
            return false
        }
    }
}