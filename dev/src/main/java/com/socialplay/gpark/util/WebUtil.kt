package com.socialplay.gpark.util

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.view.ViewGroup
import android.webkit.WebView
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.meta.pandora.Platform
import com.socialplay.gpark.ui.web.jsinterfaces.JsBridgeHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.Locale


/**
 *
 * <AUTHOR>
 * @date 2021/05/20
 */
object WebUtil {
    /**
     * 系统相关协议
     */
    val systemSchemeList = listOf("tel","voicemail","sms","mailto","geo","google.streetview","metaapp")
    const val SCHEME_HTTP = "http"
    const val SCHEME_HTTPS = "https"

    /**
     * 是否需要处理系统的url地址
     */
    fun shouldHandlingSystemUrl(uri: Uri): Boolean = systemSchemeList.contains(uri.scheme)


    /**
     * 是否是http开头的协议
     */
    fun isHttpOrHttpsScheme(url: String): Boolean {
        return url.startsWith(SCHEME_HTTP,true) || url.startsWith(SCHEME_HTTPS,true)
    }

    /**
     * 通知内核尝试停止所有处理，如动画和地理位置，但是不能停止Js，如果想全局停止Js，可以调用pauseTimers()全局停止Js，调用onResume()恢复
     */
    fun onPause(webView: WebView, useTimer: Boolean = true) {
        //
        webView.onPause()
        if (useTimer) webView.pauseTimers()
    }

    /**
     * 恢复停止的一些处理
     */
    fun onResume(webView: WebView, useTimer: Boolean = true) {
        webView.onResume()
        if (useTimer) webView.resumeTimers()
    }

    /**
     * 清理webView
     */
    fun clearWebView(webView: WebView) {

        webView.loadDataWithBaseURL(null, "", "text/html", "utf-8", null)
        //移除内部消息
        webView.handler?.removeCallbacksAndMessages(null)
        //移除webview
        if(webView.parent != null) {
            (webView.parent as ViewGroup).removeView(webView)
        }
       //移除所有子View
        webView.removeAllViews()
        //移除js接口
        webView.removeJavascriptInterface(JsBridgeHelper.JS_BRIDGE_ALIAS)
        webView.webChromeClient = null
        webView.destroy()
     }

    fun isScheme(url: String?): Boolean {
        if (url.isNullOrBlank()) return false
        return !url.lowercase(Locale.getDefault()).startsWith("http://") &&
                !url.lowercase(Locale.getDefault()).startsWith("https://") &&
                url.contains("://") &&
                url.indexOf("://") != 0
    }

    /**
     * 获取WebView的默认UserAgent
     */
    fun getUserAgent(): String {
        return Platform.userAgent.get()
    }

    /**
     * @see com.socialplay.gpark.function.router.MetaRouter.Web.navigate(androidx.fragment.app.Fragment, java.lang.String, java.lang.String, boolean, java.lang.String, boolean, boolean)
     */
    @Deprecated("some devices can not access resolveActivity, so we use try catch to handle")
    fun openUrlInBrowser(activity: Activity, url: String): Boolean {
        try {
            val intent = Intent(Intent.ACTION_VIEW)
            intent.data = Uri.parse(url)
            if (intent.resolveActivity(activity.packageManager) != null) {
                activity.startActivity(intent)
                return true
            }
        } catch (ignored: Exception) {
        }
        return false
    }

    fun loadJs(owner: LifecycleOwner, webView: WebView, jsMethod: String, vararg params: Any?) {
        owner.lifecycleScope.launch {

        }
    }

    /**
     * 加载js方法
     */
    suspend fun loadJs(webView: WebView, jsMethod: String, vararg params: Any?) {
        withContext(Dispatchers.Main) {
            val jsMethodBuilder = StringBuilder()
            jsMethodBuilder.append("javascript:")
            if (jsMethod.contains("(")) {
                jsMethodBuilder.append(jsMethod.substring(0, jsMethod.indexOf("(") + 1))
            } else {
                jsMethodBuilder.append(jsMethod).append("(")
            }

            //拼接参数
            var isFirst = true
            for (param in params) {
                if (isFirst) {
                    isFirst = false
                } else {
                    jsMethodBuilder.append(",")
                }
                jsMethodBuilder.append("\'").append(param).append("\'")
            }

            jsMethodBuilder.append(")")

            val js = jsMethodBuilder.toString()

            Timber.d("App.loadJs=$js")
            webView.loadUrl(js)
        }
    }
}