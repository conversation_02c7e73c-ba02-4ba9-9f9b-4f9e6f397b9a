package com.socialplay.gpark.util

import android.content.Context
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.account.AgeOption
import com.socialplay.gpark.function.locale.MetaLanguages
import org.koin.core.context.GlobalContext
import java.text.DateFormat
import java.text.DateFormatSymbols
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.GregorianCalendar
import java.util.Locale
import java.util.TimeZone
import java.util.concurrent.TimeUnit

object DateUtil {

    // Nov 12, 2022  14:12 AM
    private val wholeDateFormat: SimpleDateFormat
        get() {
            return SimpleDateFormat(getStringByGlobal(R.string.detail_date_format), MetaLanguages.getAppCurrentLocale(GlobalContext.get().get())).apply {

                val symbols = DateFormatSymbols(MetaLanguages.getAppCurrentLocale(GlobalContext.get().get()))
                symbols.amPmStrings = arrayOf("AM", "PM")
                dateFormatSymbols = symbols
            }
        }

    // Nov 12, 2022  14:12 AM
    private val wholeDateFormatUTC: SimpleDateFormat
        get() {
            return SimpleDateFormat(getStringByGlobal(R.string.detail_date_format), MetaLanguages.getAppCurrentLocale(GlobalContext.get().get())).apply {
                timeZone = TimeZone.getTimeZone("UTC")
                val symbols = DateFormatSymbols(MetaLanguages.getAppCurrentLocale(GlobalContext.get().get()))
                symbols.amPmStrings = arrayOf("AM", "PM")
                dateFormatSymbols = symbols
            }
        }

    // Nov 12, 2022
    private val ymdDateFormat: SimpleDateFormat
        get() {
            return SimpleDateFormat(
                getStringByGlobal(R.string.detail_date_format_mdy),
                MetaLanguages.getAppCurrentLocale(GlobalContext.get().get())
            )
        }

    private const val DEFAULT_DATE_STR = "yyyy-MM-dd HH:mm:ss"
    val debugFileFormat = SimpleDateFormat("yyyy-MM-dd_HH_mm_ss")
    val debugFileFormatMs = SimpleDateFormat("yyyy-MM-dd_HH_mm_ss_SSS")

    private val DEFAULT_FORMAT = ThreadLocal<SimpleDateFormat>().apply { set(SimpleDateFormat(DEFAULT_DATE_STR, MetaLanguages.getAppCurrentLocale(GlobalContext.get().get()))) }

    const val SECOND = 1000
    const val MINUTE = SECOND * 60
    const val HOUR = MINUTE * 60
    const val DAY = HOUR * 24

    /*今天的日期时间戳*/
    fun getToday(): Long {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd")
        val calendar = Calendar.getInstance()
        var time = 0L
        try {
            time = dateFormat.parse(dateFormat.format(calendar.time)).time
        } catch (e: ParseException) {
            e.printStackTrace()
        }
        return time
    }

    /**
     * 获得基于今天加减天数的时间戳
     * @param offset >0 今天之后xx天，<0 今天之前xx天
     */
    fun getDayAnchorToday(offset: Int): Long {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd")
        val calendar = Calendar.getInstance()
        calendar.add(Calendar.DAY_OF_YEAR, offset)
        var time = 0L
        try {
            time = dateFormat.parse(dateFormat.format(calendar.time)).time
        } catch (e: ParseException) {
            e.printStackTrace()
        }
        return time
    }

    /**
     * @param time 是否为今天
     */
    fun isTimeStampToday(format: SimpleDateFormat, time: Long): Boolean {
        return kotlin.runCatching { format.parse(format.format(Date(System.currentTimeMillis())))!!.time - format.parse(format.format(Date(time)))!!.time == 0L }.getOrDefault(false)
    }

    /**
     * 获取今天的日期的yyyy-MM-dd格式
     */
    fun getTodayString(): String {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd")
        return dateFormat.format(Date())
    }

    /**
     * 获取今天的日期的HH:mm:ss格式
     */
    fun getTodayHM(): String {
        val dateFormat = SimpleDateFormat("HH:mm")
        return dateFormat.format(Date())
    }

    fun getDateString(date: Long, pattern: String): String {
        val dateFormat = SimpleDateFormat(pattern)
        return kotlin.runCatching { dateFormat.format(Date(date)) }.getOrDefault("")
    }

    /**
     * 获取今年
     */
    fun getYear(): String {
        val dateFormat = SimpleDateFormat("yyyy")
        return dateFormat.format(Date())
    }

    /**
     * 时间（毫秒）长度转 小时:分:秒
     */
    fun convertTimeMillsToHMSString(timeMills: Long): String {
        val second = timeMills / 1000
        val day = second / 3600 / 24
        val hour = second % (3600 * 24) / 3600
        val minute = second % (3600) / 60
        val s = second % 60
        val hours = if (hour >= 10) {
            hour
        } else {
            "0$hour"
        }
        val minutes = if (minute >= 10) {
            minute
        } else {
            "0$minute"
        }
        val seconds = if (s >= 10) {
            s
        } else {
            "0$s"
        }
        return "${hours}:${minutes}:${seconds}"
    }

    private fun calendar(): Calendar {
        val cal = GregorianCalendar.getInstance(Locale.CHINESE)
        cal.firstDayOfWeek = Calendar.MONDAY
        return cal
    }

    /**
     * 今天是年中的第几天
     *
     * @return
     */
    fun dayOfYear(): Int {
        return calendar().get(Calendar.DAY_OF_YEAR)
    }

    /**
     * 将时间戳转换成 xx小时前 的样式（同微信）
     *
     * @return
     *
     * 如果小于1秒钟内，显示刚刚
     * 如果在1分钟内，显示xx秒前
     * 如果在1小时内，显示xx分钟前
     * 如果是昨天，显示昨天
     * 如果在一个月内，显示xx天前
     * 如果在一年内，显示xx月前
     * 如果在两年内，显示xx年前
     * 其余显示，2017-09-01
     */
    fun Date.formatAgoStyleForChat(context: Context): String = time.formatAgoStyleForChat(context)

    /**
     * 将时间戳转换成 xx小时前 的样式（同微信）
     *
     * @return
     *
     * 如果小于1秒钟内，显示刚刚
     * 如果在1分钟内，显示xx秒前
     * 如果在1小时内，显示xx分钟前
     * 如果是昨天，显示昨天
     * 如果在一个月内，显示xx天前
     * 如果在一年内，显示xx月前
     * 如果在两年内，显示xx年前
     * 其余显示，2017-09-01
     */
    fun formatAgoStyleForChat(context: Context, time: String, format: DateFormat = DEFAULT_FORMAT.get()!!): String =
        parseDateString2Mills(time, format).formatAgoStyleForChat(context)

    /**
     * 将时间戳转换成 xx小时前 的样式（同微信）
     *
     * @return
     *
     * 如果小于1秒钟内，显示刚刚
     * 如果在1分钟内，显示xx秒前
     * 如果在1小时内，显示xx分钟前
     * 如果是昨天，显示昨天
     * 如果在一周内，显示xx天前
     * 其余显示，xx周前
     */
    fun Long.formatAgoStyleForChat(context: Context = GlobalContext.get().get()): String {
        val time = if (this == 0L) System.currentTimeMillis() else this
        val now = System.currentTimeMillis()
        val span = now - time
        return when {
            span <= TimeUnit.SECONDS.toMillis(1) -> context.getString(R.string.just)
            span <= TimeUnit.MINUTES.toMillis(1) -> context.getString(R.string.few_seconds_ago, span / TimeUnit.SECONDS.toMillis(1))
            span <= TimeUnit.HOURS.toMillis(1) -> context.getString(R.string.few_minutes_ago, span / TimeUnit.MINUTES.toMillis(1))
            span <= TimeUnit.DAYS.toMillis(1) -> context.getString(R.string.few_hours_ago, span / TimeUnit.HOURS.toMillis(1))
            span >= TimeUnit.DAYS.toMillis(1) && span <= TimeUnit.DAYS.toMillis(1) * 2 -> context.getString(R.string.yesterday)
            span <= TimeUnit.DAYS.toMillis(1) * 7 -> context.getString(R.string.few_days_ago, span / TimeUnit.DAYS.toMillis(1))
            else -> context.getString(R.string.few_weekend_ago, span / (TimeUnit.DAYS.toMillis(7)))
        }
    }

    //判断选择的日期是否是本年
    fun isThisYear(time: Date): Boolean {
        return isThisTime(time, "yyyy")
    }

    private fun isThisTime(time: Date, pattern: String): Boolean {
        val sdf = SimpleDateFormat(pattern)
        val param = sdf.format(time) //参数时间
        val now = sdf.format(Date()) //当前时间
        return param == now
    }

    fun Long.formatAgoStyleForSimple(curTime: Long): String {
        val time = if (this <= 0L) System.currentTimeMillis() else this
        val span = curTime - time
        val result = when {
            span < MINUTE -> "Playing"
            span < HOUR -> {
                val temp = span / MINUTE
                "${temp}m"
            }

            span < DAY -> {
                val temp = span / HOUR
                "${temp}h"
            }

            isThisYear(Date(curTime)) -> {
                mdFormat.format(Date(curTime))
            }

            else -> {
                yFormat.format(Date(curTime))
            }
        }
        return result
    }

    private val mdyFormat = SimpleDateFormat("MM/dd/yyyy")
    private val mdyLineFormat = SimpleDateFormat("MM-dd-yyyy")
    private val mdyLineFormatV2 = SimpleDateFormat("MM.dd.yyyy")
    private val yFormat = SimpleDateFormat("yyyy")
    private val mdFormat = SimpleDateFormat("MM/dd")
    private val mdLineFormat = SimpleDateFormat("MM-dd")
    private val mdLineFormatV2 = SimpleDateFormat("MM.dd")

    /**
     * 解析String类型的日期为Long类型
     *
     * @param time
     * @param format
     */
    fun parseDateString2Mills(time: String, format: DateFormat = DEFAULT_FORMAT.get()!!): Long {
        return try {
            format.parse(time).time
        } catch (e: ParseException) {
            e.printStackTrace()
            -1L
        }
    }

    /**
     * 计算给定的 [timestamp] 到当前时间过去了多久，并格式化为几分钟前，几小时前、几天前这种格式
     */
//    fun fuzzingTime(context: Context, timestamp: Long): String {
//        val currentTimestamp = System.currentTimeMillis()
//        val duration = currentTimestamp - timestamp
//
//        return when {
//            duration < TimeUnit.MINUTES.toMillis(1) -> {
//                context.getString(R.string.fuzzing_time_moment)
//            }
//            duration < TimeUnit.HOURS.toMillis(1) -> {
//                context.getString(R.string.fuzzing_time_minutes_before_formatted).format(TimeUnit.MILLISECONDS.toMinutes(duration))
//            }
//            duration < TimeUnit.DAYS.toMillis(1) -> {
//                context.getString(R.string.fuzzing_time_hours_before_formatted).format(TimeUnit.MILLISECONDS.toHours(duration))
//            }
//            duration < TimeUnit.DAYS.toMillis(7) -> {
//                context.getString(R.string.fuzzing_time_days_before_formatted).format(TimeUnit.MILLISECONDS.toDays(duration))
//            }
//            duration < TimeUnit.DAYS.toMillis(30) -> {
//                context.getString(R.string.fuzzing_time_week_before_formatted).format(TimeUnit.MILLISECONDS.toDays(duration) / 7)
//            }
//            duration < TimeUnit.DAYS.toMillis(365) -> {
//                context.getString(R.string.fuzzing_time_month_before_formatted).format(TimeUnit.MILLISECONDS.toDays(duration) / 30)
//            }
//            else -> {
//                context.getString(R.string.fuzzing_time_year_before_formatted).format(TimeUnit.MILLISECONDS.toHours(duration) / 365)
//            }
//        }
//    }

    fun formatAsMMSS(value: Long) = "${"%02d".format((value / MINUTE))}:${"%02d".format((value % MINUTE) / SECOND)}"

    fun getFormatTimeByTimeStamp(timeStamp: Long): String {
        return SimpleDateFormat("HH:mm MM/dd/yyyy", Locale.ROOT).format(Date(timeStamp))
    }

    fun getFormatDayTimeByTimeStamp(timeStamp: Long): String {
        try {
            val hours = timeStamp / 3600
            val remainingSeconds = timeStamp % 3600
            val minutes = remainingSeconds / 60
            val secs = remainingSeconds % 60
            return String.format("%02d:%02d:%02d", hours, minutes, secs)
        } catch (e: Exception) {
            return "00:00:00"
        }

    }

    /**
     * 格式化发布日期：
     * 1. 今年：MM-dd
     * 2. 其他：MM-dd-yyyy
     */
    fun Long.formatPublishDate(): String {
        val time = if (this <= 0L) System.currentTimeMillis() else this
        val result = when {
            isThisYear(Date(time)) -> {
                mdLineFormat.format(Date(time))
            }

            else -> {
                mdyLineFormat.format(Date(time))
            }
        }
        return result
    }

    fun Long.formatDateSimpleV2(): String {
        val time = if (this <= 0L) System.currentTimeMillis() else this
        val result = when {
            isThisYear(Date(time)) -> {
                mdLineFormatV2.format(Date(time))
            }

            else -> {
                mdyLineFormatV2.format(Date(time))
            }
        }
        return result
    }

    fun getYearByDate(date: Date): Int {
        val cal = Calendar.getInstance().apply {
            time = date
        }
        return cal[Calendar.YEAR]
    }

    fun getAgeByBirthDay(birthDay: Date): Int {
        val cal = Calendar.getInstance()
        if (cal.before(birthDay)) {
            return 0
        }
        val yearNow = cal[Calendar.YEAR]
        val monthNow = cal[Calendar.MONTH]
        val dayOfMonthNow = cal[Calendar.DAY_OF_MONTH]
        cal.time = birthDay
        val yearBirth = cal[Calendar.YEAR]
        val monthBirth = cal[Calendar.MONTH]
        val dayOfMonthBirth = cal[Calendar.DAY_OF_MONTH]
        var age = yearNow - yearBirth
        if (monthNow < monthBirth) {
            age--
        } else if (monthNow == monthBirth && dayOfMonthNow < dayOfMonthBirth) {
            age--
        }
        return age
    }

    fun Long.formatYmdDate(): String {
        return Date(this).formatYmdDate()
    }

    /**
     * Nov 12, 2022
     */
    fun Date.formatYmdDate(): String = ymdDateFormat.format(this)

    /**
     * Nov 12, 2022  14:12 AM
     */
    fun Date.formatWholeDate(): String = wholeDateFormat.format(this)

    /**
     * Nov 12, 2022  14:12 AM
     */
    fun Long.formatWholeDate(): String = wholeDateFormat.format(Date(this))

    /**
     * Nov 12, 2022  14:12 AM
     */
    fun Date.formatWholeDateByUTC(): String = wholeDateFormatUTC.format(this)

    /**
     * Nov 12, 2022  14:12 AM
     */
    fun Long.formatWholeDateByUTC(): String = wholeDateFormatUTC.format(Date(this))

    /**
     * 评论时间
     */
    fun Long.formatCommentDate(context: Context): String {
        return DateUtilWrapper.getFormatCommentDate(context, this)
    }

    fun Long.formatUpdateDate(context: Context): String {
        return DateUtilWrapper.getUpdateFormatDate(context, this)
    }

    fun Long.formatCreateDate(context: Context): String {
        return DateUtilWrapper.getCreateFormatDate(context, this)
    }

    fun getDefaultAgeOptions(): List<AgeOption> {
        return listOf(
            AgeOption(7, "≤7") { it in 0..7 },
            AgeOption(8, "8"),
            AgeOption(9, "9"),
            AgeOption(10, "10"),
            AgeOption(11, "11"),
            AgeOption(12, "12"),
            AgeOption(13, "13"),
            AgeOption(14, "14"),
            AgeOption(15, "15"),
            AgeOption(16, "16"),
            AgeOption(17, "17"),
            AgeOption(18, "18+") { it >= 18 }
        )
    }

}