package com.socialplay.gpark.util

import android.app.ActivityManager
import android.content.Context
import android.os.Process
import android.text.TextUtils
import timber.log.Timber
import java.io.FileInputStream
import java.nio.charset.StandardCharsets

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/05/13
 * desc   :
 * </pre>
 */


object ProcessUtil {

    /**
     * 根据context获取进程名
     *
     * @param context
     * @return
     */
    fun getProcessName(context: Context): String? {
        val am = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        // 这里可能会崩溃 系统的服务可能已经挂了 所以获取不到的时候返回空
        val runningApps = kotlin.runCatching { am.runningAppProcesses }.getOrNull() ?: return null
        for (proInfo in runningApps) {
            if (proInfo.pid == Process.myPid() && proInfo.processName != null) {
                return proInfo.processName
            }
        }
        return getCurrentProcessName()
    }
    fun isMainProcess(context: Context) =
        TextUtils.equals(getProcessName(context), context.packageName)

    fun isMProcess(context: Context) =
        TextUtils.equals(getProcessName(context), getTsProcessName(context))
    /**
     * 返回当前的进程名
     *
     * @return
     */
    fun getCurrentProcessName(): String? {
        var input: FileInputStream? = null
        try {
            val fn = "/proc/self/cmdline"
            input = FileInputStream(fn)
            val buffer = ByteArray(256)
            var len = 0
            var b: Int
            while (input.read().also { b = it } > 0 && len < buffer.size) {
                buffer[len++] = b.toByte()
            }
            if (len > 0) {
                return String(buffer, 0, len, StandardCharsets.UTF_8)
            }
        } catch (e: Throwable) {
            Timber.e(e)
        } finally {
            input?.runCatching { close() }
        }
        return null
    }

    fun checkTsProcessAlive(context: Context): Boolean {
        return checkProcessAlive(context, getTsProcessName(context))
    }

    fun checkTsRProcessAlive(context: Context): Boolean {
        return checkProcessAlive(context, getTsRProcessName(context))
    }

    fun checkProcessAlive(context: Context, processName: String): Boolean {
        val am = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        // 这里可能会崩溃 系统的服务可能已经挂了 所以获取不到的时候返回空
        val runningApps = kotlin.runCatching { am.runningAppProcesses }.getOrNull() ?: return false
        for (proInfo in runningApps) {
            if (proInfo.processName == processName) {
                return true
            }
        }
        return false
    }

    fun getTsProcessName(context: Context): String {
        return context.packageName + ":m"
    }

    fun getTsRProcessName(context: Context): String {
        return context.packageName + ":r"
    }
}