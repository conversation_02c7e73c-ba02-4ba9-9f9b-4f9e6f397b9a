package com.socialplay.gpark.util

/**
 * xingxiu.hou
 * 2023/6/30
 */
fun Throwable?.simMsg(): String? {
    if (this == null) return null
    return if (cause != null) {
        with(StringBuilder()) {
            append(<EMAIL>())
            var thisCause = cause
            repeat(5) {
                if (thisCause != null) {
                    append(" | ").append(thisCause?.message)
                    thisCause = thisCause?.cause
                }
            }
            toString()
        }
    } else {
        this.toString()
    }
}
