package com.socialplay.gpark.util

import android.app.Activity
import android.content.Context
import android.graphics.Color
import android.os.Build
import android.view.View
import android.view.Window
import android.view.WindowManager
import androidx.annotation.NonNull
import androidx.fragment.app.Fragment
import timber.log.Timber


/**
 *  <AUTHOR> taiyuan
 *  time   : 2021/5/16
 *  desc   : 状态栏相关操作工具类
 */
object StatusBarUtil {

    fun setTransparent(act: Activity) {
        act.window.apply {
            // 设置 FLAG_TRANSLUCENT_STATUS 会导致出现半透明的黑色遮罩效果，因此去除该标志
            clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
            // SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN：将页面内容延伸到状态栏
            // SYSTEM_UI_FLAG_LAYOUT_STABLE：用于稳定布局，当状态栏发生动态显示和隐藏时，
            // 系统为 fitSystemWindow=true 的 View 设置的 padding 大小都不会变化，
            // 所以 View 的内容的位置也不会发生移动
            decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
            addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            statusBarColor = Color.TRANSPARENT
        }
    }

    /**
     * 获取状态栏高度
     */
    fun getStatusBarHeight(context: Context): Int {
        val defaultHeight = ScreenUtil.dp2px(context, 24F)
        return kotlin.runCatching {
            val resources = context.resources
            val resourceId = resources.getIdentifier("status_bar_height", "dimen", "android")
            if (resourceId > 0) {
                resources.getDimensionPixelSize(resourceId)
            } else {
                defaultHeight
            }
        }.getOrDefault(defaultHeight)
    }

    fun setDarkText(fragment: Fragment, isDarkText: Boolean){
        fragment.activity?.let {
            setDarkText(it, isDarkText)
        }
    }

    fun setDarkText(act: Activity, isDarkText: Boolean) {
        if (isDarkText){
            setLightMode(act)
        }else{
            setDarkMode(act)
        }
    }

    /**
     * 设置亮色模式。在该模式下，会将状态栏中的图标和文字修改为深黑色（默认是白色）。
     *
     * 官方是从 Android 6.0 开始支持修改，对于 5.0~6.0 的设备，如果支持修改，则采用反射进行修改。
     *
     * @param window 要修改的 Window 。
     */
    fun setLightMode(window: Window) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            window.apply {
                addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
                clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
                decorView.systemUiVisibility = decorView.systemUiVisibility or View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
            }
        } else {
            if (DeviceUtil.isMIUI()) {
                setMIUIStatusBarDarkIcon(window, true)
            } else if (DeviceUtil.isFlyme()) {
                setMeizuStatusBarDarkIcon(window, true)
            }
        }
    }

    /**
     * 设置亮色模式。在该模式下，会将状态栏中的图标和文字修改为深黑色（默认是白色）。
     *
     * 官方是从 Android 6.0 开始支持修改，对于 5.0~6.0 的设备，如果支持修改，则采用反射进行修改。
     *
     * @param act 要修改的 Activity 页面。
     */
    fun setLightMode(act: Activity) = setLightMode(act.window)

    /**
     * 设置暗黑模式。在该模式下，会将状态栏中的图标和文字修改回白色。
     *
     * 官方是从 Android 6.0 开始支持修改，对于 5.0~6.0 的设备，如果支持修改，则采用反射进行修改。
     *
     * @param act 要修改的 Activity 页面。
     */
    fun setDarkMode(act: Activity) = setDarkMode(act.window)

    /**
     * 设置暗黑模式。在该模式下，会将状态栏中的图标和文字修改回白色。
     *
     * 官方是从 Android 6.0 开始支持修改，对于 5.0~6.0 的设备，如果支持修改，则采用反射进行修改。
     *
     * @param act 要修改的 window。
     */
    fun setDarkMode(window: Window) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            window.apply {
                // 通过异或运算，去除 systemUiVisibility 中设置的 SYSTEM_UI_FLAG_LIGHT_STATUS_BAR 标志
                if (decorView.systemUiVisibility and View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR != 0) {
                    decorView.systemUiVisibility = decorView.systemUiVisibility xor View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
                }
            }
        } else {
            if (DeviceUtil.isMIUI()) {
                setMIUIStatusBarDarkIcon(window, false)
            } else if (DeviceUtil.isFlyme()) {
                setMeizuStatusBarDarkIcon(window, false)
            }
        }
    }

    /**
     * 反射修改 MIUI V6 以上状态栏颜色。
     */
    private fun setMIUIStatusBarDarkIcon(@NonNull window: Window, darkIcon: Boolean) {
        val clazz: Class<out Window?> = window.javaClass
        kotlin.runCatching {
            val layoutParams = Class.forName("android.view.MiuiWindowManager\$LayoutParams")
            val field = layoutParams.getField("EXTRA_FLAG_STATUS_BAR_DARK_MODE")
            val darkModeFlag = field.getInt(layoutParams)
            val extraFlagField = clazz.getMethod("setExtraFlags", Int::class.javaPrimitiveType, Int::class.javaPrimitiveType)
            extraFlagField.invoke(window, if (darkIcon) darkModeFlag else 0, darkModeFlag)
        }
    }

    /**
     * 反射修改魅族状态栏字体颜色 Flyme 4.0。
     */
    private fun setMeizuStatusBarDarkIcon(@NonNull window: Window, darkIcon: Boolean) {
        kotlin.runCatching {
            val lp = window.attributes
            val darkFlag = WindowManager.LayoutParams::class.java.getDeclaredField("MEIZU_FLAG_DARK_STATUS_BAR_ICON")
            val meizuFlags = WindowManager.LayoutParams::class.java.getDeclaredField("meizuFlags")
            darkFlag.isAccessible = true
            meizuFlags.isAccessible = true
            val bit = darkFlag.getInt(null)
            var value = meizuFlags.getInt(lp)
            value = if (darkIcon) {
                value or bit
            } else {
                value and bit.inv()
            }
            meizuFlags.setInt(lp, value)
            window.attributes = lp
        }
    }
}