package com.socialplay.gpark.util.hash;



import java.io.File;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.util.HashSet;
import java.util.Set;
/**
 * 2024/8/14
 */
public class HashCalcTemplate extends HashUtil.Calc {
    private static final boolean DEFAULT_LOWER = true;
    static final Set<HashCalcTemplate> ALL = new HashSet();
    public final String code;
    private final int validLength;

    HashCalcTemplate(String code) {
        ALL.add(this);
        this.code = code;
        this.validLength = hash(code, "1", true).length();
    }

    public boolean valid(String content) {
        return HashUtil.Calc.valid(content, this.validLength);
    }

    public byte[] getBytes(byte[] data) {
        return hashBytes(this.code, data);
    }

    public byte[] getBytes(String data) {
        return hashBytes(this.code, data);
    }

    public byte[] getBytes(String data, Charset charset) {
        return hashBytes(this.code, data, charset);
    }

    public byte[] getBytes(File data) {
        return hashBytes(this.code, data);
    }

    public byte[] getBytes(InputStream data) {
        return hashBytes(this.code, data);
    }

    public byte[] getBytes(File data, long begin, long end) {
        return hashBytes(this.code, data, begin, end);
    }

    public String get(byte[] data) {
        return hash(this.code, data, true);
    }

    public String get(String data) {
        return hash(this.code, data, true);
    }

    public String get(String data, Charset charset) {
        return hash(this.code, data, charset, true);
    }

    public String get(File data) {
        return hash(this.code, data, true);
    }

    public String get(InputStream data) {
        return hash(this.code, data, true);
    }

    public String get(File data, long begin, long end) {
        return hash(this.code, data, begin, end, true);
    }

    public String getLower(byte[] data) {
        return hash(this.code, data, true);
    }

    public String getLower(String data) {
        return hash(this.code, data, true);
    }

    public String getLower(String data, Charset charset) {
        return hash(this.code, data, charset, true);
    }

    public String getLower(File data) {
        return hash(this.code, data, true);
    }

    public String getLower(InputStream data) {
        return hash(this.code, data, true);
    }

    public String getLower(File data, long begin, long end) {
        return hash(this.code, data, begin, end, true);
    }

    public String getUpper(byte[] data) {
        return hash(this.code, data, false);
    }

    public String getUpper(String data) {
        return hash(this.code, data, false);
    }

    public String getUpper(String data, Charset charset) {
        return hash(this.code, data, charset, false);
    }

    public String getUpper(File data) {
        return hash(this.code, data, false);
    }

    public String getUpper(InputStream data) {
        return hash(this.code, data, false);
    }

    public String getUpper(File data, long begin, long end) {
        return hash(this.code, data, begin, end, false);
    }
}
