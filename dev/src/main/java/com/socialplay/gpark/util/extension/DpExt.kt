package com.socialplay.gpark.util.extension

import android.app.Activity
import android.content.Context
import android.view.View
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.RecyclerView
import androidx.viewbinding.ViewBinding
import com.socialplay.gpark.util.ScreenUtil
import org.koin.core.context.GlobalContext

/**
 * create by: bin on 2021/5/17
 */

val Float.dp get() = ScreenUtil.dp2px(GlobalContext.get().get(), this)
val Int.dp get() = ScreenUtil.dp2px(GlobalContext.get().get(), this.toFloat())
val Number.dp get() = ScreenUtil.dp2px(GlobalContext.get().get(), this.toFloat())
val Int.sp get() = ScreenUtil.sp2px(GlobalContext.get().get(), this.toFloat())
val Float.sp get() = ScreenUtil.sp2px(GlobalContext.get().get(), this)
val Number.sp get() = ScreenUtil.sp2px(GlobalContext.get().get(), this.toFloat())

fun Context.dp(dp: Number) = ScreenUtil.dp2px(this, dp.toFloat())
fun Activity.dp(dp: Number) = ScreenUtil.dp2px(this, dp.toFloat())
fun Fragment.dp(dp: Number) =
    ScreenUtil.dp2px(context ?: view?.context ?: GlobalContext.get().get(), dp.toFloat())

fun View.dp(dp: Number) = ScreenUtil.dp2px(context, dp.toFloat())
fun ViewBinding.dp(dp: Number) = ScreenUtil.dp2px(root.context, dp.toFloat())
fun RecyclerView.ViewHolder.dp(dp: Number) = ScreenUtil.dp2px(itemView.context, dp.toFloat())

fun Context.sp(sp: Number) = ScreenUtil.sp2px(this, sp.toFloat())
fun Activity.sp(sp: Number) = ScreenUtil.sp2px(this, sp.toFloat())
fun Fragment.sp(sp: Number) =
    ScreenUtil.sp2px(context ?: view?.context ?: GlobalContext.get().get(), sp.toFloat())

fun View.sp(sp: Number) = ScreenUtil.sp2px(context, sp.toFloat())
fun ViewBinding.sp(sp: Number) = ScreenUtil.sp2px(root.context, sp.toFloat())
fun RecyclerView.ViewHolder.sp(sp: Number) = ScreenUtil.sp2px(itemView.context, sp.toFloat())