package com.socialplay.gpark.util.glide

import android.graphics.Bitmap
import android.graphics.Color
import com.bumptech.glide.load.engine.bitmap_recycle.BitmapPool
import com.bumptech.glide.load.resource.bitmap.BitmapTransformation
import com.bumptech.glide.util.Util
import java.nio.ByteBuffer
import java.security.MessageDigest

/**
 * xingxiu.hou
 * 2023/1/3
 * 裁切Bitmap周边透明部分，保留中间区域
 */
class TransparentBoundCrop(
    private val step: Int = 5,
    private val region: Int = REGION_ALL
) : BitmapTransformation() {

    companion object {
        private const val ID = "com.socialplay.gpark.util.glide.TransparentBoundCrop"
        private val ID_BYTES = ID.toByteArray(CHARSET)

        const val REGION_ALL = 0
        const val REGION_HORIZONTAL = 1
        const val REGION_VERTICAL = 2
    }

    override fun transform(
        pool: BitmapPool,
        toTransform: Bitmap,
        outWidth: Int,
        outHeight: Int,
    ): Bitmap {
        return cropTransparentPixels(toTransform, step)
    }

    private fun cropTransparentPixels(bitmap: Bitmap, step: Int): Bitmap {
        val width = bitmap.width
        val height = bitmap.height
        val widthCenter = width / 2
        val heightCenter = height / 2
        //横向
        val left = if (region != REGION_VERTICAL) {
            getXBound(bitmap, Line(widthCenter, 0), Line(0, height - 1), step)
        } else {
            0
        }
        val right = if (region != REGION_VERTICAL) {
            getXBound(bitmap, Line(widthCenter, width - 1), Line(0, height - 1), step)
        } else {
            width
        }
        val top = if (region != REGION_HORIZONTAL) {
            getYBound(bitmap, Line(0, width - 1), Line(heightCenter, 0), step)
        } else {
            0
        }
        val bottom = if (region != REGION_HORIZONTAL) {
            getYBound(bitmap, Line(0, width - 1), Line(heightCenter, height - 1), step)
        } else {
            height
        }
        return Bitmap.createBitmap(bitmap, left, top, right - left, bottom - top)
    }


    private fun getXBound(bitmap: Bitmap, xLine: Line, yLine: Line, step: Int): Int {
        for (x in (if (xLine.start > xLine.end) xLine.start downTo xLine.end else xLine.start..xLine.end) step step) {
            var hit = true
            val yCenter = yLine.center()
            for (y in yCenter downTo yLine.start) {
                if (bitmap.getPixel(x, y) != Color.TRANSPARENT) {
                    hit = false
                    break
                }
            }
            if (!hit) continue
            for (y in yCenter..yLine.end) {
                if (bitmap.getPixel(x, y) != Color.TRANSPARENT) {
                    hit = false
                    break
                }
            }
            if (hit) return x
        }
        return xLine.end
    }

    private fun getYBound(bitmap: Bitmap, xLine: Line, yLine: Line, step: Int): Int {
        for (y in (if (yLine.start > yLine.end) yLine.start downTo yLine.end else yLine.start..yLine.end) step step) {
            var hit = true
            val xCenter = xLine.center()
            for (x in xCenter downTo xLine.start) {
                if (bitmap.getPixel(x, y) != Color.TRANSPARENT) {
                    hit = false
                    break
                }
            }
            if (!hit) continue
            for (x in xCenter..xLine.end) {
                if (bitmap.getPixel(x, y) != Color.TRANSPARENT) {
                    hit = false
                    break
                }
            }
            if (hit) return y
        }
        return yLine.end
    }

    override fun updateDiskCacheKey(messageDigest: MessageDigest) {
        messageDigest.update(ID_BYTES)
        val boundData = ByteBuffer.allocate(16)
            .putInt(step)
            .array()
        messageDigest.update(boundData)
    }

    override fun equals(other: Any?): Boolean {
        if (other is TransparentBoundCrop) {
            return step == other.step
        }
        return false
    }

    override fun hashCode(): Int {
        return Util.hashCode(ID.hashCode(), Util.hashCode(step))
    }

    data class Line(val start: Int, val end: Int) {
        fun center(): Int = (start + end) / 2
    }

}