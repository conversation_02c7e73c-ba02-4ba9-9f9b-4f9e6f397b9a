package com.socialplay.gpark.util.extension

import androidx.lifecycle.LifecycleOwner
import com.google.android.exoplayer2.Player

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/12/22
 *     desc   :
 * </pre>
 */
fun Player.addListener(owner: LifecycleOwner, listener: Player.Listener) {
    owner.observeOnMainThreadWhenNotDestroyed(
        register = {
            addListener(listener)
        }, unregister = {
            removeListener(listener)
        }
    )
}