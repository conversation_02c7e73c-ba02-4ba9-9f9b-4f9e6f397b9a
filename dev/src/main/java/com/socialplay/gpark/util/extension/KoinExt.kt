package com.socialplay.gpark.util.extension

import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModel
import org.koin.androidx.viewmodel.ViewModelStoreOwnerProducer
import org.koin.androidx.viewmodel.ext.android.viewModel
import org.koin.core.parameter.ParametersDefinition
import org.koin.core.qualifier.Qualifier

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/06/22
 * desc   :
 * </pre>
 */

inline fun <reified T : ViewModel> Fragment.sharedViewModelFromParentFragment(
    qualifier: Qualifier? = null,
    noinline owner: ViewModelStoreOwnerProducer = { requireParentFragment()},
    noinline parameters: ParametersDefinition? = null,
): Lazy<T> {
    return viewModel(qualifier, owner, null, parameters)
}
