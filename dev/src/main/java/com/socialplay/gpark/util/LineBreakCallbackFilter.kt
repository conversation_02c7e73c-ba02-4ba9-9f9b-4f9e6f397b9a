package com.socialplay.gpark.util

import android.text.InputFilter
import android.text.Spanned

/**
 * 禁止输入换行
 */
class LineBreakCallbackFilter(
    val callback: (() -> Unit)? = null
) : InputFilter {
    override fun filter(
        source: CharSequence?,
        start: Int,
        end: Int,
        dest: Spanned?,
        dstart: Int,
        dend: Int
    ): CharSequence? {
        source ?: return null
        for (i in start..<end) {
            if (source[i] == '\n') {
                callback?.invoke()
                // 禁止输入换行符
                return ""
            }
        }
        // 允许其他输入
        return null
    }
}