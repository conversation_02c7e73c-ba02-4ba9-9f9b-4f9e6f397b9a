package com.socialplay.gpark.util

import android.graphics.Color
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.ForegroundColorSpan

object SpanUtil {

    private fun getSpannable(
        str: String,
        start: Int,
        length: Int,
        color: Int
    ): CharSequence {
        if (str.isEmpty() || start < 0 || length < 0) {
            return str
        }
        val sp = SpannableStringBuilder()
        sp.append(str)
        sp.setSpan(
            ForegroundColorSpan(color),
            start,
            length,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        return sp
    }

    fun getHighlightSpannable(
        text: String?,
        highlightText: String?,
        color: Int = Color.parseColor("#4AB4FF")
    ): CharSequence? {
        return text?.let { str ->
            return if (highlightText.isNullOrBlank() || text.isEmpty()) str else kotlin.runCatching {
                val index = str.indexOf(highlightText)
                return if (index >= 0) {
                    val length = highlightText.length
                    val max = index + length
                    val end = if (max > str.length) str.length else max
                    getSpannable(str, index, end, color)
                } else {
                    str
                }
            }.getOrDefault(str)
        }
    }
}