package com.socialplay.gpark.util

import android.annotation.SuppressLint
import android.util.Pair
import java.util.regex.Pattern

/**
 * created by <PERSON><PERSON><PERSON><PERSON> on 2022/9/14 11:09 上午
 * @describe:
 */
object StringUtil {

    /**
     * 获取隐私手机号 185****1122
     *
     * @param phoneNumber
     * @return
     */
    fun getStarPhoneNumber(phoneNumber: String?): String {
        if (phoneNumber.isNullOrEmpty()) {
            return ""
        }
        kotlin.runCatching {
            return "${phoneNumber.substring(0, 3)}****${phoneNumber.substring(7)}"
        }.getOrElse {
            return ""
        }
    }

    @SuppressLint("ChineseStringLiteral")
    fun getStringCharCount(str: String): Int {
        var count = 0
        for (i in str.indices) {
            val c = str[i]
            // unicode编码，判断是否为汉字
            val p = Pattern.compile("[\u4e00-\u9fa5]")
            val m = p.matcher(c.toString())
            //判断是否为中文   判断是否为中文标点符号
            count += if (m.matches() || isChinesePunctuation(c)) {
                2
            } else {
                1
            }
        }
        return count
    }

    // 根据UnicodeBlock方法判断中文标点符号
    private fun isChinesePunctuation(c: Char): Boolean {
        val ub = Character.UnicodeBlock.of(c)
        return ub === Character.UnicodeBlock.GENERAL_PUNCTUATION || ub === Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION || ub === Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS || ub === Character.UnicodeBlock.CJK_COMPATIBILITY_FORMS || ub === Character.UnicodeBlock.VERTICAL_FORMS
    }

    /**
     * 是否超过字符限制，并返回最后一个字符的位置
     * @param string 判断的字符串
     * @param limitChar 限制的字符数
     * @return boolean：是否超过限制；int：可以subString的最后一个字符的位置
     */
    fun isBeyondCharacterLimit(string: String, limitChar: Int): Pair<Boolean, Int> {
        var count = 0
        var endIndex = 0
        val p = Pattern.compile("\\s*|\t|\r|\n")
        val m = p.matcher(string)
        val prep = m.replaceAll("")
        for (i in prep.indices) {
            val item = prep[i]
            count = if (item.code < 128) {
                count + 1
            } else {
                count + 2
            }
            if (limitChar == count || item.code >= 128 && limitChar + 1 == count) {
                endIndex = i
            }
        }
        val isBeyond = count > limitChar
        return Pair<Boolean, Int>(isBeyond, endIndex + 1)
    }

    /**
     * @throws NumberFormatException 不要将结果转换为float!不同国家小数点可能是逗号，或者数字不为阿拉伯数字
     */
    fun Float.getFormatAvg(): String {
        return try {
            String.format("%.1f", this)
        } catch (e: Exception) {
            "0.0"
        }
    }
}

val String?.toLongOrZero: Long
    get() {
        return if (this == null) {
            0L
        } else {
            try {
                this.toLong()
            } catch (e: NumberFormatException) {
                0
            }
        }
    }

val String?.toIntOrNull: Int?
    get() {
        return if (this == null) {
            null
        } else {
            try {
                this.toInt()
            } catch (e: NumberFormatException) {
                null
            }
        }
    }

val String?.toIntOrZero: Int
    get() {
        return this.toIntOrNull ?: 0
    }