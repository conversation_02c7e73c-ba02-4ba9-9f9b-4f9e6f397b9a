package com.socialplay.gpark.util

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okio.ByteString.Companion.decodeBase64
import java.io.File
import java.io.FileInputStream
import java.io.InputStream
import java.nio.charset.StandardCharsets
import java.security.MessageDigest

/**
 * create by: bin on 2021/6/3
 */
object Md5Util {

    suspend fun File.file2Md5Low(): String? {
        return file2Md5(this)?.lowercase()
    }

    fun String.md5(): String {
        return str2MD5(this) ?: this.decodeBase64()?.toString()?.lowercase() ?: this.hashCode().toString()
    }

    /**
     * 为大文件计算MD5值
     *
     * 替换为 file2Md5Low()
     *
     * @see file2Md5Low
     */
    @Deprecated("跟服务端保持一致, 使用小写的md5")
    private suspend fun file2Md5(inputFile: File): String? {
        return withContext(Dispatchers.IO) {
            kotlin.runCatching {
                FileInputStream(inputFile).use { fileInputStream ->
                    val messageDigest = MessageDigest.getInstance("MD5")
                    val buffer = ByteArray(1024 * 1024)
                    var len: Int
                    while (fileInputStream.read(buffer).also { len = it } != -1) {
                        messageDigest.update(buffer, 0, len)
                    } // 同样，把字节数组转换成字符串
                    messageDigest.digest().hex()
                }
            }.getOrNull()
        }
    }

    private fun ByteArray.hex(): String {
        return joinToString("") { "%02X".format(it) }
    }

    fun str2MD5(inStr: String): String? {
        val hexDigits = charArrayOf('0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f')
        return try {
            val strTemp = inStr.toByteArray(StandardCharsets.UTF_8)
            val mdTemp = MessageDigest.getInstance("MD5")
            mdTemp.update(strTemp)
            val md = mdTemp.digest()
            val j = md.size
            val str = CharArray(j * 2)
            var k = 0
            for (byte0 in md) {
                str[k++] = hexDigits[byte0.toInt() ushr 4 and 0xf]
                str[k++] = hexDigits[byte0.toInt() and 0xf]
            }
            String(str)
        } catch (e: Exception) {
            null
        }
    }

}