package com.socialplay.gpark.util


/**
 * Created by bo.li
 * Date: 2021/5/17
 * Desc: 给各种需要展示下载游戏进度的地方提供下载进度
 */
object DownloadProgressUtil {
    private const val ORIGIN_FAKE_PROGRESS = 2 + 0.5F * 3F
    /**
     * 获取展示下载进度
     */
    fun getShowProgress(realProgress: Float): Float {
        return when {
            realProgress <= 0f  -> {
                ORIGIN_FAKE_PROGRESS
            }
            realProgress <= 30f -> {
                realProgress * (50 - ORIGIN_FAKE_PROGRESS) / 30 + ORIGIN_FAKE_PROGRESS
            }
            realProgress <= 50f -> {
                realProgress + 20
            }
            realProgress <= 99f -> {
                (realProgress - 50) * 29 / 49 + 70
            }
            else                -> {
                100f
            }
        }
    }
}