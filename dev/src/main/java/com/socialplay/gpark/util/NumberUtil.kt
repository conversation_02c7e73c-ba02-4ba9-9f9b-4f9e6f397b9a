package com.socialplay.gpark.util

import java.math.BigDecimal
import kotlin.math.pow

/**
 * author : yi.zhang
 * e-mail : <EMAIL>
 * time   : 2021/09/17
 * desc   :
 */
object NumberUtil {
    private const val THOUSAND = 1000L
    private const val TEN_THOUSAND = 10000L
    private const val MILLIONS = 1000000L
    private const val BILLION = 1000000000L

    fun amountConversion(value: Long): String {
        return when {
            value >= BILLION  -> {
                "${formatNumber(value.toDouble() / BILLION, 1)}B"
            }
            value >= MILLIONS -> {
                "${formatNumber(value.toDouble() / MILLIONS, 1)}M"
            }
            value >= TEN_THOUSAND -> {
                "${formatNumber(value.toDouble() / THOUSAND, 1)}K"
            }
            else              -> {
                "$value"
            }
        }
    }

    private fun formatNumber(value: Double, length: Int): String {
        val unit = 10.0.pow(length.toDouble())
        val result: Long = (value * unit).toLong()
        val value = result.toDouble() / unit
        return BigDecimal(value.toString()).toPlainString()
    }
}