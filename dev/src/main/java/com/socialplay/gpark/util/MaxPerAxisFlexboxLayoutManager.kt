package com.socialplay.gpark.util

import android.content.Context
import android.view.View
import com.google.android.flexbox.FlexDirection
import com.google.android.flexbox.FlexWrap
import com.google.android.flexbox.JustifyContent
import com.socialplay.gpark.ui.view.CustomFlexboxLayoutManager

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/01/12
 *     desc   :
 * </pre>
 */
class MaxPerAxisFlexboxLayoutManager @JvmOverloads constructor(
    context: Context,
    @FlexDirection flexDirection: Int = FlexDirection.ROW,
    @FlexWrap flexWrap: Int = FlexWrap.WRAP
) : CustomFlexboxLayoutManager(context, flexDirection, flexWrap) {

    companion object {
        fun horizontal(context: Context, maxPerAxis: Int) =
            MaxPerAxisFlexboxLayoutManager(context).apply {
                justifyContent = JustifyContent.SPACE_BETWEEN
                this.maxPerAxis = maxPerAxis
            }
    }

    var maxPerAxis = 0

    override fun getFlexItemAt(index: Int): View {
        return super.getFlexItemAt(index).apply {
            if (maxPerAxis > 0) {
                (layoutParams as? LayoutParams)?.isWrapBefore = index % maxPerAxis == 0
            }
        }
    }
}