package com.socialplay.gpark.util

import android.os.Bundle
import timber.log.Timber
import com.socialplay.gpark.util.BundleUtil
import java.lang.Exception
import java.lang.IllegalStateException

object BundleUtil {

        const val TAG = "BundleUtil"
        fun getIntExtra(bundle: Bundle?, key: String?, defaultValue: Int): Int {
            if (bundle != null) {
                try {
                    return bundle.getInt(key, defaultValue)
                } catch (e: Exception) {
                    Timber.tag(TAG).e("getIntExtra exception: %s", e.message)
                }
            }
            return defaultValue
        }

        fun getStringExtra(bundle: Bundle?, key: String?): String? {
            if (bundle != null) {
                try {
                    return bundle.getString(key)
                } catch (e: Exception) {
                    Timber.tag(TAG).e("getStringExtra exception: %s", e.message)
                }
            }
            return null
        }

        fun getBooleanExtra(bundle: Bundle?, key: String?): <PERSON><PERSON><PERSON> {
            if (bundle != null) {
                try {
                    return bundle.getBoolean(key)
                } catch (e: Exception) {
                    Timber.tag(TAG).e("getBooleanExtra exception: %s", e.message)
                }
            }
            return false
        }

        fun getByteArrayExtra(bundle: Bundle?, key: String?): ByteArray? {
            if (bundle != null) {
                try {
                    return bundle.getByteArray(key)
                } catch (e: Exception) {
                    Timber.tag(TAG).e("getByteArrayExtra exception: %s", e.message)
                }
            }
            return null
        }
}