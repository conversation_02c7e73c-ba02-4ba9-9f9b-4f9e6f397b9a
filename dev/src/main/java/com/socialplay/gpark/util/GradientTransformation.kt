package com.socialplay.gpark.util

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.RadialGradient
import android.graphics.Rect
import android.graphics.Shader
import android.view.View
import com.bumptech.glide.load.engine.bitmap_recycle.BitmapPool
import com.bumptech.glide.load.resource.bitmap.BitmapTransformation
import java.security.MessageDigest
import kotlin.math.sqrt

/**
 * 一个渐变模糊效果的图片转换器
 * 用于首页Item的左下角那个模糊效果
 * Created by shuai.wang
 */
class GradientTransformation(private val targetView: View) : BitmapTransformation() {
    override fun transform(
        pool: BitmapPool,
        toTransform: Bitmap,
        outWidth: Int,
        outHeight: Int
    ): Bitmap {
        // 先压缩图片
        val scale = 0.25f // 压缩到原来的1/4
        val scaledWidth = (toTransform.width * scale).toInt()
        val scaledHeight = (toTransform.height * scale).toInt()

        val scaledBitmap = pool.get(scaledWidth, scaledHeight, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(scaledBitmap)
        canvas.drawBitmap(toTransform, null, Rect(0, 0, scaledWidth, scaledHeight), null)

        // 对压缩后的图片进行模糊处理
        val blurredBitmap = BlurUtil.fastBlur(scaledBitmap, 25)
        pool.put(scaledBitmap)

        // 创建最终结果
        val result = pool.get(outWidth, outHeight, Bitmap.Config.ARGB_8888)
        val resultCanvas = Canvas(result)

        // 绘制模糊后的图片
        resultCanvas.drawBitmap(blurredBitmap, null, Rect(0, 0, outWidth, outHeight), null)
        pool.put(blurredBitmap)

        // 获取目标控件的大小
        val viewWidth = targetView.width
        val viewHeight = targetView.height

        // 计算渐变半径（使用控件对角线长度）
        val radius = sqrt((viewWidth * viewWidth + viewHeight * viewHeight).toFloat())

        // 创建径向渐变遮罩
        val paint = Paint().apply {
            shader = RadialGradient(
                0f, viewHeight.toFloat(),  // 起始点（左下角）
                radius,                    // 渐变半径
                intArrayOf(
                    Color.BLACK,       // 左下角不透明
                    Color.BLACK,       // 左下角不透明
                    Color.TRANSPARENT  // 渐变到透明
                ),
                floatArrayOf(0f, 0.2f, 0.4f),  // 渐变位置
                Shader.TileMode.CLAMP
            )
            xfermode = PorterDuffXfermode(PorterDuff.Mode.DST_IN)
        }

        // 应用渐变遮罩
        resultCanvas.drawRect(0f, 0f, outWidth.toFloat(), outHeight.toFloat(), paint)

        return result
    }

    override fun updateDiskCacheKey(messageDigest: MessageDigest) {
        messageDigest.update("GradientTransformation".toByteArray())
    }

    override fun equals(other: Any?): Boolean {
        return other is GradientTransformation && other.targetView == targetView
    }

    override fun hashCode(): Int {
        return "GradientTransformation".hashCode() + targetView.hashCode()
    }
}