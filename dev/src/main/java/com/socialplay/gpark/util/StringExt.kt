package com.socialplay.gpark.util

import android.content.Context
import androidx.annotation.StringRes
import org.koin.core.context.GlobalContext

/**
 * 2023/7/31
 */
inline fun <C : CharSequence> C?.ifNullOrEmpty(defaultValue: () -> C): C =
    if (isNullOrEmpty()) defaultValue() else this

fun String.isHttp(): Boolean =
    if (isNullOrEmpty()) false else this.startsWith("http")

fun getStringByGlobal(@StringRes res: Int): String {
    return runCatching { GlobalContext.get().get<Context>().getString(res) }.getOrNull() ?:""
}
fun getStringByGlobal(@StringRes res: Int, vararg formatArgs: Any): String {
    return runCatching { GlobalContext.get().get<Context>().getString(res, *formatArgs) }.getOrNull() ?:""
}