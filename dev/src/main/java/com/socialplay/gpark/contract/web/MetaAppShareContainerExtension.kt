package com.socialplay.gpark.contract.web

import android.widget.FrameLayout
import com.meta.lib.web.core.WebCore
import com.meta.lib.web.core.container.IWebContainer
import com.meta.web.contract.IWebPlatformContract
import com.meta.web.model.BasicWebArgs

class MetaAppShareContainerExtension(
    private val webCore: WebCore,
    private val webArgs: BasicWebArgs
) : IWebPlatformContract.WebContainerExtension {

    private val navigationListener = object : IWebContainer.ContainerNavigationListener {
        override fun onWebContainerNavigateUp(webCore: WebCore): <PERSON><PERSON><PERSON> {
            return false
        }

        override fun onWebViewGoBack(webCore: WebCore): <PERSON><PERSON><PERSON> {
            return false
        }
    }

    init {

    }

    override fun onAttachedToWebContainer(containerView: FrameLayout) {

    }

    override fun onDetachedFromWebContainer(containerView: FrameLayout) {

    }
}