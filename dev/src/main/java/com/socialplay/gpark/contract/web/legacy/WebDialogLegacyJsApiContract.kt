package com.socialplay.gpark.contract.web.legacy

import android.webkit.WebView
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import com.meta.lib.web.core.WebCore
import com.meta.web.model.WebDialogArgs
import com.meta.web.ui.contract.IWebDialogInterceptor
import com.meta.web.ui.WebCoreDialog

@Deprecated("Not in use anymore, contact web dept.")
class WebDialogLegacyJsApiContract(
    private val fragment: WebCoreDialog,
    private val webCore: WebCore,
    private val webView: WebView,
    private val webArgs: WebDialogArgs,
) : AbsLegacyJsApiContract(fragment, webCore, webView, webArgs) {

    private var roleGamePay = false
    private var refreshView: Boolean = false

    //判断当前拉起游戏充值结果，
    private var isPaySuccess: Boolean = false

    init {
        fragment.lifecycle.addObserver(object : LifecycleEventObserver {
            override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
                if (event == Lifecycle.Event.ON_RESUME) {
                    onRoleResume()
                } else if (event == Lifecycle.Event.ON_DESTROY) {
                    fragment.setWebDialogInterceptor(null)
                }
            }
        })

        fragment.setWebDialogInterceptor(object : IWebDialogInterceptor {
            override fun onNavigateUp(): Boolean {
                roleGamePay = false
                return false
            }

            override fun onDismiss() {
                if (!webArgs.autoDismiss) {
                    sendPayCancelEvent()
                }
            }

            override fun onBackPressed(): Boolean {
                if (webArgs.autoDismiss) {
                    sendPayCancelEvent()
                }
                return false
            }

            private fun sendPayCancelEvent() {

            }
        })
    }

    override fun isWebViewDialog(): Boolean {
        return true
    }

    override fun preOpenNativePayPage(data: String?) {
        this.roleGamePay = true
        this.refreshView = true
    }

    private fun onRoleResume() {
        if (roleGamePay && refreshView) {
            refreshView = false
        }
    }

    override fun send2Ue(amount: Int, code: Int, errorMessage: String?) {

    }

    override fun onPaySuccess() {
        isPaySuccess = true
    }

    override fun onPayFailed() {
        isPaySuccess = false
    }

}