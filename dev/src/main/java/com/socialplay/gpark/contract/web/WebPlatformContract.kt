package com.socialplay.gpark.contract.web

import android.app.Activity
import android.app.Application
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.webkit.WebView
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.navigation.fragment.NavHostFragment
import com.meta.web.contract.IWebPlatformContract
import com.meta.pandora.Pandora
import com.meta.web.contract.model.WebShareNativeParams
import timber.log.Timber
import com.meta.lib.web.core.WebCore
import com.meta.web.model.BasicWebArgs
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.TTaiInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.di.CommonParamsProvider
import com.socialplay.gpark.function.deeplink.MetaDeepLink
import com.socialplay.gpark.function.developer.DeveloperPandoraToggle
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.function.share.ShareWrapper
import com.socialplay.gpark.ui.main.MainActivity
import com.socialplay.gpark.ui.web.WebFragment
import com.socialplay.gpark.ui.web.jsinterfaces.ext.getCommonParams
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.GsonUtil.fromJson
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

class WebPlatformContract(
    private val app: Application,
    private val accountInteractor: AccountInteractor,
    private val commonParamsProvider: CommonParamsProvider,
    private val metaKV: MetaKV,
    private val tTaiInteractor: TTaiInteractor
) : IWebPlatformContract {

    private val errorSuppressHostList = setOf("qq.com")
    private var errorWhiteList: Set<String> = errorSuppressHostList
    override val debugMode: Boolean get() = BuildConfig.DEBUG || BuildConfig.LOG_DEBUG
    override val embeddedWebContentCacheEnabled: Boolean get() = PandoraToggle.isOpenTabWebContentCache

    override val appPackageName: String get() = app.packageName
    override val appChannelName: String get() = commonParamsProvider.channelId
    override val userAgent: String get() = "GParty2/${BuildConfig.VERSION_CODE}"

    override val appVersionCode: String get() = "${BuildConfig.VERSION_CODE}"
    override val webStorage: IWebPlatformContract.WebStorage by lazy {
        object : IWebPlatformContract.WebStorage {
            override fun getString(key: String): String {
                return metaKV.web.getWebData(key)
            }

            override fun saveString(key: String, value: String) {
                metaKV.web.saveWebData(key, value)
            }
        }
    }

    override fun createLegacyJsApi(
        webCore: WebCore,
        webContainer: Any,
        webView: WebView,
        webArgs: BasicWebArgs,
    ): IWebPlatformContract.LegacyJsApi {
        return LegacyWebJsbImpl(webContainer, webCore, webView, webArgs)
    }

    override fun createWebContainerExtension(webCore:WebCore,context: Context, webArgs: BasicWebArgs): IWebPlatformContract.WebContainerExtension? {
        if (webArgs.isShowMetaAppShare) {// 如果这种东西多的话，可以放到Container里面去，根据参数区分
            return MetaAppShareContainerExtension(webCore, webArgs)
        }
        return null
    }

    override fun getUrlOptionDefaultValues(): Map<String, String> {
        val config = tTaiInteractor.getTTaiConfigFromCache(TTaiKV.ID_WEB_URL_DEFAULT_VALUES)
        return (kotlin.runCatching { config?.value?.fromJson<Map<String,Any?>>() }.getOrNull() ?: emptyMap())
            .mapValues { it.value?.toString() ?: "" }
    }

    override fun jumpByScheme(uri: Uri, context: Context): Boolean {
        Intent(Intent.ACTION_VIEW, uri).apply {
            this.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            runCatching {
                context.startActivity(this)
            }
        }
        return true
    }

    override fun jumpByScheme(uri: Uri, fragment: Fragment): Boolean {
        // TODO 跳转这一块需要整理优化，各种进程jian
        if (uri.scheme == BuildConfig.SCHEME_URI) {
            if (fragment is WebFragment || fragment.parentFragment is NavHostFragment) {
                MetaDeepLink.handle(activity = fragment.requireActivity(),
                    navHostFragment = fragment,
                    tabSelector = null,
                    uri = uri,
                    source = "web"
                )
            } else {
                MetaRouter.Main.dispatchUrl(fragment.requireActivity(), uri, "web")
            }
        } else {
            Intent(Intent.ACTION_VIEW, uri).apply {
                runCatching {
                    fragment.startActivity(this)
                }
            }
        }
        return true
    }

    override fun getAbValue(
        key: String,
        desc: String,
        defaultValue: String,
        valueType: String
    ): Any? {
        return if (DeveloperPandoraToggle.isEnable()) {
            DeveloperPandoraToggle.getValue(key, defaultValue) ?: defaultValue
        } else {
            Pandora.getAbConfig(key, defaultValue)
        }
    }

    override fun openLoginPage(fragment: Fragment) {
        val activity = fragment.activity
        if (activity !is MainActivity) {
            //从别的Activity不能直接打开登录页
            activity?.let { MetaRouter.Main.gameToLogin(it, com.socialplay.gpark.data.model.LoginSource.Web("web")) }
        } else {
            MetaRouter.Login.login(fragment, "web")
        }
    }

    override fun openLoginPage(context: Context) {
        MetaRouter.Main.gameToLogin(context, com.socialplay.gpark.data.model.LoginSource.Web("web"))
    }

    override fun getUserInfo(): Map<String, Any?> {
        val userInfo = accountInteractor.accountLiveData.value
            ?: throw IllegalArgumentException("userInfo not exist")

        /*TODO 后面一个一个的把需要的属性加进去，不要把多余的也给web,现在没时间先搞上去*/
//        val result = mutableMapOf<String, Any?>()

        val json = GsonUtil.safeToJson(userInfo)
        return GsonUtil.gsonSafeParse<Map<String, Any?>>(json) ?: emptyMap()
    }

    override fun getBaseParams(): Map<String, Any> {
        return getCommonParams()
    }

    override suspend fun share(
        activity: Activity,
        nativeParams: WebShareNativeParams,
        webShareParam: com.meta.lib.web.core.model.WebShareParam
    ) {
        ShareWrapper.webShare(
            activity,
            nativeParams,
            webShareParam
        )
    }

    override suspend fun showRealNameAuthDialog(
        fragment: Fragment,
        source: Int,
        realNameInfo: com.meta.lib.web.core.model.H5RealNameInfo?
    ) {

    }

    override fun isGameInstall(
        gamePackage: String,
        installEnvStatus: String,
        gameId: String?
    ): Boolean {
        return true
    }

    override fun goKF(activity: FragmentActivity) {

    }

    override fun openFeedbackPage(fragment: Fragment, url: String) {
        MetaRouter.Feedback.feedback(
            fragment,
            gameId = null,
            source = "H5",
            defaultSelectType = "web",
            false,
            false,
            null
        )
    }

    override fun getErrorSuppressedHostList(): Set<String> {
        val scope = MainScope()
        scope.launch {
            val tTaiConfig = tTaiInteractor.getTTaiConfigV2(TTaiKV.ID_WEB_ERROR_WHITE_LIST, true)
            errorWhiteList = tTaiConfig?.let {
                val data = GsonUtil.gsonSafeParse<MutableSet<String>>(it.value)
                Timber.i("handleAsyncError=parse== ${data}")
                data
            } ?: errorSuppressHostList
        }
        return errorWhiteList
    }
}