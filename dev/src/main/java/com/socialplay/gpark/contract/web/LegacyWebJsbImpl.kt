package com.socialplay.gpark.contract.web

import android.webkit.WebView
import com.meta.biz.ugc.model.RechargeArkMsg
import com.meta.lib.web.core.WebCore
import com.meta.lib.web.core.preload.PreloadFragment
import com.meta.web.contract.IWebPlatformContract
import com.meta.web.model.BasicWebArgs
import com.meta.web.model.WebDialogArgs
import com.meta.web.ui.EmbeddedWebCoreFragment
import com.meta.web.ui.FullScreenWebCoreDialog
import com.meta.web.ui.StandaloneWebCoreFragment
import com.meta.web.ui.WebCoreDialog
import com.socialplay.gpark.contract.web.legacy.EmbeddedLegacyJsApiContract
import com.socialplay.gpark.contract.web.legacy.FullScreenWebDialogLegacyJsApiContract
import com.socialplay.gpark.contract.web.legacy.PreloadLegacyJsApiContract
import com.socialplay.gpark.contract.web.legacy.StandaloneLegacyJsApiContract
import com.socialplay.gpark.contract.web.legacy.WebDialogLegacyJsApiContract
import com.socialplay.gpark.ui.web.jsinterfaces.JsBridgeApi
import com.socialplay.gpark.ui.web.jsinterfaces.JsBridgeHelper
import com.socialplay.gpark.util.GsonUtil

@Deprecated("Not in use anymore, contact web dept.")
class LegacyWebJsbImpl(

    webContainer: Any?,
    webCore: WebCore,
    webView: WebView,
    webArgs: BasicWebArgs,
) : IWebPlatformContract.LegacyJsApi {

    private var jsApi: JsBridgeApi? = null

    init {
        when (webContainer) {
            is StandaloneWebCoreFragment -> {
                jsApi = JsBridgeApi(
                    JsBridgeHelper(
                        StandaloneLegacyJsApiContract(
                            fragment = webContainer,
                            webCore = webCore,
                            webView = webView,
                            webArgs = webArgs,
                        ), webView
                    )
                )
            }

            is EmbeddedWebCoreFragment -> {
                jsApi = JsBridgeApi(
                    JsBridgeHelper(
                        EmbeddedLegacyJsApiContract(
                            fragment = webContainer,
                            webCore = webCore,
                            webView = webView,
                            webArgs = webArgs,
                        ), webView
                    )
                )
            }

            is WebCoreDialog -> {
                val mwJson = GsonUtil.gsonSafeParse<RechargeArkMsg>(webArgs.json)
                jsApi = JsBridgeApi(
                    JsBridgeHelper(
                        WebDialogLegacyJsApiContract(
                            fragment = webContainer,
                            webCore = webCore,
                            webView = webView,
                            webArgs = webArgs as WebDialogArgs,
                        ),
                        webView
                    )
                )
            }

            is FullScreenWebCoreDialog -> {
                val mwJson = GsonUtil.gsonSafeParse<RechargeArkMsg>(webArgs.json)
                jsApi = JsBridgeApi(
                    JsBridgeHelper(
                        FullScreenWebDialogLegacyJsApiContract(
                            fragment = webContainer,
                            webCore = webCore,
                            webView = webView,
                            webArgs = webArgs,
                        ), webView
                    )
                )
            }

            is PreloadFragment -> {
                jsApi = JsBridgeApi(
                    JsBridgeHelper(
                        PreloadLegacyJsApiContract(
                            fragment = webContainer,
                            webCore = webCore,
                            webView = webView,
                            webArgs = webArgs,
                        ), webView
                    )
                )
            }
        }
    }

    override fun exec(json: String?): String? {
        return jsApi?.exec(json)
    }
}