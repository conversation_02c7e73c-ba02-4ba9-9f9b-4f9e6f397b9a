package com.socialplay.gpark.ui.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.constraintlayout.widget.ConstraintLayout
import com.socialplay.gpark.R

class MetaLikeView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private val ivLike: ImageView
    private val tvZan: MetaTextView
    private val llLike: LinearLayout

    //    private val bgView: View
    private var defaultBackground: Int = R.drawable.icon_item_like_bg
    private var showIcon: Boolean = true

    init {
        LayoutInflater.from(context).inflate(R.layout.view_meta_like, this, true)

        ivLike = findViewById(R.id.iv_like)
        tvZan = findViewById(R.id.tv_zan)
        llLike = findViewById(R.id.ll_like)
//        bgView = findViewById(R.id.bg_view)

        attrs?.let {
            context.obtainStyledAttributes(it, R.styleable.MetaLikeView).apply {
                val iconRes = getResourceId(R.styleable.MetaLikeView_likeIcon, R.drawable.icon_item_like)
                val text = getString(R.styleable.MetaLikeView_likeText)
                val bgRes = getResourceId(R.styleable.MetaLikeView_likeBackground, R.drawable.icon_item_like_bg)
                showIcon = getBoolean(R.styleable.MetaLikeView_showIcon, true)

                ivLike.setImageResource(iconRes)
                text?.let { tvZan.text = it }
                defaultBackground = bgRes
                llLike.setBackgroundResource(bgRes)

                updateIconVisibility()

                recycle()
            }
        }
    }

    fun setLikeIcon(resId: Int) {
        ivLike.setImageResource(resId)
    }

    fun setLikeText(text: String) {
        tvZan.text = text
    }

    fun setLikeBackground(resId: Int) {
        defaultBackground = resId
        llLike.setBackgroundResource(resId)
    }

    fun setShowIcon(show: Boolean) {
        showIcon = show
        updateIconVisibility()
    }

    private fun updateIconVisibility() {
        ivLike.visibility = if (showIcon) View.VISIBLE else View.GONE
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        // 确保在重新附加到窗口时恢复背景
        llLike.setBackgroundResource(defaultBackground)
    }
} 