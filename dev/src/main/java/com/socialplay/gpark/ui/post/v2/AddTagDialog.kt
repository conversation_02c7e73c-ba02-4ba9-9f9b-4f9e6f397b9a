package com.socialplay.gpark.ui.post.v2

import android.content.DialogInterface
import android.os.Bundle
import androidx.fragment.app.setFragmentResult
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.parentFragmentViewModel
import com.bumptech.glide.RequestManager
import com.google.android.flexbox.FlexboxLayoutManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.databinding.DialogPostAddTagBinding
import com.socialplay.gpark.ui.core.BaseBottomSheetDialogFragment
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.property.viewBinding

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/09/25
 *     desc   :
 * </pre>
 */
class AddTagDialog : BaseBottomSheetDialogFragment() {

    override val binding by viewBinding(DialogPostAddTagBinding::inflate)
    private val vm by parentFragmentViewModel(PublishPostViewModel::class)

    private val tagController by lazy { buildTagController() }

    private val dp16 = 16.dp
    private var tagColor = 0

    private val itemListener = object : IPostListener {
        override fun clickTag(tag: PostTag) {
            vm.addTag(tag)
            dismissAllowingStateLoss()
        }

        override fun getGlideOrNull(): RequestManager? {
            return glide
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        tagController.onRestoreInstanceState(savedInstanceState)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        tagController.onSaveInstanceState(outState)
    }

    override fun init() {
        initView()
        initData()
    }
    private fun initView() {
        skipCollapsed()

        tagColor = requireContext().getColorByRes(R.color.color_1A1A1A)

        binding.clContent.interceptBottomSheet(viewLifecycleOwner, ::getBehavior)

        binding.lv.setRetry {
            vm.fetchRandomTags()
        }

        binding.rv.layoutManager = FlexboxLayoutManager(requireContext())
        binding.rv.setController(tagController)
    }

    private fun initData() {
        vm.onAsync(PublishPostState::randomTags, onLoading = {
            binding.lv.showLoading()
        }, onFail = { _, _ ->
            binding.lv.showError()
        }, onSuccess = {
            binding.lv.hide()
        })
    }

    private fun buildTagController() = simpleController(vm, PublishPostState::randomTags) {
        if (it is Success) {
            it.invoke()?.forEach { tag ->
                postTagItem(tag, dp16, false, tagColor, itemListener)
            }
        }
    }

    override fun getStyle() = R.style.BottomDialogStyleOrigin_NoFullScreen

    override var heightPercent = 1F
    override var heightPercentOffset = 110.dp

    override fun needCountTime() = false

    override fun getPageName() = ""

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        setFragmentResult(PublishPostFragment.REQUEST_BACK, Bundle.EMPTY)
    }

}