package com.socialplay.gpark.ui.recommend

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.CountDownTimer
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.paging.LoadState
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.epoxy.EpoxyVisibilityTracker
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.RequestManager
import com.meta.lib.mwbiz.MWBiz
import com.meta.lib.mwbiz.MWLifeCallback
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.EditorInteractor
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor
import com.socialplay.gpark.data.interactor.TokenChangedCallback
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.HomeCustomRecommend
import com.socialplay.gpark.data.model.HomeRecommendOperation
import com.socialplay.gpark.data.model.choice.ChoiceCardInfo
import com.socialplay.gpark.data.model.choice.ChoiceCardType
import com.socialplay.gpark.data.model.choice.ChoiceContentType
import com.socialplay.gpark.data.model.choice.ChoiceGameInfo
import com.socialplay.gpark.data.model.choice.IChoiceItem
import com.socialplay.gpark.data.model.room.ChatRoomInfo
import com.socialplay.gpark.data.model.room.House
import com.socialplay.gpark.data.model.share.RoBuxRecordInfo
import com.socialplay.gpark.data.model.videofeed.ChoiceHomeVideoItem
import com.socialplay.gpark.databinding.FragmentRecommendBinding
import com.socialplay.gpark.databinding.ViewChoiceContinueGameBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.analytics.resid.ResIdUtils
import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.editor.EditorUGCLaunchParams
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.maverick.mapRetained
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.ui.core.views.MetaEpoxyController
import com.socialplay.gpark.ui.core.views.loadMoreFooter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.cottage.detail.CottageRoomDetailDialog
import com.socialplay.gpark.ui.editor.BaseEditorFragmentMaverick
import com.socialplay.gpark.ui.editorschoice.ChoiceHomeFragment.Companion.TAG
import com.socialplay.gpark.ui.editorschoice.RecommendVideoAnalytics
import com.socialplay.gpark.ui.editorschoice.header.friends.ChoiceHomeHeaderFriends
import com.socialplay.gpark.ui.home.HomeViewModel
import com.socialplay.gpark.ui.home.adapter.GameItemPlayedAdapter
import com.socialplay.gpark.ui.main.MainViewModel
import com.socialplay.gpark.ui.recommend.choice.IChoiceListener
import com.socialplay.gpark.ui.recommend.choice.choiceCardItem
import com.socialplay.gpark.ui.room.HomeRoomAnalytics
import com.socialplay.gpark.ui.room.RoomDetailDialog
import com.socialplay.gpark.ui.suggestion.GameSuggestionViewModel
import com.socialplay.gpark.util.DateUtil
import com.socialplay.gpark.util.NetUtil
import com.socialplay.gpark.util.UniJumpUtil
import com.socialplay.gpark.util.extension.attachV2
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.unsetOnClick
import com.socialplay.gpark.util.extension.visible
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import okhttp3.HttpUrl.Companion.toHttpUrl
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.sharedViewModel
import org.koin.androidx.viewmodel.ext.android.viewModel
import org.koin.core.context.GlobalContext
import timber.log.Timber

class RecommendFragment : BaseEditorFragmentMaverick<FragmentRecommendBinding>(R.layout.fragment_recommend) {

    private val accountInteractor: AccountInteractor by inject()
    private val mainViewModel: MainViewModel by sharedViewModel()
    private val viewModel: RecommendViewModel by fragmentViewModel()
    private val homeViewModel by viewModel<HomeViewModel>()
    private val suggestionViewModel by sharedViewModel<GameSuggestionViewModel>()

    private val playedAdapter by lazy { GameItemPlayedAdapter(::glide) }

    private val editorInteractor: EditorInteractor by inject()

    private val metaKV: MetaKV by inject()
    private var timer: CountDownTimer? = null
    private var startTime = 0

    private val recommendController by lazy { buildRecommendController() }
    private val homeHeaderFriends by lazy { ChoiceHomeHeaderFriends(this) }

    private var isFirstResume = true
    private var headContinueGameBinding: ViewChoiceContinueGameBinding? = null

    private val accountCallback = object : TokenChangedCallback {
        override fun invoke(old: String?, new: String?) {
            if (old.isNullOrEmpty() && !new.isNullOrEmpty()) {
                accountInteractor.removeTokenChangedCallback(this)
                checkRefreshRoomList()
            }
        }
    }

    private val choiceListener = object : IChoiceListener {
        override fun onItemShow(
            cardPos: Int,
            card: ChoiceCardInfo,
            itemPos: Int,
            item: IChoiceItem,
            isBanner: Boolean
        ) {
            when (item) {
                is ChoiceGameInfo -> <EMAIL>(
                    cardPos + 1,
                    card,
                    itemPos + 1,
                    item,
                    if (isBanner) {
                        CategoryId.HOME_RECOMMEND_CATEGORY_BANNER
                    } else {
                        CategoryId.HOME_RECOMMEND_CATEGORY
                    }
                )

                is ChatRoomInfo -> HomeRoomAnalytics.trackRoomShow(
                    item,
                    CategoryId.HOME_RECOMMEND_CATEGORY
                )

                is House -> Analytics.track(
                    EventConstants.EVENT_DSHOME_ENTRY_SHOW,
                    mapOf("show_categoryid" to "2")
                )

                is ChoiceHomeVideoItem -> {// Recommend video
                    RecommendVideoAnalytics.trackVideoItemShow(
                        ResIdBean
                            .newInstance()
                            .setCategoryID(CategoryId.HOME_RECOMMEND_NEWEST_GAME)
                            .setParam1(itemPos + 1)
                            .setReqId(item.reqId),
                        item.postDetail
                    )
                }
            }
        }

        override fun onMoreClick(item: ChoiceCardInfo) {
            if (ChoiceCardType.isRoomCardType(item.cardType)) {
                HomeRoomAnalytics.trackHomeRoomCardSeeAllCLick()
                jumpToMoreRoomPage()
            } else if (ChoiceCardType.isHomeRoomCardType(item.cardType)) {
                jumpToHomeRoomPage()
            } else if (ChoiceCardType.isUgcCreate(item.cardType)) {
                if (isAdded && !isDetached) {
                    MetaRouter.MobileEditor.ugcAll(
                        this@RecommendFragment,
                        item.cardId.toString(),
                        item.cardName ?: ""
                    )
                }
            } else if (item.cardType == ChoiceCardType.TEMPLATE) {
                // 处理模板卡片的更多点击
                if (isAdded && !isDetached) {
                    MetaRouter.Template.templateListDetail(
                        this@RecommendFragment,
                        item
                    )
                }
            } else if (item.cardType == ChoiceCardType.ASSETS) {
                // 处理资源卡片的更多点击
                if (isAdded && !isDetached) {
                    MetaRouter.Assets.assetList(
                        this@RecommendFragment,
                        item
                    )
                }
            } else if (ChoiceCardType.isHomeVideoCardType(item.cardType)) {

                val resId = ResIdBean
                    .newInstance()
                    .setCategoryID(CategoryId.HOME_RECOMMEND_NEWEST_GAME)
                    .setReqId(item.videoFeeds?.reqId ?: "")

                RecommendVideoAnalytics.trackVideoMoreClick(resId)
                if (isAdded && !isDetached) {
                    MetaRouter.Video.goRecommendVideoList(
                        this@RecommendFragment,
                        resId,
                        item.videoFeeds
                    )
                }
            }
        }

        override fun onItemClick(
            cardPos: Int,
            card: ChoiceCardInfo,
            itemPos: Int,
            item: IChoiceItem,
            isBanner: Boolean
        ) {
            when (item) {
                is ChoiceGameInfo -> <EMAIL>(
                    cardPos + 1,
                    card,
                    itemPos + 1,
                    item,
                    if (isBanner) {
                        CategoryId.HOME_RECOMMEND_CATEGORY_BANNER
                    } else {
                        CategoryId.HOME_RECOMMEND_CATEGORY
                    }
                )

                is ChatRoomInfo -> showRoomDetail(item)

                is House -> {
                    Analytics.track(
                        EventConstants.EVENT_DSHOME_ENTRY_CLICK,
                        mapOf(
                            "show_categoryid" to "2",
                            "userid" to (item.ownerUuid ?: ""),
                            "homename" to (item.description ?: ""),
                            "onlinenumber" to (item.number ?: ""),
                            "roomid" to (item.roomId ?: "")
                        )
                    )
                    item.roomId?.let {
                        CottageRoomDetailDialog.show(
                            this@RecommendFragment,
                            it,
                            ResIdBean().setGameId(item.gameId)
                                .setCategoryID(CategoryId.HOME_RECOMMEND_CATEGORY),
                            "2"
                        )
                    }
                }

                is ChoiceHomeVideoItem -> {
                    val resId = ResIdBean
                        .newInstance()
                        .setCategoryID(CategoryId.HOME_RECOMMEND_NEWEST_GAME)
                        .setParam1(itemPos + 1)
                        .setReqId(item.reqId)

                    RecommendVideoAnalytics.trackVideoItemClick(resId, item.postDetail)
                    if (isAdded && !isDetached) {
                        MetaRouter.Video.goRecommendVideoFeed(
                            this@RecommendFragment,
                            resId,
                            item.postDetail.postId
                        )
                    }
                }
            }
        }

        override fun onCardShow(cardPos: Int, card: ChoiceCardInfo) {
            if (ChoiceCardType.isRoomCardType(card.cardType)) {
                HomeRoomAnalytics.trackHomeRoomCardShow(card.roomList?.size ?: 0)
            }
            card.recommend?.let { item ->
                Analytics.track(
                    EventConstants.C_FEED_ITEM_SHOW,
                    "gameid" to item.code.toString(),
                    "packagename" to item.packageName.toString(),
                    "reqid" to item.reqid.toString(),
                    "icon_type" to item.gcMode.toString(),
                    "type" to "1",
                    "show_categoryid" to CategoryId.HOME_RECOMMEND_NEWEST_GAME,
                    "show_param1" to cardPos
                )
            }
        }

        override fun bindPlayed(playedCount: Int, binding: ViewChoiceContinueGameBinding) {
            if (binding.rvRecentlyPlayed.layoutManager == null) {
                binding.rvRecentlyPlayed.layoutManager =
                    LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
            }
            playedAdapter.setOnItemClickListener { _, position ->
                playedAdapter.peek(position)?.let {
                    val house = editorInteractor.userMainHouse.value?.data
                    if (house?.gameId.equals(it.id)) {
                        Analytics.track(EventConstants.EVENT_ITEM_CLICK) {
                            put("gameid", it.id)
                            put("game_type", if (it.isUgcGame) "UGC" else "PGC")
                            putAll(ResIdUtils.getAnalyticsMap(getResid()))
                        }
                        // 小屋
                        editorInteractor.launchCottageGame(
                            this@RecommendFragment, accountInteractor.curUuid, "8",
                            CategoryId.COTTAGE_ROOM_LIST, "dsHomePlayedGame"
                        )
                    } else {
                        if (it.isUgcGame) {
                            Analytics.track(EventConstants.EVENT_ITEM_CLICK) {
                                put("gameid", it.id)
                                put("game_type", if (it.isUgcGame) "UGC" else "PGC")
                                putAll(ResIdUtils.getAnalyticsMap(getResid()))
                            }
                            if (isAdded && !isDetached) {
                                MetaRouter.MobileEditor.ugcDetail(
                                    this@RecommendFragment,
                                    it.id,
                                    null,
                                    requirePlayedGameResIdBean(it.id).setParam1(position + 1)
                                        .setTsType(ResIdBean.TS_TYPE_UCG)
                                        .setIconType(ResIdBean.ICON_TYPE_UGC)
                                )
                            }
                        } else {
                            playGame(
                                it.id,
                                it.packageName ?: "",
                                requirePlayedGameResIdBean(it.id).setParam1(position + 1)
                                    .setTsType(ResIdBean.TS_TYPE_NORMAL)
                                    .setIconType(ResIdBean.ICON_TYPE_PGC)
                            )
                        }
                    }
                }
            }
            if (binding.rvRecentlyPlayed.adapter != playedAdapter) {
                binding.rvRecentlyPlayed.adapter = playedAdapter
            }
            binding.rvRecentlyPlayed.setHasFixedSize(true)

            binding.tvPlayedManage.setOnClickListener {
                if (isAdded && !isDetached) { // 添加双重状态校验
                    MetaRouter.Played.manager(this@RecommendFragment)
                } else {
                    Timber.w("RecommendFragment not attached when click manage button")
                }
            }
            headContinueGameBinding = binding
        }

        override fun unbindPlayed(playedCount: Int, binding: ViewChoiceContinueGameBinding) {
            playedAdapter.setOnItemClickListener(null)
            binding.tvPlayedManage.unsetOnClick()
        }

        override fun getGlideOrNull(): RequestManager? {
            return glide
        }
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentRecommendBinding? {
        return FragmentRecommendBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.recyclerView.itemAnimator = null
        initTitle()
        initRv()
        refreshMyGames()
        initData()
        homeViewModel.init()
        homeViewModel.cardListLiveData.observe(viewLifecycleOwner) {
            viewModel.updateChoiceData(it)
        }
        suggestionViewModel.insertPartyPageCallback.observe(viewLifecycleOwner) {
            homeViewModel.insertTrending()
        }
        observeRefreshRoomList()
        homeHeaderFriends.initialize()
    }

    private fun initRv() {
        viewModel.setupRefreshLoading(
            RecommendViewModelState::state,
            binding.loadingView,
            binding.refreshLayout
        ) {
            if (NetUtil.isNetworkAvailable()) {
                playedAdapter.refresh()
                viewModel.refresh()
                viewModel.featRecommendOperations()
                homeViewModel.refreshData()
            } else {
                binding.refreshLayout.isRefreshing = false
                binding.loadingView.showError()
            }
        }
        EpoxyVisibilityTracker().apply {
            partialImpressionThresholdPercentage = 70
            attachV2(viewLifecycleOwner, binding.recyclerView)
        }
        binding.recyclerView.setController(recommendController)
        binding.recyclerView.setDelayMsWhenRemovingAdapterOnDetach(1)
    }

    private fun checkRefreshRoomList() {
        if (accountInteractor.checkAccountInit() && MWBiz.isAvailable()) {
            homeViewModel.checkRefreshRoomList()
        }
    }

    private fun observeRefreshRoomList() {
        accountInteractor.addTokenChangedCallback(accountCallback)
        MWLifeCallback.available.observe(viewLifecycleOwner) {
            checkRefreshRoomList()
        }
    }

    private fun buildRecommendController() = simpleController(
        viewModel,
        RecommendViewModelState::refresh,
        RecommendViewModelState::loadMore,
        RecommendViewModelState::playedCount,
        RecommendViewModelState::operationList,
        RecommendViewModelState::choiceList
    ) { refresh, loadMore, playedCount, operationList, choiceList ->
        addHeader(playedCount, operationList)

        choiceList()?.first?.forEachIndexed { cardPosition, card ->
            choiceCardItem(viewLifecycleOwner, card, cardPosition, 2, choiceListener)
        }

        val list = refresh.invoke() ?: emptyList()

        addRecommendHeader()
        addItem(list)
        addFooter(refresh, list, loadMore)
    }

    private fun MetaEpoxyController.addItem(list: List<HomeCustomRecommend.RecommendList>) {
        list.forEachIndexed { index, it ->
            addGame(it, index)
        }
    }

    private fun MetaEpoxyController.addFooter(
        refresh: Async<List<HomeCustomRecommend.RecommendList>>,
        list: List<HomeCustomRecommend.RecommendList>,
        loadMore: Async<LoadMoreState>
    ) {
        if (list.isNotEmpty()) {
            loadMoreFooter(loadMore, spanSize = 2) {
                if (!binding.refreshLayout.isRefreshing) {
                    viewModel.loadMore()
                }
            }
        } else if (refresh is Fail && loadMore.shouldLoad) {
            loadMoreFooter(refresh.mapRetained(loadMore), spanSize = 2) {
                viewModel.refresh()
            }
        } else if (refresh is Loading) {
            loadMoreFooter(refresh.mapRetained(loadMore), spanSize = 2) {

            }
        }
    }

    private fun MetaEpoxyController.addGame(
        it: HomeCustomRecommend.RecommendList,
        index: Int
    ) {
        addGameItem(index, it, {
            Analytics.track(
                EventConstants.C_FEED_ITEM_SHOW,
                "gameid" to it.gameId,
                "gamename" to it.gameName.toString(),
                "packagename" to it.packageName.toString(),
                "reqid" to it.reqid.toString(),
                "type" to "${it.gameType}",
                "icon_type" to if (it.isUGCGame()) "UGC" else "PGC",
                "show_categoryid" to CategoryId.HOME_RECOMMEND,
                "show_param1" to index + 1
            )
        }, {
            Analytics.track(
                EventConstants.C_FEED_ITEM_CLICK,
                "gameid" to it.gameId,
                "gamename" to it.gameName.toString(),
                "packagename" to it.packageName.toString(),
                "reqid" to it.reqid.toString(),
                "type" to "${it.gameType}",
                "icon_type" to if (it.isUGCGame()) "UGC" else "PGC",
                "show_categoryid" to CategoryId.HOME_RECOMMEND,
                "show_param1" to index + 1
            )
            goGameDetail(it)
        }, ::glide)
    }

    private fun MetaEpoxyController.addHeader(playedCount: Int, operationList: Async<List<HomeRecommendOperation>>) {
        // 写死，没有首页跟房，需求来自 -> 宋津瑶
        if (PandoraToggle.openHomeFlowRoom) {
            addFriendHeader(homeHeaderFriends)
        }

        addContinueHeader(playedCount, choiceListener)
    }

    private fun onRecommendOperationClick(operation: HomeRecommendOperation) {
        Analytics.track(EventConstants.HOME_BANNER_CLICK) {
            put("link", operation.param1 ?: "")
            put("source", "1")
            put(EventParamConstants.KEY_EVENT_ID, operation.id)
        }
        val jumpConfig = operation.toUniJumpConfig()
        val handled = UniJumpUtil.jump(
            this,
            jumpConfig,
            LinkData.SOURCE_CHOICE_OPERATION,
            CategoryId.HOME_RECOMMEND_CATEGORY_BANNER,
            null
        )
        if (!handled) {
            toast(R.string.not_currently_support)
        }
    }

    private fun goGameDetail(game: HomeCustomRecommend.RecommendList) {
        viewModel.tempGameItem = game
        if (game.isUGCGame()) {
            if (isAdded && !isDetached) {
                MetaRouter.MobileEditor.ugcDetail(
                    this@RecommendFragment,
                    game.gameId,
                    null,
                    getResid(game)
                )
            }
        } else {
            playGame(game.gameId, game.packageName ?: "", getResid(game))
        }
    }

    private fun getResid(item: HomeCustomRecommend.RecommendList? = null) =
        ResIdBean().setCategoryID(CategoryId.HOME_RECOMMEND).setReqId(item?.reqid)
            .setGameId(item?.gameId)
            .setIconType(if (item?.isUGCGame() == true) "UGC" else "PGC")
            .setTsType(if (item?.isUGCGame() == true) ResIdBean.TS_TYPE_UCG else ResIdBean.TS_TYPE_NORMAL)

    // 请求最近玩过列表数据
    private fun refreshMyGames() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.playedGames.collectLatest {
                playedAdapter.submitData(it)
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            playedAdapter.loadStateFlow.collect { loadStates ->
                viewModel.updatePlayedCount(playedAdapter.itemCount)
                // 当数据刷新完成时
                if (loadStates.refresh is LoadState.NotLoading
                    && playedAdapter.itemCount > 0
                    && headContinueGameBinding?.rvRecentlyPlayed?.layoutManager != null
                ) {
                    // 将列表滚动到最前面（如果首次还没绑定ViewM，不执行的话，不影响最终效果）
                    headContinueGameBinding?.rvRecentlyPlayed?.scrollToPosition(0)
                }
            }
        }
    }

    private fun initTitle() {
        binding.recyclerView.layoutManager = object : GridLayoutManager(requireContext(), 2, VERTICAL, false) {
            override fun onLayoutChildren(recycler: RecyclerView.Recycler?, state: RecyclerView.State?) {
                kotlin.runCatching {
                    super.onLayoutChildren(recycler, state)
                }
            }
        }
        binding.clRobux.setOnAntiViolenceClickListener {
            val url = GlobalContext.get().get<H5PageConfigInteractor>()
                .getH5PageUrl(H5PageConfigInteractor.ACTIVITY_ENTRANCE).toHttpUrl()
            if (isAdded && !isDetached) {
                MetaRouter.Web.navigate(this, null, url.toString())
            }
            val data = viewModel.oldState.roBuxRecordInfo.invoke()?.data
            val map = mapOf(
                "act" to "1",
                "status" to (viewModel.oldState.roBuxRecordInfo.invoke()?.data?.status ?: 0), "duration" to (data?.roundCountDown ?: 0)
            )
            Analytics.track(EventConstants.EVENT_ACTIVITY_CLICK, map)
        }
    }

    private fun initData() {
        viewModel.onAsync(RecommendViewModelState::roBuxRecordInfo, onLoading = {}, onSuccess = { it: DataResult<RoBuxRecordInfo> ->
            startUpdateTimer(it)
        })
        viewModel.onEach(RecommendViewModelState::updateTime) {
            binding.tvRobuxTips.visible(it)
        }
    }

    private fun startUpdateTimer(it: DataResult<RoBuxRecordInfo>) {
        if (PandoraToggle.isOpenRoBuxRecord) {

            when (it.data?.status) {
                RoBuxRecordInfo.NOT_STATED -> {
                    //未开始
                    binding.tvRobuxTips.text = it.data?.bubbleTips ?: getString(R.string.robux_tips)
                    binding.tvRobuxTime.text = DateUtil.getFormatDayTimeByTimeStamp((it.data?.roundTotalDuration ?: 86400))
                    binding.clRobux.visible()
                    val map = mapOf("act" to "0", "status" to RoBuxRecordInfo.NOT_STATED, "duration" to (it.data?.roundCountDown ?: 0))
                    Analytics.track(EventConstants.EVENT_ACTIVITY_CLICK, map)
                }

                RoBuxRecordInfo.PROGRESS -> {
                    //进行中
                    if (it.data?.roundCountDown != null) {
                        binding.tvRobuxTime.text = DateUtil.getFormatDayTimeByTimeStamp((it.data?.roundCountDown ?: 86400))
                        timer?.cancel()
                        timer = getCountDownTimer((it.data?.roundCountDown ?: 86400) * 1000)
                        timer?.start()
                    } else {
                        binding.tvRobuxTime.apply {
                            isEnabled = true
                            text = getString(R.string.acivity_time_empty)
                        }
                    }
                    binding.tvRobuxTips.text = it.data?.bubbleTips ?: getString(R.string.robux_tips)
                    binding.clRobux.visible()
                    val map = mapOf("act" to "0", "status" to RoBuxRecordInfo.PROGRESS, "duration" to (it.data?.roundCountDown ?: 0))
                    Analytics.track(EventConstants.EVENT_ACTIVITY_CLICK, map)
                }

                else -> {
                    //已结束，隐藏入口
                    binding.clRobux.gone()
                }
            }
        }
    }

    private fun getCountDownTimer(allTime: Long) = object : CountDownTimer(allTime, 1000) {
        override fun onTick(millisUntilFinished: Long) {
            val restTime = millisUntilFinished / 1000
            binding.tvRobuxTime.apply {
                text = DateUtil.getFormatDayTimeByTimeStamp(restTime)
            }
        }

        override fun onFinish() {
            binding.tvRobuxTime.apply {
                text = getString(R.string.acivity_time_empty)
            }
        }
    }

    private fun playGame(id: String, packageName: String, resIdBean: ResIdBean) {
        if (isAdded && !isDetached) {
            MetaRouter.GameDetail.navigate(this@RecommendFragment, id, resIdBean, packageName, type = "ts")
        }
    }

    private fun requirePlayedGameResIdBean(gameId: String): ResIdBean {
        val cacheBean = metaKV.analytic.getLaunchResIdBeanWithId(gameId)
        return ResIdBean().setCategoryID(CategoryId.RECOMMEND_MY_GAMES).apply {
            cacheBean?.also {
                setTsType(it.getTsType())
            }
        }
    }

    override fun getPageName(): String {
        return PageNameConstants.FRAGMENT_MAPS_TAB_MAPS
    }

    private fun jumpToWeb(fragment: Fragment, title: String? = null, url: String) {
        if (url.startsWith("http://", ignoreCase = true) or url.startsWith("https", true)) {
            if (isAdded && !isDetached) {
                MetaRouter.Web.navigate(fragment, title, url)
            }
        }
    }

    private fun jump(subInfo: ChoiceGameInfo, resIdBean: ResIdBean) {
        when (subInfo.type) {
            ChoiceContentType.GAME -> {
                Analytics.track(EventConstants.EVENT_ITEM_CLICK) {
                    put("gameid", subInfo.code.toString())
                    put("game_type", subInfo.gcMode.toString())
                    putAll(ResIdUtils.getAnalyticsMap(resIdBean))
                }
                viewModel.tempItem = subInfo
                if (subInfo.gcMode == "UGC") {
                    if (PandoraToggle.enableUgcDetail) {
                        if (isAdded && !isDetached) {
                            MetaRouter.MobileEditor.ugcDetail(this, subInfo.code.orEmpty(), null, resIdBean)
                        }
                    } else {
                        val params = EditorUGCLaunchParams(subInfo.code ?: "", subInfo.packageName, subInfo.displayName ?: "", subInfo.iconUrl ?: "", "")
                        editorGameLaunchHelper?.startUgcGame(this, params, resIdBean.setTsType(ResIdBean.TS_TYPE_UCG))
                    }
                } else {
                    playGame(subInfo.code ?: "", subInfo.packageName ?: "", resIdBean.setTsType(ResIdBean.TS_TYPE_NORMAL))
                }
            }

            ChoiceContentType.LINK -> {
                // 内部、外部的web链接，外部scheme
                subInfo.router?.let { router ->
                    Analytics.track(EventConstants.HOME_BANNER_CLICK) {
                        put("link", router)
                        put("source", "2")
                    }
                    if (subInfo.jumpOperation()) {
                    } else if (subInfo.jumpOutside()) {
                        // 外部scheme/http url
                        kotlin.runCatching {
                            val intent = Intent(Intent.ACTION_VIEW, Uri.parse(router))
                            startActivity(intent)
                        }.getOrElse {
                            jumpToWeb(this, subInfo.title, router)
                        }
                    } else {
                        // 内部http url
                        jumpToWeb(this, subInfo.title, router)
                    }
                }
            }

            ChoiceContentType.OPERATION -> {
                // 内部scheme
                Analytics.track(EventConstants.HOME_BANNER_CLICK) {
                    put("link", subInfo.operatingPosition?.param1.orEmpty())
                    put("source", "2")
                    put(EventParamConstants.KEY_EVENT_ID, subInfo.operatingPosition?.id.orEmpty())
                }
                subInfo.operatingPosition?.let {
                    val handled = UniJumpUtil.jump(
                        this,
                        it,
                        LinkData.SOURCE_CHOICE_OPERATION,
                        CategoryId.HOME_RECOMMEND_CATEGORY_BANNER,
                        mainViewModel,
                        null
                    )
                    if (!handled) {
                        toast(R.string.not_currently_support)
                    }
                } ?: run {
                    toast(R.string.not_currently_support)
                }
            }

            else -> {

            }
        }
    }

    private fun jumpToHomeRoomPage() {
        if (isAdded && !isDetached) {
            MetaRouter.HomeRoom.goAllHomeRoom(this)
        }
    }

    private fun jumpToMoreRoomPage() {
        if (isAdded && !isDetached) {
            MetaRouter.HomeRoom.goAllRoom(this)
        }
    }

    /**
     * 点击展示房间详情页
     */
    private fun showRoomDetail(roomInfo: ChatRoomInfo) {
        HomeRoomAnalytics.trackRoomClick(roomInfo, CategoryId.HOME_RECOMMEND_CATEGORY)
        RoomDetailDialog.show(
            this,
            roomInfo,
            ResIdBean().setGameId(roomInfo.platformGameId)
                .setCategoryID(CategoryId.HOME_RECOMMEND_CATEGORY)
        )
    }

    private fun refreshRoom() {
        homeViewModel.refreshRoomList()
    }

    private fun onItemShow(
        cardPos: Int,
        card: ChoiceCardInfo?,
        itemPos: Int,
        item: ChoiceGameInfo,
        categoryId: Int = CategoryId.HOME_RECOMMEND_CATEGORY
    ) {
        Timber.tag(TAG)
            .d("itemShow cardPos:$cardPos card:[${card?.cardId}, ${card?.cardName}, ${card?.gameList?.size}] itemPos:$itemPos item:[${item.code}, ${item.displayName}, ${item.packageName}]")
        when (item.type) {
            ChoiceContentType.LINK -> {
                Analytics.track(EventConstants.HOME_BANNER_SHOW, "link" to item.router.toString(), "source" to "2")
            }

            ChoiceContentType.OPERATION -> {
                Analytics.track(
                    EventConstants.HOME_BANNER_SHOW,
                    "link" to item.operatingPosition?.param1.orEmpty(),
                    "source" to "2",
                    EventParamConstants.KEY_EVENT_ID to item.operatingPosition?.id.orEmpty()
                )
            }

            ChoiceContentType.GAME -> {
                lifecycleScope.launch(Dispatchers.Default) {
                    Analytics.track(EventConstants.EVENT_ITEM_SHOW) {
                        put("gameid", item.code ?: "")
                        put("packagename", item.packageName ?: "")
                        put("game_type", item.typeToString())
                        putAll(
                            ResIdUtils.getAnalyticsMap(
                                ResIdBean().setGameId(item.code ?: "").setCategoryID(categoryId)
                                    .setParam1(cardPos).setParam2(itemPos).setParamExtra(card?.cardName)
                            )
                        )
                    }
                }
            }
        }
    }

    private fun onItemClick(
        cardPos: Int,
        card: ChoiceCardInfo?,
        itemPos: Int,
        item: ChoiceGameInfo,
        categoryId: Int = CategoryId.HOME_RECOMMEND_CATEGORY,
    ) {
        Timber.tag(TAG)
            .e("itemClick cardPos:$cardPos card:[${card?.cardId}, ${card?.cardName}, ${card?.gameList?.size}] itemPos:$itemPos item:[${item.code}, ${item.displayName}, ${item.packageName}]")
        val resIdBean =
            ResIdBean().setGameId(item.code ?: "").setCategoryID(categoryId).setParam1(cardPos)
                .setParam2(itemPos).setParamExtra(card?.cardName).setReqId(item.reqid.toString())
                .setIconType(item.gcMode)
        jump(item, resIdBean)
    }

    override fun invalidate() {}

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewModel.apiMonitor(
            this,
            RecommendViewModelState::refresh
        )
        recommendController.onRestoreInstanceState(savedInstanceState)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        recommendController.onSaveInstanceState(outState)
    }

    override fun onResume() {
        super.onResume()
        viewModel.tempItem = null
        if (!isFirstResume) {
            refreshRoom()
        } else {
            isFirstResume = false
        }
        Timber.d("getRoBuxRecord %s", PandoraToggle.isOpenRoBuxRecord)
        if (PandoraToggle.isOpenRoBuxRecord) {
            viewModel.updateShowTips(true)
            viewModel.getRoBuxRecord()
        }

        // 如果之前请求过了，并且有列表是空的，则再请求一次
        val s = viewModel.oldState
        if (s.choiceList.invoke()?.first == null || s.refresh.invoke().isNullOrEmpty()) {
            viewModel.refresh()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        timer?.cancel()
        timer = null
        if (PandoraToggle.isOpenRoBuxRecord) {
            viewModel.updateShowTips(false)
        }
        homeHeaderFriends?.destroy()
    }
}