package com.socialplay.gpark.ui.aibot.ugc.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.socialplay.gpark.data.model.aibot.AIBotCreateImageInfo
import com.socialplay.gpark.databinding.ItemAiBotCreateImageBinding
import com.socialplay.gpark.ui.base.adapter.BaseAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setHeight
import com.socialplay.gpark.util.extension.setWidth
import com.socialplay.gpark.util.extension.visible

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/08/26
 *     desc   :
 *
 */
class AIBotCreateImageAdapter : BaseAdapter<AIBotCreateImageInfo, ItemAiBotCreateImageBinding>() {
   private val with = (ScreenUtil.screenWidth-64.dp)/4
    override fun createViewBinding(
        layoutInflater: LayoutInflater,
        parent: ViewGroup,
        viewType: Int
    ): ItemAiBotCreateImageBinding {
        return ItemAiBotCreateImageBinding.inflate(layoutInflater, parent, false)
    }

    override fun convert(
        holder: BindingViewHolder<ItemAiBotCreateImageBinding>,
        item: AIBotCreateImageInfo,
        position: Int
    ) {
        holder.binding.clRoot.setWidth(with)
        Glide.with(context).load(item.coverImage).transform(CenterCrop(), RoundedCorners(8.dp))
            .into(holder.binding.imgStyle)
        holder.binding.viewSpace.visible(item.isSelect)
    }


}