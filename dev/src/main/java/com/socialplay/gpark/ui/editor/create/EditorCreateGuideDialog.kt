package com.socialplay.gpark.ui.editor.create

import android.graphics.Color
import android.graphics.Point
import android.view.WindowManager
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.FragmentManager
import com.bumptech.glide.GenericTransitionOptions
import com.bumptech.glide.Glide
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.editor.FormworkList
import com.socialplay.gpark.databinding.DialogEditorCreateGuideBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.ui.base.BaseDialogFragment
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.backgroundTintListByColor
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.property.viewBinding

class EditorCreateGuideDialog : BaseDialogFragment() {
    enum class CallbackStatus {
        BUILD, UGC, NEXT, NOTHING
    }

    companion object {

        fun show(
            fragmentManager: FragmentManager,
            item: FormworkList.Formwork,
            isFirst: Boolean,
            point: Point,
            callback: (CallbackStatus) -> Unit
        ) {
            EditorCreateGuideDialog().apply {
                this.item = item
                this.isFirst = isFirst
                this.callback = callback
                this.point = point
            }.show(fragmentManager, "EditorCreateGuideDialog")
        }
    }

    private var item: FormworkList.Formwork? = null
    private var isFirst: Boolean = true
    private var callback: (CallbackStatus) -> Unit = {}
    private var point: Point = Point()

    override val binding by viewBinding(DialogEditorCreateGuideBinding::inflate)

    override fun init() {
        Analytics.track(EventConstants.UGC_CREATE_GUIDE_POPUP_SHOW, "guidetype" to if (isFirst) "1" else "2")
        binding.clMove.updateLayoutParams<ConstraintLayout.LayoutParams> {
            leftMargin = if (isFirst) 8.dp else point.x - 8.dp
            topMargin = point.y - 16.dp
        }
        if (isFirst) {
            binding.cl2.gone()
//            binding.iv.visible()
            binding.tvBuild.visible()
//            Glide.with(this).load(item?.titleIcon).into(binding.iv)
            binding.tvBuild.setOnAntiViolenceClickListener {
                callback.invoke(CallbackStatus.BUILD)
                Analytics.track(
                    EventConstants.UGC_CREATE_GUIDE_POPUP_CLICK,
                    "guidetype" to if (isFirst) "1" else "2",
                    "listtype" to "3",
                    "gameid" to item?.availableGameCode.orEmpty(),
                )
                dismiss()
            }
            binding.clRoot.setOnAntiViolenceClickListener {
                callback.invoke(CallbackStatus.NEXT)
                Analytics.track(
                    EventConstants.UGC_CREATE_GUIDE_POPUP_CLICK,
                    "guidetype" to if (isFirst) "1" else "2",
                    "listtype" to "2",
                    "gameid" to item?.availableGameCode.orEmpty(),
                )
                dismiss()
            }
            binding.clGuide1.setOnAntiViolenceClickListener {
                callback.invoke(CallbackStatus.NEXT)
                Analytics.track(
                    EventConstants.UGC_CREATE_GUIDE_POPUP_CLICK,
                    "guidetype" to if (isFirst) "1" else "2",
                    "listtype" to "1",
                    "gameid" to item?.availableGameCode.orEmpty(),
                )
                dismiss()
            }
            kotlin.runCatching {
                binding.tvBuild.backgroundTintListByColor(Color.parseColor(item?.startBuildConfig))
            }
        } else {
            binding.cl2.visible()
//            binding.iv.gone()
            binding.tvBuild.gone()
            binding.apply {
                val item = item?.gameList?.firstOrNull() ?: return@apply
                Glide.with(this@EditorCreateGuideDialog).load(item.banner)
                    .centerCrop()
                    .transition(GenericTransitionOptions.withNoTransition())
                    .into(ivCover)
                tvPv.text = UnitUtil.formatPlayerCount(item.pvCount)
                tvTitle.text = item.ugcGameName
                Glide.with(this@EditorCreateGuideDialog).load(item.userIcon)
                    .centerCrop()
                    .transition(GenericTransitionOptions.withNoTransition())
                    .into(ivAvatar)
                tvAuthorName.text = item.userName
            }
            binding.cl2.setOnAntiViolenceClickListener {
                Analytics.track(
                    EventConstants.UGC_CREATE_GUIDE_POPUP_CLICK,
                    "guidetype" to if (isFirst) "1" else "2",
                    "listtype" to "4",
                    "gameid" to item?.availableGameCode.orEmpty(),
                )
                callback.invoke(CallbackStatus.UGC)
                dismiss()
            }
            binding.clRoot.setOnAntiViolenceClickListener {
                Analytics.track(
                    EventConstants.UGC_CREATE_GUIDE_POPUP_CLICK,
                    "guidetype" to if (isFirst) "1" else "2",
                    "listtype" to "2",
                    "gameid" to item?.availableGameCode.orEmpty(),
                )
                callback.invoke(CallbackStatus.NOTHING)
                dismiss()
            }
            binding.clGuide1.gone()
            binding.clGuide2.visible()
            binding.clGuide2.setOnAntiViolenceClickListener {
                callback.invoke(CallbackStatus.NEXT)
                Analytics.track(
                    EventConstants.UGC_CREATE_GUIDE_POPUP_CLICK,
                    "guidetype" to if (isFirst) "1" else "2",
                    "listtype" to "1",
                    "gameid" to item?.availableGameCode.orEmpty(),
                )
                dismiss()
            }
        }
    }

    override fun isBackPressedDismiss(): Boolean {
        return false
    }

    override fun windowHeight(): Int {
        return WindowManager.LayoutParams.MATCH_PARENT
    }

    override fun isFullScreen(): Boolean {
        return true
    }

    override fun loadFirstData() {
    }
}