package com.socialplay.gpark.ui.kol.list.provider

import android.view.View
import android.widget.ImageView
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.RequestManager
import com.chad.library.adapter.base.provider.BaseItemProvider
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.creator.CreatorMultiInfo
import com.socialplay.gpark.data.model.creator.CreatorMultiType
import com.socialplay.gpark.ui.kol.list.IKolCreatorAdapterListener
import com.socialplay.gpark.ui.kol.list.adapter.KolUgcLabelAdapter
import com.socialplay.gpark.ui.view.MetaTextView
import com.socialplay.gpark.ui.view.center.CenterLayoutManager
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setPaddingEx

/**
 * Kol ugc作品标签选择器
 */
class KolUgcLabelListProvider(
    private val glide: RequestManager,
    private val listener: IKolCreatorAdapterListener
) :
    BaseItemProvider<CreatorMultiInfo>() {

    override val layoutId: Int
        get() = R.layout.provider_creator_ugc_label_list

    override val itemViewType: Int = CreatorMultiType.TYPE_ALL_UGC_GAME_SELECTOR

    companion object {
        const val RV_STATE_KEY = "KolUgcLabelList"
    }

    override fun convert(helper: BaseViewHolder, item: CreatorMultiInfo, payloads: List<Any>) {
        super.convert(helper, item, payloads)
        payloads.forEach {
            val payload = it.toString()
            if (payload.contains("select")) {
                val rv = helper.getView<RecyclerView>(R.id.rvHor)
                val list = listener.getUgcLabelList().toMutableList()
                val adapter = rv.adapter as? KolUgcLabelAdapter ?: return
                adapter.submitData(
                    listener.getLifecycle(),
                    list,
                    list.isEmpty()
                ) {
                    val position =
                        payload.split("_").lastOrNull()?.toIntOrNull() ?: return@submitData
                    if (position in adapter.data.indices) {
                        rv.layoutManager?.smoothScrollToPosition(
                            rv,
                            RecyclerView.State(),
                            position
                        )
                    }
                }
            }
        }
    }

    override fun convert(helper: BaseViewHolder, item: CreatorMultiInfo) {
        val ivMore = helper.getView<ImageView>(R.id.ivMore)
        val vMore = helper.getView<View>(R.id.vMore)
        val list = listener.getUgcLabelList()
        val showMore = list.size > 3
        ivMore.setOnAntiViolenceClickListener {
            listener.showMoreUgcLabel()
        }
        helper.getView<MetaTextView>(R.id.tvTitle).text = item.title
        vMore.isVisible = showMore
        ivMore.isVisible = showMore

        val adapter = initAdapter()
        val rv = helper.getView<RecyclerView>(R.id.rvHor)
        rv.layoutManager = CenterLayoutManager(helper.itemView.context, RecyclerView.HORIZONTAL, false).apply {
            listener.popRvStoredState(RV_STATE_KEY)?.let {
                onRestoreInstanceState(it)
            }
        }
        rv.setPaddingEx(helper.dp(16))
        rv.adapter = adapter
        rv.itemAnimator = null
        adapter.setList(list)
    }

    override fun onViewDetachedFromWindow(holder: BaseViewHolder) {
        holder.getViewOrNull<RecyclerView>(R.id.rvHor)?.layoutManager?.onSaveInstanceState()?.let {
            listener.saveRvState(RV_STATE_KEY, it)
        }
        super.onViewDetachedFromWindow(holder)
    }

    private fun initAdapter(): KolUgcLabelAdapter {
        val adapter =
            KolUgcLabelAdapter(glide)
        adapter.setOnItemClickListener { _, view, position ->
            val item = adapter.getItem(position)
            listener.selectUgcLabel(item.tagId)
        }
        return adapter
    }
}