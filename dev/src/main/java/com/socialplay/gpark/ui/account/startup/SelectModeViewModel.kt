package com.socialplay.gpark.ui.account.startup

import android.content.ComponentCallbacks
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/05/24
 *     desc   :
 * </pre>
 */
data class SelectModeState(
    val mode: Int = 0
) : MavericksState {
    companion object {
        const val MODE_WORLD_MAP = 1
        const val MODE_PARTY_FEED = 2
    }
}

class SelectModeViewModel(
    initialState: SelectModeState
) : BaseViewModel<SelectModeState>(initialState) {

    companion object : KoinViewModelFactory<SelectModeViewModel, SelectModeState>() {
        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: SelectModeState
        ): SelectModeViewModel {
            return SelectModeViewModel(state)
        }
    }

    fun updateMode(mode: Int) {
        setState { copy(mode = mode) }
    }
}