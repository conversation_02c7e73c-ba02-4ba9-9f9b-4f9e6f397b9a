package com.socialplay.gpark.ui.recommend.choice

import androidx.lifecycle.LifecycleOwner
import com.socialplay.gpark.data.model.choice.ChoiceCardInfo
import com.socialplay.gpark.data.model.choice.ChoiceCardType
import com.socialplay.gpark.databinding.ViewChoiceContinueGameBinding
import com.socialplay.gpark.ui.core.IBaseEpoxyItemListener
import com.socialplay.gpark.ui.core.views.MetaModelCollector
import com.socialplay.gpark.ui.editorschoice.ChoiceHomeCardAdapter


/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/10/31
 *     desc   :
 * </pre>
 */
interface IChoiceListener : ChoiceHomeCardAdapter.HomeItemCallback, IBaseEpoxyItemListener {
    fun onMoreClick(item: ChoiceCardInfo)
    fun bindPlayed(playedCount: Int, binding: ViewChoiceContinueGameBinding)
    fun unbindPlayed(playedCount: Int, binding: ViewChoiceContinueGameBinding)
}

fun MetaModelCollector.choiceCardItem(
    owner: LifecycleOwner,
    card: ChoiceCardInfo,
    cardPosition: Int,
    spanSize: Int,
    listener: IChoiceListener
) {
    when (card.cardType) {
        ChoiceCardType.SMALL -> {
            choiceSmallCard(card, cardPosition, spanSize, listener)
        }

        ChoiceCardType.TEMPLATE -> {
            templateCardList(card, cardPosition, spanSize, listener)
        }

        ChoiceCardType.ASSETS -> {
            assetCardList(card, cardPosition, spanSize, listener)
        }

        ChoiceCardType.BANNER -> {
            choiceBannerCard(owner, card, cardPosition, spanSize, listener)
        }

        ChoiceCardType.ROOM -> {
            choiceRoomCard(card, cardPosition, spanSize, listener)
        }

        ChoiceCardType.HOME -> {
            choiceCottageRoomCard(card, cardPosition, spanSize, listener)
        }

        ChoiceCardType.UGC_CREATE -> {
            choiceUgcCreateCard(card, cardPosition, spanSize, listener)
        }

        ChoiceCardType.RECOMMEND_VIDEO_FEED_LIST -> {
            choiceVideoFeedCard(card, cardPosition, spanSize, listener)
        }
    }
}


