package com.socialplay.gpark.ui.im

import android.graphics.Rect
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.RequestManager
import com.meta.box.biz.friend.model.FriendInfo
import com.socialplay.gpark.databinding.ViewOnlineFriendHeaderBinding
import com.socialplay.gpark.ui.base.adapter.BaseAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder
import com.socialplay.gpark.util.extension.dp

class OnlineFriendListHeader(requestManager: RequestManager) :
    BaseAdapter<List<FriendInfo>, ViewOnlineFriendHeaderBinding>() {

    val onlineFriendListAdapter = OnlineFriendListAdapter(requestManager)

    private val onlineFriendItemSpacingDecoration = object : RecyclerView.ItemDecoration() {
        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: RecyclerView.State
        ) {
            val holder = parent.getChildViewHolder(view) ?: return
            val itemCount = state.itemCount

            outRect.top  = 8.dp
            outRect.bottom  = 8.dp

            if(holder.bindingAdapterPosition == 0){
                outRect.left = 6.dp
            }

            val isLastItem = holder.bindingAdapterPosition == itemCount - 1
            if (isLastItem) {
                outRect.right = 6.dp
            }
        }
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): BindingViewHolder<ViewOnlineFriendHeaderBinding> {
        return super.onCreateViewHolder(parent, viewType).apply {
            setIsRecyclable(false)
        }
    }

    override fun createViewBinding(
        layoutInflater: LayoutInflater,
        parent: ViewGroup,
        viewType: Int
    ): ViewOnlineFriendHeaderBinding {
        return ViewOnlineFriendHeaderBinding.inflate(layoutInflater, parent, false)
    }

    override fun convert(holder: BindingViewHolder<ViewOnlineFriendHeaderBinding>, item: List<FriendInfo>, position: Int) {
        holder.binding.rvOnlineFriendList.removeItemDecoration(onlineFriendItemSpacingDecoration)
        holder.binding.rvOnlineFriendList.addItemDecoration(onlineFriendItemSpacingDecoration)
        holder.binding.rvOnlineFriendList.adapter = onlineFriendListAdapter
        onlineFriendListAdapter.setList(item)
    }
}