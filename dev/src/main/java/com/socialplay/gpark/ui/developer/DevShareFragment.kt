package com.socialplay.gpark.ui.developer

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.databinding.FragmentDeveloperShareBinding
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.util.property.viewBinding

/**
 * xingxiu.hou
 * 2021/9/16
 */
class DevShareFragment : BaseFragment<FragmentDeveloperShareBinding>() {

    companion object {
        private const val TAG = "DEV:Sharing"
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentDeveloperShareBinding? {
        return FragmentDeveloperShareBinding.inflate(inflater, container, false)
    }

    override fun init() {

        binding.btnShareToSnapchat.setOnClickListener {

            val sendIntent: Intent = Intent().apply {
                `package` = "com.snapchat.android"
                action = Intent.ACTION_SEND
                putExtra(Intent.EXTRA_TEXT, BuildConfig.DEV_SHARE_TEXT)
                type = "text/plain"
            }

            if(sendIntent.resolveActivity(requireContext().packageManager) != null){
                startActivity(sendIntent)
            }
        }
    }

    override fun loadFirstData() {
    }

    override fun getFragmentName() = PageNameConstants.FRAGMENT_NAME_DEV_APP_PARAMS
}