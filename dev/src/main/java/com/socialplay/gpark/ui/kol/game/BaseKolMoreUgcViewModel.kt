package com.socialplay.gpark.ui.kol.game

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Uninitialized
import com.socialplay.gpark.data.model.creator.CreatorUgcGame
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.views.LoadMoreState

/**
 * Created by bo.li
 * Date: 2024/8/8
 * Desc:kol更多ugc游戏
 */
data class KolMoreUgcModelState(
    val asyncList: Async<List<CreatorUgcGame>> = Uninitialized,
    val loadMore: Async<LoadMoreState> = Uninitialized,
    val rollId: String? = null
) : MavericksState {
    val list: List<CreatorUgcGame>
        get() = asyncList.invoke() ?: emptyList()
}

abstract class BaseKolMoreUgcViewModel(
    initialState: KolMoreUgcModelState
) : BaseViewModel<KolMoreUgcModelState>(initialState) {

    abstract fun refresh()
    abstract fun loadMore()
}