package com.socialplay.gpark.ui.mgs.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.meta.biz.mgs.data.model.MgsPlayerBuildingInfo
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.ItemPlayerBuildingBinding
import com.socialplay.gpark.ui.base.adapter.BaseAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder
import com.socialplay.gpark.util.extension.dp

/**
 * @author: ning.wang
 * @date: 2022-03-14 1:58 下午
 * @desc:
 */
class PlayerBuildingAdapter :
    BaseAdapter<MgsPlayerBuildingInfo, ItemPlayerBuildingBinding>() {

    override fun createViewBinding(layoutInflater: LayoutInflater, parent: <PERSON>Group, viewType: Int): ItemPlayerBuildingBinding {
        return ItemPlayerBuildingBinding.inflate(LayoutInflater.from(parent.context), parent, false)
    }

    override fun convert(holder: BindingViewHolder<ItemPlayerBuildingBinding>, item: MgsPlayerBuildingInfo, position: Int) {
        Glide.with(holder.binding.ivBuilding)
            .load(item.banner)
            .placeholder(R.drawable.icon_placeholder_img_default)
            .transform(CenterCrop(), RoundedCorners(13.dp))
            .into(holder.binding.ivBuilding)
    }
}