package com.socialplay.gpark.ui.im.groupchat

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.code
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.exception
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.model.groupchat.GroupChatAddMembers
import com.socialplay.gpark.data.model.groupchat.GroupChatInfo
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatApplyJoinRequest
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatListRequest
import com.socialplay.gpark.data.model.user.Gender
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.util.getStringByGlobal
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import org.koin.android.ext.android.get

data class GroupChatListModelState(
    val refreshFlag: Int = 0,
    val gender: Gender = Gender.Other,
    val groups: Async<List<GroupChatInfo>> = Uninitialized,
    val rollId: String? = null,
    val loadMore: Async<LoadMoreState> = Uninitialized,
    val applyJoinResult: Async<Pair<GroupChatInfo, DataResult<Boolean>>> = Uninitialized,
    val joinResult: Async<Pair<GroupChatInfo, DataResult<GroupChatAddMembers>>> = Uninitialized
) : MavericksState

data class GroupChatListViewModel(
    val initialState: GroupChatListModelState,
    val metaRepository: IMetaRepository,
) : BaseViewModel<GroupChatListModelState>(initialState) {
    fun getUserInfo(targetUid: String) {
        viewModelScope.launch {
            metaRepository.queryUserProfile(targetUid).collect { dataResult ->
                if (dataResult.succeeded) {
                    val genderCode = dataResult.data?.gender ?: Gender.Other.value
                    setState {
                        copy(
                            gender = Gender.parse(genderCode) ?: Gender.Other
                        )
                    }
                }
            }
        }
    }

    fun getGroups(
        targetUid: String,
        refresh: Boolean,
        searchType: String = MgsGroupChatListRequest.SEARCH_TYPE_CREATE,
    ) = withState { state ->
        if (state.groups is Loading || state.loadMore is Loading) {
            return@withState
        }
        if (refresh) {
            setState {
                copy(
                    refreshFlag = state.refreshFlag + 1,
                    groups = Loading(),
                    rollId = null,
                )
            }
        } else {
            setState {
                copy(loadMore = Loading())
            }
        }
        val dataList = state.groups.invoke() ?: emptyList()
        val rollId = if (refresh || dataList.isEmpty()) {
            null
        } else {
            state.rollId
        }
        viewModelScope.launch {
            val dataResult = metaRepository.getGroupChatInfoList(
                MgsGroupChatListRequest(
                    targetUid = targetUid,
                    searchType = searchType,
                    rollId = rollId,
                    checkJoin = true,
                    size = 20
                )
            )
            if (dataResult.succeeded && dataResult.data != null) {
                val groupsPage = dataResult.data!!
                val groups = groupsPage.chatGroups ?: emptyList()
                setState {
                    copy(
                        groups = Success(
                            if (refresh || dataList.isEmpty()) {
                                groups
                            } else {
                                dataList + groups
                            }
                        ),
                        rollId = groupsPage.rollId,
                        loadMore = Success(LoadMoreState(isEnd = groupsPage.rollId == null || groups.isEmpty()))
                    )
                }
            } else {
                if (refresh) {
                    setState {
                        copy(
                            groups = Fail(
                                dataResult.exception ?: Exception("unknown exception")
                            )
                        )
                    }
                } else {
                    setState {
                        copy(
                            loadMore = Fail(
                                dataResult.exception ?: Exception("unknown exception")
                            )
                        )
                    }
                }
            }
        }
    }

    fun updateGroupInfo(groupInfo: GroupChatInfo) {
        val groups = oldState.groups.invoke() ?: emptyList()
        val targetIndex = groups.indexOfFirst { groupInfo.id == it.id }.takeIf { it != -1 }
        if (targetIndex == null) {
            return
        }
        val newGroups = groups.toMutableList()
        newGroups[targetIndex] = groupInfo
        setState {
            copy(
                groups = Success(newGroups)
            )
        }
    }

    fun applyJoinGroup(groupChatInfo: GroupChatInfo, reason: String) {
        if (oldState.applyJoinResult is Loading) {
            return
        }
        val groupId = groupChatInfo.id ?: return
        if ((groupChatInfo.joinStatus != GroupChatInfo.JOIN_STATUS_CAN_JOIN && groupChatInfo.joinStatus != GroupChatInfo.JOIN_STATUS_CAN_APPLY_JOIN)
            || groupChatInfo.requestJoinStatus == GroupChatInfo.REQUEST_JOIN_STATUS_LOADING
        ) {
            return
        }
        setState {
            copy(
                applyJoinResult = Loading()
            )
        }
        viewModelScope.launch {
            metaRepository.reviewPrivateMessageRisk(reason, null).map { result->
                if (result.succeeded && result.data?.checkPass() == true) {
                    metaRepository.applyJoinGroupChat(
                        MgsGroupChatApplyJoinRequest(
                            chatGroupId = groupId.toInt(),
                            reason = reason
                        )
                    )
                } else {
                    DataResult.Error(
                        result.code ?: 0,
                        getStringByGlobal(R.string.risk_review_not_pass)
                    )
                }
            }.execute { result->
                copy(
                    applyJoinResult = if (result is Success) {
                        Success(groupChatInfo to result.invoke())
                    } else if (result is Fail) {
                        Success(groupChatInfo to DataResult.Error(0, ""))
                    } else {
                        Loading()
                    }
                )
            }
        }
    }

    fun joinGroupChat(groupChatInfo: GroupChatInfo) {
        if (oldState.joinResult is Loading) {
            return
        }
        val groupId = groupChatInfo.id ?: return
        if (groupChatInfo.joinStatus != GroupChatInfo.JOIN_STATUS_CAN_JOIN
            || groupChatInfo.requestJoinStatus == GroupChatInfo.REQUEST_JOIN_STATUS_LOADING
        ) {
            return
        }
        setState {
            copy(
                joinResult = Loading()
            )
        }
        viewModelScope.launch {
            val dataResult = metaRepository.joinGroupChat(groupId)
            setState {
                copy(
                    joinResult = Success(groupChatInfo to dataResult)
                )
            }
        }
    }

    companion object :
        KoinViewModelFactory<GroupChatListViewModel, GroupChatListModelState>() {
        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: GroupChatListModelState
        ): GroupChatListViewModel {
            return GroupChatListViewModel(state, get())
        }
    }
}