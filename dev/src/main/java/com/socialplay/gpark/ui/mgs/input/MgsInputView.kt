package com.socialplay.gpark.ui.mgs.input

import android.Manifest
import android.app.Activity
import android.app.Application
import android.app.Dialog
import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.AttributeSet
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.inputmethod.EditorInfo
import android.widget.EditText
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.MetaMgsInputDialogBinding
import com.socialplay.gpark.databinding.ViewMgsInputBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.mgs.MgsDialogManager
import com.socialplay.gpark.ui.dialog.GameCreateDialogHelper
import com.socialplay.gpark.ui.mgs.dialog.MgsInputDialog
import com.socialplay.gpark.ui.mgs.listener.OnMgsFloatInputListener
import com.socialplay.gpark.ui.permission.PermissionRequest
import com.socialplay.gpark.ui.view.voice.WaveVoiceView
import com.socialplay.gpark.util.InputUtil
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.invisible
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible
import kotlinx.coroutines.*

/**
 * Created by bo.li
 * Date: 2022/1/26
 * Desc: 聊天输入框
 */
class MgsInputView: RelativeLayout {

    lateinit var binding: ViewMgsInputBinding

    private var listener: OnMgsFloatInputListener? = null

    // message地方没有语音view
    private var hasVoiceView: Boolean = true
    private var location: String = "msg_button"

    constructor(context: Context, location: String) : super(context) {
        this.location = location
        initView(null)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        initView(attrs)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        initView(attrs)
    }

    fun setInputListener(listener: OnMgsFloatInputListener) {
        this.listener = listener
    }

    fun initHint(hint: String, color: Int) {
        binding.etInput.hint = hint
        binding.etInput.setHintTextColor(color)
    }

    /**
     * 展示输入框（加入房间）
     * 隐藏输入框（离开房间）
     */
    fun setInputViewVisible(visible: Boolean) {
        binding.root.isVisible = visible
    }

    private fun initView(attrs: AttributeSet?) {
        if (attrs != null) {
            val typedArray = context.obtainStyledAttributes(attrs, R.styleable.MgsInputView)
            if (typedArray.hasValue(R.styleable.MgsInputView_hasVoice)) {
                hasVoiceView = typedArray.getBoolean(R.styleable.MgsInputView_hasVoice, true)
            }
            typedArray.recycle()
        }
        binding = ViewMgsInputBinding.inflate(LayoutInflater.from(context), this, true)
        binding.etInput.setOnAntiViolenceClickListener {
            listener?.getCurrentActivity()?.let { it1 -> showInputDialog(context as Application, it1) }
        }
        initVoiceView()
    }
    private fun initSwitch(){
        binding.clVoice.visible(listener?.canShowInputAudio()?:false)
        changeVoiceStatus(listener?.isAllMute()?:false)
        binding.mscSwitch.setOnClickListener {
            listener?.muteAllRemoteAudioStreams((!(listener?.isAllMute()?:false)))
        }
    }

    fun changeVoiceStatus(mute:Boolean) {
        binding.mscSwitch.setImageResource(if (mute) R.drawable.icon_all_close else R.drawable.icon_all_open)
    }
    fun updateSelfAudioState(open: Boolean){
        binding.voiceView.invisible(!open)
        binding.voiceShield.visible(!open)
    }
    private fun initVoiceView() {
        if (!hasVoiceView) {
            changeVoiceVisible(false, false)
            return
        }
        initSwitch()
        binding.voiceView.setVolumeListener(object : WaveVoiceView.IClickVolumeListener {
            override fun onMute() {
                handleClickAudio(false)
            }

            override fun onUnmute() {
                handleClickAudio(true)
            }
        })
        binding.voiceShield.setOnAntiViolenceClickListener {
            ToastUtil.gameShowShort(context.getString(R.string.voice_shield))
        }
    }

    fun handleClickAudio(isOpen: Boolean) {
        val event = if (isOpen) EventConstants.MGS_VOICE_OPEN_CLICK else EventConstants.MGS_VOICE_CLOSE_CLICK
        Analytics.track(event) {
            listener?.getGameAnalytics()?.let { putAll(it) }
            put("type", "mgs")
        }
        if (!PermissionRequest.checkSelfPermission(context, Manifest.permission.RECORD_AUDIO)) {
            listener?.checkMicPermission()
            return
        }
        listener?.changeVoiceMuteState(isOpen)
    }

    /**
     * 改变语音按钮状态
     */
    fun changeVoiceState(isOpen: Boolean, lastVolume: Int = 0, nowVolume: Int = 0) {
        if (!hasVoiceView) return
        if (listener?.canShowInputAudio() == true) {
            binding.voiceView.updateVoiceState(isOpen, lastVolume, nowVolume)
        }
    }

    /**
     * 改变语音按钮是否可见
     */
    fun changeVoiceVisible(isShow: Boolean, isOpenMic: Boolean) {
        binding.clVoice.visible(isShow)
        changeVoiceState(isOpenMic)
    }

    private fun showInputDialog(metaApp: Application, activity: Activity) {
        MgsDialogManager.showInputDialog(
            metaApp,
            activity,
            metaApp.getString(R.string.meta_mgs_send_message),
            "",
            150,
            object : MgsInputDialog.OnInputDialogListener {
                override fun onEditeChange(text: String?) {
                }

                override fun onSubmit(text: String?) {
                    listener?.getGameAnalytics()?.let {
                        Analytics.track(EventConstants.EVENT_CLICK_MGS_MESSAGE_INPUT) {
                            putAll(it)
                            put("location", location)
                        }
                    }
                    text?.let { sendMessage(it) }
                }

                override fun cancel(text: String?) {

                }

            }
        )
    }



    /**
     * 发送消息
     */
    private fun sendMessage(etMessage: String): Boolean {
        listener?.getGameAnalytics()?.let {
            Analytics.track(EventConstants.EVENT_CLICK_MGS_MESSAGE_SEND) {
                putAll(it)
                put("location", "msg_button")
            }
        }
        return if (etMessage.isBlank()) {
            false
        } else {
            listener?.sendMessage(etMessage)
            true
        }
    }

    fun isShow(): Boolean {
        return MgsDialogManager.isShowInputDialog()
    }

}