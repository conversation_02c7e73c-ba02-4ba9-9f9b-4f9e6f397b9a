package com.socialplay.gpark.ui.qrcode

import android.content.Context
import android.graphics.BitmapFactory
import androidx.camera.core.Camera
import androidx.camera.core.Preview
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.model.qrcode.DecodeResult
import com.socialplay.gpark.data.model.qrcode.Source
import com.socialplay.gpark.util.extension.LifecycleCallback
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.concurrent.Executors


class QRCodeScanViewModel : ViewModel() {

    private val cameraExecutor by lazy { Executors.newSingleThreadExecutor() }

    val qrCodeScanResultCallback: LifecycleCallback<(DataResult<DecodeResult>) -> Unit> = LifecycleCallback()


    fun previewAndDecode(context: Context, viewLifecycleOwner: LifecycleOwner,
                         surfaceProvider: Preview.SurfaceProvider,
                         viewportProvider: ViewportProvider,
                         cameraAvailableCallback: (Camera) -> Unit
    ) {
        QRCodeScanner.previewAndDecode(context, viewLifecycleOwner, surfaceProvider, viewportProvider, cameraAvailableCallback, cameraExecutor) {
            qrCodeScanResultCallback.dispatchOnMainThread { invoke(DataResult.Success(DecodeResult(it, Source.Camera))) }
        }
    }

    fun decodeFromLocalPath(context: Context, qrCodeImgPath: String) = viewModelScope.launch {
        withContext(Dispatchers.IO) {
            val bmp = kotlin.runCatching { BitmapFactory.decodeFile(qrCodeImgPath) }.getOrNull()

            if (bmp != null) {
                try {
                    val decodeResult = QRCodeScanner.decodeCodeResult(bmp)
                    if (decodeResult != null) {
                        qrCodeScanResultCallback.dispatchOnMainThread { invoke(DataResult.Success(DecodeResult(decodeResult, Source.Library))) }
                    } else {
                        qrCodeScanResultCallback.dispatchOnMainThread { invoke(DataResult.Error(-1, context.getString(R.string.qr_code_not_found))) }
                    }
                } finally {
                    bmp.recycle()
                }
            } else {
                Timber.w("Failed to read bitmap from path %s", qrCodeImgPath)
                qrCodeScanResultCallback.dispatchOnMainThread { invoke(DataResult.Error(-1, context.getString(R.string.qr_code_not_found))) }
            }
        }
    }
}