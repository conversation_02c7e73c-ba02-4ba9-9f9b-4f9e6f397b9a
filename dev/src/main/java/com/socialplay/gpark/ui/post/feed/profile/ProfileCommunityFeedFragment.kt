package com.socialplay.gpark.ui.post.feed.profile

import android.os.Bundle
import android.os.Parcelable
import android.view.View
import com.airbnb.mvrx.args
import com.airbnb.mvrx.asMavericksArgs
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.parentFragmentViewModel
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.post.CommunityFeedInfo
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.analytics.EventParamConstants.COMMUNITY_POST_TYPE
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.editor.EditorGameInteractHelper
import com.socialplay.gpark.function.editor.RoleGameTryOn
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.editor.tab.FullScreenEditorActivityArgs
import com.socialplay.gpark.ui.main.MainViewModel
import com.socialplay.gpark.ui.post.feed.base.BaseCommunityFeedViewModel
import com.socialplay.gpark.ui.post.feed.CommunityFeedFragment
import com.socialplay.gpark.ui.post.feed.base.ICommunityFeedModelState
import com.socialplay.gpark.ui.profile.BaseProfilePage
import com.socialplay.gpark.ui.profile.home.ProfileViewModel
import com.socialplay.gpark.ui.view.LoadingView
import com.socialplay.gpark.ui.view.video.FeedVideoHelper
import com.socialplay.gpark.util.extension.toast
import kotlinx.parcelize.Parcelize
import org.koin.androidx.viewmodel.ext.android.sharedViewModel
import org.koin.androidx.viewmodel.ext.android.viewModel

/**
 * Created by bo.li
 * Date: 2023/9/15
 * Desc:
 */
@Parcelize
data class ProfileCommunityFeedFragmentArgs(val otherUid: String, val isMe: Boolean) : Parcelable

class ProfileCommunityFeedFragment : CommunityFeedFragment() {

    private val args by args<ProfileCommunityFeedFragmentArgs>()
    private val profileViewModel: ProfileViewModel by parentFragmentViewModel()
    private val viewModel: ProfileCommunityFeedViewModel by fragmentViewModel()
    private val mainViewModel: MainViewModel by sharedViewModel()

    private var otherUid = ""
    private var force = false

    companion object {
        fun newInstance(args: ProfileCommunityFeedFragmentArgs): ProfileCommunityFeedFragment {
            return ProfileCommunityFeedFragment().apply {
                arguments = args.asMavericksArgs()
            }
        }
    }

    override val feedViewModel: BaseCommunityFeedViewModel<ICommunityFeedModelState>
        get() = viewModel as BaseCommunityFeedViewModel<ICommunityFeedModelState>

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val newUuid = profileViewModel.oldState.uuid
        val needRefresh = otherUid.isNotEmpty() && otherUid != newUuid
        otherUid = newUuid
        setDetailFragmentResultListener()
        binding.rvFeed.skipIntercept = true
        if (needRefresh) {
            refreshPage()
        }
        if (args.isMe) {
            viewModel.accountInteractor.accountLiveData.observe(viewLifecycleOwner) {
                val uuid = it?.uuid ?: return@observe
                if (otherUid != uuid) {
                    otherUid = uuid
                    force = true
                    refreshPage()
                }
            }
        }
    }

    override fun refreshPage() {
        val force = force
        this.force = false
        viewModel.refresh(otherUid, force)
    }

    override fun loadMoreFeed() {
        viewModel.loadMore(otherUid)
    }

    override fun goPost(item: CommunityFeedInfo) {
        Analytics.track(EventConstants.EVENT_COMMUNITY_POST_CLICK) {
            put(EventParamConstants.KEY_POSTID, item.postId)
            put(
                COMMUNITY_POST_TYPE,
                if (item.top) EventParamConstants.ANALYTIC_COMMUNITY_TYPE_PIN else EventParamConstants.ANALYTIC_COMMUNITY_TYPE_NORMAL
            )
            item.tagList?.map { it.tagId }?.let { put(EventParamConstants.KEY_TAG_LIST, it) }
        }
        val videoProgress = FeedVideoHelper.getVideoProgressByResId(item.localUniqueId)
        MetaRouter.Post.goPostDetail(this, item.postId, "profile", videoProgress = videoProgress)
    }

    override fun initLoadingView(loading: LoadingView) {
        binding.loadingFeed.setVerticalBias(0.08f)
    }

    override fun goTopic(tag: PostTag, postId: String) {
        if (tag.tagId <= 0) {
            toast(R.string.tag_reviewing_toast)
            return
        }
        topDetail(tag, postId)
    }

    private fun topDetail(tag: PostTag, postId: String?) {
        MetaRouter.Post.topicDetail(this, tag, EventParamConstants.SOURCE_TOPIC_PROFILE, postId)
    }

    override fun goProfile(uuid: String) {
        // 已经在个人主页了
    }

    override val showUserStatus: Boolean = false

    override val likeLocation: String = EventParamConstants.LOCATION_LIKE_PROFILE

    override val showPin: Boolean = false

    override val showTagList: Boolean = false
    override fun getEmptyTip(): String {
        return getString(R.string.user_post_empty, profileViewModel.nickname)
    }

    override fun getFeedTag(): String? = null

    override fun refreshParent() {

    }

    override val useVideoFunc: Boolean = true

    override val needInterceptParent: Boolean = true

    override val tabSource: Int = TAB_SOURCE_PROFILE

    override val firstFeedTop get() = 0
}