package com.socialplay.gpark.ui.kol.creator.type

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.view.isInvisible
import com.airbnb.mvrx.fragmentViewModel
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.CommonTabItem
import com.socialplay.gpark.databinding.FragmentTypeKolCreatorParentBinding
import com.socialplay.gpark.databinding.TabIndicatorSp16Binding
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.view.viewpager.CommonTabStateAdapter
import com.socialplay.gpark.ui.view.viewpager.adapterAllowStateLoss
import com.socialplay.gpark.util.extension.addOnTabSelectedListener
import com.socialplay.gpark.util.extension.attach
import com.socialplay.gpark.util.extension.navigateUp

/**
 * Created by bo.li
 * Date: 2024/8/9
 * Desc: kol 更多创作者 tab页
 */
class TypeKoCreatorParentFragment :
    BaseFragment<FragmentTypeKolCreatorParentBinding>(R.layout.fragment_type_kol_creator_parent) {

    private val viewModel: TypeKolCreatorParentViewModel by fragmentViewModel()

    private var tabLayoutMediator: TabLayoutMediator? = null

    private val tabCallback = object : TabLayout.OnTabSelectedListener {
        override fun onTabSelected(tab: TabLayout.Tab) {
            setTabSelectUI(tab, true)
        }

        override fun onTabUnselected(tab: TabLayout.Tab) {
            setTabSelectUI(tab, false)
        }

        override fun onTabReselected(tab: TabLayout.Tab) {}
    }

    private fun setTabSelectUI(tab: TabLayout.Tab, select: Boolean) {
        tab.customView?.findViewById<TextView>(R.id.tv_selected)?.isInvisible = !select
        tab.customView?.findViewById<TextView>(R.id.tv_normal)?.isInvisible = select
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentTypeKolCreatorParentBinding? {
        return FragmentTypeKolCreatorParentBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.title.setOnBackAntiViolenceClickedListener { navigateUp() }
        viewModel.onEach(
            TypeKolCreatorParentModelState::tabItems,
        ) { tabItems ->
            initTab(tabItems)
        }
    }

    private fun initTab(tabs: List<CommonTabItem>) {
        binding.tlMoreCreator.addOnTabSelectedListener(viewLifecycleOwner, tabCallback)
        val titles = tabs.map {
            getString(it.title)
        }
        binding.vpMoreCreator.adapterAllowStateLoss = CommonTabStateAdapter(
            tabs.map { it.fragmentInvoke },
            childFragmentManager,
            viewLifecycleOwner.lifecycle
        )
        tabLayoutMediator = TabLayoutMediator(
            binding.tlMoreCreator,
            binding.vpMoreCreator
        ) { tab: TabLayout.Tab, position: Int ->
            val tabBinding = TabIndicatorSp16Binding.inflate(layoutInflater)
            tabBinding.tvNormal.text = titles[position]
            tabBinding.tvSelected.text = titles[position]

            tab.customView = tabBinding.root
        }
        tabLayoutMediator?.attach(viewLifecycleOwner)
    }

    override fun invalidate() {

    }

    override fun getPageName(): String =
        PageNameConstants.FRAGMENT_NAME_KOL_MORE_CREATOR_TYPE_PARENT
}