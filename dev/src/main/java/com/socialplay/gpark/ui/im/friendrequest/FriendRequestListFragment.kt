package com.socialplay.gpark.ui.im.friendrequest

import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.ConcatAdapter
import androidx.recyclerview.widget.LinearLayoutManager
import com.bumptech.glide.Glide
import com.socialplay.gpark.R
import com.meta.box.biz.friend.model.FriendRequestInfo
import com.socialplay.gpark.databinding.FragmentFriendRequestListBinding
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.ui.base.adapter.withStatusAndRefresh
import com.socialplay.gpark.ui.home.adapter.HomeLoadMoreFooterAdapter
import com.socialplay.gpark.ui.view.UserLabelView
import com.socialplay.gpark.util.NetUtil
import com.socialplay.gpark.util.extension.tagIds
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.ifNullOrEmpty
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.flow.collectLatest
import org.koin.androidx.viewmodel.ext.android.viewModel

/**
 * Created by bo.li
 * Date: 2021/11/16
 * Desc: 好友申请列表页面
 */
class FriendRequestListFragment : BaseFragment<FragmentFriendRequestListBinding>() {

    private val viewModel: FriendRequestListViewModel by viewModel()
    private val listAdapter = FriendRequestsAdapter(::glide) {
        UserLabelView.showDescDialog(this, it)
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentFriendRequestListBinding? {
        return FragmentFriendRequestListBinding.inflate(inflater, container, false)
    }

    override fun init() {
        initView()
        initData()
    }

    override fun loadFirstData() {
        viewModel.markAllFriendRequestsAsRead()
    }

    private fun initView() {
        binding.titleBar.setOnBackClickedListener { navigateUp() }
        binding.recyclerView.layoutManager = LinearLayoutManager(requireContext())
        initAdapter()
    }

    private fun initAdapter() {
        listAdapter.apply {
            withStatusAndRefresh(viewLifecycleOwner, binding.lv, binding.clEmptyLayout, binding.slRefreshLayout)

            val loadMoreAdapter = HomeLoadMoreFooterAdapter {
                retry()
            }
            addLoadStateListener { loadStates ->
                loadMoreAdapter.loadState = loadStates.append
            }
            addChildClickViewIds(R.id.ivDisAgree, R.id.tvAgree, R.id.ivAvatar, R.id.tvUserName)
            setOnItemChildClickListener { view, position ->
                if (!NetUtil.isNetworkAvailable()) {
                    toast(R.string.net_unavailable)
                    return@setOnItemChildClickListener
                }
                val item = peek(position)?.info
                if (item == null || item.uuid.isNullOrEmpty()) {
                    toast(R.string.common_error)
                    return@setOnItemChildClickListener
                }
                when (view.id) {
                    R.id.ivDisAgree -> {
                        viewModel.disAgreeFriendRequest(item, position)
                    }
                    R.id.tvAgree    -> {
                        viewModel.agreeFriendRequest(item, position)
                    }
                    R.id.ivAvatar, R.id.tvUserName   -> {
                        MetaRouter.Profile.other(
                            this@FriendRequestListFragment,
                            item.uuid,
                            "12"
                        )
                    }
                }
            }
            val concatAdapter = ConcatAdapter(listAdapter, loadMoreAdapter)
            binding.recyclerView.adapter = concatAdapter
        }
    }

    private fun initData() {
        viewLifecycleOwner.lifecycleScope.launchWhenCreated {
            viewModel.friendRequests.collectLatest {
                listAdapter.submitData(it)
            }
        }

        viewModel.requestStateCallback.observe(viewLifecycleOwner) {
            binding.lv.visible(false)
            when (it) {
                FriendRequestListViewModel.RequestState.Start    -> {
                    binding.lv.visible()
                }
                FriendRequestListViewModel.RequestState.Failed   -> {
                    toast(it.msg.ifNullOrEmpty { getString(R.string.failed_to_process_request) })
                }
                FriendRequestListViewModel.RequestState.Disagree -> {
                    it.item?.apply {
                        dealRequest(this, it.position, 2)
                    }
                }
                FriendRequestListViewModel.RequestState.Agree    -> {
                    it.item?.apply {
                        dealRequest(this, it.position, 1)
                    }
                }
            }
        }
    }

    private fun dealRequest(item: FriendRequestInfo, position: Int, status: Int) {
        val friendRequestInfo = kotlin.runCatching { listAdapter.peek(position)?.info }.getOrNull() ?: return
        if (friendRequestInfo.uuid != item.uuid) return
        friendRequestInfo.status = status
        listAdapter.notifyItemChanged(position)
        if (status == 1) {
            MetaRouter.IM.gotoConversation(
                fragment = this,
                otherUid = item.uuid,
                title = item.name,
                tagIds = item.tagIds,
                labelInfo = item.labelInfo
            )
        }
    }


    override fun onDestroyView() {
        binding.recyclerView.adapter = null
        super.onDestroyView()
    }

    override fun getFragmentName(): String = PageNameConstants.FRAGMENT_NAME_FRIEND_REQUEST_LIST
}