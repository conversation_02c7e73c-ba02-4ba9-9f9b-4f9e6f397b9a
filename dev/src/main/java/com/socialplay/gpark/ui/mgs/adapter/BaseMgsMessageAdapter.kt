package com.socialplay.gpark.ui.mgs.adapter

import android.content.Context
import android.text.Spannable
import android.text.SpannableStringBuilder
import androidx.core.content.ContextCompat
import androidx.viewbinding.ViewBinding
import com.meta.biz.mgs.data.model.MGSMessage
import com.meta.biz.mgs.data.model.MGSMessageExtra
import com.socialplay.gpark.R
import com.socialplay.gpark.ui.base.adapter.BaseAdapter
import com.socialplay.gpark.util.SpannableHelper
import timber.log.Timber

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2021/08/05
 *     desc   : 文本消息
 *
 */
abstract class BaseMgsMessageAdapter<Binding : ViewBinding>(private val isShowUser: Boolean = true) :
    BaseAdapter<MGSMessage, Binding>() {


    fun getContent(context: Context, data: MGSMessage): Spannable {
        kotlin.runCatching {
            return when (data.mgsMessageExtra?.type) {
                MGSMessageExtra.TYPE_TEXT_MESSAGE                               -> {
                    //正常文字聊天
                    val build: SpannableHelper.Builder = SpannableHelper.Builder()
                    if (isShowUser) {
                        build.text(data.mgsMessageExtra?.imUser?.name + ": ")
                            .color(ContextCompat.getColor(context, R.color.colorAccentPrimary))
                    }
                    build.text(data.content).color(ContextCompat.getColor(context, R.color.white))
                        .build()
                }
                MGSMessageExtra.TYPE_JOIN_ROOM, MGSMessageExtra.TYPE_LEAVE_ROOM -> {
                    Timber.d("mgs_message_joinorleave data.content= %s", data.content)
                    //加入房间,退出房间
                    val build = SpannableHelper.Builder()
                    var contentColor = R.color.color_mgs_chat_system_content
                    if (isShowUser) {
                        build.text(data.mgsMessageExtra?.imUser?.name + ": ")
                            .color(ContextCompat.getColor(context, R.color.colorAccentPrimary))
                        contentColor = R.color.white
                    }
                    build.text(data.content).color(ContextCompat.getColor(context, contentColor))
                        .build()
                }
                MGSMessageExtra.TYPE_INFORMATION -> {
                    //小灰条消息
                    val build: SpannableHelper.Builder = SpannableHelper.Builder()
                    if (isShowUser) {
                        build.text(context.getString(R.string.app_name) + ": ")
                            .color(ContextCompat.getColor(context, R.color.colorAccentPrimary))
                    }
                    build.text(data.content).color(ContextCompat.getColor(context, R.color.white))
                        .build()
                }
                else -> {
                    SpannableHelper.Builder()
                        .text(context.getString(R.string.not_support_cap))
                        .build()
                }
            }
        }.getOrElse {
            Timber.d("mgs_message_error %s", it.message)
        }
        return SpannableStringBuilder()
    }

    fun checkSystemMessage(data: MGSMessage): Boolean {
        val type = data.mgsMessageExtra?.type
        return type == MGSMessageExtra.TYPE_INFORMATION || type == MGSMessageExtra.TYPE_JOIN_ROOM || type == MGSMessageExtra.TYPE_LEAVE_ROOM
    }

    fun checkInformationMessage(data: MGSMessage): Boolean {
        val type = data.mgsMessageExtra?.type
        return type == MGSMessageExtra.TYPE_INFORMATION
    }

}