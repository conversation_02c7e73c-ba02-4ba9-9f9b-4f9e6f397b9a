package com.socialplay.gpark.ui.im.setting

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ly123.tes.mgs.metacloud.model.Conversation
import com.meta.box.biz.friend.FriendBiz
import com.meta.box.biz.friend.model.FriendInfo
import com.meta.lib.api.resolve.data.model.data
import com.meta.lib.api.resolve.data.model.succeeded
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.errMsg
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.FriendInteractor
import com.socialplay.gpark.data.interactor.ImInteractor
import com.socialplay.gpark.data.model.im.ImUpdate
import com.socialplay.gpark.util.extension.LifecycleCallback
import kotlinx.coroutines.launch


/**
 *  <AUTHOR> taiyuan
 *  time   : 2021/6/28
 *  desc   :
 */
class ChatSettingViewModel(
    private val iMetaRepository: IMetaRepository,
    private val imInteractor: ImInteractor,
    private val friendInteractor: FriendInteractor,
) : ViewModel() {
    private val _chatSettingLiveData = MutableLiveData<ChatSetState>()
    val chatSettingLiveData: LiveData<ChatSetState> = _chatSettingLiveData
    val messageToTopCallback: LifecycleCallback<(Boolean) -> Unit> = LifecycleCallback()
    private val _imUpdateLiveData = MutableLiveData<ImUpdate>()
    val imUpdateLiveData: LiveData<ImUpdate> = _imUpdateLiveData

    /**
     * 获取用户信息
     */
    fun getUserInfo(uuid: String?) = viewModelScope.launch {
        if (uuid.isNullOrBlank()) return@launch
        dispathcChatSetCallback(ChatSetState.Start)
        val result = iMetaRepository.queryFriendInfo(uuid)
        val state = if (result.succeeded && result.data != null && !result.data?.uuid.isNullOrEmpty()) {
            ChatSetState.GetUserInfoSuccess.friendInfo = result.data
            ChatSetState.GetUserInfoSuccess
        } else {
            ChatSetState.GetUserInfoFailed.msg = result.toString()
            ChatSetState.GetUserInfoFailed
        }

        dispathcChatSetCallback(state)
    }

    fun getConversationTopState(uuid: String) = viewModelScope.launch {
        imInteractor.getConversationTop(Conversation.ConversationType.PRIVATE, uuid) {
            messageToTopCallback.dispatchOnMainThread {
                invoke(it)
            }
        }
    }

    /**
     * 消息置顶
     */
    fun conersationToTop(uuid: String, isTop: Boolean) = viewModelScope.launch {
        imInteractor.setConversationToTop(Conversation.ConversationType.PRIVATE, uuid, isTop) {
            _imUpdateLiveData.postValue(it)
        }
    }

    /**
     * 清空聊天记录
     */
    fun clearMessages(uuid: String) = viewModelScope.launch {
        imInteractor.deleteMessages(Conversation.ConversationType.PRIVATE, uuid) {
            _imUpdateLiveData.postValue(it)
        }
    }

    /**
     * 删除好友
     */
    fun removeFriend(uuid: String) = viewModelScope.launch {
        dispathcChatSetCallback(ChatSetState.Start)
        val result = FriendBiz.deleteFriend(uuid)

        val state = if (result.succeeded && result.data == true) {
            friendInteractor.refreshFriendsAsync()
            recoverChatSetting(uuid)
            ChatSetState.DeleteFriendSuccess
        } else {
            ChatSetState.Failed.msg = result.errMsg()
            ChatSetState.Failed
        }
        dispathcChatSetCallback(state)
    }

    /**
     * 恢复聊天设置
     */
    private suspend fun recoverChatSetting(uuid: String) {
        imInteractor.deleteFriend(uuid, Conversation.ConversationType.PRIVATE)
    }

    private fun dispathcChatSetCallback(state: ChatSetState) {
        _chatSettingLiveData.value = state
    }

    enum class ChatSetState(var msg: String? = "", var friendInfo: FriendInfo? = null) {
        Start, Failed, DeleteFriendSuccess, GetUserInfoSuccess, GetUserInfoFailed
    }
}