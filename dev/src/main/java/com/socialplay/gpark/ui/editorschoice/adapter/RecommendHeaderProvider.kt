package com.socialplay.gpark.ui.editorschoice.adapter

import com.chad.library.adapter.base.provider.BaseItemProvider
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.choice.ChoiceCardInfo
import com.socialplay.gpark.data.model.choice.ChoiceCardType

/**
 *
 * <AUTHOR>
 * @date 2021/07/05
 */
class RecommendHeaderProvider: BaseItemProvider<ChoiceCardInfo>() {

    override val itemViewType: Int = ChoiceCardType.RECOMMEND_HEADER

    override val layoutId = R.layout.header_recommend

    override fun convert(helper: BaseViewHolder, card: ChoiceCardInfo) {}
}