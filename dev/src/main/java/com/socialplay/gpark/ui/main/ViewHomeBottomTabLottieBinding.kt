package com.socialplay.gpark.ui.main

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import com.airbnb.lottie.LottieAnimationView
import com.socialplay.gpark.R
import com.socialplay.gpark.ui.view.MetaTextView

/**
 * 支持Lottie动画的底部Tab布局绑定类
 */
class ViewHomeBottomTabLottieBinding private constructor(
    val root: ConstraintLayout,
    val ivIcon: ImageView,
    val lavIcon: LottieAnimationView,
    val tvTitle: MetaTextView,
    val viewUnReadCount: View
) {
    
    companion object {
        fun inflate(inflater: LayoutInflater): ViewHomeBottomTabLottieBinding {
            val root = inflater.inflate(R.layout.view_home_bottom_tab_lottie, null, false) as ConstraintLayout
            return bind(root)
        }
        
        fun bind(root: View): ViewHomeBottomTabLottieBinding {
            val ivIcon = root.findViewById<ImageView>(R.id.iv_icon)
            val lavIcon = root.findViewById<LottieAnimationView>(R.id.lav_icon)
            val tvTitle = root.findViewById<MetaTextView>(R.id.tv_title)
            val viewUnReadCount = root.findViewById<View>(R.id.viewUnReadCount)
            
            return ViewHomeBottomTabLottieBinding(
                root as ConstraintLayout,
                ivIcon,
                lavIcon,
                tvTitle,
                viewUnReadCount
            )
        }
    }
} 