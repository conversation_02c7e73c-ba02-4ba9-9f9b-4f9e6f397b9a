/*
 * Copyright 2019 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.socialplay.gpark.ui.view.video;

import android.content.Context;
import android.content.ContextWrapper;
import android.util.AttributeSet;

import com.google.android.exoplayer2.ui.StyledPlayerControlView;

import androidx.annotation.Nullable;
import timber.log.Timber;

public class MetaStyledPlayerControlView extends StyledPlayerControlView {

    public MetaStyledPlayerControlView(Context context) {
        this(context, null);
    }

    public MetaStyledPlayerControlView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public MetaStyledPlayerControlView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, null);
    }

    public MetaStyledPlayerControlView(Context context, @Nullable AttributeSet attrs, int defStyleAttr, @Nullable AttributeSet playbackAttrs) {
        super(new RestrictContext(context), attrs, defStyleAttr, attrs);
    }

    private static class RestrictContext extends ContextWrapper {
        public RestrictContext(Context base) {
            super(base);
        }

        @Override
        public boolean isRestricted() {
            Timber.tag("RestrictContext").d("Calling isRestricted");
            //阻止加载字体，但是可能会潜在的导致其他问题。
            return true;
        }
    }
}
