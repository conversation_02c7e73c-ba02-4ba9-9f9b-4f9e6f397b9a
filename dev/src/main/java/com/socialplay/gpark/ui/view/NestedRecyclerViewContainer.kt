package com.socialplay.gpark.ui.view

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.widget.FrameLayout
import androidx.recyclerview.widget.RecyclerView
import kotlin.math.abs

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/07/07
 *     desc   : https://blog.csdn.net/qq_43679375/article/details/126270646
 * </pre>
 */
class NestedRecyclerViewContainer @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0,
) : FrameLayout(context, attrs, defStyleAttr) {

    private var offsetX = 0f
    private var offsetY = 0f

    private var lastPosX = 0f
    private var lastPosY = 0f

    private val interval = 0

    private var orientation = RecyclerView.VERTICAL

    fun setOrientation(orientation: Int) {
        this.orientation = orientation
    }

    override fun dispatchTouchEvent(ev: MotionEvent): Boolean {
        val result: Boolean
        when (ev.action) {
            MotionEvent.ACTION_DOWN -> {
                offsetX = 0f
                offsetY = 0f
                lastPosX = ev.x
                lastPosY = ev.y
                result = super.dispatchTouchEvent(ev)
            }
            else -> {
                val thisPosX = ev.x
                val thisPosY = ev.y
                offsetX += abs(thisPosX - lastPosX)
                offsetY += abs(thisPosY - lastPosY)
                lastPosX = thisPosX
                lastPosY = thisPosY
                result = if (offsetX < interval && offsetY < interval) {
                    super.dispatchTouchEvent(ev)
                } else if (offsetX <= offsetY && orientation == RecyclerView.VERTICAL) {
                    false
                } else if (offsetX > offsetY && orientation == RecyclerView.HORIZONTAL) {
                    false
                } else {
                    super.dispatchTouchEvent(ev)
                }
            }
        }
        return result
    }

}