package com.socialplay.gpark.ui.account.startup

import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.GridLayoutManager
import com.airbnb.mvrx.args
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.Glide
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.editor.DefaultRoleInfo
import com.socialplay.gpark.data.model.guide.GuideScene
import com.socialplay.gpark.databinding.FragmentStartupSelectBirthdayBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.editor.BaseJumpRoleFragmentV2
import com.socialplay.gpark.util.QuitAppUtil
import com.socialplay.gpark.util.extension.enableWithAlpha
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.invisible
import com.socialplay.gpark.util.extension.navigateUp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible
import kotlinx.parcelize.Parcelize


/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/10/20
 *     desc   :
 * </pre>
 */
@Parcelize
data class SelectBirthdayFragmentArgs(
    val avatar: DefaultRoleInfo,
    val nickname: String,
    val portraitRes: Int,
    val skipAvatar: Boolean,
    val gender: Int? = null, // 添加性别信息支持
    val age: Int? = null // 添加年龄信息支持
) : Parcelable

class SelectBirthdayFragment :
    BaseJumpRoleFragmentV2<FragmentStartupSelectBirthdayBinding>(R.layout.fragment_startup_select_birthday) {

    override var navColorRes = R.color.color_0E0922

    private val vm: SelectBirthdayViewModel by fragmentViewModel()
    private val args by args<SelectBirthdayFragmentArgs>()

    private val selectBirthdayController by lazy { buildSelectBirthdayController() }

    private val selectBirthDayListener = object : ISelectBirthdayListener {
        override fun pickAge(age: Int) {
            vm.updateAge(age)
        }
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentStartupSelectBirthdayBinding? {
        return FragmentStartupSelectBirthdayBinding.inflate(inflater, container, false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        statusBarViewModel.update(false)
        selectBirthdayController.onRestoreInstanceState(savedInstanceState)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        selectBirthdayController.onSaveInstanceState(outState)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        if (args.skipAvatar) {
            binding.ivAvatar.gone()
        } else {
            Glide.with(this)
                .load(args.avatar.portrait)
                .placeholder(
                    if (args.portraitRes != 0) {
                        args.portraitRes
                    } else {
                        R.drawable.icon_default_avatar
                    }
                )
                .centerCrop()
                .into(binding.ivAvatar)
        }

        binding.rv.layoutManager = GridLayoutManager(requireContext(), 4)
        binding.rv.setController(selectBirthdayController)

        binding.tbl.invisible(true)
        binding.tbl.setOnBackClickedListener {
            jumpBack()
        }
        binding.tvNextBtn.setOnAntiViolenceClickListener {
            binding.tvNextBtn.enableWithAlpha(false)
            binding.vCover.visible()
            
            // 判断下一步是否是输入昵称界面
            val isNextEnterName = PandoraToggle.enableNewbieGuideName
            
            if (isNextEnterName) {
                // 下一步是输入昵称界面，跳过网络请求，直接跳转
                jumpNext()
            } else {
                // 下一步是主页，正常进行网络请求
                vm.updateProfile(args.nickname, args.avatar, args.gender)
            }
        }

        vm.onEach(SelectBirthdayState::age, SelectBirthdayState::ageOptions) { age, ageOptions ->
            binding.tvNextBtn.enableWithAlpha(ageOptions.any { it.inAgeRange(age) })
        }
        vm.onAsync(
            SelectBirthdayState::chooseRoleResult,
            deliveryMode = uniqueOnly(),
            onFail = { _ ->
                vm.backupChooseRole(args.avatar)
            })
        vm.onEach(
            SelectBirthdayState::updateProfileResult,
            SelectBirthdayState::chooseRoleResult,
            deliveryMode = uniqueOnly()
        ) { updateProfileResult, chooseRoleResult ->
            if (chooseRoleResult.complete && updateProfileResult.complete) {
                binding.tvNextBtn.enableWithAlpha(true)
                binding.vCover.gone()
                if (updateProfileResult.invoke() == true) {
                    jumpNext()
                }
            }
        }
        vm.registerAsyncErrorToast(SelectBirthdayState::updateProfileResult)

        Analytics.track(EventConstants.NEWBIE_GUIDE_AGE_SHOW)
    }

    private fun jumpNext() {
        vm.onEach(SelectBirthdayState::age) { age ->
            MetaRouter.Startup.guideNext(
                this, 
                GuideScene.SELECT_BIRTHDAY, 
                args.avatar, 
                args.portraitRes, 
                args.skipAvatar, 
                args.nickname,
                age = age,
                gender = args.gender
            )
        }
    }

    private fun buildSelectBirthdayController() = simpleController(
        vm,
        SelectBirthdayState::age,
        SelectBirthdayState::ageOptions
    ) { age, ageOptions ->
        ageOptions.forEach {
            createSelectBirthdayItem(it, it.inAgeRange(age), selectBirthDayListener)
        }
    }

    override fun invalidate() {}

    override fun getPageName() = PageNameConstants.FRAGMENT_STARTUP_SELECT_BIRTHDAY

    override fun jumpBack() {
        navigateUp()
    }

    override fun handleClickBack() {
//        jumpBack()
        activity?.let {
            QuitAppUtil.checkClickBackPressed(it)
        }
    }
}