package com.socialplay.gpark.ui.kol.list

import android.os.Parcelable
import androidx.lifecycle.Lifecycle
import com.socialplay.gpark.data.model.UniJumpConfig
import com.socialplay.gpark.data.model.creator.ICreatorItem
import com.socialplay.gpark.data.model.creator.KolCreatorInfo
import com.socialplay.gpark.data.model.creator.label.KolCreatorLabel
import com.socialplay.gpark.data.model.creator.label.UgcPublishLabel

interface IKolCreatorAdapterListener {
    fun goProfile(uuid: String, from: String)
    fun onClickUgc(ugId: String, gameCode: String, item: ICreatorItem)
    fun goUniJump(info: UniJumpConfig, source: String?, categoryId: Int)
    fun goMoreFollowedCreator(type: Int)
    fun goMoreRecommendCreator()
    fun goMoreGame(type: Int)
    fun showMoreUgcLabel()
    fun showMoreCreatorLabel()

    fun changeFollow(uuid: String, toFollow: Boolean)
    fun selectUgcLabel(labelId: Int?)
    fun selectCreatorLabel(tagId: Int)
    fun sendStarCreatorShow(uuid: String)

    fun saveBannerPosition(position: Int)
    fun getBannerPosition(): Int
    fun popRvStoredState(key: String): Parcelable?
    fun saveRvState(key: String, state: Parcelable?)
    fun getUgcLabelList(): List<UgcPublishLabel>
    fun getLabelCreatorList(): List<KolCreatorInfo>
    fun getCreatorLabelList(): List<KolCreatorLabel>
    fun getLifecycle(): Lifecycle

    fun notifyNeedUpdateDailyTaskStatus()
}