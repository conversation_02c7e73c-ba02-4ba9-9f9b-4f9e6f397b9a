package com.socialplay.gpark.ui.editor.share

import android.view.View
import android.view.animation.AnimationUtils
import com.airbnb.epoxy.ModelCollector
import com.bumptech.glide.RequestManager
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.editor.share.AvatarShareCompositeBackground
import com.socialplay.gpark.data.model.editor.share.AvatarShareCompositingImage
import com.socialplay.gpark.databinding.AdapterAvatarShareImageListType1Binding
import com.socialplay.gpark.databinding.AdapterAvatarShareImageListType2Binding
import com.socialplay.gpark.databinding.AdapterAvatarShareImageListType3Binding
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.visible

fun ModelCollector.avatarShareImageListItem(
    position: Int,
    item: AvatarShareCompositingImage,
    glide: RequestManager?,
    itemClickListener: ((position: Int, img: AvatarShareCompositingImage, view: View) -> Unit)? = null,
) {

    when (item.background.type) {
        AvatarShareCompositeBackground.TYPE_1 -> {
            add(AvatarShareImageListItemType1(position, item, glide).apply {
                this.itemClickListener = itemClickListener
                this.id(position)
            })
        }
        AvatarShareCompositeBackground.TYPE_2 -> {
            add(AvatarShareImageListItemType2(position, item, glide).apply {
                this.itemClickListener = itemClickListener
                this.id(position)
            })
        }
        AvatarShareCompositeBackground.TYPE_3 -> {
            add(AvatarShareImageListItemType3(position, item, glide).apply {
                this.itemClickListener = itemClickListener
                this.id(position)
            })
        }
        else -> {}
    }

}

private data class AvatarShareImageListItemType1(
    private val position: Int,
    private val item: AvatarShareCompositingImage,
    val glide: RequestManager?,
) : ViewBindingItemModel<AdapterAvatarShareImageListType1Binding>(
    R.layout.adapter_avatar_share_image_list_type1,
    AdapterAvatarShareImageListType1Binding::bind
) {

    var itemClickListener: ((position: Int, img: AvatarShareCompositingImage, view: View) -> Unit)? = null

    override fun AdapterAvatarShareImageListType1Binding.onBind() {
        root.setOnClickListener { itemClickListener?.invoke(position, item, root) }
        glide?.run {
            load(item.background.url).into(ivBackground)

            load(item.body.url)
                .placeholder(R.color.transparent)
                .skipMemoryCache(true)
                .diskCacheStrategy(DiskCacheStrategy.NONE)
                .into(ivBody)
        }

        if (item.body.isPlaceholder) {
            llLoading.visible()
            ivLoading.startAnimation(AnimationUtils.loadAnimation(this.root.context, R.anim.anim_rotation_linear))
        } else {
            llLoading.gone()
        }
    }
}
private data class AvatarShareImageListItemType2(
    private val position: Int,
    private val item: AvatarShareCompositingImage,
    val glide: RequestManager?,
) : ViewBindingItemModel<AdapterAvatarShareImageListType2Binding>(
    R.layout.adapter_avatar_share_image_list_type2,
    AdapterAvatarShareImageListType2Binding::bind
) {

    var itemClickListener: ((position: Int, img: AvatarShareCompositingImage, view: View) -> Unit)? = null

    override fun AdapterAvatarShareImageListType2Binding.onBind() {
        root.setOnClickListener { itemClickListener?.invoke(position, item, root) }
        glide?.run {
            load(item.background.url).into(ivBackground)

            load(item.body.url).placeholder(R.color.transparent)
                .skipMemoryCache(true)
                .diskCacheStrategy(DiskCacheStrategy.NONE)
                .into(ivBody)
        }

        if (item.body.isPlaceholder) {
            llLoading.visible()
            ivLoading.startAnimation(AnimationUtils.loadAnimation(this.root.context, R.anim.anim_rotation_linear))
        } else {
            llLoading.gone()
        }
    }
}
private data class AvatarShareImageListItemType3(
    private val position: Int,
    private val item: AvatarShareCompositingImage,
    val glide: RequestManager?,
) : ViewBindingItemModel<AdapterAvatarShareImageListType3Binding>(
    R.layout.adapter_avatar_share_image_list_type3,
    AdapterAvatarShareImageListType3Binding::bind
) {

    var itemClickListener: ((position: Int, img: AvatarShareCompositingImage, view: View) -> Unit)? = null

    override fun AdapterAvatarShareImageListType3Binding.onBind() {
        root.setOnClickListener { itemClickListener?.invoke(position, item, root) }
        glide?.run {
            load(item.background.url).into(ivBackground)

            load(item.body.url).placeholder(R.color.transparent)
                .skipMemoryCache(true)
                .diskCacheStrategy(DiskCacheStrategy.NONE)
                .into(ivBody)
        }


        if (item.body.isPlaceholder) {
            llLoading.visible()
            val loadAnimation = AnimationUtils.loadAnimation(this.root.context, R.anim.anim_rotation_linear)
            ivLoading.startAnimation(loadAnimation)
        } else {
            llLoading.gone()
        }
    }
}