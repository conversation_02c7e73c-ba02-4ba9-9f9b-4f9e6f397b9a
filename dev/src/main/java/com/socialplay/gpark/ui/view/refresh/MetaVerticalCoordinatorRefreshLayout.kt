package com.socialplay.gpark.ui.view.refresh

import android.content.Context
import android.util.AttributeSet
import androidx.coordinatorlayout.widget.CoordinatorLayout
import com.google.android.material.appbar.AppBarLayout

class MetaVerticalCoordinatorRefreshLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = 0
) : MetaRefreshLayout(context, attrs, defStyle) {
    override fun canChildScrollUp(): Bo<PERSON>an {
        for (i in 0 until childCount) {
            val child = getChildAt(i)
            if (child != null && child is CoordinatorLayout) {
                for (j in 0 until child.childCount) {
                    val appBarLayout = child.getChildAt(j)
                    if (appBarLayout != null && appBarLayout is AppBarLayout) {
                        return appBarLayout.top != 0
                    }
                }
            }
        }
        return super.canChildScrollUp()
    }
}