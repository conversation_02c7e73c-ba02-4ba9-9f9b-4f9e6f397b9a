package com.socialplay.gpark.ui.account.startup

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.BanBlockInteractor
import com.socialplay.gpark.data.model.editor.DefaultRoleInfo
import com.socialplay.gpark.data.repository.EditorRepository
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import org.koin.android.ext.android.get

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/05/24
 *     desc   :
 * </pre>
 */
data class BuildGuideState(
    val playOrBuild: Boolean? = null,
    val availableAvatars: Async<List<DefaultRoleInfo>> = Uninitialized
) : MavericksState {

    fun getAvatarInfo(): Pair<DefaultRoleInfo, Int> {
        return (availableAvatars()?.getOrNull(1)
            ?: EditorRepository.DEFAULT_PLACEHOLDER_AVATAR_INFO_V2[1]) to R.drawable.ic_create_avatar_portrait_male
    }
}

class BuildGuideViewModel(
    initialState: BuildGuideState,
    private val repo: IMetaRepository,
    private val accountInteractor: AccountInteractor,
    val banBlockInteractor: BanBlockInteractor,
) : BaseViewModel<BuildGuideState>(initialState) {

    init {
        fetchRolesList()
    }

    fun fetchRolesList() {
        repo.getRoleListV2().execute {
            copy(availableAvatars = it)
        }
    }


    fun selectMode(playOrBuild: Boolean) = setState {
        copy(playOrBuild = playOrBuild)
    }

    fun isBanned() = accountInteractor.isAccountBanned()

    companion object : KoinViewModelFactory<BuildGuideViewModel, BuildGuideState>() {
        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: BuildGuideState
        ): BuildGuideViewModel {
            return BuildGuideViewModel(state, get(), get(), get())
        }
    }
}