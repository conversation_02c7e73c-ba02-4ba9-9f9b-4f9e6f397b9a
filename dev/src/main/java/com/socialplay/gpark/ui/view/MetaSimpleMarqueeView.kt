package com.socialplay.gpark.ui.view

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.View
import com.gongwen.marqueen.SimpleMarqueeView
import com.gongwen.marqueen.util.Util

class MetaSimpleMarqueeView<E> : SimpleMarqueeView<E>, View.OnLayoutChangeListener {
    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        if (attrs != null) {
            val a = getContext().obtainStyledAttributes(attrs, intArrayOf(
                android.R.attr.maxLines,
                android.R.attr.lines,
                ), 0, 0)

            maxLines = a.getInt(0, maxLines)

            @SuppressLint("ResourceType")
            lines = a.getInt(1, lines)

            a.recycle()


            val marqueeViewAttrs = getContext().obtainStyledAttributes(attrs, com.gongwen.marqueen.R.styleable.SimpleMarqueeView, 0, 0)
            textSingleLine = a.getBoolean(com.gongwen.marqueen.R.styleable.SimpleMarqueeView_smvTextSingleLine, false)

            marqueeViewAttrs.recycle()
        }
    }

    private var textSingleLine: Boolean = false

    private var maxLines: Int = -1
    private var lines: Int = -1

    private var lastDisplayedChild = -1

    private var itemChangedListener: ItemChangedListener<E>? = null

    fun setItemChangedListener(itemChangedListener: ItemChangedListener<E>?) {
        this.itemChangedListener = itemChangedListener
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        addOnLayoutChangeListener(this)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        removeOnLayoutChangeListener(this)
    }

    override fun refreshChildViews() {
        lastDisplayedChild = -1
        super.refreshChildViews()
        if(!textSingleLine && (maxLines != -1 || lines != -1)){
            val factory = this.factory
            if (factory is TextViewMarqueeFactory) {
                val views = factory.getMarqueeViews()
                for (textView in views) {
                    textView.isSingleLine = false

                    if(maxLines != -1){
                        textView.maxLines = maxLines
                    }
                    
                    if(lines != -1){
                        textView.setLines(lines)
                    }
                }
            }
        }
    }


    override fun onLayoutChange(
        v: View?,
        left: Int,
        top: Int,
        right: Int,
        bottom: Int,
        oldLeft: Int,
        oldTop: Int,
        oldRight: Int,
        oldBottom: Int,
    ) {
        if (factory == null || Util.isEmpty(factory.data) || childCount == 0) {
            return
        }

        if (lastDisplayedChild != displayedChild) {
            this.lastDisplayedChild = displayedChild

            val displayedChild = displayedChild
            val data = factory.data[displayedChild]

            itemChangedListener?.onItemChanged(displayedChild, data, currentView)
        }
    }

    interface ItemChangedListener<E> {
        fun onItemChanged(position: Int, data: E, itemView: View)
    }
}