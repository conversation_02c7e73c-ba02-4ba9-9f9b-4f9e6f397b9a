package com.socialplay.gpark.ui.editor.create

import android.view.ViewGroup
import com.bumptech.glide.GenericTransitionOptions
import com.bumptech.glide.RequestManager
import com.chad.library.adapter.base.module.LoadMoreModule
import com.socialplay.gpark.data.model.editor.FormworkList
import com.socialplay.gpark.databinding.AdapterEditorCreateV2FormworkGameBinding
import com.socialplay.gpark.ui.base.adapter.BaseVBViewHolder
import com.socialplay.gpark.ui.base.adapter.BasicQuickAdapter
import com.socialplay.gpark.ui.base.adapter.createViewBinding
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.setMargin

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/04/21
 *     desc   :
 * </pre>
 */
class EditorCreateV2GameAdapter(
    private val glide: RequestManager
) : BasicQuickAdapter<FormworkList.FormworkGame, AdapterEditorCreateV2FormworkGameBinding>(), LoadMoreModule {

    override fun convert(
        holder: BaseVBViewHolder<AdapterEditorCreateV2FormworkGameBinding>, item: FormworkList.FormworkGame
    ) {
        holder.binding.apply {
            glide.load(item.banner)
                .centerCrop()
                .transition(GenericTransitionOptions.withNoTransition())
                .into(ivCover)
            tvPv.text = UnitUtil.formatPlayerCount(item.pvCount)
            tvTitle.text = item.ugcGameName
            glide.load(item.userIcon)
                .centerCrop()
                .transition(GenericTransitionOptions.withNoTransition())
                .into(ivAvatar)
            tvAuthorName.text = item.userName
        }
    }

    override fun viewBinding(
        parent: ViewGroup, viewType: Int
    ): AdapterEditorCreateV2FormworkGameBinding {
        return parent.createViewBinding(AdapterEditorCreateV2FormworkGameBinding::inflate)
    }

}