package com.socialplay.gpark.ui.developer.adapter

import android.graphics.Color
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.text.isDigitsOnly
import com.socialplay.gpark.app.initialize.LibBuildConfigInit
import com.socialplay.gpark.databinding.AdapterDeveloperBuildConfigBinding
import com.socialplay.gpark.ui.base.adapter.BaseAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder
import androidx.core.widget.addTextChangedListener
import com.socialplay.gpark.R
import com.socialplay.gpark.data.kv.BuildConfigKV
import timber.log.Timber
import java.util.*

/**
 * create by: bin on 2021/5/11
 */
class BuildConfigAdapter : BaseAdapter<String, AdapterDeveloperBuildConfigBinding>() {

    private var selectStatus = mutableSetOf<String>()

    companion object {
        private val disableFields = setOf(
            BuildConfigKV.KEY_BASE_URL,
        )
    }

    override fun createViewBinding(layoutInflater: LayoutInflater, parent: ViewGroup, viewType: Int): AdapterDeveloperBuildConfigBinding {
        return AdapterDeveloperBuildConfigBinding.inflate(layoutInflater, parent, false)
    }

    override fun convert(holder: BindingViewHolder<AdapterDeveloperBuildConfigBinding>, item: String, position: Int) {
        val fieldValue = LibBuildConfigInit.getFieldValue(item)
        val suffix = fieldValue?.javaClass?.simpleName
        "${item}\r\n($suffix)".also { holder.binding.tvName.text = it }

        if (selectStatus.contains(item)) {
            holder.binding.tvName.setBackgroundColor(Color.parseColor("#EEEEEE"))
        } else {
            holder.binding.tvName.setBackgroundColor(Color.TRANSPARENT)
        }

        if (holder.binding.etValue.tag is TextWatcher) {
            holder.binding.etValue.removeTextChangedListener(holder.binding.etValue.tag as TextWatcher)
        }
        holder.binding.etValue.isEnabled = !disableFields.contains(item)
        val value = if(fieldValue?.javaClass?.isArray == true) (fieldValue as Array<out Any?>).contentToString() else fieldValue?.toString() ?: ""
        Timber.d("name:$item, value: $value")
        holder.binding.etValue.setText(value)
        val textWatcher = holder.binding.etValue.addTextChangedListener(afterTextChanged = { text ->
            val textValue = text?.trimStart()?.trimEnd().toString()
            val isValid = when {
                fieldValue is Boolean && textValue in arrayListOf("true", "false") -> {
                    LibBuildConfigInit.updateField(item, textValue.toBoolean())
                    true
                }
                fieldValue is Int && textValue.isDigitsOnly()                      -> {
                    LibBuildConfigInit.updateField(item, textValue.toInt())
                    true
                }
                fieldValue is String                                               -> {
                    LibBuildConfigInit.updateField(item, textValue)
                    true
                }
                fieldValue is Float                                                -> {
                    LibBuildConfigInit.updateField(item, textValue.toFloat())
                    true
                }
                fieldValue is Long && textValue.isDigitsOnly()                     -> {
                    LibBuildConfigInit.updateField(item, textValue.toLong())
                    true
                }
                else                                                               -> {
                    false
                }
            }
            Timber.d("isValid:$isValid name:$item, fieldValue: $fieldValue textValue: $textValue")
            holder.binding.etValue.error = if (isValid) {
                null
            } else {
                context.getString(R.string.debug_wrong_type)
            }
        })
        holder.binding.etValue.tag = textWatcher
    }

}