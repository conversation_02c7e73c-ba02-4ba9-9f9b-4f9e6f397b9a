package com.socialplay.gpark.ui.videofeed

import android.util.DisplayMetrics
import android.view.View
import android.view.animation.DecelerateInterpolator
import android.view.animation.Interpolator
import androidx.recyclerview.widget.LinearSmoothScroller
import androidx.recyclerview.widget.PagerSnapHelper
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.SnapHelper
import androidx.viewpager2.widget.ViewPager2
import timber.log.Timber
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min


class SmoothPagerSnapHelper : PagerSnapHelper() {

    private companion object {
        private const val MILLISECONDS_PER_INCH = 100f
        private const val MAX_SCROLL_ON_FLING_DURATION = 120
    }

    private var recyclerView: RecyclerView? = null
    private var viewPager: ViewPager2? = null

    fun attachToViewPager(viewPager2: ViewPager2) {
        this.viewPager = viewPager2

        try {
            ViewPager2::class.java.getDeclaredField("mRecyclerView").apply {
                isAccessible = true
                recyclerView = get(viewPager2) as? RecyclerView
            }

            val pagerSnapHelperField =
                ViewPager2::class.java.getDeclaredField("mPagerSnapHelper").apply {
                    isAccessible = true
                }

            val oldPagerSnapHelper = pagerSnapHelperField.get(viewPager2) as PagerSnapHelper

            SnapHelper::class.java.getDeclaredMethod("destroyCallbacks").apply {
                isAccessible = true
                invoke(oldPagerSnapHelper)
            }

            pagerSnapHelperField.set(viewPager2, this)

            attachToRecyclerView(recyclerView)

            Timber.d("Attach smoothPagerSnapHelper to ViewPager2 success")
        } catch (e: Exception) {
            Timber.e(e, "Failed to attach smoothPagerSnapHelper to ViewPager2")
        }
    }

    override fun findSnapView(layoutManager: RecyclerView.LayoutManager?): View? {
        // When interrupting a smooth scroll with a fake drag, we stop RecyclerView's scroll
        // animation, which fires a scroll state change to IDLE. PagerSnapHelper then kicks in
        // to snap to a page, which we need to prevent here.
        // Simplifying that case: during a fake drag, no snapping should occur.
        return if (viewPager?.isFakeDragging == true) null else super.findSnapView(layoutManager)
    }


    // 新增减速插值器
    private val interpolator: Interpolator = DecelerateInterpolator(2.1f)



    // 重写 createScroller 以使用自定义的插值器
    override fun createScroller(layoutManager: RecyclerView.LayoutManager): RecyclerView.SmoothScroller? {
        val recyclerView = this.recyclerView ?: return super.createScroller(layoutManager)

        return if (layoutManager !is RecyclerView.SmoothScroller.ScrollVectorProvider) {
            null
        } else object : LinearSmoothScroller(recyclerView.context) {

            override fun onTargetFound(targetView: View, state: RecyclerView.State, action: Action) {

                val snapDistances = calculateDistanceToFinalSnap(recyclerView.layoutManager!!, targetView)

                val dx = snapDistances!![0]
                val dy = snapDistances[1]
                val time = calculateTimeForDeceleration(max(abs(dx), abs(dy)))

                if (time > 0) {
                    // 使用自定义插值器
                    action.update(dx, dy, time, interpolator)
                }
            }

            override fun calculateSpeedPerPixel(displayMetrics: DisplayMetrics): Float {
                return MILLISECONDS_PER_INCH / displayMetrics.densityDpi
            }

            override fun calculateTimeForScrolling(dx: Int): Int {
                return min(MAX_SCROLL_ON_FLING_DURATION, super.calculateTimeForScrolling(dx))
            }
        }
    }
}