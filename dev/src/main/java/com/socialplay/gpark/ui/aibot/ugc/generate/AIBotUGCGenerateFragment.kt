package com.socialplay.gpark.ui.aibot.ugc.generate

import android.graphics.LinearGradient
import android.graphics.Shader
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.addCallback
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.bumptech.glide.Glide
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.model.aibot.AIBotGenerateInfo
import com.socialplay.gpark.data.model.aibot.AiBotCreateResultEvent
import com.socialplay.gpark.data.model.aibot.BotInfo
import com.socialplay.gpark.data.model.aibot.BotLabelInfo
import com.socialplay.gpark.databinding.FragmentAiBotCreateCharacterBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.ui.dialog.ConfirmDialog
import com.socialplay.gpark.util.KeyboardHeightUtilV2
import com.socialplay.gpark.util.SoftHideKeyBoardUtil
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.addTextChangedListener
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.navigateUp
import com.socialplay.gpark.util.extension.setHeight
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.property.viewBinding
import org.greenrobot.eventbus.EventBus
import org.koin.androidx.viewmodel.ext.android.viewModel
import timber.log.Timber


/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/08/28
 *     desc   :
 *
 */
class AIBotUGCGenerateFragment : BaseFragment<FragmentAiBotCreateCharacterBinding>() {
    val viewModel by viewModel<AIBotUGCGenerateViewModel>()
    val args by navArgs<AIBotUGCGenerateFragmentArgs>()
    private var softHideKeyBoardUtil: SoftHideKeyBoardUtil? = null

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentAiBotCreateCharacterBinding? {
        return FragmentAiBotCreateCharacterBinding.inflate(inflater, container, false)
    }

    override fun init() {
        initView()
        initData()
    }

    private fun initView() {
        binding.imgBack.setOnAntiViolenceClickListener { showBackConfirmDialog() }
        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner) {
            showBackConfirmDialog()
        }
        val shadow = LinearGradient(
            0f,
            0f,
            binding.tvGenerate.paint.measureText(binding.tvGenerate.getText().toString()),
            binding.tvGenerate.paint.textSize,
            ContextCompat.getColor(requireContext(), R.color.color_758CFF),
            ContextCompat.getColor(requireContext(), R.color.color_FF8BAF),
            Shader.TileMode.CLAMP
        )
        binding.tvGenerate.paint.setShader(shadow)
        binding.tvComplete.setOnAntiViolenceClickListener {
            binding.loadMoreLoadingView.visible()
            viewModel.saveAIBotInfo(
                binding.etName.text.toString(),
                binding.etSet.text.toString(),
                binding.etDialogue.text.toString(),
                binding.etPrologue.text.toString(),
                binding.etIntro.text.toString()
                    )
            Analytics.track(EventConstants.EVENT_AI_BOT_CHARACTER_GENERATE_CLICK)
        }
        binding.clTagMore.setOnAntiViolenceClickListener {
            val list = viewModel.getSelectTagList()
            MetaRouter.AiBot.showAiTagDialog(this, list, true, AIBotUGCGenerateViewModel.MAX_COUNT) { list ->
                list?.let { it1 -> viewModel.updateLabelSelectList(it1, false) }
            }
        }
        binding.clGenerate.setOnAntiViolenceClickListener {
            binding.loadMoreLoadingView.visible()
            viewModel.generateInfo()
            Analytics.track(EventConstants.EVENT_AI_BOT_CHARACTER_WRITE_CLICK)
        }
        binding.imgClear.setOnAntiViolenceClickListener {
            showDeleteConfirmDialog()
        }
        binding.etName.addTextChangedListener(viewLifecycleOwner){
            checkStatus()
        }
        binding.etSet.addTextChangedListener(viewLifecycleOwner){
            checkStatus()
        }
        binding.etDialogue.addTextChangedListener(viewLifecycleOwner){
            checkStatus()
        }
        binding.loadMoreLoadingView.setOnAntiViolenceClickListener {

        }

    }

    private fun initData() {
        viewModel.aiBotInfoLiveData.observe(viewLifecycleOwner) {
            Glide.with(this).load(it.icon).into(binding.imgUser)
        }
        viewModel.tagListLiveData.observe(viewLifecycleOwner) {
            setLabel(it)
            checkStatus()
        }
        viewModel.generateInfo.observe(viewLifecycleOwner) {
            binding.loadMoreLoadingView.gone()
            if (it != null) {
                updateGenerateInfo(it)
            }
        }
        viewModel.saveAiBotInfoLiveData.observe(viewLifecycleOwner) {
            binding.loadMoreLoadingView.gone()
            if (it != null) {
                if (it.succeeded) {
                    it.data?.let { it1 -> updateErrorTips(it1) }
                    EventBus.getDefault().post(AiBotCreateResultEvent( it.data?.id?:""))
                    this.findNavController().popBackStack()
                    MetaRouter.AiBot.gotoConversation(this, it.data?.botId ?: "", it.data?.id ?: "", "3")
                } else {
                    it.data?.let { it1 -> updateErrorTips(it1) }
                    ToastUtil.showShort(it.message)
                }
            }
        }
    }
    private fun updateErrorTips(botInfo: BotInfo) {
        val filedAuditMap = botInfo.filedAuditMap
        if (filedAuditMap != null) {
            if (filedAuditMap[binding.etName.text.toString()] == false) {
                binding.tvNameError.visible()
            } else {
                binding.tvNameError.gone()
            }
            if (filedAuditMap[binding.etSet.text.toString()] == false) {
                binding.tvSetError.visible()
            } else {
                binding.tvSetError.gone()
            }
            if (filedAuditMap[binding.etDialogue.text.toString()] == false) {
                binding.tvDialogueError.visible()
            } else {
                binding.tvDialogueError.gone()
            }
            if (filedAuditMap[binding.etIntro.text.toString()] == false) {
                binding.tvIntroError.visible()
            } else {
                binding.tvIntroError.gone()
            }
            if (filedAuditMap[binding.etPrologue.text.toString()] == false) {
                binding.tvPrologueError.visible()
            } else {
                binding.tvPrologueError.gone()
            }
        } else {
            binding.tvNameError.gone()
            binding.tvSetError.gone()
            binding.tvDialogueError.gone()
            binding.tvIntroError.gone()
            binding.tvPrologueError.gone()
        }
    }

    private fun updateGenerateInfo(generateInfo: AIBotGenerateInfo) {
        binding.etSet.setText(generateInfo.setting)
        binding.etDialogue.setText(generateInfo.dialogueExample)
        binding.etPrologue.setText(generateInfo.prologue)
        binding.etIntro.setText(generateInfo.intro)
        binding.etName.setText(generateInfo.name)
    }
    private fun showDeleteConfirmDialog() {
        ConfirmDialog.Builder(this)
            .content(getString(R.string.ai_bot_delete_title))
            .cancelBtnTxt(getString(R.string.cancel), lightBackground = false)
            .confirmBtnTxt(getString(R.string.delete_cap), lightBackground = true)
            .isRed(true)
            .cancelCallback {

            }
            .confirmCallback {
                viewModel.clean()
            }
            .show()
    }
    private fun showTipDialog() {
        if (viewModel.isFirstAICreateTips()) {
            ConfirmDialog.Builder(this)
                .title(getString(R.string.hint))
                .content(getString(R.string.ai_bot_set_tips),
                    contentColor = R.color.color_757575,
                    contentGravity = Gravity.LEFT and Gravity.CENTER_VERTICAL,
                    contentSpace = 14.dp)
                .cancelBtnTxt(isVisible = false)
                .setCancelable(false)
                .setCanceledOnTouchOutside(false)
                .confirmBtnTxt(getString(R.string.got_it), lightBackground = true)
                .cancelCallback {

                }
                .confirmCallback {

                }
                .show()
        }
    }
    private fun showBackConfirmDialog() {
        ConfirmDialog.Builder(this)
            .content(getString(R.string.ai_bot_back_alert))
            .cancelBtnTxt(getString(R.string.cancel), lightBackground = false)
            .confirmBtnTxt(getString(R.string.delete_cap), lightBackground = true)
            .isRed(true)
            .cancelCallback {

            }
            .confirmCallback {
                navigateUp()
            }
            .show()
    }
    private fun checkStatus(){
        viewModel.aiBotInfoLiveData.value?.let { updateErrorTips(it) }
        if (viewModel.getSelectTagList().isNullOrEmpty() ||
            binding.etName.text.toString().isNullOrEmpty() ||
            binding.etSet.text.toString().isNullOrEmpty()
        ) {
            binding.tvComplete.isEnabled = false
            binding.tvComplete.alpha = 0.5f
            return
        }
        binding.tvComplete.isEnabled = true
        binding.tvComplete.alpha = 1f
    }

    private fun setLabel(labels: List<BotLabelInfo>) {
        binding.flowTag.removeAllViews()
        for (label in labels) {
            if (!label.tagName.isNullOrEmpty()) {
                val labelView = getLabelView(label)
                binding.flowTag.addView(labelView)
            }
        }
    }


    private fun getLabelView(label: BotLabelInfo): View {
        val view = LayoutInflater.from(requireContext()).inflate(R.layout.adapter_aibot_label_dialog_item, null)
        updateTagView(view, label.tagName, label.icon, label.selected ?: false)
        view.setOnAntiViolenceClickListener {
            viewModel.updateLabelSelect(label)
        }
        return view
    }

    private fun updateTagView(view: View, name: String?, icon: String?, isSelect: Boolean) {
        val root = view.findViewById<ConstraintLayout>(R.id.cl_root)
        val textview = view.findViewById<TextView>(R.id.tvTableName)
        if (isSelect) {
            root.setBackgroundResource(R.drawable.bg_b884ff_corner_360)
        } else {
            root.setBackgroundResource(R.drawable.bg_424242_corner_360)
        }
        textview.setTextColor(ContextCompat.getColor(requireContext(), R.color.white_90))
        textview.text = name
        Glide.with(requireContext()).load(icon).into(view.findViewById<ImageView>(R.id.icon_tag))
    }

    override fun loadFirstData() {
        viewModel.initAIBotInfo(args)
        showTipDialog()
    }

    override fun getFragmentName(): String = PageNameConstants.FRAGMENT_AI_BOT_GENERATE
}