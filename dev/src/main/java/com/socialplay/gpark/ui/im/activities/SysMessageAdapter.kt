package com.socialplay.gpark.ui.im.activities

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.viewbinding.ViewBinding
import com.bumptech.glide.RequestManager
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.socialplay.gpark.data.model.SysActivitiesInfo
import com.socialplay.gpark.databinding.AdapterSysMessageBinding
import com.socialplay.gpark.databinding.AdapterSystemMessageItem2Binding
import com.socialplay.gpark.ui.base.adapter.BasePagingDataAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder
import com.socialplay.gpark.ui.core.GlideGetter
import com.socialplay.gpark.ui.view.FolderTextView
import com.socialplay.gpark.util.DateUtil.formatAgoStyleForChat
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.orNull
import com.socialplay.gpark.util.extension.visible

class SysMessageAdapter(private val glide: GlideGetter, private val isGame: Boolean) :
    BasePagingDataAdapter<SysActivitiesInfo, ViewBinding>(DIFF_ITEM_CALLBACK) {
    private val foldStateMap = hashMapOf<String, Boolean>()

    companion object {
        private const val PAYLOAD_NAME = 1
        private const val PAYLOAD_ICON = 2
        private const val PAYLOAD_TIME = 3
        private const val PAYLOAD_TITLE = 4
        private const val PAYLOAD_CONTENT = 5
        private const val PAYLOAD_IMAGE = 6
        private const val PAYLOAD_ADDITION = 7
        private const val PAYLOAD_SUB_ICON = 8
        private const val PAYLOAD_JUMP_ARROW = 9

        private val DIFF_ITEM_CALLBACK = object : DiffUtil.ItemCallback<SysActivitiesInfo>() {
            override fun areItemsTheSame(
                oldItem: SysActivitiesInfo,
                newItem: SysActivitiesInfo
            ): Boolean {
                return oldItem.msgId == newItem.msgId && oldItem.contentType == newItem.contentType
            }

            override fun areContentsTheSame(
                oldItem: SysActivitiesInfo,
                newItem: SysActivitiesInfo
            ): Boolean {
                return oldItem.content == newItem.content &&
                        oldItem.additionalValue == newItem.additionalValue &&
                        oldItem.fromIcon == newItem.fromIcon &&
                        oldItem.fromName == newItem.fromName &&
                        oldItem.sendTime == newItem.sendTime &&
                        oldItem.subGroup == newItem.subGroup &&
                        oldItem.linkValue == newItem.linkValue
            }

            override fun getChangePayload(
                oldItem: SysActivitiesInfo,
                newItem: SysActivitiesInfo
            ): Any? {
                val payload = lazy { arrayListOf<Int>() }
                if (oldItem.fromName != newItem.fromName) {
                    payload.value.add(PAYLOAD_NAME)
                }
                if (oldItem.fromIcon != newItem.fromIcon) {
                    payload.value.add(PAYLOAD_ICON)
                }
                if (oldItem.sendTime != newItem.sendTime) {
                    payload.value.add(PAYLOAD_TIME)
                }
                if (oldItem.content.title != newItem.content.title) {
                    payload.value.add(PAYLOAD_TITLE)
                }
                if (oldItem.content.content != newItem.content.content) {
                    payload.value.add(PAYLOAD_CONTENT)
                }
                if (oldItem.content.image != newItem.content.image) {
                    payload.value.add(PAYLOAD_IMAGE)
                }
                if (oldItem.additionalValue != newItem.additionalValue || oldItem.additionalType != newItem.additionalType) {
                    payload.value.add(PAYLOAD_ADDITION)
                }
                if (oldItem.subGroup?.listIcon != newItem.subGroup?.listIcon) {
                    payload.value.add(PAYLOAD_SUB_ICON)
                }
                if (oldItem.linkType != newItem.linkType || oldItem.linkValue != newItem.linkValue) {
                    payload.value.add(PAYLOAD_JUMP_ARROW)
                }

                return payload.orNull()
            }
        }
    }

    override fun createViewBinding(
        layoutInflater: LayoutInflater,
        parent: ViewGroup,
        viewType: Int
    ): ViewBinding {
        if (isGame){
            return AdapterSystemMessageItem2Binding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        }else{
            return AdapterSysMessageBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        }
    }

    override fun convert(
        holder: BindingViewHolder<ViewBinding>,
        item: SysActivitiesInfo,
        position: Int
    ) {
        if (isGame){
            (holder as BindingViewHolder<AdapterSystemMessageItem2Binding>).binding.apply {
                glide()?.run {
                    load(item.fromIcon).circleCrop()
                        .into(imgIcon)

                    load(item.content.image)
                        .transform(CenterCrop())
                        .into(ivContentImage)
                }
                tvMessage.setOnFoldTextStateChangeListener(object : FolderTextView.OnFoldTextStateChangeListener{
                    override fun onFoldChange(isFold: Boolean) {
                        foldStateMap[item.msgId] = isFold
                    }
                })
                tvMessage.text = item.content.content

                tvTime.text = item.sendTime.formatAgoStyleForChat(tvTime.context)
                ivContentImage.visible(!item.content.image.isNullOrEmpty())
                val visible = !item.linkValue.isNullOrEmpty()
                tvJump.visible(visible)
            }
        }else{
            (holder as BindingViewHolder<AdapterSysMessageBinding>).binding.apply {
                glide()?.run {
                    load(item.fromIcon).circleCrop()
                        .into(imgIcon)

                    load(item.content.image).transform(CenterCrop(), RoundedCorners(8.dp))
                        .into(ivContentImage)
                }
                tvTitle.text = item.content.title
                tvMessage.setOnFoldTextStateChangeListener(object : FolderTextView.OnFoldTextStateChangeListener{
                    override fun onFoldChange(isFold: Boolean) {
                        foldStateMap[item.msgId] = isFold
                    }
                })
                tvMessage.text = item.content.content

                tvTime.text = item.sendTime.formatAgoStyleForChat(tvTime.context)
                tvName.text = item.fromName
                ivContentImage.visible(!item.content.image.isNullOrEmpty())
                val visible = !item.linkValue.isNullOrEmpty()
                lineBottom.visible(visible)
                tvJump.visible(visible)
                ivJumpArrow.visible(visible)
            }
        }
    }

}