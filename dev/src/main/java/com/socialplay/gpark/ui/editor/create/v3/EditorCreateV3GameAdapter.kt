package com.socialplay.gpark.ui.editor.create.v3

import android.view.ViewGroup
import com.bumptech.glide.GenericTransitionOptions
import com.bumptech.glide.RequestManager
import com.chad.library.adapter.base.module.LoadMoreModule
import com.socialplay.gpark.data.model.editor.FormworkList
import com.socialplay.gpark.databinding.AdapterEditorCreateV3FormworkGameBinding
import com.socialplay.gpark.ui.base.adapter.BaseVBViewHolder
import com.socialplay.gpark.ui.base.adapter.BasicQuickAdapter
import com.socialplay.gpark.ui.base.adapter.createViewBinding
import com.socialplay.gpark.util.UnitUtil

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/04/21
 *     desc   :
 * </pre>
 */
class EditorCreateV3GameAdapter(
    private val glide: RequestManager
) : BasicQuickAdapter<FormworkList.FormworkGame, AdapterEditorCreateV3FormworkGameBinding>(),
    LoadMoreModule {

    override fun convert(
        holder: BaseVBViewHolder<AdapterEditorCreateV3FormworkGameBinding>,
        item: FormworkList.FormworkGame
    ) {
        holder.binding.apply {
            glide.load(item.banner)
                .centerCrop()
                .transition(GenericTransitionOptions.withNoTransition())
                .into(ivCover)
            tvPv.text = UnitUtil.formatPlayerCount(item.pvCount)
            tvTitle.text = item.ugcGameName
            glide.load(item.userIcon)
                .centerCrop()
                .transition(GenericTransitionOptions.withNoTransition())
                .into(ivAvatar)
            tvAuthorName.text = item.userName
        }
    }

    override fun viewBinding(
        parent: ViewGroup, viewType: Int
    ): AdapterEditorCreateV3FormworkGameBinding {
        return parent.createViewBinding(AdapterEditorCreateV3FormworkGameBinding::inflate)
    }

}