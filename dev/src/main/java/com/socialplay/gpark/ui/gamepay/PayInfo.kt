package com.socialplay.gpark.ui.gamepay

import com.meta.biz.ugc.model.RechargeArkMsg

data class PayInfo(
    /**
     * 是否从MW发起的支付
     */
    val fromMW: Boolean,
    /**
     * 是否显示二次弹窗
     */
    val showConfirm: Boolean = true,
    /**
     * 代币不够自动触发代币购买时, 代币购买成功后, 是否触发代币消耗流程
     */
    val autoConfirmAfterRecharge: Boolean = true,
    val gameId: String,
    val productCode: String,
    val productName: String,
    /**
     * 商品单价
     */
    val productPrice:Long,
    /**
     * 购买的商品数量
     */
    val productCount:Int = 1,
    /**
     * 游戏外支付场景会用到
     */
    val sceneCode: Int? = null,
    /**
     * 本次支付需要的代币/积分数量
     */
    val payAmount: Long,
    /**
     * 支付方式
     */
    val payType: PayType = PayType.COINS,
    /**
     * 用于埋点
     * 游戏类型
     * 1：PGC游戏-对应BaseGameDetailCommonFragment.GAME_TYPE_PGC
     * 2：UGC游戏-对应BaseGameDetailCommonFragment.GAME_TYPE_UGC
     */
    val gameType: Int? = null,
    /**
     * 用于埋点
     * 用于表述：钱是为什么而充值
     * profile：我的页面钱包
     * avatar：mw那边的协议调用
     * flower：送花带来的曝光
     * gamets：ts游戏里面拉起
     */
    val source: String? = null,
    /**
     * 用于埋点
     * pop：推荐弹窗带来的订单
     * wallet：钱包页面带来的订单
     */
    val pageType: String? = null,
    /**
     * 从游戏/角编里面发起支付时, 会有msg对象
     */
    val msg: RechargeArkMsg? = null,
    val rawData: MutableMap<String, Any?> = mutableMapOf(),
    val onPayResult: ((payResult: PayResult) -> Unit)? = null,
)

data class PayResult(
    val resultCode: Int,
    /**
     * 订单号, 当充值成功时, 会有这个值
     */
    val orderId: String? = null,
    /**
     * 代币余额, 当充值成功时, 会有这个值, 网络请求失败时, 这个值也可能为 null
     */
    val coinsBalance: Long? = null,
    /**
     * 失败错误码, 服务器返回失败时, 会有这个值
     */
    val failedCode: Int? = null,
    /**
     * 失败原因, 服务器返回失败时, 会有这个值
     */
    val failedReason: String? = null,
) {
    companion object {
        /**
         * 充值失败
         */
        const val RESULT_CODE_FAILED = 0
        /**
         * 充值成功
         */
        const val RESULT_CODE_SUCCESS = 1
        /**
         * 取消充值
         * 当用户点确认充值, 充值失败后, 用户再手动关闭充值弹窗, 也算取消充值
         */
        const val RESULT_CODE_CANCEL = 2

        /**
         * 充值被自动取消
         * 1. 当 autoConfirmAfterRecharge = false 时会有此场景
         * 2. 内部状态异常, 或者不满足要求
         */
        const val RESULT_CODE_AUTO_CANCEL = 3
    }

    val isSuccess get() = resultCode == RESULT_CODE_SUCCESS
}

enum class PayType {
    /**
     * 代币支付(GPark币或者派对币)
     */
    COINS,

    /**
     * 积分支付
     */
    POINTS;

    companion object {
        fun parsePayType(payType: String?): PayType {
            if (payType == null) {
                return COINS
            } else {
                val type = kotlin.runCatching { payType.toString().toInt() }.getOrNull()
                return if (type == null || type == 1) {
                    COINS
                } else {
                    POINTS
                }
            }
        }
    }
}

/**
 * 支付渠道（枚举）【1:支付宝,2:微信支付,4:QQ钱包支付,8:平台代币支付 16：模拟支付 32：乐币支付 64：ARK币支付 65：谷歌支付 66：苹果支付 67：Gpark币支付 68：派对币支付 69：移动积分支付 72扫码支付 73: 用户积分支付】
 */
enum class PayChannel(val value: Int){
    G_PARK_COIN(67),
    PARTY_COIN(68)
}