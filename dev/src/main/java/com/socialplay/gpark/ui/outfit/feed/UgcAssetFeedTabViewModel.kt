package com.socialplay.gpark.ui.outfit.feed

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.meta.pandora.utils.ConcurrentSet
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.BaseAccountInteractor
import com.socialplay.gpark.data.interactor.TTaiInteractor
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.data.model.outfit.UgcAssetEntrance
import com.socialplay.gpark.data.model.outfit.UgcAssetNotice
import com.socialplay.gpark.data.model.outfit.UgcDesignFeed
import com.socialplay.gpark.data.model.outfit.UgcDesignFeedRequest
import com.socialplay.gpark.data.model.outfit.UgcDesignLikeRequest
import com.socialplay.gpark.data.repository.UgcRepository
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.maverick.copyEx
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.util.extension.insertAt
import com.socialplay.gpark.util.extension.replaceAt
import com.socialplay.gpark.util.extension.replaceSingle
import kotlinx.coroutines.flow.map
import org.koin.android.ext.android.get

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/11/06
 *     desc   :
 * </pre>
 */
data class UgcAssetFeedTabState(
    val outfits: Async<List<UgcDesignFeed>> = Uninitialized,
    val loadMore: Async<LoadMoreState> = Uninitialized,
    val orderType: Int = UgcDesignFeedRequest.ORDER_DEFAULT,
    val page: Int = 1,
    val uniqueTag: Int = 0,
    val notices: Async<List<UgcAssetNotice>> = Uninitialized,
    val entrances: Async<List<UgcAssetEntrance>> = Uninitialized,
    val filterEntrance: Int? = null,
    val filterTags: List<Int> = emptyList(),
    val guideInvoke: Pair<Int, Boolean>? = null,
) : MavericksState

class UgcAssetFeedTabViewModel(
    initState: UgcAssetFeedTabState,
    val accountInteractor: AccountInteractor,
    private val ugcRepo: UgcRepository,
    private val tTaiInteractor: TTaiInteractor,
) : BaseViewModel<UgcAssetFeedTabState>(initState) {

    val curUuid = accountInteractor.curUuid

    private val assetSet = ConcurrentSet<String>()

    init {
        getFeed(true)
        fetchOperationNotices()
    }

    fun getFeed(isRefresh: Boolean) = withState { s ->
        if (s.loadMore is Loading) return@withState
        val newPage = if (isRefresh) 1 else s.page + 1
        ugcRepo.getUgcDesignFeedV2(
            s.orderType,
            newPage,
            PAGE_SIZE,
            s.filterEntrance,
            s.filterTags
        ).map {
            if (isRefresh) {
                assetSet.clear()
            }
            val newList = it.data?.filter { outfit ->
                val result = !assetSet.contains(outfit.feedId)
                if (result) {
                    assetSet.add(outfit.feedId)
                }
                result
            }
            it.copy(
                data = if (isRefresh) {
                    newList
                } else {
                    oldState.outfits().insertAt(-1, newList)
                },
                isLastPage = it.isLastPage || newList.isNullOrEmpty()
            )
        }.execute { result ->
            when (result) {
                is Success -> {
                    val wrapper = result()
                    copy(
                        outfits = Success(wrapper.data.orEmpty()),
                        loadMore = Success(LoadMoreState(isEnd = wrapper.isLastPage)),
                        page = newPage,
                        uniqueTag = if (isRefresh) uniqueTag.xor(1) else uniqueTag
                    )
                }

                is Fail -> {
                    copy(
                        outfits = if (isRefresh) Fail(result.error, outfits()) else outfits,
                        loadMore = Fail(
                            result.error,
                            if (isRefresh) LoadMoreState(needRefresh = true) else null
                        )
                    )
                }

                else -> {
                    copy(
                        outfits = if (isRefresh) Loading(outfits()) else outfits,
                        loadMore = Loading()
                    )
                }
            }
        }
    }

    fun updateOrderType(orderType: Int) = withState { s ->
        if (s.loadMore is Loading) return@withState
        if (s.orderType != orderType) {
            setState { copy(orderType = orderType) }
        }
        getFeed(true)
    }

    fun likeFeed(outfit: UgcDesignFeed, position: Int) = withState { s ->
        val oldListResult = s.outfits
        val oldList = oldListResult()
        val newOutfit = outfit.switchLike()
        val newList = oldList.replaceAt(position, newOutfit)
        val newListResult = oldListResult.copyEx(newList)
        setState { copy(outfits = newListResult) }
        ugcRepo.likeUgcDesign(UgcDesignLikeRequest(outfit.feedId, newOutfit.isFavorite)).map {
            if (PandoraToggle.enableModuleGuideFirstInteract && newOutfit.isFavorite && accountInteractor.assetFirstInteract) {
                val newGuideInvoke =
                    BaseAccountInteractor.MODULE_GUIDE_INVOKE_LIKE to !(oldState.guideInvoke?.second
                        ?: false)
                setState { copy(guideInvoke = newGuideInvoke) }
            }
        }.execute {
            this
        }
    }

    fun updateLikeCount(itemId: String, isLike: Boolean, likeCount: Long) = withState { s ->
        val oldListResult = s.outfits
        val oldList = oldListResult()
        val (ok, newList) = oldList.replaceSingle(
            predicate = {
                it.feedId == itemId
            },
            transform = {
                it.copy(isFavorite = isLike, favorites = likeCount)
            }
        )
        if (!ok) return@withState
        setState {
            copy(outfits = outfits.copyEx(newList))
        }
    }

    fun fetchOperationNotices() {
        tTaiInteractor.getTTaiWithTypeV3<List<UgcAssetNotice>>(TTaiKV.ID_UGC_ASSET_FEED_NOTICE)
            .map {
                it.orEmpty()
            }
            .execute(retainValue = UgcAssetFeedTabState::notices) {
                copy(notices = it)
            }
    }

    fun updateFilter(orderType: Int, entrance: Int?, tags: List<Int>) = withState {
        setState { copy(orderType = orderType, filterEntrance = entrance, filterTags = tags) }
        getFeed(true)
    }

    fun fetchTagsIfNeed() = withState { s ->
        if (s.entrances.shouldLoad) {
            ugcRepo.getUgcAssetTagTree().execute { copy(entrances = it) }
        }
    }

    companion object : KoinViewModelFactory<UgcAssetFeedTabViewModel, UgcAssetFeedTabState>() {

        const val PAGE_SIZE = 20

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: UgcAssetFeedTabState
        ): UgcAssetFeedTabViewModel {
            return UgcAssetFeedTabViewModel(state, get(), get(), get())
        }
    }
}