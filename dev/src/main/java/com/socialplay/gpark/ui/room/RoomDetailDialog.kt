package com.socialplay.gpark.ui.room

import android.annotation.SuppressLint
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.bumptech.glide.Glide
import com.meta.biz.mgs.data.model.MgsBriefRoomInfo
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.room.ChatRoomInfo
import com.socialplay.gpark.databinding.DialogRoomDetailBinding
import com.socialplay.gpark.databinding.PopUpRoomReportBinding
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.cdnview.CacheCdnImageTask
import com.socialplay.gpark.function.mgs.MgsGameRoomLauncher
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.base.BaseBottomSheetDialogFragment
import com.socialplay.gpark.ui.reportBlock.ReportReasonDialog
import com.socialplay.gpark.ui.reportBlock.ReportSuccessDialogAnalyticsParams
import com.socialplay.gpark.ui.view.PopupWindowCompat
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.flow.firstOrNull
import org.koin.android.ext.android.inject
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 * create by: bin on 2023/7/11
 */
class RoomDetailDialog :  BaseBottomSheetDialogFragment() {
    var room: ChatRoomInfo? = null
    var resid: ResIdBean? = null
    var isRecommend:Boolean = false
    val metaKV:MetaKV by inject()
    val viewModel:RoomDetailViewModel by inject()

    private lateinit var popupWindow: PopupWindowCompat
    private val popupBinding by lazy { PopUpRoomReportBinding.inflate(layoutInflater) }
        companion object {
        fun show(fragment: Fragment, room: ChatRoomInfo, resid:ResIdBean, isRecommend:Boolean = false) {
            val dialog = RoomDetailDialog()
            dialog.room = room
            dialog.resid = resid
            dialog.isRecommend = isRecommend
            dialog.show(fragment.childFragmentManager, "room")
        }
    }

    override val binding by viewBinding(DialogRoomDetailBinding::inflate)

    @SuppressLint("SetTextI18n")
    override fun init() {
        val room = room ?: return
        binding.tvCreate.visible(isRecommend)
        binding.tvCreate.setOnAntiViolenceClickListener {
            MetaRouter.HomeRoom.goCreateRoom(this)
            dismissAllowingStateLoss()
        }
        HomeRoomAnalytics.trackRoomDetailShow(room,resid?.getCategoryID()?:0)

        updateView(room)

        viewModel.room.observe(viewLifecycleOwner){
            updateView(it)
        }
        initPopView()
        requireActivity().supportFragmentManager.setFragmentResultListener(ReportReasonDialog.REQUEST_REPORT_REASON_DIALOG, viewLifecycleOwner) { key, bundle ->
            if (key == ReportReasonDialog.REQUEST_REPORT_REASON_DIALOG) {
                ReportReasonDialog.showReportSuccessDialog(
                    this,
                    ReportSuccessDialogAnalyticsParams.Room(
                        roomId = room.roomId
                    )
                )
            }
        }
    }

    private fun updateView(room: ChatRoomInfo) {
        Glide.with(this).load(CacheCdnImageTask.CDN_ROOM_DETAIL).into(binding.iv)
        Glide.with(this).load(room.creatorAvatar).placeholder(R.drawable.placeholder_circle).circleCrop().into(binding.ivHost)
        binding.imgMore.setOnClickListener {  showPopView()}

        binding.tvDialogTitle.text =
            if (room.roomName.isNullOrEmpty()) getString(R.string.default_room_name) else room.roomName
        binding.tvOccupancy.text = "${getString(R.string.occupancy)}(${room.number}/${room.limitNumber})"
        binding.tvRoomStyle.text = room.getRoomStyle()
        binding.tvLive.text =
            if (room.tag.isNullOrEmpty()) getString(R.string.default_room_tag) else room.tag
        binding.tvJoin.setOnAntiViolenceClickListener {
            viewLifecycleOwner.lifecycleScope.launchWhenResumed {
                val pkg = GlobalContext.get().get<IMetaRepository>()
                    .fetchGameInfoByIdFromRemoteWithCache(room.platformGameId)
                    .firstOrNull()?.data?.packageName

                MgsGameRoomLauncher.enterMgsGame(
                    this@RoomDetailDialog,
                    pkg ?: "",
                    room.platformGameId,
                    MgsBriefRoomInfo(
                        roomIdFromCp = room.roomId,
                        roomName = room.roomName,
                        roomShowNum = null,
                        roomTags = null
                    ),
                    "room_detail_dialog",
                    0,
                    metaKV.account.uuid,
                    true,
                    resid?.setCategoryID(CategoryId.VOICE_ROOM),
                    room.roomName
                )
                dismissAllowingStateLoss()
            }
        }

        binding.rv.adapter = RoomMembersAdapter().apply { setList(room.members) }
        binding.lyOccupancy.gone(room.members.isNullOrEmpty())
    }

    override fun loadFirstData() {
        viewModel.load(room?.roomId?:"")
    }

    override fun needCountTime(): Boolean = false

    override fun getPageName(): String = ""

    override var heightPercent: Float = 0f

    override fun getStyle(): Int {
        return R.style.BottomDialogStyle
    }

    private fun showPopView(){
        popupWindow.showAsDropDownByLocation(
            binding.viewAchor,
            (-117).dp,
            -(10).dp
        )

    }
    private fun initPopView(){
         popupWindow = PopupWindowCompat(
            popupBinding.root,
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        ).apply {
            isTouchable = true
            isOutsideTouchable = true
            isFocusable = true
            isClippingEnabled = false
            animationStyle = R.style.PopupAnimationFromRight
        }
        popupBinding.root.setOnClickListener {
            popupWindow.dismiss()
        }
        popupBinding.tvReport.setOnClickListener {
            val room = viewModel.room.value ?: return@setOnClickListener
            MetaRouter.Report.reportRoom(this, room.roomId, room.creatorUuid)
            popupWindow.dismiss()
        }
    }


}