package com.socialplay.gpark.ui.dialog

import android.content.Context
import android.content.DialogInterface
import android.graphics.Typeface
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.Gravity
import android.view.WindowManager
import androidx.annotation.DrawableRes
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.core.view.marginLeft
import androidx.core.view.marginRight
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import androidx.navigation.NavArgs
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.DialogFragmentSimpleBinding
import com.socialplay.gpark.ui.base.BaseDialogFragment
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setFragmentResultByActivity
import com.socialplay.gpark.util.extension.setHeight
import com.socialplay.gpark.util.extension.setMargin
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.property.viewBinding
import timber.log.Timber

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/05/11
 * desc   :
 * </pre>
 */


class ConfirmDialog : BaseDialogFragment() {

    companion object {
        const val REQUEST_KEY = "SimpleDialogFragment_Request_Key_Result"
        const val RESULT_KEY = "SimpleDialogFragment_Result_Key"
        const val CANCEL = 0
        const val CONFIRM = 1
        const val DISMISS = 2
    }

    override val binding by viewBinding(DialogFragmentSimpleBinding::inflate)

    private var result = DISMISS

    private val args by navArgs<ConfirmDialogArgs>()

    override fun init() {
        args.run {
            binding.content.isVisible = args.showContent
            binding.content.text = args.content ?: ""
            binding.title.isVisible = args.showTitle
            binding.title.text = args.title ?: ""
            binding.tips.isVisible = args.showTips
            binding.tips.text = args.tips ?: ""
            binding.btnCancel.isVisible = args.showLeftBtn
            binding.btnCancel.text = args.leftBtnText ?: getString(R.string.dialog_cancel)
            context?.let {
                binding.content.setTextColor(it.getColor(args.contentColor))
                binding.content.gravity = args.contentGravity
                binding.btnCancel.setTextColor(getTextColor(it, leftTextColor, args.leftLightBackground))
                binding.btnCancel.background = (getBackground(it, args.leftLightBackground))
                if (isRed) {
                    binding.btnConfirm.setTextColor(getTextColor(it, rightTextColor, args.rightLightBackground))
                    binding.btnConfirm.background = (getBackground(it, args.rightLightBackground))
                } else {
                    binding.btnConfirm.setTextColor(ContextCompat.getColor(it, R.color.textColorPrimary))
                    binding.btnConfirm.background = (ContextCompat.getDrawable(it, R.drawable.selector_button_warn))
                }
                if (args.imageId > 0) {
                    binding.iv.setImageDrawable(ContextCompat.getDrawable(it, args.imageId))
                }
            }
            binding.spaceIvTitle.isVisible = args.imageId > 0 && (args.showContent || args.showTitle)
            binding.spaceIvContent.isVisible = args.imageId > 0 && args.showContent && !args.showTitle
            binding.spaceTitleContent.isVisible = args.showContent && args.showTitle
            binding.spaceTitleContent.setHeight(args.contentSpace)
            binding.btnConfirm.isVisible = args.showRightBtn
            binding.btnConfirm.text = args.rightBtnText ?: getString(R.string.dialog_confirm)
            binding.btnConfirm.setTypeface(null, if (args.isBold) Typeface.BOLD else Typeface.NORMAL)
            binding.btnCancel.setOnAntiViolenceClickListener {
                result = CANCEL
                dismissAllowingStateLoss()
            }
            binding.btnConfirm.setOnAntiViolenceClickListener {
                result = CONFIRM
                dismissAllowingStateLoss()
            }

            if (args.dialogWidth > 0) {
                dialog?.window?.setLayout(args.dialogWidth, windowHeight())
            }
            if (!args.showLeftBtn && args.showRightBtn) {
                binding.btnConfirm.setMargin(left = binding.btnConfirm.marginRight)
            } else if (args.showLeftBtn && !args.showRightBtn) {
                binding.btnCancel.setMargin(right = binding.btnConfirm.marginLeft)
            }
            dialog?.setCancelable(args.isCancelable)
            dialog?.setCanceledOnTouchOutside(args.isCanceledOnTouchOutside)

            if (args.isCancelable && args.isCanceledOnTouchOutside) {
                binding.root.setOnClickListener { dismissAllowingStateLoss() }
            }
        }
    }

    private fun getBackground(it: Context, lightBackground: Boolean): Drawable? {
        return if (lightBackground) {
            ContextCompat.getDrawable(it, R.drawable.selector_button_error)
        } else {
            ContextCompat.getDrawable(it, R.drawable.selector_button_cancel)
        }
    }

    private fun getTextColor(context: Context, defaultColor: Int, isLight: Boolean): Int {
        return ContextCompat.getColor(
            context, when {
                defaultColor > 0 -> {
                    defaultColor
                }

                isLight -> {
                    R.color.common_dialog_confirm_text
                }

                else -> {
                    R.color.common_dialog_cancel_text
                }
            }
        )
    }

    override fun gravity(): Int = Gravity.BOTTOM

    override fun marginHorizontal(context: Context): Int = 0

    override fun getStyle(): Int {
        return if (args.style == 0) {
            R.style.DialogStyleNonFullScreen
        } else {
            args.style
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        setFragmentResultByActivity(REQUEST_KEY, bundleOf(RESULT_KEY to result))
    }

    override fun loadFirstData() {
    }

    override fun windowHeight(): Int {
        return args.height
    }

    class Builder(private val fragment: Fragment? = null) {

        constructor() : this(null)

        private var titleVisible = false
        private var title: String? = null
        private var tips: String? = null
        private var tipsVisible = false
        private var content: String? = null
        private var contentColor: Int? = null
        private var contentGravity: Int? = null
        private var contentSpace: Int? = null

        @DrawableRes
        private var imageId: Int = -1
        private var contentVisible = false
        private var cancelBtnTxt: String? = null
        private var cancelBtnVisible = true
        private var cancelLight = false
        private var cancelTextColor: Int = 0
        private var confirmBtnTxt: String? = null
        private var confirmBtnVisible = true
        private var confirmLight = true
        private var confirmTextColor: Int = 0
        private var isBold: Boolean = false
        private var dialogWidth: Int = -1
        private var isCanceledOnTouchOutside = true
        private var isCancelable = true
        private var isSignOut = false
        private var isRed = false
        private var cancelCallback: (() -> Unit)? = null
        private var confirmCallback: (() -> Unit)? = null
        private var dismissCallback: ((byAction: Int) -> Unit)? = null
        private var style = 0
        private var height = WindowManager.LayoutParams.MATCH_PARENT

        fun dialogWidth(dialogWidth: Int): Builder {
            this.dialogWidth = dialogWidth
            return this
        }

        fun image(@DrawableRes drawableId: Int): Builder {
            this.imageId = drawableId
            return this
        }

        fun content(
            content: String? = null, isVisible: Boolean = true,
            contentColor: Int = R.color.color_1A1A1A,
            contentGravity: Int = Gravity.CENTER, contentSpace: Int = 2.dp
        ): Builder {

            this.content = content
            this.contentVisible = isVisible
            this.contentColor = contentColor
            this.contentGravity = contentGravity
            this.contentSpace = contentSpace
            return this
        }

        fun title(content: String? = null, isVisible: Boolean = true): Builder {
            this.title = content
            this.titleVisible = isVisible
            return this
        }

        fun tips(tips: String? = null, isVisible: Boolean = true): Builder {
            this.tips = tips
            this.tipsVisible = isVisible
            return this
        }

        fun cancelBtnTxt(
            text: String? = null,
            isVisible: Boolean = true,
            lightBackground: Boolean = false,
            textColor: Int = -1
        ): Builder {
            this.cancelBtnTxt = text
            this.cancelBtnVisible = isVisible
            this.cancelLight = lightBackground
            this.cancelTextColor = textColor
            return this
        }

        fun confirmBtnTxt(
            text: String? = null,
            isVisible: Boolean = true,
            lightBackground: Boolean = true,
            textColor: Int = -1,
            isBold: Boolean = false
        ): Builder {
            this.confirmBtnTxt = text
            this.confirmBtnVisible = isVisible
            this.confirmLight = lightBackground
            this.confirmTextColor = textColor
            this.isBold = isBold
            return this
        }

        fun isRed(isRed: Boolean): Builder {
            this.isRed = isRed
            return this
        }

        fun setCancelable(cancel: Boolean): Builder {
            this.isCancelable = cancel
            return this
        }

        fun setCanceledOnTouchOutside(cancel: Boolean): Builder {
            this.isCanceledOnTouchOutside = cancel
            return this
        }

        fun cancelCallback(callback: () -> Unit): Builder {
            this.cancelCallback = callback
            return this
        }

        fun confirmCallback(callback: () -> Unit): Builder {
            this.confirmCallback = callback
            return this
        }

        fun dismissCallback(callback: (byAction: Int) -> Unit): Builder {
            this.dismissCallback = callback
            return this
        }

        fun setStyle(style: Int): Builder {
            this.style = style
            return this
        }

        fun setHeight(height: Int): Builder {
            this.height = height
            return this
        }

        fun navigate(activity: FragmentActivity, tag: String) {
            if (activity.isFinishing) return
            val bundle = getBundle()
            val fragment = ConfirmDialog()
            fragment.arguments = bundle
            val supportFragmentManager = activity.supportFragmentManager
            val old = supportFragmentManager.findFragmentByTag(tag)
            supportFragmentManager.beginTransaction()
                .also {
                    if (old != null) {
                        it.remove(old)
                    }
                }
                .add(fragment, tag)
                .commit()
            setFragmentResult(activity, activity)
        }

        @Deprecated(message = "Use show() instead.", replaceWith = ReplaceWith("show()"))
        fun navigate(navOptions: NavOptions? = null) {
            show()
        }

        fun show() {
            fragment ?: error("fragment is null")
            val activity = fragment.activity ?: return
            if (activity.isFinishing) return
            if (fragment.isDetached) return
            ConfirmDialog().apply {
                arguments = getBundle()
                show(fragment.childFragmentManager, "ConfirmDialog" + System.currentTimeMillis())
            }
            setFragmentResult(activity, fragment)
        }

        private fun getBundle() = ConfirmDialogArgs(
            content = content,
            imageId = imageId,
            leftBtnText = cancelBtnTxt,
            rightBtnText = confirmBtnTxt,
            showTitle = titleVisible,
            title = title,
            showTips = tipsVisible,
            tips = tips,
            showContent = contentVisible,
            showLeftBtn = cancelBtnVisible,
            showRightBtn = confirmBtnVisible,
            leftLightBackground = cancelLight,
            rightLightBackground = confirmLight,
            leftTextColor = cancelTextColor,
            rightTextColor = confirmTextColor,
            isBold = isBold,
            dialogWidth = dialogWidth,
            isCancelable = isCancelable,
            isCanceledOnTouchOutside = isCanceledOnTouchOutside,
            isSignOut = isSignOut,
            isRed = isRed,
            contentGravity = contentGravity ?: Gravity.CENTER,
            contentColor = contentColor ?: R.color.color_1A1A1A,
            contentSpace = contentSpace ?: 2.dp,
            style = style,
            height = height
        ).toBundle()

        private fun setFragmentResult(activity: FragmentActivity, lifecycleOwner: LifecycleOwner) {
            activity.supportFragmentManager.setFragmentResultListener(
                REQUEST_KEY,
                lifecycleOwner,
                { requestKey, result ->
                    if (requestKey == REQUEST_KEY) {
                        when (result.getInt(RESULT_KEY, DISMISS)) {
                            CANCEL -> {
                                Timber.d("LEFT")
                                cancelCallback?.invoke()
                                dismissCallback?.invoke(CANCEL)
                                activity.supportFragmentManager.clearFragmentResult(REQUEST_KEY)
                                activity.supportFragmentManager.clearFragmentResultListener(REQUEST_KEY)
                            }

                            CONFIRM -> {
                                Timber.d("RIGHT")
                                confirmCallback?.invoke()
                                dismissCallback?.invoke(CONFIRM)
                                activity.supportFragmentManager.clearFragmentResult(REQUEST_KEY)
                                activity.supportFragmentManager.clearFragmentResultListener(REQUEST_KEY)
                            }

                            DISMISS -> {
                                Timber.d("DISMISS")
                                dismissCallback?.invoke(DISMISS)
                                activity.supportFragmentManager.clearFragmentResult(REQUEST_KEY)
                                activity.supportFragmentManager.clearFragmentResultListener(REQUEST_KEY)
                            }
                        }
                    }
                }
            )
        }
    }
}