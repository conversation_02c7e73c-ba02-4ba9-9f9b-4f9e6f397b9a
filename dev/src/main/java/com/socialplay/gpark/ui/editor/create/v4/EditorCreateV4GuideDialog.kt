package com.socialplay.gpark.ui.editor.create.v4

import android.graphics.Color
import android.graphics.Point
import android.view.View
import android.view.WindowManager
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.FragmentManager
import com.bumptech.glide.GenericTransitionOptions
import com.bumptech.glide.Glide
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.editor.FormWorkV4Info
import com.socialplay.gpark.databinding.DialogBuildV4CreateGuideBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.ui.base.BaseDialogFragment
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.invisible
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.property.viewBinding

class EditorCreateV4GuideDialog : BaseDialogFragment() {
    enum class CallbackStatus {
        CANCEL, NEXT
    }

    companion object {

        fun show(
            itemView: View,
            fragmentManager: FragmentManager,
            item: FormWorkV4Info?,
            point: Point,
            callback: (CallbackStatus) -> Unit
        ) {
            if (item == null) {
                return
            }
            EditorCreateV4GuideDialog().apply {
                this.itemView = itemView
                this.item = item
                this.callback = callback
                this.point = point
            }.show(fragmentManager, "EditorCreateV4GuideDialog")
        }
    }

    private lateinit var itemView: View
    private lateinit var item: FormWorkV4Info
    private var callback: (CallbackStatus) -> Unit = {}
    private var point: Point = Point()

    override val binding by viewBinding(DialogBuildV4CreateGuideBinding::inflate)

    override fun init() {
        if (!::item.isInitialized || !::itemView.isInitialized) {
            dismiss()
            return
        }
        Analytics.track(EventConstants.BUILD_NEWBIE_GUIDE_BULID_SHOW)
        binding.clMove.updateLayoutParams<ConstraintLayout.LayoutParams> {
            leftMargin = 8.dp
            topMargin = point.y - 16.dp
        }

        Glide.with(this).load(item.formworkImg ?: "")
            .placeholder(R.drawable.placeholder_corner_12)
            .transition(GenericTransitionOptions.withNoTransition())
            .into(binding.ivCover)
        val rate = runCatching {
            item.level?.toFloat() ?: 1.0f
        }.getOrElse {
            3.0f
        }.coerceIn(1.0f, 3.0f)
        binding.ratingbar.rating = rate
        binding.ratingbar.ratingCount = rate.toInt()
        binding.tvTitle.text = item.title

        if (item.tagModel?.name.isNullOrEmpty()) {
            binding.tvTag.invisible()
        } else {
            binding.tvTag.visible()
            binding.tvTag.text = item.tagModel?.name
            item.tagConfig?.let {
                runCatching {
                    binding.tvTag.background.setTint(Color.parseColor(it))
                }
            }
        }

        binding.clMove.setOnAntiViolenceClickListener {
            callback.invoke(CallbackStatus.NEXT)
            dismiss()
        }
        binding.clRoot.setOnAntiViolenceClickListener {
            // 产品要求，点击其他地方没有效果
//            callback.invoke(CallbackStatus.CANCEL)
//            dismiss()
        }
    }

    override fun isBackPressedDismiss(): Boolean {
        return false
    }

    override fun windowHeight(): Int {
        return WindowManager.LayoutParams.MATCH_PARENT
    }

    override fun isFullScreen(): Boolean {
        return true
    }

    override fun loadFirstData() {
    }
}