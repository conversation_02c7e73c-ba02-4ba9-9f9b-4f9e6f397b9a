package com.socialplay.gpark.ui.developer.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.ui.developer.bean.MMKVDB
import com.socialplay.gpark.ui.developer.bean.MMKVItem
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

/**
 * xingxiu.hou
 * 2021/6/7
 */
class DevMMKVManagementViewModel(
    val kv: MetaKV
) : ViewModel() {

    private val _dbListFlow = MutableStateFlow<List<MMKVDB>>(emptyList())
    val dbListFlow: Flow<List<MMKVDB>> = _dbListFlow

    private val _itemListFlow = MutableStateFlow<List<MMKVItem>>(emptyList())
    val itemListFlow: Flow<List<MMKVItem>> = _itemListFlow


    fun getItemList(db: MMKVDB) = viewModelScope.launch {
        _itemListFlow.emit((db.kvRef.allKeys() ?: emptyArray<String>()).map {
            MMKVItem(db, it, null)
        })
    }

    fun getDBList() = viewModelScope.launch {
        _dbListFlow.emit(
            listOf(
                MMKVDB("app", kv.appKV.mmkv),
                MMKVDB("TTai", kv.tTaiKV.mmkv),
            )
        )
    }


    fun removeItem(item: MMKVItem) = viewModelScope.launch {
        val db = item.db
        db.kvRef.remove(item.key)
        _itemListFlow.emit((db.kvRef.allKeys() ?: emptyArray<String>()).map {
            MMKVItem(db, it, null)
        })
    }
}