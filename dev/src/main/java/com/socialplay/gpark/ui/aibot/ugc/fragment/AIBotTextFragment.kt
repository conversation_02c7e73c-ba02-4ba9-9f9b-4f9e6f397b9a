package com.socialplay.gpark.ui.aibot.ugc.fragment


import android.os.Bundle
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.TextView.OnEditorActionListener
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.FragmentAiBotTextBinding
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.ui.aibot.ugc.AIBotUGCCreateViewModel
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.util.InputUtil
import com.socialplay.gpark.util.extension.addTextChangedListener
import com.socialplay.gpark.util.extension.sharedViewModelFromParentFragment
import com.socialplay.gpark.util.property.viewBinding


/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/09/02
 *     desc   :
 *
 */
class AIBotTextFragment : BaseFragment<FragmentAiBotTextBinding>() {

    val viewModel: AIBotUGCCreateViewModel by sharedViewModelFromParentFragment<AIBotUGCCreateViewModel>()

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentAiBotTextBinding? {
        return FragmentAiBotTextBinding.inflate(inflater, container, false)
    }

    override fun init() {
        binding.etInputAi.addTextChangedListener(viewLifecycleOwner){
           viewModel.uploadText(binding.etInputAi.text.toString())
        }
        binding.etInputAi.setOnEditorActionListener(OnEditorActionListener { v, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_NEXT) {
                viewModel.sendText()
                InputUtil.hideKeyboard(binding.etInputAi)
                return@OnEditorActionListener true
            }
            false
        })
        binding.etInputAi.setImeActionLabel(getString(R.string.ai_bot_generate), KeyEvent.KEYCODE_ENTER)
    }

    override fun loadFirstData() {

    }

    override fun getFragmentName(): String = PageNameConstants.FRAGMENT_AI_BOT_IMAGE
}