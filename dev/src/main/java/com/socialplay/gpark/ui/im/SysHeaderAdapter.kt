package com.socialplay.gpark.ui.im

import android.view.LayoutInflater
import android.view.ViewGroup
import com.socialplay.gpark.data.model.SysHeaderInfo
import com.socialplay.gpark.databinding.ViewFriendRequestsHeaderBinding
import com.socialplay.gpark.ui.base.adapter.BaseAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder
import com.socialplay.gpark.ui.core.GlideGetter
import com.socialplay.gpark.util.DateUtil.formatAgoStyleForChat
import com.socialplay.gpark.util.ThreadHelper
import com.socialplay.gpark.util.extension.visible

class SysHeaderAdapter(private val glide: GlideGetter) :
    BaseAdapter<SysHeaderInfo, ViewFriendRequestsHeaderBinding>() {
//
//    override fun onCreateViewHolder(
//        parent: ViewGroup,
//        viewType: Int
//    ): BindingViewHolder<ViewFriendRequestsHeaderBinding> {
//        return super.onCreateViewHolder(parent, viewType).apply {
//            setIsRecyclable(false)
//        }
//    }

    override fun createViewBinding(
        layoutInflater: LayoutInflater,
        parent: ViewGroup,
        viewType: Int
    ): ViewFriendRequestsHeaderBinding {
        return ViewFriendRequestsHeaderBinding.inflate(layoutInflater, parent, false)
    }

    override fun convert(
        holder: BindingViewHolder<ViewFriendRequestsHeaderBinding>,
        item: SysHeaderInfo,
        position: Int
    ) {
        holder.binding.tvPoint.visible(item.unread >= 1)
        holder.binding.tvPoint.text = if (item.unread > 99) "99+" else "${item.unread}"
        holder.binding.tvMessageContent.visible(item.lastMsgSimple?.isNotEmpty() == true)
        glide()?.run {
            ThreadHelper.runOnUiThreadCatching {
                load(item.icon).circleCrop()
                    .into(holder.binding.ivUserAvatar)
            }
        }
        holder.binding.tvUserName.text = item.title
        holder.binding.tvMessageContent.text = item.lastMsgSimple
        holder.binding.tvGo.text = item.lastModifyTime.formatAgoStyleForChat()
    }

}