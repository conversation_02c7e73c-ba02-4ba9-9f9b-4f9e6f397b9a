package com.socialplay.gpark.ui.view.scroll

import android.content.Context
import android.graphics.PointF
import android.util.DisplayMetrics
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.LinearSmoothScroller
import androidx.recyclerview.widget.RecyclerView


/**
 * Created by bo.li
 * Date: 2023/4/20
 * Desc:
 */
class ScrollLinearLayoutManager(context: Context) : LinearLayoutManager(context) {
    private var millisecondsPerInch = 0.5f //修改可以改变数据,越大速度越慢

    override fun smoothScrollToPosition(recyclerView: RecyclerView, state: RecyclerView.State?, position: Int) {
        val linearSmoothScroller: LinearSmoothScroller = object : LinearSmoothScroller(recyclerView.context) {
            override fun computeScrollVectorForPosition(targetPosition: Int): PointF? {
                return <EMAIL>(targetPosition)
            }

            override fun calculateSpeedPerPixel(displayMetrics: DisplayMetrics): Float {
                return millisecondsPerInch / displayMetrics.density //返回滑动一个pixel需要多少毫秒
            }
        }
        linearSmoothScroller.targetPosition = position
        startSmoothScroll(linearSmoothScroller)
    }

    fun setSpeedSlow(x: Float) {
        millisecondsPerInch = x
    }
}