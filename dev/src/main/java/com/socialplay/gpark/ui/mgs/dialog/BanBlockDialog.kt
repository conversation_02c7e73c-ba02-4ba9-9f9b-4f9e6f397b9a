package com.socialplay.gpark.ui.mgs.dialog

import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.view.Gravity
import android.view.LayoutInflater
import android.view.WindowManager
import android.widget.TextView
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.DialogGameBanBlockBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.ui.dialog.GameCreateDialogHelper

/**
 * Created by bo.li
 * Date: 2022/2/18
 * Desc: 游戏中封禁/屏蔽弹窗
 */
class BanBlockDialog(
    private val metaApp: Context,
    private val activity: Activity,
    private val type: String,
    private val reason: String,
): Dialog(activity, android.R.style.Theme_Dialog) {

    companion object {
        const val TYPE_BLOCK = "block"
        const val TYPE_BAN = "ban"
    }

    init {
        initView()
    }

    private fun initDialogParams() {
        setCancelable(false)
        setCanceledOnTouchOutside(false)
        val binding = DialogGameBanBlockBinding.inflate(LayoutInflater.from(metaApp))
        val gravity: Int = Gravity.CENTER
        GameCreateDialogHelper.customInflated(
            activity,
            metaApp,
            this,
            binding.root,
            dimAmount = 0.75F,
            gravity = gravity,
            width = WindowManager.LayoutParams.MATCH_PARENT,
            height = WindowManager.LayoutParams.MATCH_PARENT
        )
    }

    private fun initView() {
        if (window == null) {
            dismiss()
            return
        }
        initDialogParams()
        when (type) {
            TYPE_BAN   -> {
                findViewById<TextView>(R.id.title)?.text = metaApp.getString(R.string.content_account_ban)
                Analytics.track(EventConstants.EVENT_ACCOUNT_IRREGULARITIES_DIALOG_SHOW) {
                    put("reason", reason)
                }
            }
            TYPE_BLOCK -> {
                findViewById<TextView>(R.id.title)?.text = metaApp.getString(R.string.content_account_block)
                Analytics.track(EventConstants.EVENT_USER_BLOCK_DIALOG_SHOW) {
                    put("reason", reason)
                }
            }
            else       -> {
                findViewById<TextView>(R.id.title)?.text = ""
            }
        }
        findViewById<TextView>(R.id.btnLeft)?.setOnClickListener {
            dismiss()
        }
        findViewById<TextView>(R.id.btnRight)?.setOnClickListener {
            dismiss()
        }
    }
}