package com.socialplay.gpark.ui.friend.contacts

import androidx.lifecycle.*
import androidx.lifecycle.Observer
import com.meta.box.biz.friend.FriendBiz
import com.meta.box.biz.friend.internal.model.toLocalStatus
import com.meta.box.biz.friend.model.FriendInfo
import com.meta.lib.api.resolve.data.model.DataResult
import com.socialplay.gpark.EnvConfig
import com.socialplay.gpark.data.interactor.FriendInteractor
import com.socialplay.gpark.util.extension.displayName
import com.socialplay.gpark.util.extension.friendListIndexChar
import com.socialplay.gpark.util.extension.pinyin
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine

class ContactListViewModel(
    private val friendInteractor: FriendInteractor,
) : ViewModel() {


    val friendListRefreshStatusFlow: StateFlow<DataResult<Boolean>> get() = FriendBiz.friendListLoadState

    val friendListFlow = friendInteractor.aiBotFollowList.asFlow()
        .combine(friendInteractor.friendList.asFlow()) { a, b -> sortFriendList(a + b) }


    fun loadFriendList() {
        friendInteractor.refreshFriendsAsync()
        friendInteractor.refreshAiBotFollowAsync()
    }

    fun loadUnreadFriendRequestsCount() {
        friendInteractor.refreshFriendsUnreadRequestsAsync()
    }

    private fun sortFriendList(list: Collection<FriendInfo>): List<FriendInfo> {
        val enablePinyin = EnvConfig.isParty()
        val bigChar = 'Z' + 1
        // 默认字符串排序规则是按照编码顺序来的, 由于'#'的ASCII码排在'A'前面
        // 为了让特殊名字的用户排在后面, 这里用 bigChar 进行替代
        return list.sortedWith(compareBy({
            if (enablePinyin) {
                it.displayName.pinyin("$bigChar")
            } else {
                val ch = it.displayName.friendListIndexChar(bigChar)
                "$ch${it.displayName}"
            }
        }, { it.status.toLocalStatus() }))
    }

    override fun onCleared() {

        super.onCleared()
    }
}
