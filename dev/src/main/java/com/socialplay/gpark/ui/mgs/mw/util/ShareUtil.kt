package com.socialplay.gpark.ui.mgs.mw.util

import android.app.Activity
import android.app.Application
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import com.meta.biz.mgs.data.model.MgsGameShareResult
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.GameDetailInfo
import com.socialplay.gpark.function.mgs.MgsConstants
import com.socialplay.gpark.function.mgs.MgsDialogManager
import com.socialplay.gpark.function.share.ShareWrapper
import com.socialplay.gpark.util.ToastUtil


/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/05/28
 *     desc   :
 *
 */
object ShareUtil {
    fun shareRoomInfo(
        shareInfo: MgsGameShareResult?,
        type: String,
        gameInfo: GameDetailInfo?,
        metaApp: Context,
        activity: Activity?
    ) {
        if (shareInfo == null) {
            ToastUtil.gameShowShort(metaApp.getString(R.string.mgs_room_un_create))
            return
        }

        when (type) {
            MgsConstants.TYPE_SYSTEM    -> {
                if (activity != null) {
                    shareBySystem(activity, shareInfo)
                }
            }

            MgsConstants.TYPE_COPY_LINK -> {
                if (activity != null) {
                    shareByClipboard(activity, shareInfo, metaApp)
                }
            }
            MgsConstants.TYPE_FACE_TO_FACE -> {
                if (shareInfo.jumpUrl.isNullOrEmpty()) {
                    ToastUtil.gameShowShort(metaApp.getString(R.string.create_share_info_failed))
                    return
                }
                if (activity != null) {
                    MgsDialogManager.showQrCodeDialog(
                        shareInfo.jumpUrl.orEmpty(),
                        shareInfo.content?.roomShowNum ?: "",
                        activity,
                        metaApp,
                        gameInfo
                    )
                }
            }
        }
    }
    private fun shareBySystem(activity: Activity, shareInfo: MgsGameShareResult) {
        val intent = Intent(Intent.ACTION_SEND)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        intent.putExtra(Intent.EXTRA_TEXT, shareInfo.jumpUrl)
        intent.type = "text/plain"
        if (intent.resolveActivity(activity.packageManager) != null) {
            activity.startActivity(intent)
        }
    }

    private fun shareByClipboard(
        activity: Activity,
        shareInfo: MgsGameShareResult,
        metaApp: Context
    ) {
        val clipboard = activity.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        clipboard.setPrimaryClip(ClipData.newPlainText("ShareLink", shareInfo.jumpUrl))
        ToastUtil.gameShowShort(metaApp.getString(R.string.msg_invite_clipboard_link_is_copied))
    }

}