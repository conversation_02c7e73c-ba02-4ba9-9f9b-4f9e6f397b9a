package com.socialplay.gpark.ui.post.comment

import com.socialplay.gpark.util.extension.NavParams

data class ArticleCommentInputDialogParams(
    val hint: String?,
    val resId: String?,
    val gameCircleName: String?,
    val type: Int,
    val dimAmount: Float,
    val showEmoji: <PERSON><PERSON><PERSON>,
    val showImage: <PERSON><PERSON><PERSON>,
    val requestKey: String,
    val contentType: Int,
    val imageCount: Int,
    val acceptImageOnly: <PERSON>ole<PERSON>,
    val pageName: String,
) : NavParams {

    companion object {
        const val CONTENT_TYPE_POST_DETAIL = 1
        const val CONTENT_TYPE_PGC_DETAIL = 2
        const val CONTENT_TYPE_UGC_DETAIL = 3
        const val CONTENT_TYPE_UGC_DESIGN_DETAIL = 4
    }

    val isPostDetail get() = contentType == CONTENT_TYPE_POST_DETAIL
    val isPgcDetail get() = contentType == CONTENT_TYPE_PGC_DETAIL
    val isUgcDetail get() = contentType == CONTENT_TYPE_UGC_DETAIL
    val isUgcDesignDetail get() = contentType == CONTENT_TYPE_UGC_DESIGN_DETAIL

    val isMap get() = isPgcDetail || isUgcDetail
}