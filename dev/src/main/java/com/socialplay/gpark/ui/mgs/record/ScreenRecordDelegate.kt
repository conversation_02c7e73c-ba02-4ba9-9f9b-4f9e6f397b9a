package com.socialplay.gpark.ui.mgs.record

import android.Manifest
import android.app.Activity
import android.app.Application
import android.content.Intent
import android.net.Uri
import android.view.WindowManager
import com.bin.cpbus.CpEventBus
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.event.ScreenRecordUserActionEvent
import com.socialplay.gpark.function.mgs.MgsDialogManager
import com.socialplay.gpark.function.record.ScreenRecordAnalytics
import com.socialplay.gpark.function.record.ScreenRecordReceiver
import com.socialplay.gpark.function.record.ScreenRecordUtil
import com.socialplay.gpark.ui.mgs.listener.OnMgsRecordListener
import com.socialplay.gpark.ui.permission.GamePermissionActivity
import com.socialplay.gpark.ui.permission.PermissionRequest
import com.socialplay.gpark.util.PackageUtil
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 * @des:
 * @author: lijunjia
 * @date: 2022/9/21 17:43
 */
class ScreenRecordDelegate(
    private val metaApp: Application, private val gameApp: Application, private val mGameId:String,
    private val packageName: String, private val onMgsRecordListener: OnMgsRecordListener,
    private val recordView: MgsRecordView, private val getGameName: () -> String
) {
    private val metaKv = GlobalContext.get().get<MetaKV>()

    private var mCurrentActivity: Activity? = null
    var isRecording: Boolean = false

    var widthRecord: Int = WindowManager.LayoutParams.WRAP_CONTENT
    var heightRecord: Int = WindowManager.LayoutParams.WRAP_CONTENT

    init {
        postRecordUserActionEvent(ScreenRecordUserActionEvent.ACTION_STOP_RECORD, false)
    }


    fun registerRecordReceiver() {
        ScreenRecordReceiver.registerRecordReceiver(gameApp, packageName, recordActionCallback)
    }

    fun stopRecord(showEndDialog: Boolean = true) {
        hideRecordView()
        ScreenRecordAnalytics.eventRecordViewClick(mGameId)
        postRecordUserActionEvent(ScreenRecordUserActionEvent.ACTION_STOP_RECORD, showEndDialog)
    }

    fun checkStopRecord() {
        if (isRecording) {
            postRecordUserActionEvent(ScreenRecordUserActionEvent.ACTION_STOP_RECORD, false)
        }
    }

    private fun showRecordView() {
        widthRecord = WindowManager.LayoutParams.WRAP_CONTENT
        heightRecord = WindowManager.LayoutParams.WRAP_CONTENT
        onMgsRecordListener.onShowRecordView()
    }

    private fun hideRecordView() {
        widthRecord = 1
        heightRecord = 1
        onMgsRecordListener.onHideRecordView()
    }

    fun checkSwitchAudioRecord() {
        val isRecordAudio = metaKv.screenRecordKV.isRecordAudio
        if (isRecordAudio) {
            switchAudioRecord(false)
        } else {
            val hasPermission = PermissionRequest.checkSelfPermission(metaApp, Manifest.permission.RECORD_AUDIO)
            if (hasPermission) {
                switchAudioRecord(true)
            } else {
                GamePermissionActivity.start(gameApp, packageName, mGameId, PackageUtil.getAppName(gameApp), GamePermissionActivity.LOGIC_FROM_SCREEN_RECORD_AUDIO)
            }
        }
    }

    private fun switchAudioRecord(isRecordAudio: Boolean) {
        postRecordUserActionEvent(if (isRecordAudio) ScreenRecordUserActionEvent.ACTION_OPEN_AUDIO else ScreenRecordUserActionEvent.ACTION_CLOSE_AUDIO)
        ScreenRecordUtil.changeRecordAudio(isRecordAudio, mGameId)
        recordView.updateVoiceRecordState()
    }


    fun onActivityResumed(activity: Activity) {
        this.mCurrentActivity = activity

    }


    private val recordActionCallback = object : ScreenRecordReceiver.RecordActionCallback {
        override fun onRecordAction(actionType: Int, intent: Intent) {
            when (actionType) {
                ScreenRecordReceiver.ACTION_TYPE_START_COUNT_DOWN -> {
                    showCountDownDialog()
                }
                ScreenRecordReceiver.ACTION_TYPE_BEFORE_START -> {
                    onBeforeStartRecord()
                }
                ScreenRecordReceiver.ACTION_TYPE_STARTED -> {
                    isRecording = true
                    onStartRecord()
                }
                ScreenRecordReceiver.ACTION_TYPE_START_FAILED -> {
                    onStartRecordFailed()
                }
                ScreenRecordReceiver.ACTION_TYPE_END_RECORD -> {
                    isRecording = false
                    onEndRecord()
                }
                ScreenRecordReceiver.ACTION_TYPE_AFTER_SAVE -> {
                    val filePath = intent.getStringExtra(ScreenRecordReceiver.EXTRA_FILE_PATH)
                    val fileUri = intent.getParcelableExtra<Uri?>(ScreenRecordReceiver.EXTRA_FILE_URI)
                    val showEndDialog = intent.getBooleanExtra(ScreenRecordReceiver.EXTRA_SHOW_END_DIALOG, true)
                    if (filePath != null && fileUri != null) {
                        onAfterSaveRecord(filePath, fileUri, showEndDialog)
                    }
                }
            }
        }

    }

    private fun onAfterSaveRecord(saveFilePath: String, saveFileUri: Uri, showResult: Boolean) {
        Timber.d("onAfterSaveRecord path:$saveFilePath")
        ScreenRecordAnalytics.eventStopRecordSuccess(mGameId)
        recordView.resetChronometer()
        hideRecordView()
        if (showResult) {
            showRecordEndDialog(saveFilePath, saveFileUri)
        }
    }

    private fun showRecordEndDialog(filePath: String, fileUri: Uri) {
        mCurrentActivity?.let {
            ScreenRecordAnalytics.eventRecordFinishDialogShow(mGameId)
            MgsDialogManager.showRecordEndDialog(
                filePath,
                fileUri,
                gameId = mGameId,
                gamePackageName = packageName,
                gameName = getGameName.invoke(),
                activity = it,
                metaApp
            ) {

            }
        }
    }


    fun onEndRecord() {
        Timber.d("game assistant onEndRecord")
        recordView.onEndRecord()
    }

    fun onBeforeStartRecord() {
        Timber.d("game assistant onBeforeStartRecord")
        showRecordView()
    }

    fun onStartRecord() {
        Timber.d("game assistant onStartRecordSuccess")
        ScreenRecordAnalytics.eventStartRecordSuccess(mGameId)
        recordView.onStartRecord()
    }

    fun onStartRecordFailed() {
        Timber.d("game assistant onStartRecordFailed")
        hideRecordView()
    }

    /**
     * 由于是在主进程进行录屏的所以通过
     */
    private fun postRecordUserActionEvent(action: Int, showEndDialog: Boolean = true) {
        val screenRecordUserActionEvent = ScreenRecordUserActionEvent(action)
        screenRecordUserActionEvent.showEndDialog = showEndDialog
        CpEventBus.post(screenRecordUserActionEvent)
    }

    private fun showCountDownDialog() {
        checkStopRecord()
        mCurrentActivity?.let {
            ScreenRecordAnalytics.eventCountDownDialogShow(mGameId)
            MgsDialogManager.showStartRecordCountDownDialog(metaApp, it) {
                Timber.d("count down end,start record")
                postRecordUserActionEvent(ScreenRecordUserActionEvent.ACTION_START_RECORD)
            }
        }
    }
}