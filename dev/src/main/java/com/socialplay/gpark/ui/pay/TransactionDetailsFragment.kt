package com.socialplay.gpark.ui.pay

import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.fragmentViewModel
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.FragmentTransactionDetailsBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.core.views.empty
import com.socialplay.gpark.ui.core.views.endOfListFooter
import com.socialplay.gpark.ui.core.views.loadMoreFooter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.core.views.spacer
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.navigateUp

class TransactionDetailsFragment :
    BaseFragment<FragmentTransactionDetailsBinding>(R.layout.fragment_transaction_details) {
    private val vm: TransactionDetailsViewModel by fragmentViewModel()

    private val transactionDetailsController by lazy { buildTransactionDetailsController() }

    override val destId: Int = R.id.transactionDetailsPage

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentTransactionDetailsBinding? {
        return FragmentTransactionDetailsBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Analytics.track(
            EventConstants.EVENT_CLIENT_WALLET_DETAIL_SHOW,
        )
        binding.tblTitleBar.setOnBackClickedListener {
            navigateUp()
        }
        binding.loadingView.setRetry {
            vm.getList(true)
        }
        binding.rv.setController(transactionDetailsController)
        vm.onAsync(TransactionDetailsState::coinsRecordList, onFail = { _ ->
            binding.loadingView.showError()
        }, onSuccess = {
            binding.loadingView.hide()
        })

        vm.setupRefreshLoading(
            TransactionDetailsState::coinsRecordList,
            binding.loadingView,
            binding.refreshLayout,
            emptyMsg = getString(R.string.transaction_details_page_no_transactions)
        ) {
            vm.getList(true)
        }
        vm.getList(true)
    }

    private fun buildTransactionDetailsController() = simpleController(
        vm,
        TransactionDetailsState::refreshFlag,
        TransactionDetailsState::coinsRecordList,
        TransactionDetailsState::loadMore,
    ) { refreshFlag, coinsRecordList, loadMore ->
        if (coinsRecordList is Success) {
            val coinsRecordListData = coinsRecordList.invoke()
            val lastCommentPosition = coinsRecordListData.size - 1
            coinsRecordListData.forEachIndexed { index, coinsRecord ->
                if (index == 0) {
                    spacer(height = 16.dp, idStr = "Spacer-first-$refreshFlag")
                }
                add {
                    TransactionDetailsItem(coinsRecord)
                        .id("TransactionDetailsItem-$refreshFlag-${coinsRecord.id}")
                }
                if (index in 0 until lastCommentPosition) {
                    spacer(height = 8.dp, idStr = "Spacer-$refreshFlag-${coinsRecord.id}")
                }
            }
            if (coinsRecordListData.isNotEmpty() && loadMore.invoke()?.isEnd == true) {
                spacer(height = 16.dp, idStr = "$refreshFlag")
                endOfListFooter(idStr = "$refreshFlag")
            }
        }
        if (loadMore !is Uninitialized) {
            if (coinsRecordList is Success
                && loadMore is Success
                && coinsRecordList.invoke().isEmpty()
            ) {
                empty(
                    iconRes = R.drawable.icon_no_recent_activity,
                    descRes = R.string.transaction_details_page_no_transactions,
                    desc = null,
                    top = 26.dp,
                    height = ViewGroup.LayoutParams.WRAP_CONTENT,
                    gravity = Gravity.CENTER_HORIZONTAL
                )
            } else if (coinsRecordList is Success && coinsRecordList.invoke().isNotEmpty()) {
                loadMoreFooter(loadMore, idStr = "LoadMoreFooter-$refreshFlag", endText = "") {
                    vm.getList(false)
                }
            }
        }
    }

    override fun invalidate() {}

    override fun getPageName() = PageNameConstants.FRAGMENT_TRANSACTION_DETAILS
}