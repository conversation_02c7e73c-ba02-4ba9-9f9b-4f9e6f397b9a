package com.socialplay.gpark.ui.view

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.widget.FrameLayout
import com.socialplay.gpark.R
import kotlin.math.cos
import kotlin.math.sin
import kotlin.math.sqrt

/**
 * A FrameLayout that draws a gradient border around its contents.
 * The center of this layout is transparent, allowing content to be seen.
 */
class GradientBorderFrameLayout @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private val borderPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val borderRect = RectF()
    private var borderWidthValue: Float
    private var cornerRadiusValue: Float
    private var lightAngleValue: Float
    private var highlightColorValue: Int
    private var shadowColorValue: Int

    init {
        // ViewGroup 默认不执行 onDraw, 需要设置为 false
        setWillNotDraw(false)

        val a = context.obtainStyledAttributes(attrs, R.styleable.GradientBorderFrameLayout, defStyleAttr, 0)
        borderWidthValue = a.getDimension(R.styleable.GradientBorderFrameLayout_borderWidth, 4f)
        cornerRadiusValue = a.getDimension(R.styleable.GradientBorderFrameLayout_cornerRadius, 0f)
        // 角度约定：0度为右方，90度为下方，180度为左方，270度为上方。
        // 用户说的"左上角45度"照射过来，即光线来自225度方向或指向45度方向。
        // 我们这里定义 lightAngle 为光线指向的方向。
        lightAngleValue = a.getFloat(R.styleable.GradientBorderFrameLayout_lightAngle, 45f)
        highlightColorValue = a.getColor(R.styleable.GradientBorderFrameLayout_highlightColor, Color.WHITE)
        shadowColorValue = a.getColor(R.styleable.GradientBorderFrameLayout_shadowColor, Color.DKGRAY)
        a.recycle()

        borderPaint.style = Paint.Style.STROKE
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        drawBorder(canvas)
    }

    private fun drawBorder(canvas: Canvas) {
        borderPaint.strokeWidth = borderWidthValue
        borderRect.set(
            borderWidthValue / 2,
            borderWidthValue / 2,
            width - borderWidthValue / 2,
            height - borderWidthValue / 2
        )

        updateGradientShader()

        canvas.drawRoundRect(borderRect, cornerRadiusValue, cornerRadiusValue, borderPaint)
    }

    private fun updateGradientShader() {
        if (width == 0 || height == 0) return

        val w = width.toFloat()
        val h = height.toFloat()
        val cx = w / 2
        val cy = h / 2

        // 定义渐变方向
        // 光源方向和渐变方向一致，从阴影色到高光色
        val angleRad = Math.toRadians(lightAngleValue.toDouble())

        // 为了确保渐变能完全覆盖整个View，我们使用View对角线长度作为渐变线段的长度
        val length = sqrt(w * w + h * h)

        val startX = (cx - cos(angleRad) * length / 2).toFloat()
        val startY = (cy - sin(angleRad) * length / 2).toFloat()
        val endX = (cx + cos(angleRad) * length / 2).toFloat()
        val endY = (cy + sin(angleRad) * length / 2).toFloat()

        // 渐变从阴影色开始，到高光色结束
        borderPaint.shader = LinearGradient(
            startX, startY,
            endX, endY,
            shadowColorValue,
            highlightColorValue,
            Shader.TileMode.CLAMP
        )
    }

    // --- Public methods for dynamic adjustment ---

    fun setLightAngle(angle: Float) {
        lightAngleValue = angle
        invalidate() // 重新绘制
    }

    fun setBorderWidth(widthInPx: Float) {
        borderWidthValue = widthInPx
        invalidate()
    }

    fun setCornerRadius(radiusInPx: Float) {
        cornerRadiusValue = radiusInPx
        invalidate()
    }

    fun setHighlightColor(color: Int) {
        highlightColorValue = color
        invalidate()
    }

    fun setShadowColor(color: Int) {
        shadowColorValue = color
        invalidate()
    }
} 