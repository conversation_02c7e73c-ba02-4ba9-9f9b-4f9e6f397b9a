package com.socialplay.gpark.ui.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.RectF
import android.graphics.Shader
import android.util.AttributeSet
import android.widget.FrameLayout
import com.socialplay.gpark.R

/**
 * A FrameLayout that draws a gradient border around its contents.
 * The center of this layout is transparent, allowing content to be seen.
 */
class GradientBorderFrameLayout @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private val borderPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.FILL
    }

    private val maskPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        xfermode = PorterDuffXfermode(PorterDuff.Mode.DST_OUT)
    }

    private var cornerRadius = 0f
    private var borderWidth = 0f

    private val viewBounds = RectF()
    private val borderBounds = RectF()

    private var startColor: Int = Color.WHITE
    private var endColor: Int = Color.BLACK

    init {
        // This is crucial for dispatchDraw to be called for a layout.
        setWillNotDraw(false)

        val a = context.obtainStyledAttributes(attrs, R.styleable.GradientBorderFrameLayout, defStyleAttr, 0)
        startColor = a.getColor(R.styleable.GradientBorderFrameLayout_gb_startColor, Color.WHITE)
        endColor = a.getColor(R.styleable.GradientBorderFrameLayout_gb_endColor, Color.BLACK)
        borderWidth = a.getDimension(R.styleable.GradientBorderFrameLayout_gb_strokeWidth, 4f)
        cornerRadius = a.getDimension(R.styleable.GradientBorderFrameLayout_gb_cornerRadius, 0f)
        a.recycle()
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        if (w > 0 && h > 0) {
            viewBounds.set(0f, 0f, w.toFloat(), h.toFloat())
            borderBounds.set(viewBounds)
            val gradient = LinearGradient(
                0f, 0f, 0f, h.toFloat(),
                startColor, endColor,
                Shader.TileMode.CLAMP
            )
            borderPaint.shader = gradient
        }
    }

    override fun dispatchDraw(canvas: Canvas) {
        // Using saveLayer is crucial for the xfermode to work correctly.
        val save = canvas.saveLayer(viewBounds, null)

        // 1. Draw the outer gradient shape which will become the border.
        canvas.drawRoundRect(borderBounds, cornerRadius, cornerRadius, borderPaint)

        // 2. "Punch a hole" in the middle using the mask paint.
        // This erases the center of the gradient shape, leaving only the border.
        val innerRadius = (cornerRadius - borderWidth).coerceAtLeast(0f)
        canvas.drawRoundRect(
            borderWidth,
            borderWidth,
            width - borderWidth,
            height - borderWidth,
            innerRadius,
            innerRadius,
            maskPaint
        )

        canvas.restoreToCount(save)

        // 3. Now, let the children (the content) draw themselves into the transparent area.
        super.dispatchDraw(canvas)
    }
} 