package com.socialplay.gpark.ui.editor.bannerv2

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.UniJumpConfig
import com.socialplay.gpark.databinding.AdapterUgcBannerV3Binding
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setMargin
import com.youth.banner.adapter.BannerAdapter

class UgcBannerV3Adapter(
    data: MutableList<UniJumpConfig>? = null,
    private val marginHorizontalDp: Int = 16
) : BannerAdapter<UniJumpConfig, UgcBannerV3Adapter.ViewHolder>(data) {

    fun updateData(data: List<UniJumpConfig>?) {
        //这里的代码自己发挥，比如如下的写法等等
        mDatas.clear()
        data?.let { mDatas.addAll(it) }
        notifyDataSetChanged()
    }

    override fun onCreateHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding =
            AdapterUgcBannerV3Binding.inflate(LayoutInflater.from(parent.context), parent, false)
        binding.iv.layoutParams = RecyclerView.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
        return ViewHolder(binding)
    }

    override fun onBindView(holder: ViewHolder, data: UniJumpConfig, position: Int, size: Int) {
        holder.bind(data)
    }

    inner class ViewHolder(private val binding: AdapterUgcBannerV3Binding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(info: UniJumpConfig) {
            val margin = dp(marginHorizontalDp)
            binding.iv.setMargin(left = margin, right = margin)
            Glide.with(itemView)
                .load(info.iconUrl)
                .placeholder(R.color.color_placeholder)
                .into(binding.iv)
        }
    }
}