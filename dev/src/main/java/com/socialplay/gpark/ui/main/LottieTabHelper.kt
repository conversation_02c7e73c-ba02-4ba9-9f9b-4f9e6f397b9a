package com.socialplay.gpark.ui.main

import android.animation.ValueAnimator
import android.view.View
import androidx.core.view.isVisible
import androidx.lifecycle.LifecycleOwner
import com.airbnb.lottie.LottieAnimationView
import com.google.android.material.tabs.TabLayout
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.ViewHomeBottomTabLottieBinding
import timber.log.Timber

/**
 * Lottie动画Tab辅助工具类
 * 负责管理底部导航栏中的Lottie动画功能，支持动画进度控制和红点逻辑
 */
object LottieTabHelper {

    /**
     * 设置Lottie Tab的初始状态
     */
    fun setupLottieTab(
        binding: ViewHomeBottomTabLottieBinding,
        item: MainBottomNavigationItem,
        lifecycleOwner: LifecycleOwner,
        isSelected: Boolean = false
    ) {
        try {
            // 设置标题
            binding.tvTitle.setText(item.titleRes)

            // 配置LottieAnimationView
            with(binding.lavIcon) {
                // 设置动画资源
                setAnimation(item.lottieRes)

                // 配置生命周期管理
                if (lifecycleOwner is androidx.fragment.app.Fragment) {
                    lifecycleOwner.lifecycle.addObserver(object : androidx.lifecycle.DefaultLifecycleObserver {
                        override fun onPause(owner: LifecycleOwner) {
                            pauseAnimation()
                        }

                        override fun onResume(owner: LifecycleOwner) {
                            if (isAnimating) {
                                resumeAnimation()
                            }
                        }

                        override fun onDestroy(owner: LifecycleOwner) {
                            cancelAnimation()
                        }
                    })
                }

                // 设置初始状态
                progress = if (isSelected) item.lottieSelectedProgress else item.lottieDefaultProgress
                pauseAnimation()
            }

            // 设置传统图标作为降级方案
            binding.ivIcon.setImageResource(item.iconRes)

            // 显示Lottie动画，隐藏传统icon
            binding.ivIcon.isVisible = false
            binding.lavIcon.isVisible = true

        } catch (e: Exception) {
            Timber.w(e, "Failed to setup lottie tab, falling back to traditional icon")
            // 降级处理：隐藏Lottie，显示传统icon
            binding.ivIcon.isVisible = true
            binding.lavIcon.isVisible = false
            binding.ivIcon.setImageResource(item.iconRes)
        }
    }

    /**
     * 处理Tab状态变化（选中/取消选中）
     */
    fun handleTabStateChange(
        binding: ViewHomeBottomTabLottieBinding,
        item: MainBottomNavigationItem,
        isSelected: Boolean
    ) {
        try {
            with(binding.lavIcon) {
                cancelAnimation() // 先停止当前动画

                if (isSelected) {
                    // 选中状态
                    setAnimation(item.lottieRes)

                    if (item.playSelectedAnimation) {
                        // 播放选中动画
                        repeatCount = if (item.loopSelectedAnimation) ValueAnimator.INFINITE else 0
                        playAnimation()
                    } else {
                        // 直接跳到选中状态进度
                        progress = item.lottieSelectedProgress
                        pauseAnimation()
                    }
                } else {
                    // 未选中状态
                    setAnimation(item.lottieRes)
                    progress = item.lottieDefaultProgress
                    pauseAnimation()
                }
            }
        } catch (e: Exception) {
            Timber.w(e, "Failed to handle lottie tab state change")
            // 降级处理：切换传统icon
            binding.ivIcon.setImageResource(item.iconRes)
        }
    }

    /**
     * 处理Lottie Tab的选中状态变化（TabLayout版本）
     */
    fun handleTabStateChange(
        item: MainBottomNavigationItem,
        tab: TabLayout.Tab,
        isSelected: Boolean,
        lifecycleOwner: LifecycleOwner? = null
    ) {
        if (!item.enableLottie) return

        val customView = tab.customView ?: return

        try {
            val binding = ViewHomeBottomTabLottieBinding.bind(customView)
            handleTabStateChange(binding, item, isSelected)
        } catch (e: Exception) {
            Timber.w(e, "Failed to handle lottie tab state change via TabLayout")
        }
    }

    /**
     * 设置未读消息数量（红点显示）
     * 支持Lottie动画Tab的红点逻辑
     */
    fun setUnReadCount(
        binding: ViewHomeBottomTabLottieBinding,
        count: Int
    ) {
        // 显示/隐藏红点
        val shouldShow = count > 0

        if (shouldShow) {
            // 红点显示逻辑（这个布局中的红点是View，不是TextView，所以不设置文字）
            if (!binding.viewUnReadCount.isVisible) {
                // 红点出现动画
                binding.viewUnReadCount.isVisible = true
                binding.viewUnReadCount.scaleX = 0f
                binding.viewUnReadCount.scaleY = 0f
                binding.viewUnReadCount.animate()
                    .scaleX(1f)
                    .scaleY(1f)
                    .setDuration(200)
                    .start()
            }
        } else {
            if (binding.viewUnReadCount.isVisible) {
                // 红点消失动画
                binding.viewUnReadCount.animate()
                    .scaleX(0f)
                    .scaleY(0f)
                    .setDuration(150)
                    .withEndAction {
                        binding.viewUnReadCount.isVisible = false
                    }
                    .start()
            }
        }
    }

    /**
     * 设置Tab的红点显示状态（TabLayout版本）
     */
    fun setUnReadCount(tab: TabLayout.Tab, count: Int): Boolean {
        val customView = tab.customView ?: return false

        try {
            val binding = ViewHomeBottomTabLottieBinding.bind(customView)
            setUnReadCount(binding, count)
            return true
        } catch (e: Exception) {
            // 降级到传统红点逻辑
            val unReadCountView = customView.findViewById<View>(R.id.viewUnReadCount)
            return if (unReadCountView != null) {
                unReadCountView.isVisible = count > 0
                true
            } else {
                false
            }
        }
    }

    /**
     * 创建Lottie动画的MainBottomNavigationItem便捷方法
     */
    fun createLottieItem(
        itemId: Int,
        titleRes: Int,
        lottieRes: String,
        clickSource: String,
        fragmentFactory: () -> androidx.fragment.app.Fragment,
        iconRes: Int = 0, // 降级时使用的图标
        iconResDark: Int = 0,
        lottieDefaultProgress: Float = 0f,
        lottieSelectedProgress: Float = 1f,
        playSelectedAnimation: Boolean = true,
        loopSelectedAnimation: Boolean = false
    ): MainBottomNavigationItem {
        return MainBottomNavigationItem(
            itemId = itemId,
            titleRes = titleRes,
            iconRes = iconRes,
            iconResDark = iconResDark,
            clickSource = clickSource,
            factory = fragmentFactory,
            enableLottie = true,
            lottieRes = lottieRes,
            lottieDefaultProgress = lottieDefaultProgress,
            lottieSelectedProgress = lottieSelectedProgress,
            playSelectedAnimation = playSelectedAnimation,
            loopSelectedAnimation = loopSelectedAnimation
        )
    }
} 