package com.socialplay.gpark.ui.kol.list.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.creator.CreatorUgcGame
import com.socialplay.gpark.databinding.AdapterKolCreatorWorkBinding
import com.socialplay.gpark.ui.base.adapter.BaseVBViewHolder
import com.socialplay.gpark.ui.base.adapter.BasicQuickAdapter
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.backgroundTintListByColorStr
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setWidth

/**
 * Created by bo.li
 * Date: 2024/8/5
 * Desc: Kol创作者作品列表
 */
class KolCreatorUgcAdapter(
    data: MutableList<CreatorUgcGame>,
    private val itemWidth: Int,
    private val glide: RequestManager
) :
    BasicQuickAdapter<CreatorUgcGame, AdapterKolCreatorWorkBinding>(data) {

    companion object {
        const val DEFAULT_TAG_BG = "#FF5F42"
    }

    override fun convert(
        holder: BaseVBViewHolder<AdapterKolCreatorWorkBinding>,
        item: CreatorUgcGame
    ) {
        holder.binding.tvTagNew.maxWidth = itemWidth - 8.dp
        holder.binding.spaceEnd.setWidth(10.dp)
        holder.binding.ivIcon.setWidth(itemWidth)
        glide.load(item.banner).placeholder(R.drawable.placeholder_corner_12)
            .into(holder.binding.ivIcon)
        glide.load(item.userIcon).placeholder(R.drawable.icon_default_avatar)
            .into(holder.binding.ivAuthorAvatar)
        holder.binding.tvName.text = item.ugcGameName
        holder.binding.tvAuthorName.text = item.userName
        holder.binding.tvHeat.text = UnitUtil.formatPlayerCount(item.pvCount)
        holder.binding.tvTagNew.isVisible = item.localLabel != null
        holder.binding.tvTagNew.text = item.localLabel?.labelName
        runCatching {
            holder.binding.tvTagNew.backgroundTintListByColorStr(item.localLabel?.backgroundColor.orEmpty())
        }.getOrElse {
            holder.binding.tvTagNew.backgroundTintListByColorStr(DEFAULT_TAG_BG)
        }
    }

    override fun viewBinding(parent: ViewGroup, viewType: Int): AdapterKolCreatorWorkBinding {
        return AdapterKolCreatorWorkBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    }

}