package com.socialplay.gpark.ui.account.startup

import android.app.Application
import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.account.AgeOption
import com.socialplay.gpark.data.model.editor.DefaultRoleInfo
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.data.interactor.EditorGameLoadInteractor
import com.socialplay.gpark.util.DateUtil
import com.socialplay.gpark.util.extension.ifNullOrBlank
import kotlinx.coroutines.flow.map
import org.koin.android.ext.android.get
import java.util.Calendar

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/11/10
 *     desc   :
 * </pre>
 */
data class SelectBirthdayState(
    val age: Int = -1,
    val ageOptions: List<AgeOption> = DateUtil.getDefaultAgeOptions(),
    val updateProfileResult: Async<Boolean> = Uninitialized,
    val chooseRoleResult: Async<Boolean> = Uninitialized
) : MavericksState
class SelectBirthdayViewModel(
    private val context: Application,
    private val metaKV: MetaKV,
    private val accountInteractor: AccountInteractor,
    private val editorGameLoadInteractor: EditorGameLoadInteractor,
    initialState: SelectBirthdayState
) : BaseViewModel<SelectBirthdayState>(initialState) {

    fun updateProfile(name: String, avatar: DefaultRoleInfo, gender: Int? = null) = withState { s ->
        if (s.updateProfileResult.shouldLoad) {
            val calendar = Calendar.getInstance()
            calendar.set(Calendar.MONTH, 0)
            calendar.set(Calendar.DATE, 1)
            val birthYear = calendar.get(Calendar.YEAR) - s.age
            calendar.set(Calendar.YEAR, birthYear)
            Analytics.track(
                EventConstants.BIRTHDAY_PAGE_PLAY_CLICK,
                "age" to s.age
            )


            if (s.age in 9..18) {
                Analytics.track(EventConstants.EVENT_AF_AGE_9_18)
            }


            accountInteractor.updateUser(
                mNickname = name,
                mBirthday = calendar.time.time,
                mGender = gender ?: -1,
                reviewBirth = true
            )
                .map {
                    assert(it.succeeded && it.data == true) {
                        it.message.ifNullOrBlank {
                            context.getString(
                                R.string.common_failed
                            )
                        }
                    }
                    true
                }
                .execute { copy(updateProfileResult = it) }
        }
        if (s.chooseRoleResult.shouldLoad) {
            editorGameLoadInteractor.chooseRole(avatar).map {
                assert(it.succeeded && it.data == true) {
                    it.message.ifNullOrBlank {
                        context.getString(
                            R.string.common_failed
                        )
                    }
                }
                true
            }.execute { copy(chooseRoleResult = it) }
        }
    }

    fun updateAge(age: Int) = setState { copy(age = age) }

    fun backupChooseRole(avatar: DefaultRoleInfo) {
        metaKV.account.setBackupChooseRole(avatar)
    }

    companion object : KoinViewModelFactory<SelectBirthdayViewModel, SelectBirthdayState>() {
        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: SelectBirthdayState
        ): SelectBirthdayViewModel {
            return SelectBirthdayViewModel(get(), get(), get(), get(), state)
        }
    }

}