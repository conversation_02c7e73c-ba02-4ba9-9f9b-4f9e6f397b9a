package com.socialplay.gpark.ui.videofeed.recommend

import android.graphics.Rect
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.Glide
import com.chad.library.adapter.base.loadmore.SimpleLoadMoreView
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.FragmentRecommendVideoListBinding
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.editorschoice.RecommendVideoAnalytics
import com.socialplay.gpark.util.NetUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.navigateUp
import com.socialplay.gpark.util.extension.setOnAntiViolenceItemClickListener

class RecommendVideoListFragment :
    BaseFragment<FragmentRecommendVideoListBinding>(R.layout.fragment_recommend_video_list) {

    override fun getPageName() = PageNameConstants.FRAGMENT_RECOMMEND_VIDEO_LIST

    private val viewModel: RecommendVideoListViewModel by fragmentViewModel()

    override fun invalidate() {

    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentRecommendVideoListBinding? {
        return FragmentRecommendVideoListBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        RecommendVideoAnalytics.trackVideoListPageShow()

        binding.tblTitleBar.setOnBackClickedListener {
            RecommendVideoAnalytics.trackVideoListBackClick()
            navigateUp()
        }

        viewModel.setupRefreshLoading(
            RecommendVideoListViewModelState::refresh,
            binding.loading,
            binding.slRefreshLayout
        ) {
            viewModel.refresh()
        }


        val adapter = RecommendVideoListAdapter(mutableListOf(), Glide.with(this))
        binding.rvList.adapter = adapter

        binding.rvList.addItemDecoration(object : RecyclerView.ItemDecoration() {
            override fun getItemOffsets(
                outRect: Rect,
                view: View,
                parent: RecyclerView,
                state: RecyclerView.State
            ) {

                super.getItemOffsets(outRect, view, parent, state)
                val spanCount = 2

                val position = parent.getChildAdapterPosition(view)
                val isFirstRow = position < spanCount
                val isLastRow = position >= state.itemCount - spanCount

                val spacing = 12.dp
                val column = position % spanCount
                outRect.left = column * spacing / spanCount
                outRect.right = spacing - (column + 1) * spacing / spanCount

                if (!isFirstRow) {
                    outRect.top = 16.dp
                } else {
                    outRect.top = 12.dp
                }

                if (isLastRow) {
                    outRect.bottom = 16.dp
                }
            }
        })

        adapter.setOnItemShowListener { item, position ->
            RecommendVideoAnalytics.trackVideoItemShow(
                ResIdBean
                    .newInstance()
                    .setCategoryID(CategoryId.RECOMMEND_VIDEO_LIST)
                    .setParam1(position + 1)
                    .setReqId(item.reqId), item.postDetail
            )
        }

        setupLoadMoreView(adapter)

        viewModel.onEach(RecommendVideoListViewModelState::refresh) {
            adapter.setNewInstance(it.invoke()?.toMutableList())
        }

        viewModel.onEach(RecommendVideoListViewModelState::items) {
            adapter.setDiffNewData(it.toMutableList())
        }

        adapter.setOnAntiViolenceItemClickListener { _, _, position ->
            val videoItem = adapter.getItemOrNull(position) ?: return@setOnAntiViolenceItemClickListener
            val postDetail = videoItem.postDetail

            val resId = ResIdBean
                .newInstance()
                .setCategoryID(CategoryId.RECOMMEND_VIDEO_LIST)
                .setParam1(position + 1)
                .setReqId(videoItem.reqId)

            RecommendVideoAnalytics.trackVideoItemClick(resId, postDetail)

            MetaRouter.Video.goRecommendVideoFeed(
                this@RecommendVideoListFragment,
                resId,
                postDetail.postId
            )
        }
    }

    private fun setupLoadMoreView(adapter: RecommendVideoListAdapter) {
        adapter.loadMoreModule.apply {
            preLoadNumber = 1
            isEnableLoadMoreIfNotFullPage = false
            loadMoreView = SimpleLoadMoreView()
            setOnLoadMoreListener {
                if (!NetUtil.isNetworkAvailable()) {
                    adapter.loadMoreModule.loadMoreFail()
                }else {
                    viewModel.loadMore()
                }
            }
        }


        viewModel.onEach(RecommendVideoListViewModelState::loadMore) {
            if (it is Loading) {
                adapter.loadMoreModule.loadMoreToLoading()
            } else if (it is Success) {
                val isEnd = it.invoke()?.isEnd == true
                if (isEnd) {
                    adapter.loadMoreModule.loadMoreEnd()
                } else {
                    adapter.loadMoreModule.loadMoreComplete()
                }
            } else if (it is Fail) {
                adapter.loadMoreModule.loadMoreFail()
            }
        }
    }

}