package com.socialplay.gpark.ui.im.setting

import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.CompoundButton
import android.widget.LinearLayout
import androidx.activity.OnBackPressedCallback
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.setFragmentResult
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.meta.box.biz.friend.FriendBiz
import com.meta.box.biz.friend.model.FriendInfo
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.ImInteractor
import com.socialplay.gpark.data.model.SimpleListData
import com.socialplay.gpark.data.model.im.ImUpdate
import com.socialplay.gpark.data.model.im.ImUpdateType
import com.socialplay.gpark.databinding.FragmentChatSettingBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.ui.dialog.ListDialog
import com.socialplay.gpark.ui.reportBlock.ReportViewModel
import com.socialplay.gpark.ui.view.UserLabelView
import com.socialplay.gpark.ui.view.preference.PreferenceItemStyle
import com.socialplay.gpark.util.ClipBoardUtil
import com.socialplay.gpark.util.NetUtil
import com.socialplay.gpark.util.extension.displayName
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.tagIds
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel


/**
 *  <AUTHOR> taiyuan
 *  time   : 2021/6/24
 *  desc   :
 */
class ChatSettingFragment : BaseFragment<FragmentChatSettingBinding>() {
    private val args by navArgs<ChatSettingFragmentArgs>()
    private val viewmodel by viewModel<ChatSettingViewModel>()
    private val imInteractor by inject<ImInteractor>()
    private val backPressedCallback by lazy { getBackPressed() }
    private val msgOnCheckedChangeListener by lazy { getMsgCheckedChanageListener() }
    private var clearMsg = false
    private var friendInfo: FriendInfo? = null

    companion object {
        const val KEY_CHATSETTING_RESULT_REMARK = "remark.result.chatsetting"
        const val KEY_CHATSETTING_RESULT_CLEAR_MSG = "msg.clear.result.chatsetting"
        const val KEY_CHATSETTING_RESULT_DELETE_FRIEND = "delete.friend.result.chatsetting"
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentChatSettingBinding? {
        return FragmentChatSettingBinding.inflate(inflater, container, false)
    }

    override fun init() {
        initView()
        initData()
    }

    override fun isStatusBarTextDark(): Boolean {
        return true
    }

    private fun initView() {
        //显示角色形象，不显示标题
//        binding.titleBar.setTitle(getString(R.string.friend_chat_setting))
        binding.titleBar.setOnBackClickedListener { pop(false) }

        binding.slRemarks.updateStyleParams<PreferenceItemStyle.Text> {
            val desc = if (friendInfo?.remark.isNullOrEmpty()) getString(R.string.no_remark) else friendInfo?.remark.toString()
            this.text = desc
        }

        binding.slRemarks.setOnAntiViolenceClickListener {
            friendInfo?.apply {
                Analytics.track(EventConstants.EVENT_CHAT_SETTING_REMARK_CLICK)
                MetaRouter.IM.goRemarkAlert(this@ChatSettingFragment, name ?: "", remark ?: "", args.uuid!!) { remark ->
                    friendInfo?.remark = remark
                    imInteractor.setRemark(uuid, remark)
                    updateRemarkText()
                }
            }
        }

//
//        binding.viewAvatarBg.setOnAntiViolenceClickListener {
//            args.uuid?.let {
//                MetaRouter.IM.goAddFriendDialog(this, it, false)
//            }
//        }
//
        binding.slMessageTop.apply {
            updateMsgCheckState(false)
        }

        binding.slClearMessage.apply {
            setOnAntiViolenceClickListener {
                Analytics.track(EventConstants.EVENT_CHAT_SETTING_CLEAR_MSG_CLICK)
                showReSureDialog(false)
            }
        }
        binding.slDeleteFriend.apply {
            setOnAntiViolenceClickListener {
                Analytics.track(EventConstants.EVENT_CHAT_SETTING_DELETE_FRIEND_CLICK) {
                    put("location", 1)
                }
                showReSureDialog(true)
            }
        }

        binding.slReportFriend.setOnAntiViolenceClickListener {
            friendInfo?.uuid?.let {
                MetaRouter.Report.userReport(
                    this@ChatSettingFragment,
                    ReportViewModel.SOURCE_CHAT,
                    friendInfo?.displayName.orEmpty(),
                    it,
                    "request_key_chat_setting_${it}"
                )
                Analytics.track(EventConstants.EVENT_USER_REPORT_CLICK, EventParamConstants.KEY_SOURCE to EventParamConstants.SOURCE_REPORT_CHAT)
            }
        }

        //断网后点击重试
        binding.lv.setRetry {
            if (!NetUtil.isNetworkAvailable()) {
                toast(R.string.net_unavailable)
            } else {
                viewmodel.getUserInfo(args.uuid)
            }
        }
        args.uuid?.let {
            viewmodel.getConversationTopState(it)
        }
        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner, backPressedCallback)
        binding.labelGroup.setListener(viewLifecycleOwner) {
            UserLabelView.showDescDialog(this, it)
        }
        binding.tv233Count.setOnLongClickListener {
            val id = binding.tv233Count.text?.toString() ?: return@setOnLongClickListener false
            if (!viewLifecycleOwner.lifecycleScope.isActive) return@setOnLongClickListener false
            if (!isBindingAvailable()) return@setOnLongClickListener false
            viewLifecycleOwner.lifecycleScope.launch {
                context?.let { ClipBoardUtil.setClipBoardContent(id, it, "GPark ID") }
                context?.let { toast(R.string.copied_to_clipboard) }
            }
            true
        }
    }

    private fun initData() {

        viewmodel.messageToTopCallback.observe(viewLifecycleOwner) {
            updateMsgCheckState(it)
        }

        viewmodel.chatSettingLiveData.observe(viewLifecycleOwner) {
            binding.lv.visible(false)
            when (it) {
                ChatSettingViewModel.ChatSetState.Start               -> {
                    binding.lv.visible()
                    binding.lv.showLoading(false)
                }
                ChatSettingViewModel.ChatSetState.Failed              -> {
                    it.msg?.let { toast(it) }
                }
                ChatSettingViewModel.ChatSetState.DeleteFriendSuccess -> {
                    pop(true)
                }
                ChatSettingViewModel.ChatSetState.GetUserInfoSuccess  -> {
                    friendInfo = it.friendInfo
                    friendInfo?.apply {
                        Glide.with(this@ChatSettingFragment).load(avatar).into(binding.ivAvatar)
                        updateRemarkText()
                        binding.labelGroup.show(tagIds, labelInfo, glide = glide)
                        binding.groupContent.visible()
                        val isFriend = FriendBiz.hasFriendByUuid(args.uuid) || isFriend()
                        binding.slRemarks.isVisible = isFriend
                        binding.slDeleteFriend.isVisible = isFriend
                    }
                }
                ChatSettingViewModel.ChatSetState.GetUserInfoFailed   -> {
                    binding.lv.showError()
                }
            }
        }
        viewmodel.imUpdateLiveData.observe(viewLifecycleOwner) {
            onUpdate(it)
        }
    }

    fun onUpdate(imUpdate: ImUpdate) {
        when (imUpdate.updateType) {
            ImUpdateType.DELETE_MESSAGE -> {
                clearMsg = true
                toast(R.string.friend_msg_clear_success)
            }

            else -> {}
        }
    }

    private fun updateMsgCheckState(isChecked: Boolean) {
        binding.slMessageTop.updateStyleParams<PreferenceItemStyle.Toggle> {
            this.toggled  = isChecked
            this.toggleListener = msgOnCheckedChangeListener
        }
    }

    private fun updateRemarkText() {
        friendInfo?.apply {
            binding.tvFriendName.text = name
            binding.tv233Count.text = metaNumber

            binding.slRemarks.updateStyleParams<PreferenceItemStyle.Text> {
                this.text = if (friendInfo?.remark.isNullOrEmpty()) {
                    getString(R.string.no_remark)
                } else {
                    friendInfo?.remark.toString()
                }
            }
        }
    }

    private fun getMsgCheckedChanageListener() = CompoundButton.OnCheckedChangeListener { _, isChecked ->
        val opType = if (isChecked) "top" else "untop"
        Analytics.track(EventConstants.EVENT_CHAT_SETTING_MSG_TO_TOP_CLICK) {
            put("type", opType)
        }
        args.uuid?.let { viewmodel.conersationToTop(it, isChecked) }
    }

    private fun showReSureDialog(deleteFriend: Boolean) {
        val title = if (deleteFriend) {
            getString(R.string.friend_delete_resure, if (friendInfo?.remark.isNullOrBlank()) friendInfo?.name else friendInfo?.remark)
        } else {
            getString(R.string.friend_clear_msg_resure)
        }
        ListDialog().list(
            mutableListOf(
                SimpleListData(
                    getString(if (deleteFriend) R.string.delete_cap else R.string.clear),
                    R.drawable.selector_button_error,
                    R.color.white
                ),
                SimpleListData(getString(R.string.dialog_cancel))
            )
        ).content(title).clickCallback {
            if (it?.text.equals(getString(R.string.dialog_cancel))) {
                //取消
                if (deleteFriend) {
                    Analytics.track(EventConstants.EVENT_CHAT_SETTING_DELETE_FRIEND_CANCEL_CLICK) {
                        put("location", 1)
                    }
                } else {
                    Analytics.track(EventConstants.EVENT_CHAT_SETTING_CLEAR_MSG_CANCEL_CLICK)
                }
            } else if (it?.text.equals(getString(R.string.delete_cap))) {
                removeFriend()
            } else if (it?.text.equals(getString(R.string.clear))) {
                clearMessage()
            }

        }.onViewCreateCallback {
            it.iv.visible(deleteFriend)
            it.iv.updateLayoutParams<LinearLayout.LayoutParams> {
                width = 60.dp
                height = 60.dp
            }
            if (deleteFriend) {
                Glide.with(this).load(friendInfo?.avatar).transform(RoundedCorners(360)).into(it.iv)
            }

        }.show(childFragmentManager, "")
    }

    private fun removeFriend() {
        Analytics.track(EventConstants.EVENT_CHAT_SETTING_DELETE_FRIEND_CONFIRM_CLICK) {
            put("location", 1)
        }
        if (!NetUtil.isNetworkAvailable()) {
            toast(R.string.net_unavailable)
            return
        }
        args.uuid?.let { viewmodel.removeFriend(it) }
    }

    private fun clearMessage() {
        Analytics.track(EventConstants.EVENT_CHAT_SETTING_CLEAR_MSG_CONFIRM_CLICK)
        if (!NetUtil.isNetworkAvailable()) {
            toast(R.string.net_unavailable)
            return
        }
        args.uuid?.let { viewmodel.clearMessages(it) }
    }

    override fun loadFirstData() {
        args.uuid?.let {
            viewmodel.getUserInfo(it)
        }
    }

    private fun pop(deleteFriend: Boolean) {
        setFragmentResult(args.chatSettingResultKey, Bundle().apply {
            putBoolean(KEY_CHATSETTING_RESULT_CLEAR_MSG, clearMsg)
            putBoolean(KEY_CHATSETTING_RESULT_DELETE_FRIEND, deleteFriend)
            putString(KEY_CHATSETTING_RESULT_REMARK, friendInfo?.remark)
        })
        findNavController().popBackStack()
    }

    private fun getBackPressed() = object : OnBackPressedCallback(true /* enabled by default */) {
        override fun handleOnBackPressed() {
            pop(false)
        }
    }

    override fun onDestroyView() {
        binding.slMessageTop.setStyle(null)
        super.onDestroyView()
    }

    override fun getFragmentName() = PageNameConstants.FRAGMENT_NAME_CHAT_SETTING
}