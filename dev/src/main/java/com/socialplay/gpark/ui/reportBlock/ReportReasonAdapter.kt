package com.socialplay.gpark.ui.reportBlock

import android.view.LayoutInflater
import android.view.ViewGroup
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.reportBlock.ReportReasonItem
import com.socialplay.gpark.databinding.AdapterReportReasonItemBinding
import com.socialplay.gpark.ui.base.adapter.BaseVBViewHolder
import com.socialplay.gpark.ui.base.adapter.BasicQuickAdapter

/**
 * created by <PERSON><PERSON><PERSON><PERSON> on 2022/9/27 10:45 上午
 * @describe:
 */
class ReportReasonAdapter : BasicQuickAdapter<ReportReasonItem, AdapterReportReasonItemBinding>() {
    override fun convert(
        holder: BaseVBViewHolder<AdapterReportReasonItemBinding>,
        item: ReportReasonItem
    ) {
        holder.binding.tvText.text = item.content
        val iconRes = if (item.isChecked) R.drawable.icon_check else R.drawable.icon_uncheck
        holder.binding.ivIcon.setImageResource(iconRes)
    }

    override fun viewBinding(parent: ViewGroup, viewType: Int): AdapterReportReasonItemBinding {
        return  AdapterReportReasonItemBinding.inflate(LayoutInflater.from(context), parent, false)
    }
}