package com.socialplay.gpark.ui.post.topic.detail

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.ApiResultCodeException
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.PublishPostInteractor
import com.socialplay.gpark.data.model.post.PostPublishStatus
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.data.model.post.topic.TopicDetailInfo
import com.socialplay.gpark.data.model.post.topic.TopicTabType
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.maverick.copyEx
import com.socialplay.gpark.ui.core.maverick.map
import com.socialplay.gpark.util.ToastData
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import org.koin.android.ext.android.get
import timber.log.Timber

/**
 * Created by bo.li
 * Date: 2024/4/7
 * Desc:
 */

/**
 * @param tagId 话题id
 * @param topicDetailInfo 话题详情信息
 * @param viewCount 话题浏览人数
 * @param followerCount 话题关注人数
 * @param title 话题标题
 * @param followed 是否关注话题
 * @param sortTabList tab列表
 * @param selectedTabIndex 当前选中的tab index
 * @param publishingState 正在发帖的状态
 * @param jumpToNew 是否跳转到最新tab
 */
data class TopicDetailTabModelState(
    val tagId: Long,
    val sortTabList: List<TopicTabType>,
    val selectedTabIndex: Int,
    val topicDetailInfo: Async<TopicDetailInfo> = Uninitialized,
    val viewCount: Long = 0,
    val viewCountAdded:Boolean = false,
    val followerCount: Long = 0,
    val title: String? = null,
    val followed: Async<Boolean> = Uninitialized,
    val publishingState: PostPublishStatus? = null,
    val jumpToNew: Boolean = false,
    val toastData: ToastData = ToastData.EMPTY,
) : MavericksState {

    val topicInfo: PostTag? = topicDetailInfo()?.tagId?.let { PostTag(it, title) }

    val sortTabNameResList = sortTabList.map { it.title }
}

class TopicDetailTabViewModel(
    private val repository: IMetaRepository,
    private val publishPostInteractor: PublishPostInteractor,
    initialState: TopicDetailTabModelState
) : BaseViewModel<TopicDetailTabModelState>(initialState) {

    init {
        refresh()
        observePublish()
        onEach(TopicDetailTabModelState::topicDetailInfo) {
            if (it !is Fail) return@onEach
            if ((it.error as? ApiResultCodeException)?.code == 1016)  return@onEach
            setState { copy(toastData = toastData.toError(it)) }
        }
    }

    private fun observePublish() = viewModelScope.launch {
        publishPostInteractor.publishPostSharedFlow.collectLatest {
            distributePublishingItem(it)
        }
    }

    fun refresh() {
        withState {
            repository.fetchTopicDetail(it.tagId).execute { result->
                val info = result()
                copy(
                    topicDetailInfo = result,
                    viewCount = info?.viewCount ?: 0,
                    followerCount = info?.followCount ?: 0,
                    title = info?.tagName,
                    followed = if (result is Success) result.map { info?.follow == true } else Uninitialized
                )
            }
        }
    }

    fun changeSelectedTag(tabIndex: Int) {
        withState {
            if (it.selectedTabIndex == tabIndex || tabIndex !in 0..it.sortTabList.lastIndex) {
                return@withState
            }
            setState {
                copy(selectedTabIndex = tabIndex)
            }
        }
    }

    fun changeSelectedTag(toTab: TopicTabType) {
        withState {
            val toIndex = it.sortTabList.indexOfFirst { eachTag ->
                eachTag == toTab
            } ?: -1
            if (it.selectedTabIndex == toIndex || toIndex < 0) {
                return@withState
            }
            setState {
                copy(selectedTabIndex = toIndex)
            }
        }
    }

    fun changeFollow() = viewModelScope.launch {
        val state = awaitState()
        if (state.followed is Loading) return@launch
        val currentFollowed = state.followed() ?: return@launch
        Analytics.track(EventConstants.COMMUNITY_TAG_PAGE_FOLLOW) {
            put(EventParamConstants.KEY_ACT, if (currentFollowed) EventParamConstants.V_UNFOLLOW else EventParamConstants.V_FOLLOW)
            put(EventParamConstants.KEY_TAG_PAGE, state.tagId)
        }
        repository.changeTopicFollow(!currentFollowed, state.tagId).map {
            !currentFollowed
        }.execute(retainValue = TopicDetailTabModelState::followed) { result ->
            copy(followed = result).let {
                if (result is Success) {
                    val newFollow = result()
                    val newFollowerCount = followerCount + if (currentFollowed) -1 else 1
                    it.copy(
                        followerCount = newFollowerCount,
                        topicDetailInfo = topicDetailInfo.copyEx(
                            topicDetailInfo()?.copy(
                                follow = newFollow,
                                followCount = newFollowerCount
                            )
                        )
                    )
                } else {
                    it
                }
            }
        }
    }

    private fun distributePublishingItem(publishStatus: PostPublishStatus) {
        withState { state ->
            val lowerTitle = state.title?.lowercase()
            if (publishStatus.post?.communityTagList?.any { it.tagName.lowercase() == lowerTitle } != true && publishStatus.post?.localExistTags?.any { it.tagId == state.tagId } != true) {
                // 只处理携带当前话题的帖子
                return@withState
            }
            Timber.d("checkcheck_publish distributePublishingItem, next: ${publishStatus.status}, current: ${publishStatus.status}, errorMessage: ${publishStatus.errorMessage}")
            setState {
                copy(
                    publishingState = publishStatus,
                    toastData = toastData.toMsg(publishStatus.errorMessage),
                    jumpToNew = (publishingState == null || publishingState.ts != publishStatus.ts) && publishStatus.hasPublishState()
                )
            }
        }
    }

    fun clearPublishing() {
        suspend {
            delay(2000)
        }.execute {
            if (it is Success) {
                copy(publishingState = null, jumpToNew = false, toastData = ToastData.EMPTY)
            } else {
                copy()
            }
        }
    }

    fun addTopicViewCount() {
        withState {
            if(!it.viewCountAdded) {
                viewModelScope.launch {
                    repository.addTopicViewCount(listOf(it.tagId.toString()))
                }
                setState {
                    copy(
                        viewCountAdded = true,
                        viewCount = viewCount + 1,
                        topicDetailInfo = topicDetailInfo.copyEx(
                            topicDetailInfo()?.copy(
                                viewCount = viewCount + 1
                            )
                        )
                    )
                }
            }
        }
    }

    companion object : KoinViewModelFactory<TopicDetailTabViewModel, TopicDetailTabModelState>() {
        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: TopicDetailTabModelState
        ): TopicDetailTabViewModel {
            return TopicDetailTabViewModel(get(), get(), state)
        }

        override fun ComponentCallbacks.initialState(
            viewModelContext: ViewModelContext
        ): TopicDetailTabModelState {
            val args = viewModelContext.args as TopicDetailTabFragmentArgs
            val tabs =  listOf(
                TopicTabType.New,
                TopicTabType.Hot,
            )
            val idx = tabs.indexOfFirst {
                it.sortType == args.initTab
            }
            return TopicDetailTabModelState(
                args.tagInfo.tagId,
                tabs,
                idx
            )
        }
    }
}