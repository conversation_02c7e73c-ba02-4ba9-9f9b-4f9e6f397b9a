package com.socialplay.gpark.ui.gamedetail.cover

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.socialplay.gpark.data.model.gamecover.VideoGameCover

/**
 * created by liyanfeng on 2022/10/28 10:16 上午
 * @describe:
 * @param attachPercent item可见百分比
 */
class CoverScrollerHelper(private val attachPercent: Int = 50) {

    private var percentAttachListener: OnChildPercentAttachStateChangeListener? = null

    private var cacheInvokeMap = mutableMapOf<Int, String>()

    private var mRecyclerView: RecyclerView? = null

    fun setRecyclerView(recyclerView: RecyclerView) {
        this.mRecyclerView = recyclerView
    }

    fun setListener(listener: OnChildPercentAttachStateChangeListener) {
        this.percentAttachListener = listener
    }

    fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
        this.mRecyclerView = recyclerView
        if (newState == RecyclerView.SCROLL_STATE_IDLE) {
            scrollCallback(false)
        }
    }

    fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
        scrollCallback(false)
    }

    private fun scrollCallback(isResume: Boolean) {
        val percentMap = getItemPercent() ?: return
        val firstAttach = getFirstAttach(percentMap) ?: return
        if (firstAttach.first < 0) return
        if (isVideo(firstAttach.first)) {
            percentAttachListener?.onFirstChildViewAttachedToWindow(firstAttach.first)
            cacheInvokeMap[firstAttach.first] = "Attached"
        }
        if (isResume) return
        percentMap.forEach {
            if (isVideo(it.key)) {
                if (it.value >= attachPercent) {
                    if (cacheInvokeMap[it.key] != "Attached" && it.key != firstAttach.first) {
                        cacheInvokeMap[it.key] = "Attached"
                        percentAttachListener?.onChildViewAttachedToWindow(it.key)
                    }
                } else {
                    if (cacheInvokeMap[it.key] != "Detached") {
                        cacheInvokeMap[it.key] = "Detached"
                        percentAttachListener?.onChildViewDetachedFromWindow(it.key)
                    }
                }
            }
        }
    }

    fun onResume() {
        mRecyclerView?.post {
            scrollCallback(true)
        }
    }

    fun onPause() {
        val percentMap = getItemPercent() ?: return
        percentMap.forEach {
            if (isVideo(it.key)) {
                percentAttachListener?.onChildViewDetachedFromWindow(it.key)
            }
        }
    }

    private fun clearCache() {
        cacheInvokeMap.clear()
    }

    private fun getItemPercent(): Map<Int, Int>? {
        mRecyclerView ?: return null
        mRecyclerView?.adapter ?: return null
        val layoutManager: LinearLayoutManager = mRecyclerView!!.layoutManager as LinearLayoutManager
        val firstVisPos = layoutManager.findFirstVisibleItemPosition()
        val lastVisPos = layoutManager.findLastVisibleItemPosition()

        val rvRect = Rect()
        //获取recyclerview可见区域相对屏幕左上角的位置坐标
        mRecyclerView!!.getGlobalVisibleRect(rvRect)
        val map = mutableMapOf<Int, Int>()
        for (position in firstVisPos..lastVisPos) {
            var visiblePercent: Int
            //根据position获得对应的view
            val itemView: View = layoutManager.findViewByPosition(position) ?: return null
            val itemWidth: Int = itemView.width
            val rowRect = Rect()
            //获取item可见区域相对屏幕左上角的位置坐标
            itemView.getGlobalVisibleRect(rowRect)
            visiblePercent = if (rowRect.right >= rvRect.right) { //item在recyclerview底部且有部分不可见
                val visibleHeightFirst: Int = rvRect.right - rowRect.left
                visibleHeightFirst * 100 / itemWidth
            } else { //item在recyclerview中或顶部
                val visibleHeightFirst: Int = rowRect.right - rvRect.left
                visibleHeightFirst * 100 / itemWidth
            }
            if (visiblePercent > 100) visiblePercent = 100

            map[position] = visiblePercent
        }
        return map
    }

    private fun getFirstAttach(map: Map<Int, Int>): Pair<Int, Int>? {
        val mapToSort = map.entries.sortedBy { it.key }.associateBy({ it.key }, { it.value })
        mapToSort.forEach {
            if (isVideo(it.key) && it.value >= attachPercent) {
                return Pair(it.key, it.value)
            }
        }
        return null
    }

    private fun isVideo(position: Int): Boolean {
        mRecyclerView ?: return false
        mRecyclerView?.adapter ?: return false
        return kotlin.runCatching { (mRecyclerView?.adapter as GameDetailCoverAdapter).getItem(position) is VideoGameCover }.getOrDefault(false)
    }

}

interface OnChildPercentAttachStateChangeListener {
    fun onFirstChildViewAttachedToWindow(position: Int)
    fun onChildViewAttachedToWindow(position: Int)
    fun onChildViewDetachedFromWindow(position: Int)
}
