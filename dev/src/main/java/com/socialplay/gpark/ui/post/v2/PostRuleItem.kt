package com.socialplay.gpark.ui.post.v2

import android.text.SpannableString
import android.text.style.BulletSpan
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.AdapterPostRuleContentBinding
import com.socialplay.gpark.databinding.AdapterPostRuleHeaderBinding
import com.socialplay.gpark.databinding.AdapterPostRuleList1Binding
import com.socialplay.gpark.databinding.AdapterPostRuleList2Binding
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaModelCollector
import com.socialplay.gpark.ui.view.center.CenterImageSpan

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/10/07
 *     desc   :
 * </pre>
 */
fun MetaModelCollector.postRuleHeaderItem(
    content: String
) {
    add {
        PostRuleHeaderItem(content).id("PostRuleHeaderItem-$it")
    }
}

data class PostRuleHeaderItem(
    val content: String
) : ViewBindingItemModel<AdapterPostRuleHeaderBinding>(
    R.layout.adapter_post_rule_header,
    AdapterPostRuleHeaderBinding::bind
) {
    override fun AdapterPostRuleHeaderBinding.onBind() {
        tvTitle.text = content
    }
}

fun MetaModelCollector.postRuleContentItem(
    content: String
) {
    add {
        PostRuleContentItem(content).id("PostRuleContentItem-$it")
    }
}

data class PostRuleContentItem(
    val content: String
) : ViewBindingItemModel<AdapterPostRuleContentBinding>(
    R.layout.adapter_post_rule_content,
    AdapterPostRuleContentBinding::bind
) {
    override fun AdapterPostRuleContentBinding.onBind() {
        tvContent.text = content
    }
}

fun MetaModelCollector.postRuleList1Item(
    content: String
) {
    add {
        PostRuleList1Item(content).id("PostRuleList1Item-$it")
    }
}

data class PostRuleList1Item(
    val content: String
) : ViewBindingItemModel<AdapterPostRuleList1Binding>(
    R.layout.adapter_post_rule_list_1,
    AdapterPostRuleList1Binding::bind
) {
    override fun AdapterPostRuleList1Binding.onBind() {
        tvBullet.text = content
        tvContent.text = content
    }
}

fun MetaModelCollector.postRuleList2Item(
    content: String
) {
    add {
        PostRuleList2Item(content).id("PostRuleList2Item-$it")
    }
}

data class PostRuleList2Item(
    val content: String
) : ViewBindingItemModel<AdapterPostRuleList2Binding>(
    R.layout.adapter_post_rule_list_2,
    AdapterPostRuleList2Binding::bind
) {
    override fun AdapterPostRuleList2Binding.onBind() {
        tvBullet.text = content
        tvContent.text = content
    }
}