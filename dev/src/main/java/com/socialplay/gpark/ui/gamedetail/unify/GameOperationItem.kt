package com.socialplay.gpark.ui.gamedetail.unify

import android.graphics.Color
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.game.OperationInfo
import com.socialplay.gpark.databinding.ItemGameDetailOperationBinding
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaEpoxyController
import com.socialplay.gpark.util.extension.backgroundTintListByColor
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.unsetOnClick
import com.socialplay.gpark.util.extension.visible

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/11/21
 *     desc   :
 * </pre>
 */
interface IGameOperationListener {
    fun clickOperation(item: OperationInfo, position: Int)
}

fun MetaEpoxyController.gameOperationItem(
    item: OperationInfo,
    position: Int,
    listener: IGameOperationListener
) {
    add {
        GameOperationItem(item, position, listener).id("GameOperation-${item.bizId}-${item.positionId}-${item.updateTime}")
    }
}

data class GameOperationItem(
    val item: OperationInfo,
    val position: Int,
    val listener: IGameOperationListener
) : ViewBindingItemModel<ItemGameDetailOperationBinding>(
    R.layout.item_game_detail_operation,
    ItemGameDetailOperationBinding::bind
) {

    override fun ItemGameDetailOperationBinding.onBind() {
        if (item.opTag?.tag.isNullOrEmpty()) {
            tvTag.gone()
        } else {
            tvTag.visible()
            val color = kotlin.runCatching {
                item.opTag?.tagBgColor?.let {
                    Color.parseColor(it)
                } ?: getColorByRes(R.color.color_4AB4FF)
            }.getOrDefault(getColorByRes(R.color.color_4AB4FF))
            tvTag.text = item.opTag?.tag
            tvTag.backgroundTintListByColor(color)
        }
        tvTitle.text = item.title
        root.setOnAntiViolenceClickListener {
            listener.clickOperation(item, position)
        }
    }

    override fun ItemGameDetailOperationBinding.onUnbind() {
        root.unsetOnClick()
    }
}