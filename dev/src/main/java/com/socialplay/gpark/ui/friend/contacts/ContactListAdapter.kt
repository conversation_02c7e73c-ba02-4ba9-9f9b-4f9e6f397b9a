package com.socialplay.gpark.ui.friend.contacts

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import com.bumptech.glide.RequestManager
import com.meta.box.biz.friend.model.FriendInfo
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.AdapterFriendListBinding
import com.socialplay.gpark.function.im.FriendStatusHelper
import com.socialplay.gpark.ui.base.adapter.BasePagingDataAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder
import com.socialplay.gpark.ui.base.adapter.ItemClickListener
import com.socialplay.gpark.ui.view.UserLabelListener
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.enableAllWithAlpha
import com.socialplay.gpark.util.extension.tagIds
import com.socialplay.gpark.util.extension.unsetOnClick
import com.socialplay.gpark.util.extension.visible

/**
 *     author : wei.zhu
 *     e-mail : <EMAIL>
 *     time   : 2021/06/04
 *     desc   : 好友列表适配器
 */
class ContactListAdapter(
    private val isCheckFriends: Boolean,
    private val maxCheckCount: Int,
    private val glide: RequestManager,
    private val enableLabel: Boolean = true,
    private val isGroupMember: ((userId: String) -> Boolean)? = null,
    private val listener: UserLabelListener
) : BasePagingDataAdapter<FriendInfo, AdapterFriendListBinding>(DIFF_CALLBACK) {
    private val _checkedFriends: MutableMap<String, FriendInfo> = mutableMapOf()
    val checkedFriends get() = _checkedFriends

    companion object {
        private val DIFF_CALLBACK = object : DiffUtil.ItemCallback<FriendInfo>() {

            override fun areItemsTheSame(oldItem: FriendInfo, newItem: FriendInfo) =
                oldItem.uuid == newItem.uuid

            override fun areContentsTheSame(oldItem: FriendInfo, newItem: FriendInfo): Boolean {
                return oldItem.uuid == newItem.uuid &&
                        oldItem.name == newItem.name &&
                        oldItem.remark == newItem.remark &&
                        oldItem.avatar == newItem.avatar &&
                        oldItem.status == newItem.status
            }
        }
    }
    /*

        private fun updateFriendActiveStatus(binding: AdapterFriendListBinding, item: FriendInfo) {
            binding.tvFriendActiveStatus.visible(item.status != null)

            when (item.status.toLocalStatus()) {
                FriendStatus.OFFLINE_NOT_PLAY_GAME_IN_RECENTLY -> { //离线
                    binding.tvFriendActiveStatus.text = context.getString(R.string.offline_status)
                    binding.ivOnlineDot.isVisible = false
                }
                FriendStatus.OFFLINE_PLAYED_GAME_IN_RECENTLY   -> { //离线 最近玩过游戏
                    val lastPlay = item.status?.playTime
                    val lastPlayingGameTime = lastPlay?.gameTime ?: 0

                    binding.ivOnlineDot.isVisible = false
                    binding.tvFriendActiveStatus.text = context.getString(R.string.last_online_formatted).format(sdf.format(lastPlayingGameTime))
                }
                FriendStatus.ONLINE                            -> { //逛乐园
                    binding.tvFriendActiveStatus.text = context.getString(R.string.online_status)
                    binding.ivOnlineDot.isVisible = true
                }
                FriendStatus.PLAYING_GAME                      -> { //玩游戏
                    binding.tvFriendActiveStatus.text = context.getString(R.string.playing_formatted).format(item.status?.gameStatus?.gameName)
                    binding.ivOnlineDot.isVisible = true
                }
            }
        }
    */

    override fun createViewBinding(
        layoutInflater: LayoutInflater,
        parent: ViewGroup,
        viewType: Int
    ): AdapterFriendListBinding {
        return AdapterFriendListBinding.inflate(LayoutInflater.from(context), parent, false).apply {
            if (isCheckFriends) {
                ivCheckBox.visible(true)
            }
            labelGroup.visible(enableLabel)
        }
    }

    private var itemClickListener: ItemClickListener? = null
    override fun setOnItemClickListener(listener: ItemClickListener?) {
        if (isCheckFriends) {
            itemClickListener = listener
        } else {
            super.setOnItemClickListener(listener)
        }
    }


    override fun convert(
        holder: BindingViewHolder<AdapterFriendListBinding>,
        item: FriendInfo,
        position: Int
    ) {
        val binding = holder.binding
        binding.tvFriendName.text = if (item.remark.isNullOrBlank()) item.name else item.remark
        glide.load(item.avatar).placeholder(R.drawable.icon_default_avatar).into(binding.ivAvatar)
        if (enableLabel) {
            binding.labelGroup.show(item.tagIds, item.labelInfo, glide = glide)
        }
        FriendStatusHelper.updateStatus(
            item.uuid,
            item.status,
            binding.tvFriendStatus,
            FriendStatusHelper.SOURCE_CONTACT_LIST,
            binding.ivOnline
        )
        if (isCheckFriends) {
            if (isGroupMember?.invoke(item.uuid) == true) {
                holder.itemView.unsetOnClick()
                binding.ivCheckBox.setImageResource(R.drawable.icon_group_chat_friend_checked)
                binding.ivCheckBox.enableAllWithAlpha(false)
            } else {
                binding.ivCheckBox.enableAllWithAlpha(true)
                binding.ivCheckBox.setImageResource(
                    if (_checkedFriends.contains(item.uuid)) {
                        R.drawable.icon_group_chat_friend_checked
                    } else {
                        R.drawable.icon_group_chat_friend_unchecked
                    }
                )
                holder.itemView.setOnClickListener { view ->
                    if (_checkedFriends.contains(item.uuid)) {
                        _checkedFriends.remove(item.uuid)
                        binding.ivCheckBox.setImageResource(R.drawable.icon_group_chat_friend_unchecked)
                    } else {
                        if (_checkedFriends.size >= maxCheckCount) {
                            ToastUtil.showShort(R.string.toast_invite_members_select_maximum_limit)
                            return@setOnClickListener
                        }
                        _checkedFriends[item.uuid] = item
                        binding.ivCheckBox.setImageResource(R.drawable.icon_group_chat_friend_checked)
                    }
                    itemClickListener?.invoke(view, position)
                }
            }
        }
    }

    override fun onViewAttachedToWindow(holder: BindingViewHolder<AdapterFriendListBinding>) {
        super.onViewAttachedToWindow(holder)
        holder.binding.labelGroup.setListener(listener)
    }

    override fun onViewDetachedFromWindow(holder: BindingViewHolder<AdapterFriendListBinding>) {
        super.onViewDetachedFromWindow(holder)
        holder.binding.labelGroup.setListener(null)
    }

}