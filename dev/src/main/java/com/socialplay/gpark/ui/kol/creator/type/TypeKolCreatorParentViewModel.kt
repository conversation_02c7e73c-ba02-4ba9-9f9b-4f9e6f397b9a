package com.socialplay.gpark.ui.kol.creator.type

import android.content.ComponentCallbacks
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.CommonTabItem
import com.socialplay.gpark.data.model.creator.morecreator.TypeCreatorRequest
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory

/**
 * Created by bo.li
 * Date: 2024/8/9
 * Desc:
 */

data class TypeKolCreatorParentModelState(
    val tabItems: List<CommonTabItem>
) : MavericksState {
    constructor() : this(
        listOf(
            CommonTabItem(
                R.string.kol_follow_creator,
                TypeCreatorRequest.TYPE_FOLLOWED.toString()
            ) {
                TypeKolCreatorFragment.newInstance(
                    TypeKolCreatorFragmentArgs(TypeCreatorRequest.TYPE_FOLLOWED)
                )
            },
            CommonTabItem(
                R.string.kol_recommend_creator,
                TypeCreatorRequest.TYPE_RECOMMEND.toString()
            ) {
                TypeKolCreatorFragment.newInstance(
                    TypeKolCreatorFragmentArgs(TypeCreatorRequest.TYPE_RECOMMEND)
                )
            },
        )
    )
}

class TypeKolCreatorParentViewModel(
    initialState: TypeKolCreatorParentModelState
) : BaseViewModel<TypeKolCreatorParentModelState>(initialState) {

    companion object :
        KoinViewModelFactory<TypeKolCreatorParentViewModel, TypeKolCreatorParentModelState>() {

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: TypeKolCreatorParentModelState
        ): TypeKolCreatorParentViewModel {
            return TypeKolCreatorParentViewModel(state)
        }
    }
}