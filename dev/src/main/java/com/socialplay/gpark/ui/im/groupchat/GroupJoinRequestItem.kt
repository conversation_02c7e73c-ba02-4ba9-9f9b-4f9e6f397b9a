package com.socialplay.gpark.ui.im.groupchat

import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.groupchat.GroupChatApplyInfo
import com.socialplay.gpark.databinding.ItemGroupJoinRequestBinding
import com.socialplay.gpark.ui.core.IBaseEpoxyItemListener
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTextColorByRes
import com.socialplay.gpark.util.extension.setTextWithArgs
import com.socialplay.gpark.util.extension.visible

interface IItemGroupApplyClickedListener : IBaseEpoxyItemListener {
    fun onAvatarClicked(info: GroupChatApplyInfo)
    fun onCenterClicked(info: GroupChatApplyInfo)
    fun onMoreClicked(info: GroupChatApplyInfo)
}

data class GroupJoinRequestItem(
    val applyInfo: GroupChatApplyInfo,
    val isNew: Boolean,
    private val itemClickedListener: IItemGroupApplyClickedListener
) : ViewBindingItemModel<ItemGroupJoinRequestBinding>(
    R.layout.item_group_join_request,
    ItemGroupJoinRequestBinding::bind
) {
    override fun ItemGroupJoinRequestBinding.onBind() {
        itemClickedListener.getGlideOrNull()?.run {
            load(applyInfo.userAvatar).placeholder(R.drawable.icon_item_group_chat_avatar)
                .into(ivUserAvatar)
        }
        tvUserName.text = applyInfo.userName ?: ""
        tvTagNew.visible(isNew)
        tvJoinApplyDesc.setTextWithArgs(
            R.string.request_want_to_join_group_desc,
            applyInfo.chatGroupName ?: ""
        )
        when (applyInfo.status) {
            GroupChatApplyInfo.STATUS_APPLYING -> {
                tvMoreBtn.setText(R.string.request_join_group_agree)
                tvMoreBtn.setTextColorByRes(R.color.color_212121)
                tvMoreBtn.setBackgroundResource(R.drawable.bg_ffef30_round_40)
            }

            GroupChatApplyInfo.STATUS_REJECT -> {
                tvMoreBtn.setText(R.string.request_join_group_rejected)
                tvMoreBtn.setTextColorByRes(R.color.color_CCCCCC)
                tvMoreBtn.setBackgroundResource(R.drawable.bg_f0f0f0_corner_40)
            }

            GroupChatApplyInfo.STATUS_AGREE -> {
                tvMoreBtn.setText(R.string.request_join_group_agreed)
                tvMoreBtn.setTextColorByRes(R.color.color_CCCCCC)
                tvMoreBtn.setBackgroundResource(R.drawable.bg_f0f0f0_corner_40)
            }

            else -> {
                tvMoreBtn.text = ""
                tvMoreBtn.setTextColorByRes(R.color.color_CCCCCC)
                tvMoreBtn.setBackgroundResource(R.drawable.bg_f0f0f0_corner_40)
            }
        }
        viewUserAvatarClick.setOnAntiViolenceClickListener {
            itemClickedListener.onAvatarClicked(applyInfo)
        }
        viewCenterClick.setOnAntiViolenceClickListener {
            itemClickedListener.onCenterClicked(applyInfo)
        }
        viewMoreClick.setOnAntiViolenceClickListener {
            itemClickedListener.onMoreClicked(applyInfo)
        }
    }
}