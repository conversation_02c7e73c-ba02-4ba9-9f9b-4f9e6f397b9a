package com.socialplay.gpark.ui.mgs.dialog

import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.view.Gravity
import android.view.LayoutInflater
import android.view.WindowManager
import androidx.core.view.setPadding
import com.bumptech.glide.Glide
import com.meta.box.biz.friend.model.LabelInfo
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.DialogUserLabelDescriptionBinding
import com.socialplay.gpark.ui.dialog.GameCreateDialogHelper
import com.socialplay.gpark.ui.view.UserLabelView.Companion.TYPE_CREATOR
import com.socialplay.gpark.ui.view.UserLabelView.Companion.TYPE_MEDAL
import com.socialplay.gpark.ui.view.UserLabelView.Companion.TYPE_OFFICIAL
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.goneIfValueEmpty
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener

/**
 * Created by bo.li
 * Date: 2022/2/18
 * Desc:
 */
class UserLabelDescriptionDialog(
    private val metaApp: Context,
    private val activity: Activity,
    private val data: Pair<Int, LabelInfo?>
) : Dialog(activity, android.R.style.Theme_Dialog) {

    private lateinit var binding: DialogUserLabelDescriptionBinding

    init {
        initView()
    }

    private fun initDialogParams() {
        setCancelable(true)
        setCanceledOnTouchOutside(true)
        binding = DialogUserLabelDescriptionBinding.inflate(LayoutInflater.from(metaApp))
        GameCreateDialogHelper.customInflated(
            activity,
            metaApp,
            this,
            binding.root,
            0.75F,
            gravity = Gravity.CENTER,
            width = WindowManager.LayoutParams.MATCH_PARENT,
            height = WindowManager.LayoutParams.MATCH_PARENT
        )
    }

    private fun initView() {
        if (window == null) {
            dismiss()
            return
        }
        initDialogParams()
        binding.root.setOnAntiViolenceClickListener {
            dismiss()
        }
        binding.tvBtn.setOnAntiViolenceClickListener {
            dismiss()
        }
        val (type, labelInfo) = data
        val imgRes: Int
        val titleRes: Int
        val contentRes: Int
        when (type) {
            TYPE_OFFICIAL -> {
                imgRes = R.drawable.icon_official_verification_big
                titleRes = R.string.verified_account_title
                contentRes = R.string.verified_account_content
            }

            TYPE_MEDAL -> {
                labelInfo?.let {
                    binding.ivIcon.setBackgroundResource(R.drawable.bg_e3fff6_round_30)
                    binding.ivIcon.setPadding(context.dp(15))
                    Glide.with(binding.ivIcon)
                        .load(it.icon)
                        .into(binding.ivIcon)
                    binding.tvTitle.text = it.name
                    binding.tvContent.goneIfValueEmpty(it.desc)
                }
                return
            }

            TYPE_CREATOR -> {
                imgRes = R.drawable.ic_label_creator_desc
                titleRes = R.string.creator_cap
                contentRes = R.string.creator_label_desc
            }

            else -> {
                return
            }
        }
        binding.ivIcon.setImageResource(imgRes)
        binding.tvTitle.setText(titleRes)
        binding.tvContent.setText(contentRes)
    }
}