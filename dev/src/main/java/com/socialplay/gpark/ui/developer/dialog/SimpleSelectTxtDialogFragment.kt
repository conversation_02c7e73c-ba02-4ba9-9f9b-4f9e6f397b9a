package com.socialplay.gpark.ui.developer.dialog

import android.content.Context
import android.content.DialogInterface
import android.view.Gravity
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.AdapterSimpleSelectTxtItemBinding
import com.socialplay.gpark.databinding.DialogFragmentSimpleSelectTxtBinding
import com.socialplay.gpark.ui.base.BaseDialogFragment
import com.socialplay.gpark.ui.base.adapter.BaseAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.extension.setFragmentResultByActivity
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber

class SimpleSelectTxtDialogFragment : BaseDialogFragment() {
    companion object {
        const val REQUEST_KEY = "SimpleSelectTxtDialogFragment_Request_Key_Result"
        const val RESULT_KEY = "SimpleSelectTxtDialogFragment_Result_Key"
        const val RESULT_SELECT_TXT = "SimpleSelectTxtDialogFragment_Result_SELECT_TXT"
        const val LEFT = 0
        const val RIGHT = 1
        const val DISMISS = 2
        const val SELECT_TXT = 3
    }

    override val binding by viewBinding(DialogFragmentSimpleSelectTxtBinding::inflate)

    private var result = DISMISS
    private val args by navArgs<SimpleSelectTxtDialogFragmentArgs>()
    private var selectTxt: String = ""
    private val adapter by lazy {
        selectTxt = args.selectTxt
        Adapter(args.selectTxt)
    }

    override fun init() {
        val args = arguments?.let { SimpleSelectTxtDialogFragmentArgs.fromBundle(it) }
        args?.run {
            binding.title.isVisible = args.showTitle
            binding.title.text = args.title ?: ""
            binding.title.textSize = args.titleSize
            binding.btnLeft.isVisible = args.showLeftBtn
            binding.btnLeft.text = args.leftBtnText ?: getString(R.string.dialog_cancel)
            context?.let {
                binding.btnLeft.setTextColor(getTextColor(it, leftTextColor, args.leftLightBackground))
                binding.btnRight.setTextColor(getTextColor(it, rightTextColor, args.rightLightBackground))
            }
            binding.btnRight.isVisible = args.showRightBtn
            binding.btnRight.text = args.rightBtnText ?: getString(R.string.dialog_confirm)
            binding.btnLeft.setOnAntiViolenceClickListener {
                result = LEFT
                dismissAllowingStateLoss()
            }
            binding.btnRight.setOnAntiViolenceClickListener {
                result = RIGHT
                dismissAllowingStateLoss()
            }
            binding.bottomBtn.visible(args.showLeftBtn || args.showRightBtn)
        }
        adapter.setOnItemClickListener { _, position ->
            val txt = this.adapter.data[position]
            result = SELECT_TXT
            selectTxt = txt
            dismissAllowingStateLoss()
        }
        binding.recyclerView.adapter = adapter
        adapter.setList(args?.selectItems?.toMutableList())
    }

    private fun getTextColor(context: Context, defaultColor: Int, isLight: Boolean): Int {
        return ContextCompat.getColor(
            context, if (defaultColor > 0) {
                defaultColor
            } else if (isLight) {
                R.color.white
            } else {
                R.color.color_080D2D
            }
        )
    }

    override fun isClickOutsideDismiss(): Boolean {
        return args.isClickOutsideDismiss
    }

    override fun isBackPressedDismiss(): Boolean {
        return args.isBackPressedDismiss
    }

    override fun gravity(): Int = Gravity.CENTER

    override fun marginHorizontal(context: Context): Int = ScreenUtil.dp2px(context, 48F)

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        setFragmentResultByActivity(
            REQUEST_KEY, bundleOf(
                RESULT_KEY to result,
                RESULT_SELECT_TXT to selectTxt
            )
        )
    }

    override fun loadFirstData() {
    }

    class Builder(private val fragment: Fragment? = null) {

        constructor() : this(null)

        private var title: String? = null
        private var titleSize: Float = 15f
        private var titleVisible = true
        private var leftBtnTxt: String? = null
        private var leftBtnVisible = true
        private var leftLight = false
        private var leftTextColor: Int = 0
        private var rightBtnTxt: String? = null
        private var rightBtnVisible = true
        private var rightLight = true
        private var rightTextColor: Int = 0
        private var leftCallback: (() -> Unit)? = null
        private var rightCallback: (() -> Unit)? = null
        private var selectTxtCallback: ((String) -> Unit)? = null
        private var dismissCallback: ((byAction: Int) -> Unit)? = null
        private var isClickOutsideDismiss = true
        private var isBackPressedDismiss = true
        private var selectItems: List<String> = emptyList()
        private var selectTxt: String = ""

        fun title(title: String? = null, isVisible: Boolean = true): Builder {
            this.title = title
            this.titleVisible = isVisible
            return this
        }

        fun selectItems(selectItems: List<String>): Builder {
            this.selectItems = selectItems
            return this
        }

        fun selectTxt(selectTxt: String): Builder {
            this.selectTxt = selectTxt
            return this
        }

        fun titleSize(titleSize: Float): Builder {
            this.titleSize = titleSize
            return this
        }

        fun leftBtnTxt(
            text: String? = null,
            isVisible: Boolean = true,
            lightBackground: Boolean = false,
            textColor: Int = -1
        ): Builder {
            this.leftBtnTxt = text
            this.leftBtnVisible = isVisible
            this.leftLight = lightBackground
            this.leftTextColor = textColor
            return this
        }

        fun rightBtnTxt(
            text: String? = null,
            isVisible: Boolean = true,
            lightBackground: Boolean = true,
            textColor: Int = -1
        ): Builder {
            this.rightBtnTxt = text
            this.rightBtnVisible = isVisible
            this.rightLight = lightBackground
            this.rightTextColor = textColor
            return this
        }

        fun leftCallback(callback: () -> Unit): Builder {
            this.leftCallback = callback
            return this
        }

        fun rightCallback(callback: () -> Unit): Builder {
            this.rightCallback = callback
            return this
        }

        fun selectTxtCallback(callback: (String) -> Unit): Builder {
            this.selectTxtCallback = callback
            return this
        }

        fun dismissCallback(callback: (byAction: Int) -> Unit): Builder {
            this.dismissCallback = callback
            return this
        }

        fun isClickOutsideDismiss(isClickOutsideDismiss: Boolean): Builder {
            this.isClickOutsideDismiss = isClickOutsideDismiss
            return this
        }

        fun isBackPressedDismiss(isBackPressedDismiss: Boolean): Builder {
            this.isBackPressedDismiss = isBackPressedDismiss
            return this
        }

        fun navigate(activity: FragmentActivity, tag: String) {
            if (activity.isFinishing) return
            val bundle = getBundle()
            val fragment = SimpleSelectTxtDialogFragment()
            fragment.arguments = bundle
            val supportFragmentManager = activity.supportFragmentManager
            val old = supportFragmentManager.findFragmentByTag(tag)
            supportFragmentManager.beginTransaction()
                .also {
                    if (old != null) {
                        it.remove(old)
                    }
                }
                .add(fragment, tag)
                .commit()
            setFragmentResult(activity, activity)
        }

        fun navigate(navOptions: NavOptions? = null) {
            fragment ?: error("fragment is null")
            val activity = fragment.activity ?: return
            if (activity.isFinishing) return
            if (fragment.isDetached) return
            val bundle = getBundle()
            fragment.findNavController().navigate(R.id.dialog_simple_select_txt, bundle, navOptions)
            setFragmentResult(activity, fragment)
        }

        private fun getBundle() = SimpleSelectTxtDialogFragmentArgs(
            title = title,
            leftBtnText = leftBtnTxt,
            rightBtnText = rightBtnTxt,
            showTitle = titleVisible,
            showLeftBtn = leftBtnVisible,
            showRightBtn = rightBtnVisible,
            leftLightBackground = leftLight,
            rightLightBackground = rightLight,
            leftTextColor = leftTextColor,
            rightTextColor = rightTextColor,
            titleSize = titleSize,
            isClickOutsideDismiss = isClickOutsideDismiss,
            isBackPressedDismiss = isBackPressedDismiss,
            selectItems = selectItems.toTypedArray(),
            selectTxt = selectTxt,
        ).toBundle()

        private fun setFragmentResult(activity: FragmentActivity, lifecycleOwner: LifecycleOwner) {
            activity.supportFragmentManager.setFragmentResultListener(
                REQUEST_KEY,
                lifecycleOwner
            ) { requestKey, result ->
                if (requestKey == REQUEST_KEY) {
                    lifecycleOwner.lifecycleScope.launch {
                        delay(10)
                        when (result.getInt(RESULT_KEY, DISMISS)) {
                            LEFT -> {
                                Timber.d("LEFT")
                                leftCallback?.invoke()
                                dismissCallback?.invoke(LEFT)
                                activity.supportFragmentManager.clearFragmentResult(REQUEST_KEY)
                                activity.supportFragmentManager.clearFragmentResultListener(REQUEST_KEY)
                            }
                            RIGHT -> {
                                Timber.d("RIGHT")
                                rightCallback?.invoke()
                                dismissCallback?.invoke(RIGHT)
                                activity.supportFragmentManager.clearFragmentResult(REQUEST_KEY)
                                activity.supportFragmentManager.clearFragmentResultListener(REQUEST_KEY)
                            }
                            DISMISS -> {
                                Timber.d("DISMISS")
                                dismissCallback?.invoke(DISMISS)
                                activity.supportFragmentManager.clearFragmentResult(REQUEST_KEY)
                                activity.supportFragmentManager.clearFragmentResultListener(REQUEST_KEY)
                            }
                            SELECT_TXT -> {
                                Timber.d("SELECT_TXT")
                                selectTxtCallback?.invoke(result.getString(RESULT_SELECT_TXT, ""))
                                activity.supportFragmentManager.clearFragmentResult(REQUEST_KEY)
                                activity.supportFragmentManager.clearFragmentResultListener(REQUEST_KEY)
                            }
                        }
                    }
                }
            }
        }
    }


    private class Adapter(private val selectTxt: String) :
        BaseAdapter<String, AdapterSimpleSelectTxtItemBinding>() {
        override fun convert(holder: BindingViewHolder<AdapterSimpleSelectTxtItemBinding>, item: String, position: Int) {
            holder.binding.tvName.let {
                it.text = item
                it.setTextColor(
                    ContextCompat.getColor(
                        holder.itemView.context,
                        if (selectTxt == item) R.color.color_FF5F42 else R.color.color_080D2D_50
                    )
                )
            }
        }

        override fun createViewBinding(layoutInflater: LayoutInflater, parent: ViewGroup, viewType: Int): AdapterSimpleSelectTxtItemBinding {
            return AdapterSimpleSelectTxtItemBinding.inflate(layoutInflater, parent, false)
        }
    }
}