package com.socialplay.gpark.ui.room.create

import android.os.Bundle
import android.text.InputFilter.LengthFilter
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewConfiguration
import android.view.ViewGroup
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.RequestManager
import com.meta.biz.mgs.data.model.MgsBriefRoomInfo
import com.meta.lib.mwbiz.MWBiz
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.model.SimpleListData
import com.socialplay.gpark.data.model.editor.EditorCreationShowInfo
import com.socialplay.gpark.data.model.room.ChatRoomInfo
import com.socialplay.gpark.data.model.room.RoomStyle
import com.socialplay.gpark.databinding.FragmentHomeCreateRoomBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.kernel.PandoraInit
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.mgs.MgsGameRoomLauncher
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.core.views.loadMoreVerticalFooter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.dialog.ConfirmDialog
import com.socialplay.gpark.ui.dialog.ListDialog
import com.socialplay.gpark.ui.editor.BaseEditorFragmentMaverick
import com.socialplay.gpark.ui.room.HomeRoomAnalytics
import com.socialplay.gpark.util.InputUtil
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.extension.addTextChangedListener
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.invisible
import com.socialplay.gpark.util.extension.setHeight
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import kotlinx.coroutines.flow.firstOrNull
import org.koin.androidx.viewmodel.ext.android.viewModel
import org.koin.core.context.GlobalContext
import kotlin.math.abs

/**
 * @des:
 * @author: lijunjia
 * @date: 2023/7/12 18:03
 */
class HomeCreateRoomFragment :
    BaseEditorFragmentMaverick<FragmentHomeCreateRoomBinding>(R.layout.fragment_home_create_room) {

    companion object {
        const val maxLength = 30
        const val ITEM_WIDTH_DEFAULT = 110
        const val ITEM_HEIGHT_DEFAULT = 120

        private const val STATUS_INIT = 0
        private const val STATUS_PREPARE = 1
        private const val STATUS_OK = 2
    }

    private val viewModel by viewModel<HomeCreateRoomViewModel>()
    private val vm2: HomeCreateRoomViewModelV2 by fragmentViewModel()
    private val scaleTouchSlop by lazy {
        ViewConfiguration.get(requireContext()).scaledTouchSlop
    }
    private val adapterRoomTag by lazy {
        RoomTagAdapter()
    }
    private val styleController by lazy { buildStyleController() }
    private val roomController by lazy { buildRoomController() }

    private var itemWidth = ITEM_WIDTH_DEFAULT.dp
    private var itemHeight = ITEM_HEIGHT_DEFAULT.dp

    private val itemListener = object : IHomeCreateRoomListener {
        override fun selectStyle(roomStyle: RoomStyle) {
            vm2.selectStyle(roomStyle)
        }

        override fun selectRoom(room: EditorCreationShowInfo) {
            Analytics.track(
                EventConstants.EVENT_CHATROOM_UGC_TEMPLATE_CHOOSE,
                "ugcid" to room.getUgcId().orEmpty()
            )
            vm2.selectRoom(room)
        }

        override fun operateRoom(room: EditorCreationShowInfo, position: Int) {
            Analytics.track(
                EventConstants.EVENT_CHATROOM_UGC_TEMPLATE_POPUP_SHOW,
                "ugcid" to room.getUgcId().orEmpty()
            )
            showOptionDialog(room, position)
        }

        override fun getGlideOrNull(): RequestManager? {
            return glide
        }
    }

    private var refreshStatus = STATUS_INIT
    private var refreshFileId: String? = null

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentHomeCreateRoomBinding? {
        return FragmentHomeCreateRoomBinding.inflate(inflater, container, false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        itemWidth = if (ScreenUtil.isPad(requireContext())) {
            ITEM_WIDTH_DEFAULT.dp
        } else {
            (ScreenUtil.screenWidth - 24.dp) * 10 / 32
        }
        itemHeight = itemWidth * ITEM_HEIGHT_DEFAULT / ITEM_WIDTH_DEFAULT
        styleController.onRestoreInstanceState(savedInstanceState)
        roomController.onRestoreInstanceState(savedInstanceState)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        styleController.onSaveInstanceState(outState)
        roomController.onSaveInstanceState(outState)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        HomeRoomAnalytics.trackCreateRoomPageShow()
        initView()
        initData()
    }

    private fun initView() {
        binding.root.autoHideKeyboard(viewLifecycleOwner, needClearFocus = true)
        binding.circleProgressBar.setProgressFormatter(null)
        binding.etRoomName.filters = arrayOf(LengthFilter(maxLength))
        binding.titleBar.setOnBackClickedListener {
            findNavController().popBackStack()
        }

        binding.recyclerView.layoutManager = object : GridLayoutManager(requireContext(), 3) {
            override fun canScrollVertically(): Boolean {
                return false
            }
        }
        binding.recyclerView.adapter = adapterRoomTag.apply {
            addChildClickViewIds(R.id.tv_room_tag)
            setOnItemChildClickListener { view, position ->
                if (view.id == R.id.tv_room_tag) {
                    viewModel.selectRoomTag(position)
                }
            }
        }
        binding.etRoomName.addTextChangedListener(viewLifecycleOwner, afterTextChanged = { s ->
            val length = s?.length ?: 0
            val process = length.toFloat() / maxLength
            viewModel.setEditInputProgress(process)
            checkCreateRoomBtnState(length > 0)
        })
        binding.rvStyle.setHeight(itemHeight)
        binding.rvStyle.layoutManager = LinearLayoutManager(
            requireContext(),
            LinearLayoutManager.HORIZONTAL,
            false
        )
        binding.rvStyle.setController(styleController)

        binding.tvCreate.setOnAntiViolenceClickListener {
            viewModel.createRoom(binding.etRoomName.text.toString(), vm2.curStyle)
        }
        binding.ivAbout.setOnAntiViolenceClickListener {
            showLiveRoomIntroduceDialog()
        }
        checkCreateRoomBtnState(!binding.etRoomName.text.isNullOrEmpty())

        binding.nestScrollView.setOnTouchListener(onTouchListener)

        vm2.onEach(HomeCreateRoomState::styles) {
            val showStyle = !it.invoke().isNullOrEmpty()
            binding.tvStyleLabel.visible(showStyle)
            binding.rvStyle.visible(showStyle)
        }

        if (PandoraToggle.enableUgcLiveRoom) {
            binding.rvRoom.setHeight(itemHeight)
            binding.rvRoom.layoutManager = LinearLayoutManager(
                requireContext(),
                LinearLayoutManager.HORIZONTAL,
                false
            )
            binding.rvRoom.setController(roomController)

            binding.tvNewBuilt.setOnAntiViolenceClickListener {
                val context = context ?: return@setOnAntiViolenceClickListener
                if (!MWBiz.isAvailable() && !PandoraInit.checkEsVersion(context)) {
                    toast(R.string.opengl_es_tips)
                } else {
                    Analytics.track(EventConstants.EVENT_CHATROOM_UGC_TEMPLATE_ENTER_CLICK)
                    vm2.getGameTemplate()
                }
            }

            vm2.onEach(HomeCreateRoomState::rooms) {
                val showRoom = !it.invoke()?.dataList.isNullOrEmpty()
                binding.tvRoomLabel.visible(showRoom)
                binding.rvRoom.visible(showRoom)
            }
            vm2.onEach(HomeCreateRoomState::draftPair, deliveryMode = uniqueOnly()) {
                val draft = it.first ?: return@onEach
                val fileId = draft.jsonConfig.fileId.orEmpty()
                editorGameLaunchHelper?.startLocalGame(
                    this,
                    draft.jsonConfig.gid,
                    draft.path,
                    draft.jsonConfig.parentPackageName.orEmpty(),
                    fileId,
                    ResIdBean().setClickGameTime(System.currentTimeMillis())
                        .setGameCode(draft.jsonConfig.gid)
                        .setGameId(draft.jsonConfig.gid)
                        .setCategoryID(CategoryId.HOME_ROOM_CREATE)
                        .setTsType(ResIdBean.TS_TYPE_LOCAL)
                )
                refreshStatus = STATUS_PREPARE
                refreshFileId = fileId
            }
            vm2.onEach(HomeCreateRoomState::templateId) {
                binding.tvNewBuilt.invisible(it.isNullOrBlank())
            }
            vm2.onAsync(HomeCreateRoomState::template, deliveryMode = uniqueOnly()) {
                editorGameLaunchHelper?.startTemplateGame(
                    this,
                    it,
                    ResIdBean.newInstance().setCategoryID(CategoryId.HOME_ROOM_CREATE)
                )
                refreshStatus = STATUS_PREPARE
            }
            vm2.onEach(HomeCreateRoomState::scrollToFirst, deliveryMode = uniqueOnly()) {
                if (it != -1L) {
                    binding.rvRoom.smoothScrollToPosition(0)
                }
            }
            vm2.registerAsyncErrorToast(HomeCreateRoomState::template)
        }
    }

    private val onTouchListener = object : View.OnTouchListener {
        private var mDownY = 0f
        override fun onTouch(v: View, event: MotionEvent): Boolean {
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    mDownY = event.y
                }

                MotionEvent.ACTION_MOVE -> {
                    if (abs(event.y - mDownY) > scaleTouchSlop) {
                        InputUtil.hideKeyboard(binding.etRoomName)
                    }
                }

                MotionEvent.ACTION_UP -> {
                    mDownY = 0f
                }
            }
            return false
        }
    }

    private fun showLiveRoomIntroduceDialog() {
        LiveRoomIntroduceDialog.showDialog(childFragmentManager)
    }

    private fun checkCreateRoomBtnState(enable: Boolean) {
        binding.tvCreate.isEnabled = enable
        binding.tvCreate.alpha = if (enable) 1f else 0.5f
    }


    private fun initData() {
        viewModel.tagRoomLiveData.observe(viewLifecycleOwner) {
            binding.recyclerView.setHeight(((it.size + 2) / 3) * 53.dp)
            adapterRoomTag.setList(it)
        }
        viewModel.progressInput.observe(viewLifecycleOwner) {
            when (it) {
                1f -> {
                    binding.ivError.visible()
                    binding.circleProgressBar.gone()
                }

                0f -> {
                    binding.ivError.gone()
                    binding.circleProgressBar.gone()
                }

                else -> {
                    binding.ivError.gone()
                    binding.circleProgressBar.visible()
                    binding.circleProgressBar.progress = (it * 100).toInt()
                }
            }
        }
        viewModel.createRoomResultLiveData.observe(viewLifecycleOwner) {
            val chatRoomInfo = it.first
            if (chatRoomInfo != null) {
                launchGameAndJoinRoom(chatRoomInfo)
            } else {
                if (it.second > 0) {
                    toast(it.second)
                }
            }
        }

        viewModel.loadingLiveData.observe(viewLifecycleOwner) {
            if (it) {
                getLoadingView().showLoading()
            } else {
                getLoadingView().hide()
            }
        }
    }

    private fun launchGameAndJoinRoom(room: ChatRoomInfo) {
        viewLifecycleOwner.lifecycleScope.launchWhenResumed {
            val pkg = room.pkg ?: GlobalContext.get().get<IMetaRepository>()
                .fetchGameInfoByIdFromRemoteWithCache(room.platformGameId)
                .firstOrNull()?.data?.packageName
            MgsGameRoomLauncher.enterMgsGame(
                this@HomeCreateRoomFragment,
                pkg ?: "",
                room.platformGameId,
                MgsBriefRoomInfo(
                    roomIdFromCp = room.roomId,
                    roomName = room.roomName,
                    roomShowNum = null,
                    roomTags = null
                ),
                "create_room",
                0,
                vm2.uuid,
                true,
                ResIdBean.newInstance().setGameId(room.platformGameId)
                    .setCategoryID(CategoryId.HOME_ROOM_CREATE),
                room.roomName
            )
            findNavController().popBackStack()
        }
    }

    private fun getLoadingView() = binding.loadingView

    private fun showOptionDialog(room: EditorCreationShowInfo, position: Int) {
        val edit = SimpleListData(
            getString(R.string.edit),
            bgResource = R.drawable.bg_ffef30_round_30

        )
        val delete = SimpleListData(
            getString(R.string.delete_cap),
            textColor = R.color.color_FF5F42
        )
        val list = buildList {
            if (room.draftInfo != null) add(edit)
            add(delete)
        }
        ListDialog()
            .list(list)
            .clickCallback {
                when (it) {
                    edit -> {
                        Analytics.track(
                            EventConstants.EVENT_CHATROOM_UGC_TEMPLATE_EDIT_CLICK,
                            "ugcid" to room.getUgcId().orEmpty()
                        )
                        vm2.editDraft(room)
                    }

                    delete -> {
                        Analytics.track(
                            EventConstants.EVENT_CHATROOM_UGC_TEMPLATE_DELETE_CLICK,
                            "ugcid" to room.getUgcId().orEmpty()
                        )
                        showDeleteDialog(room, position)
                    }
                }
            }
            .show(childFragmentManager,"OperateRoomStyleDialog")
    }

    private fun showDeleteDialog(room: EditorCreationShowInfo, position: Int) {
        ConfirmDialog.Builder(this)
            .content(getString(R.string.delete_ugc_room_template_tips))
            .isRed(true)
            .confirmBtnTxt(getString(R.string.delete_cap), lightBackground = true)
            .confirmCallback {
                vm2.deleteRoom(room, position)
            }
            .show()
    }

    private fun buildStyleController() = simpleController(
        vm2,
        HomeCreateRoomState::styles,
        HomeCreateRoomState::curStyle
    ) { styles, curStyle ->
        styles.invoke()?.forEachIndexed { index, item ->
            homeCreateRoomStyleItem(
                item,
                curStyle != null && item == curStyle,
                itemWidth,
                index,
                itemListener
            )
        }
    }

    private fun buildRoomController() = simpleController(
        vm2,
        HomeCreateRoomState::rooms,
        HomeCreateRoomState::curRoom,
        HomeCreateRoomState::roomsLoadMore,
    ) { rooms, curRoom, roomLoadMore ->
        if (PandoraToggle.enableUgcLiveRoom) {
            rooms.invoke()?.dataList?.forEachIndexed { index, item ->
                homeCreateRoomItem(
                    item,
                    curRoom != null && item == curRoom,
                    itemWidth,
                    index,
                    itemListener
                )
            }
            if (rooms is Success) {
                loadMoreVerticalFooter(
                    roomLoadMore,
                    idStr = "HomeCreateRoomFooter",
                    spanSize = 2,
                    showRetry = false,
                    showEnd = false
                ) {
                    vm2.getRooms()
                }
            }
        }
    }

    override fun hideLoadingUI(launchSuccess: Boolean, msg: String?, needGoMine: Boolean) {
        super.hideLoadingUI(launchSuccess, msg, needGoMine)
        if (refreshStatus == STATUS_PREPARE && launchSuccess) {
            refreshStatus = STATUS_OK
        }
    }

    override fun onStart() {
        super.onStart()
        if (refreshStatus == STATUS_OK) {
            refreshStatus = STATUS_INIT
            vm2.getDrafts(true, refreshFileId)
            refreshFileId = null
        }
    }

    override fun invalidate() {}

    override fun getPageName(): String = PageNameConstants.FRAGMENT_NAME_HOME_CREATE_ROOM
}