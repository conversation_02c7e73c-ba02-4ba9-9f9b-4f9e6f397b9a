package com.socialplay.gpark.ui.developer.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import com.socialplay.gpark.databinding.AdapterDeveloperDialogSelectItemBinding
import com.socialplay.gpark.ui.base.adapter.BaseAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder

/**
 * xingxiu.hou
 * 2021/6/7
 */
class DeveloperSelectAdapter : BaseAdapter<String, AdapterDeveloperDialogSelectItemBinding>() {

    override fun convert(holder: BindingViewHolder<AdapterDeveloperDialogSelectItemBinding>, item: String, position: Int) {
        holder.binding.tvSelectItem.text = item
    }

    override fun createViewBinding(layoutInflater: LayoutInflater, parent: ViewGroup, viewType: Int): AdapterDeveloperDialogSelectItemBinding {
        return AdapterDeveloperDialogSelectItemBinding.inflate(
            layoutInflater,
            parent,
            false
        )
    }

}