package com.socialplay.gpark.ui.moments.main

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.viewbinding.ViewBinding
import com.bumptech.glide.Glide
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.moments.PlotTemplate
import com.socialplay.gpark.data.model.moments.PlotTemplateCollection
import com.socialplay.gpark.data.model.moments.PlotTemplateStyle
import com.socialplay.gpark.data.model.moments.PlotTitle
import com.socialplay.gpark.databinding.AdapterPlotCollectionBinding
import com.socialplay.gpark.databinding.AdapterPlotItemBinding
import com.socialplay.gpark.databinding.AdapterPlotTitleBinding
import com.socialplay.gpark.ui.base.adapter.BaseVBViewHolder
import com.socialplay.gpark.ui.base.adapter.BasicQuickAdapter
import com.socialplay.gpark.ui.base.adapter.createViewBinding
import com.socialplay.gpark.ui.core.GlideGetter
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.context
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.screenWidth
import com.socialplay.gpark.util.extension.setHeight
import com.socialplay.gpark.util.extension.setWidth
import java.util.Locale

/**
 * 2023/9/19
 */

typealias OnMomentMoreClick = (PlotTitle) -> Unit

class MomentsListAdapter(private val glide: GlideGetter) : SimMultiAdapter() {

    private var onMomentMoreClick: OnMomentMoreClick? = null

    companion object {
        const val PADDING_TYPE_RV = 1
        const val PADDING_TYPE_LEFT = 2
        const val PADDING_TYPE_CENTER = 3
        const val PADDING_TYPE_RIGHT = 4
    }

    init {
        addType(
            PlotTitle.TYPE_TITLE,
            PlotTitleBindViewHolder(glide) { onMomentMoreClick?.invoke(it) })
        addType(
            PlotTemplateStyle.TYPE_N_ROW_2_COLUMN,
            PlotItemBindViewHolder(glide, ::getPaddingArray)
        )
        addType(
            PlotTemplateStyle.TYPE_N_ROW_3_COLUMN,
            PlotItemBindViewHolder(glide, ::getPaddingArray)
        )
        addType(
            PlotTemplateStyle.TYPE_1_ROW_N_COLUMN,
            PlotCollectionItemBindViewHolder(glide, ::onPlotClick, ::getPaddingArray)
        )
    }

    private fun getPaddingArray(binding: ViewBinding, localePaddingType: Int, itemType: Int): IntArray {
        return when (localePaddingType) {
            PADDING_TYPE_RV -> {
                intArrayOf(binding.dp(5), binding.dp(12), binding.dp(5), 0)
            }

            PADDING_TYPE_LEFT -> {
                if (itemType == PlotTemplateStyle.TYPE_N_ROW_2_COLUMN) {
                    intArrayOf(binding.dp(16), binding.dp(12), binding.dp(5.5f), 0)
                } else {
                    intArrayOf(binding.dp(16), binding.dp(12), binding.dp(2.7f), 0)
                }
            }

            PADDING_TYPE_RIGHT -> {
                if (itemType == PlotTemplateStyle.TYPE_N_ROW_2_COLUMN) {
                    intArrayOf(binding.dp(5.5f), binding.dp(12), binding.dp(16), 0)
                } else {
                    intArrayOf(binding.dp(2.7f), binding.dp(12), binding.dp(16), 0)
                }
            }

            else -> intArrayOf(binding.dp(9.3f), binding.dp(12), binding.dp(9.3f), 0)
        }
    }

    private fun onPlotClick(position: Int, item: PlotTemplate) {
        mOnItemClickListener?.invoke(position, item)
    }

    fun setOnMomentMoreClick(onMomentMoreClick: OnMomentMoreClick) {
        this.onMomentMoreClick = onMomentMoreClick
    }
}

class PlotCollectionAdapter(
    private val glide: GlideGetter,
    data: ArrayList<PlotTemplate>,
    private val itemWidth: Int,
    private val paddingArray: (binding: ViewBinding, localePaddingType: Int, itemType: Int) -> IntArray
) : BasicQuickAdapter<PlotTemplate, AdapterPlotItemBinding>(data) {

    override fun convert(holder: BaseVBViewHolder<AdapterPlotItemBinding>, item: PlotTemplate) {
        onBindPlotView(
            glide,
            item,
            itemWidth,
            paddingArray(holder.binding, item.localPaddingType, item.getLocalRowColumnStyle()),
            holder.binding
        )
    }

    override fun viewBinding(parent: ViewGroup, viewType: Int) =
        parent.createViewBinding(AdapterPlotItemBinding::inflate)
}

class PlotTitleViewHolder(binding: ViewBinding) : BaseSimMultiViewHolder(binding)
class PlotItemViewHolder(binding: ViewBinding) : BaseSimMultiViewHolder(binding)
class PloCollectionViewHolder(binding: ViewBinding) : BaseSimMultiViewHolder(binding)

class PlotTitleBindViewHolder(
    private val glide: GlideGetter,
    private val onMomentMoreClick: OnMomentMoreClick,
) : OnSimMultiItemBindViewHolder<PlotTitle, AdapterPlotTitleBinding> {
    override fun onBindView(binding: AdapterPlotTitleBinding, position: Int, item: PlotTitle) {
        binding.tvMore.isVisible = item.showMore
        if (item.showMore) {
            binding.tvMore.setOnClickListener {
                onMomentMoreClick.invoke(item)
            }
        }
        binding.spaceTop.setHeight(if (position == 0) binding.dp(16) else binding.dp(25))
        binding.tvTitle.text = item.name.replaceFirstChar {
            if (it.isLowerCase()) it.titlecase(Locale.ROOT) else it.toString()
        }
        glide()?.run {
            if (item.iconRes == null) {
                load(item.icon).into(binding.ivIcon)
            } else {
                load(item.iconRes).into(binding.ivIcon)
            }
        }
    }

    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup,
        attachToParent: Boolean,
    ): BaseSimMultiViewHolder {
        return PlotTitleViewHolder(
            AdapterPlotTitleBinding.inflate(
                inflater,
                parent,
                attachToParent
            )
        )
    }

}

class PlotItemBindViewHolder(
    private val glide: GlideGetter,
    private val paddingArray: (binding: ViewBinding, localePaddingType: Int, itemType: Int) -> IntArray
) : OnSimMultiItemBindViewHolder<PlotTemplate, AdapterPlotItemBinding> {
    override fun onBindView(binding: AdapterPlotItemBinding, position: Int, item: PlotTemplate) {
        onBindPlotView(
            glide,
            item,
            ViewGroup.LayoutParams.MATCH_PARENT,
            paddingArray(binding, item.localPaddingType, item.getLocalRowColumnStyle()),
            binding
        )
    }

    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup,
        attachToParent: Boolean,
    ): BaseSimMultiViewHolder {
        return PlotItemViewHolder(
            AdapterPlotItemBinding.inflate(
                inflater,
                parent,
                attachToParent
            )
        )
    }
}

class PlotCollectionItemBindViewHolder(
    private val glide: GlideGetter,
    private val plotClickListener: (Int, PlotTemplate) -> Unit,
    private val paddingArray: (binding: ViewBinding, localePaddingType: Int, itemType: Int) -> IntArray
) :
    OnSimMultiItemBindViewHolder<PlotTemplateCollection, AdapterPlotCollectionBinding> {
    override fun onBindView(
        binding: AdapterPlotCollectionBinding,
        position: Int,
        item: PlotTemplateCollection
    ) {
        val itemWidth = ((binding.screenWidth - binding.dp(22)) / 3.3F).toInt()
        val adapter = PlotCollectionAdapter(
            glide,
            ArrayList(item.dataList),
            itemWidth,
            paddingArray
        ).apply {
            setOnItemClickListener { _, _, position ->
                val plot = getItem(position)
                plotClickListener.invoke(position, plot)
            }
        }
        binding.rvCollection.adapter = adapter
    }

    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup,
        attachToParent: Boolean
    ): BaseSimMultiViewHolder {
        return PloCollectionViewHolder(
            AdapterPlotCollectionBinding.inflate(
                inflater,
                parent,
                attachToParent
            )
        )
    }
}

private fun formatShort(context: Context, short: Int): String {
    return context.getString(R.string.moment_user_format, UnitUtil.formatKMCount(short.toLong()))
}

private fun onBindPlotView(
    glide: GlideGetter,
    item: PlotTemplate,
    itemWidth: Int,
    padding: IntArray,
    binding: AdapterPlotItemBinding
) {
    binding.spaceStart.setWidth(padding[0])
    binding.spaceTop.setHeight(padding[1])
    binding.spaceEnd.setWidth(padding[2])
    binding.root.setWidth(itemWidth)
    glide()?.run {
        load(item.materialUrl).into(binding.ivImg)
    }
    binding.tvShortNum.text = formatShort(binding.context, item.useCount)
    binding.tvName.text = item.templateName
}