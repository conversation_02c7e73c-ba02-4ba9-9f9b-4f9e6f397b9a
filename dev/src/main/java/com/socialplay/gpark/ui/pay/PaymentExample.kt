package com.socialplay.gpark.ui.pay

import androidx.fragment.app.Fragment
import com.socialplay.gpark.data.model.pay.PaymentResult
import com.socialplay.gpark.util.ToastUtil

/**
 * 支付弹框使用示例
 */
object PaymentExample {

    /**
     * 显示支付弹框示例
     */
    fun showPaymentDialog(fragment: Fragment) {
        PaymentBottomSheetDialog.show(
            fragmentManager = fragment.childFragmentManager,
            productName = "高级会员套餐",
            paymentAmount = 9900L, // 99.00 平台币
            actualPrice = "$9.99",
            productId = "premium_monthly",
            gameId = "game_123",
            source = "home_page"
        ) { result ->
            when (result) {
                is PaymentResult.Success -> {
                    ToastUtil.showShort("支付成功！")
                    // 处理支付成功逻辑
                }
                is PaymentResult.Failed -> {
                    ToastUtil.showShort("支付失败：${result.errorMessage}")
                    // 处理支付失败逻辑
                }
                is PaymentResult.Cancelled -> {
                    ToastUtil.showShort("支付已取消")
                    // 处理支付取消逻辑
                }
            }
        }
    }
} 