package com.socialplay.gpark.ui.outfit

import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.GridLayoutManager
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.mvrx.args
import com.airbnb.mvrx.asMavericksArgs
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.parentFragmentViewModel
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.outfit.UgcAssetProfile
import com.socialplay.gpark.data.model.outfit.UgcAssetProfileEntrance
import com.socialplay.gpark.databinding.FragmentProfileTabUgcDesignBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.dialog.LoadingDialogFragment
import com.socialplay.gpark.ui.editor.BaseEditorFragmentV2
import com.socialplay.gpark.ui.profile.home.ProfileViewModel
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.isPad
import com.socialplay.gpark.util.extension.screenWidth
import com.socialplay.gpark.util.extension.toast
import kotlinx.parcelize.Parcelize

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/11/05
 *     desc   :
 * </pre>
 */
@Parcelize
data class ProfileUgcDesignFragmentArgs(val isMe: Boolean, val uuid: String) : Parcelable

class ProfileUgcDesignFragment :
    BaseEditorFragmentV2<FragmentProfileTabUgcDesignBinding>(R.layout.fragment_profile_tab_ugc_design) {

    companion object {
        fun newInstance(isMe: Boolean, uuid: String): ProfileUgcDesignFragment {
            return ProfileUgcDesignFragment().apply {
                arguments = ProfileUgcDesignFragmentArgs(isMe, uuid).asMavericksArgs()
            }
        }
    }

    private val args by args<ProfileUgcDesignFragmentArgs>()
    private val vm: ProfileUgcDesignViewModel by fragmentViewModel()
    private val profileViewModel: ProfileViewModel by parentFragmentViewModel()

    override val recyclerView: EpoxyRecyclerView
        get() = binding.rv

    private var spanSize = 4

    private var otherUid: String = ""

    private var loadingDialog: LoadingDialogFragment? = null

    private var needRefresh = 0

    private val itemListenerV2 = object : IProfileUgcAssetListener {
        override fun clickMore(item: UgcAssetProfileEntrance) {
            Analytics.track(
                EventConstants.MY_LIBRARY_TAB_MORE_CLICK,
                "type" to (if (item.isDesign) 0 else 1)
            )
            MetaRouter.UgcDesign.assetList(
                this@ProfileUgcDesignFragment,
                item.entrance,
                vm.oldState.uuid,
                args.isMe,
                item.msg
            )
        }

        override fun clickAsset(item: UgcAssetProfile, position: Int) {
            if (item.published) {
                MetaRouter.UgcDesign.detail(
                    this@ProfileUgcDesignFragment,
                    item.itemId,
                    CategoryId.UGC_DESIGN_PROFILE_DESIGN_TAB
                )
            } else {
                toast(R.string.ugc_asset_click_hidden_tips)
            }
        }

        override fun getGlideOrNull(): RequestManager? {
            return glide
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        vm.apiMonitor(
            this,
            ProfileUgcDesignState::entrances
        )
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentProfileTabUgcDesignBinding? {
        return FragmentProfileTabUgcDesignBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        otherUid = profileViewModel.oldState.uuid
        vm.updateUuid(otherUid)
        if (args.isMe) {
            vm.accountInteractor.accountLiveData.observe(viewLifecycleOwner) {
                val uuid = it?.uuid ?: return@observe
                if (otherUid != uuid) {
                    otherUid = uuid
                    vm.updateUuid(otherUid)
                }
            }
        }
        spanSize = if (isPad) {
            ((screenWidth - dp(24)) / dp(88)).coerceAtLeast(4)
        } else {
            4
        }
        vm.updateSize(spanSize)

        binding.loadingUgc.setVerticalBias(0.08f)

        recyclerView.layoutManager = GridLayoutManager(requireContext(), spanSize)

        binding.loadingUgc.setEmptyBtnClick {
            Analytics.track(
                EventConstants.GUIDE_CREATE_ONE_MINUTE_CLICK
            )
            if (needRefresh == 0) {
                needRefresh = 1
            }
            vm.getModuleTemplate()
        }

        vm.setupRefreshLoading(
            ProfileUgcDesignState::entrances,
            binding.loadingUgc,
            binding.refresh,
            {
                if (vm.showGuideBtn) {
                    binding.loadingUgc.showEmpty(
                        if (args.isMe) {
                            getString(R.string.profile_assets_tab_empty)
                        } else {
                            getString(R.string.profile_assets_tab_empty_other)
                        },
                        R.drawable.icon_no_recent_activity,
                        true,
                        getString(R.string.ugc_module_guide_create_one_minute)
                    )
                } else {
                    binding.loadingUgc.showEmpty(
                        if (args.isMe) {
                            getString(R.string.profile_assets_tab_empty)
                        } else {
                            getString(R.string.profile_assets_tab_empty_other)
                        },
                        R.drawable.icon_no_recent_activity,
                    )
                }
            }
        ) {
            vm.load()
        }
        vm.onAsync(ProfileUgcDesignState::tags, onLoading = {
            binding.loadingUgc.showLoading()
        }, onFail = { _, _ ->
            binding.loadingUgc.showError()
        })
        vm.onAsync(ProfileUgcDesignState::template, deliveryMode = uniqueOnly(), onFail = { _, _ ->
            loadingDialog?.dismissAllowingStateLoss()
            loadingDialog = null
        }, onLoading = {
            loadingDialog?.dismissAllowingStateLoss()
            loadingDialog = MetaRouter.Dialog.loading(childFragmentManager)
        }) {
            editorGameLaunchHelper?.startTemplateGame(
                this,
                it,
                ResIdBean().setCategoryID(CategoryId.UGC_DESIGN_PROFILE_DESIGN_TAB)
                    .setIsModuleGuide(true)
            )
        }
        vm.registerAsyncErrorToast(ProfileUgcDesignState::loadMore)
        vm.registerAsyncErrorToast(ProfileUgcDesignState::deleteUgcDesignResult)
    }

    override fun showLoadingUI() {
        loadingDialog?.dismissAllowingStateLoss()
        loadingDialog = null
        super.showLoadingUI()
    }

    override fun hideLoadingUI(launchSuccess: Boolean, msg: String?, needGoMine: Boolean) {
        super.hideLoadingUI(launchSuccess, msg, needGoMine)
        if (needRefresh == 1) {
            needRefresh = if (launchSuccess) {
                2
            } else {
                0
            }
        }
    }

    override fun enableGoMine() = false

    override fun epoxyController() = simpleController(
        vm,
        ProfileUgcDesignState::entrances
    ) { entrances ->
        entrances()?.forEachIndexed { index, item ->
            profileUgcAssetEntrance(item, args.isMe, index, spanSize, itemListenerV2)
        }
    }

    override fun invalidate() {}

    override fun getPageName() = PageNameConstants.FRAGMENT_PROFILE_UGC_DESIGN_TAB

    override fun onStop() {
        super.onStop()
        if (needRefresh == 2) {
            needRefresh = 3
        }
    }

    override fun onResume() {
        super.onResume()
        if (needRefresh == 3) {
            needRefresh = 0
            vm.load()
        }
    }
}