package com.socialplay.gpark.ui.post.feed.base

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.MavericksState
import com.socialplay.gpark.data.model.post.CommunityFeedInfo
import com.socialplay.gpark.data.model.post.CommunityFeedWrapper
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.util.ToastData

/**
 * 帖子feed流 基础state
 */
interface ICommunityFeedModelState : MavericksState {
    val refresh: Async<List<CommunityFeedInfo>>
    val toastMsg: ToastData
    val loadMore: Async<LoadMoreState>
    val nextPage: Int
    val notifyCheckVideo: Async<Long>
    val list: List<CommunityFeedInfo> get() = refresh.invoke() ?: emptyList()

    // 仅更新feed数据，不改变状态
    fun updateFeedData(list: List<CommunityFeedInfo>): ICommunityFeedModelState
    fun toast(toastMsg: ToastData): ICommunityFeedModelState

    // 检查feed视频
    fun checkVideo(checkVideo: Async<Long>): ICommunityFeedModelState

    // 刷新列表数据，改变状态
    fun feedRefresh(result: Async<CommunityFeedWrapper>): ICommunityFeedModelState

    // 列表加载更多数据
    fun feedLoadMore(result: Async<CommunityFeedWrapper>): ICommunityFeedModelState
}