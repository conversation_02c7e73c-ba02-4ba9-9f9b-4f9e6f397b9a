package com.socialplay.gpark.ui.web

import android.os.Bundle
import android.view.ViewGroup
import android.view.WindowManager
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.FrameLayout
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.lifecycleScope
import com.airbnb.mvrx.args
import com.airbnb.mvrx.asMavericksArgs
import com.bin.cpbus.CpEventBus
import com.meta.biz.ugc.model.GameCommonFeature
import com.meta.web.WebRouter
import com.socialplay.gpark.R
import com.socialplay.gpark.contract.web.gameId
import com.socialplay.gpark.data.model.event.InGameOpenWebEvent
import com.socialplay.gpark.data.model.event.MWTransWebEvent
import com.socialplay.gpark.databinding.DialogGameWebBinding
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.mw.GameCommonFeatureResolver
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.core.BaseDialogFragment
import com.socialplay.gpark.ui.view.FixedScrollWebView
import com.socialplay.gpark.ui.web.jsinterfaces.JsBridgeApi
import com.socialplay.gpark.ui.web.jsinterfaces.JsBridgeHelper
import com.socialplay.gpark.ui.web.jsinterfaces.contract.FragmentJsBridgeContract
import com.socialplay.gpark.ui.web.jsinterfaces.contract.GameActivityJsBridgeContract
import com.socialplay.gpark.ui.web.webclients.DefaultWebChromeClient
import com.socialplay.gpark.ui.web.webclients.DefaultWebSettings
import com.socialplay.gpark.ui.web.webclients.DefaultWebViewClient
import com.socialplay.gpark.util.WebUtil
import com.socialplay.gpark.util.extension.setFragmentResultByActivity
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.Subscribe
import timber.log.Timber

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/05/20
 *     desc   :
 * </pre>
 */
class GameWebDialog : BaseDialogFragment() {

    companion object {
        const val KEY = "GameWebDialog"

        fun show(
            activity: FragmentActivity,
            inGameOpenWebEvent: InGameOpenWebEvent,
            listener: (BaseDialogFragment) -> Unit
        ): DialogFragment {
            if (PandoraToggle.isOpenBabelWeb) {
                return WebRouter.showFullScreenDialog(
                    fragmentManager = activity.supportFragmentManager,
                    resIdBean = ResIdBean(),
                    url = inGameOpenWebEvent.url,
                    fragmentResultCallback = { dialog, data ->
                        listener(dialog)
                        inGameOpenWebEvent.orientation?.let { activity.requestedOrientation = it }
                    }
                ) {
                    this.gameId = inGameOpenWebEvent.gameId
                    this.preloadUniqueId = inGameOpenWebEvent.preUnique/*TODO
                    this.backToClose = backToClose
                    this.reload = reload*/
                    this.releaseWhenNavigateUp = inGameOpenWebEvent.backToRelease
                }
            } else {
                return GameWebDialog().apply {
                    activity.supportFragmentManager.setFragmentResultListener(KEY, activity) { _, _ ->
                        activity.supportFragmentManager.clearFragmentResult(KEY)
                        activity.supportFragmentManager.clearFragmentResultListener(KEY)
                        listener(this)
                    }
                    arguments = inGameOpenWebEvent.asMavericksArgs()
                    show(activity.supportFragmentManager, KEY)
                }
            }
        }
    }

    private val args by args<InGameOpenWebEvent>()
    override val binding by viewBinding(DialogGameWebBinding::inflate)
    private var webView: WebView? = null

    private var notifyClose = false
    //关闭的时候，是否需要释放webview
    private var closeNeedReleaseWebView: Boolean = true

    override fun init() {
        this.webView =  runCatching {
            createOrGetWebView()
        }.getOrElse {
            toast(R.string.failed_to_load)
            goBack()
            return
        }
        closeNeedReleaseWebView = args.backToRelease
        webView?.setBackgroundColor(resources.getColor(R.color.transparent))
        binding.root.addView(
            webView,
            0,
            FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
            )
        )

        //添加js接口支持
        webView?.also {
            setupWebView(it)
            it.webChromeClient = DefaultWebChromeClient(this)
            it.webViewClient = DefaultWebViewClient(
                this,
                callback = { isSuccess, errorCode ->
                    Timber.i("isSuccess: $isSuccess $errorCode")
                },
                onRenderProcessGone = {
                    runCatching {
                        toast(R.string.failed_to_load)
                        dismissAllowingStateLoss()
                    }
                }
            )
        }
        Timber.i("will load url = ${args.url} ${args.preUnique} ${WebViewCache.hasCache(args.preUnique)} ${args.reload}")
        if (args.reload || !WebViewCache.hasCache(args.preUnique)) {
            Timber.i("load ${args.url}")
            webView?.loadUrl(args.url)
        }

    }

    private fun createOrGetWebView(): WebView {
        val preUnique = args.preUnique
        if (!preUnique.isNullOrEmpty()) {
            return WebViewCache.acquireWebView(preUnique, requireContext())
        }
        return FixedScrollWebView(requireContext())
    }

    private fun setupWebView(webView: WebView) {
        val bridgeContract = FragmentJsBridgeContract(this)
        if (WebViewCache.hasCache(args.preUnique) && webView is FixedScrollWebView && webView.getJsBridgeApi() != null) {
            (webView.getJsBridgeApi()?.helper?.contract as? GameActivityJsBridgeContract)?.setProxyContract(bridgeContract)
        } else {
            //添加js接口支持
            val jsBridgeHelper = JsBridgeHelper(bridgeContract,  webView)
            webView.addJavascriptInterface(JsBridgeApi(jsBridgeHelper), JsBridgeHelper.JS_BRIDGE_ALIAS)
            //webview默认设置项
            val cache = if (WebViewCache.hasCache(args.preUnique)) WebSettings.LOAD_NO_CACHE else null
            //webview默认设置项
            DefaultWebSettings.setWebSettings(webView, cache)
        }
    }

    /**
     * 后退
     */
    private fun goBack() {
        val webView = webView
        if (!args.backToClose && webView?.canGoBack() == true) {
            //能够后退则进行后退操作
            webView.goBack()
        } else {
            dismissAllowingStateLoss()
        }
    }

    override fun onResume() {
        super.onResume()
        webView?.let {
            WebUtil.onResume(it, !WebViewCache.has())
        }
    }

    override fun onPause() {
        super.onPause()
        webView?.let {
            WebUtil.onPause(it, !WebViewCache.has())
        }
    }

    fun navigateToPreviousPage(removeWebView: Boolean = true) {
        closeNeedReleaseWebView = removeWebView
        notifyClose()
        dismissAllowingStateLoss()
    }

    private fun notifyClose() {
        if (!notifyClose) {
            notifyClose = true
            GameCommonFeatureResolver.notifyWebClose(
                args.gameId, args.messageId, args.url, closeNeedReleaseWebView, args.preUnique
            )
        }
    }

    fun dismissDialog() {
        activity?.let {
            if (!it.isFinishing) {
                dismissAllowingStateLoss()
            }
        }
    }

    override fun onDestroyView() {
        notifyClose()
        setFragmentResultByActivity(KEY, Bundle.EMPTY)
        webView?.let {
            it.webChromeClient = null
            it.webViewClient = WebViewClient()
            (it.parent as? ViewGroup)?.removeView(it)
            if (args.preUnique.isNullOrEmpty()) {
                WebUtil.clearWebView(it)
            } else {
                WebViewCache.releaseWebView(it, closeNeedReleaseWebView)
            }
            webView = null
        }
        super.onDestroyView()
    }

    override fun windowHeight() = WindowManager.LayoutParams.MATCH_PARENT

    override fun isFullScreen(): Boolean {
        return true
    }

    override fun isHideNavigation(): Boolean {
        return true
    }

    override fun getFragmentName() = PageNameConstants.DIALOG_GAME_WEB

    override fun onBackPressed(): Boolean {
        goBack()
        return true
    }
}