package com.socialplay.gpark.ui.editorschoice.header.friends

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import com.bumptech.glide.RequestManager
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.choice.ChoiceFriendInfo
import com.socialplay.gpark.data.model.choice.ChoiceFriendInfoDiff
import com.socialplay.gpark.databinding.AdapterItemFriendsActionBinding
import com.socialplay.gpark.databinding.AdapterItemFriendsBinding
import com.socialplay.gpark.ui.base.adapter.BaseAdapter
import com.socialplay.gpark.ui.base.adapter.BasePagingDataAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder

/**
 * 2023/8/11
 */
class ChoiceHomeHeaderFriendsActionAdapter :
    BaseAdapter<ActionItem, AdapterItemFriendsActionBinding>() {
    override fun createViewBinding(
        layoutInflater: LayoutInflater,
        parent: ViewGroup,
        viewType: Int,
    ): AdapterItemFriendsActionBinding {
        return AdapterItemFriendsActionBinding.inflate(layoutInflater, parent, false)
    }

    override fun convert(
        holder: BindingViewHolder<AdapterItemFriendsActionBinding>,
        item: ActionItem,
        position: Int,
    ) {
        holder.binding.apply {
            ivActionIcon.setImageResource(item.icon)
            tvActionName.text = item.name
        }
    }

}