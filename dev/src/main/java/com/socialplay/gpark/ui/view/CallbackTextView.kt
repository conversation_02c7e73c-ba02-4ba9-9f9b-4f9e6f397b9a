package com.socialplay.gpark.ui.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Shader
import android.text.TextPaint
import android.util.AttributeSet
import androidx.annotation.AttrRes
import androidx.lifecycle.LifecycleOwner
import com.socialplay.gpark.util.extension.argb
import com.socialplay.gpark.util.extension.observeOnMainThreadWhenNotDestroyed


class CallbackTextView : MetaTextView {

    private var callback: ((Boolean) -> Unit)? = null

    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, @AttrRes defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    fun setCallback(callback: ((<PERSON><PERSON>an) -> Unit)?) {
        this.callback = callback
    }

    fun setCallback(owner: LifecycleOwner, callback: ((<PERSON><PERSON><PERSON>) -> Unit)?) {
        owner.observeOnMainThreadWhenNotDestroyed(
            register = {
                this.callback = callback
            },
            unregister = {
                this.callback = null
            }
        )
    }

    override fun onDraw(canvas: Canvas) {
        callback?.invoke(paint.measureText(text.toString()) > measuredWidth)
        super.onDraw(canvas)
    }
}