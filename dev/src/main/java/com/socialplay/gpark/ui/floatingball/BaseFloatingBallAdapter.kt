package com.socialplay.gpark.ui.floatingball

import android.annotation.SuppressLint
import android.app.Activity
import android.graphics.PixelFormat
import android.os.Build
import android.view.Gravity
import android.view.View
import android.view.WindowManager

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/08/06
 * desc   :
 * </pre>
 */


abstract class BaseFloatingBallAdapter {

    //页面中所有的view
    val viewList = ArrayList<View>()
    //当前页面绘制的view
    val viewCacheList = ArrayList<View>()

    fun createAllView() {
        val count = getViewCount()
        if (count <= 0) {
            error("must have a view")
        }
        for (i in 0 until count) {
            val view = createView(i)
            viewList.add(view)
            viewCacheList.add(view)
        }
    }

    fun notifyLayoutSizeAndLocationChanged(windowManager: WindowManager) {
        if (viewList.isEmpty()) {
            return
        }
        viewList.forEachIndexed { index, view ->
            val layoutParams = view.layoutParams
            if (layoutParams is LayoutParams) {
                val width = getWidth(index)
                val height = getHeight(index)
                val x = getX(index)
                val y = getY(index)
                if (layoutParams.width != width || layoutParams.height != height || layoutParams.x != x || layoutParams.y != y) {
                    layoutParams.width = width
                    layoutParams.height = height
                    layoutParams.x = x
                    layoutParams.y = y
                    kotlin.runCatching { windowManager.updateViewLayout(view, layoutParams) }
                }
            }
        }
    }

    fun notifyLayoutParamsSizeChanged(windowManager: WindowManager) {
        if (viewList.isEmpty()) {
            return
        }
        viewList.forEachIndexed { index, view ->
            val layoutParams = view.layoutParams
            if (layoutParams is LayoutParams) {
                val width = getWidth(index)
                val height = getHeight(index)
                if (layoutParams.width != width || layoutParams.height != height) {
                    layoutParams.width = width
                    layoutParams.height = height
                    kotlin.runCatching { windowManager.updateViewLayout(view, layoutParams) }
                }
            }
        }
    }

    fun notifyLayoutParamsLocation(activity: Activity, windowManager: WindowManager) {
        if (viewList.isEmpty()) {
            return
        }
        viewList.forEachIndexed { index, view ->
            val layoutParams = view.layoutParams
            if (layoutParams is LayoutParams) {
                val x = getX(index)
                val y = getY(index)
                if (layoutParams.x != x || layoutParams.y != y) {
                    layoutParams.x = x
                    layoutParams.y = y
                    kotlin.runCatching { windowManager.updateViewLayout(view, layoutParams) }
                }
            } else {
                kotlin.runCatching { windowManager.updateViewLayout(view, createLayoutParams(index, activity)) }
            }
        }
    }

    fun notifyChanged(activity: Activity, windowManager: WindowManager) {
        if (viewCacheList.isEmpty()) {
            return
        }
        updateAllViewPosition(activity)
        viewCacheList.forEachIndexed { index, view ->
            kotlin.runCatching { windowManager.removeViewImmediate(view) }
            val layoutParams = createLayoutParams(index, activity)
            kotlin.runCatching { windowManager.addView(view, layoutParams) }
        }
    }
    fun notifyRemove(activity: Activity, windowManager: WindowManager) {
        if (viewList.isEmpty()) {
            return
        }
        viewList.forEachIndexed { index, view ->
            kotlin.runCatching { windowManager.removeViewImmediate(view) }
        }
    }

    fun removeView(view: View) {
        viewList.remove(view)
        viewCacheList.remove(view)
    }


    abstract fun getViewCount(): Int

    abstract fun createView(position: Int): View
    abstract fun updateAllViewPosition(activity: Activity)

    open fun getWidth(position: Int): Int {
        return WindowManager.LayoutParams.WRAP_CONTENT
    }

    open fun getHeight(position: Int): Int {
        return WindowManager.LayoutParams.WRAP_CONTENT
    }

    open fun getX(position: Int): Int {
        return 0
    }

    open fun getY(position: Int): Int {
        return 0
    }

    open fun createLayoutParams(position: Int, activity: Activity): LayoutParams {
        return generateDefLayoutParams(position, activity).also {
            it.x = getX(position)
            it.y = getY(position)
        }
    }

    @SuppressLint("WrongConstant")
    protected open fun generateDefLayoutParams(position: Int, activity: Activity): LayoutParams {
        val layoutParams = LayoutParams()
        layoutParams.width = getWidth(position)
        layoutParams.height = getHeight(position)
        layoutParams.flags = (WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
                or WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
                or WindowManager.LayoutParams.FLAG_LAYOUT_INSET_DECOR
                or WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN
                or WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH
                or WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS // 防止状态栏遮挡通知
                or WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION) // 防止导航栏遮挡通知

        layoutParams.type = WindowManager.LayoutParams.LAST_APPLICATION_WINDOW
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            // 和当前的activity的window异形屏展示保持一致
            layoutParams.layoutInDisplayCutoutMode = activity.window.attributes.layoutInDisplayCutoutMode
        }
        layoutParams.gravity = Gravity.LEFT or Gravity.TOP
        layoutParams.format = PixelFormat.RGBA_8888
        return layoutParams
    }

    open fun afterCreateAllView() {
    }

    class LayoutParams : WindowManager.LayoutParams()
}