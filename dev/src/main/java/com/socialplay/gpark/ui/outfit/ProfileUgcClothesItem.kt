package com.socialplay.gpark.ui.outfit

import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.outfit.ProfileCurrentCloth
import com.socialplay.gpark.databinding.ItemProfileUgcClothBinding
import com.socialplay.gpark.ui.core.IBaseEpoxyItemListener
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaEpoxyController
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.unsetOnClick
import com.socialplay.gpark.util.extension.visible

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/11/20
 *     desc   :
 * </pre>
 */
interface IProfileUgcClothListener : IBaseEpoxyItemListener {
    fun clickCloth(item: ProfileCurrentCloth, position: Int)
}

fun MetaEpoxyController.profileUgcCloth(
    item: Profile<PERSON>urrent<PERSON>loth,
    position: Int,
    listener: IProfileUgcClothListener
) {
    add {
        ProfileUgcClothItem(
            item,
            position,
            listener
        ).id("ProfileUgcCloth-${item.itemId}")
    }
}


data class ProfileUgcClothItem(
    val item: ProfileCurrentCloth,
    val position: Int,
    val listener: IProfileUgcClothListener
) : ViewBindingItemModel<ItemProfileUgcClothBinding>(
    R.layout.item_profile_ugc_cloth,
    ItemProfileUgcClothBinding::bind
) {

    override fun ItemProfileUgcClothBinding.onBind() {
        listener.getGlideOrNull()?.run {
            load(item.iconUrl).into(ivThumb)
        }
        if (item.viewable) {
            ivLabel.visible()
            root.setOnAntiViolenceClickListener {
                listener.clickCloth(item, position)
            }
        } else if (!item.ugc) {
            ivLabel.gone()
            root.setOnAntiViolenceClickListener {
                listener.clickCloth(item, position)
            }
        } else {
            ivLabel.gone()
            root.unsetOnClick()
        }
    }

    override fun ItemProfileUgcClothBinding.onUnbind() {
        root.unsetOnClick()
    }
}