package com.socialplay.gpark.ui.account.startup

import android.content.DialogInterface
import android.os.Parcelable
import android.view.Gravity
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.GridLayoutManager
import com.airbnb.epoxy.EpoxyController
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.mvrx.asMavericksArgs
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.withState
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.DialogChooseAgeBinding
import com.socialplay.gpark.ui.core.BaseRecyclerViewDialogFragment
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.util.extension.enableWithAlpha
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.parcelize.Parcelize

/**
 * Created by bo.li
 * Date: 2023/11/20
 * Desc:
 */
@Parcelize
data class ChooseAgeDialogArgs(
    val currentAge: Int?
) : Parcelable

class ChooseAgeDialog : BaseRecyclerViewDialogFragment() {

    override var navColorRes = R.color.white

    override val binding by viewBinding(DialogChooseAgeBinding::inflate)
    override val recyclerView: EpoxyRecyclerView
        get() = binding.rv

    private val vm: ChooseAgeStateViewModel by fragmentViewModel()
    private var chooseCallback: ((Int) -> Unit)? = null
    private var dismissCallback: (() -> Unit)? = null

    private val selectBirthDayListener = object : ISelectBirthdayListener {
        override fun pickAge(age: Int) {
            vm.updateAge(age)
        }
    }

    companion object {
        fun show(
            fragment: Fragment,
            currentAge: Int?,
            resultCallback: (Int) -> Unit,
            dismissCallback: () -> Unit
        ) {
            ChooseAgeDialog().apply {
                chooseCallback = resultCallback
                this.dismissCallback = dismissCallback
                arguments = ChooseAgeDialogArgs(currentAge).asMavericksArgs()
            }.show(fragment.childFragmentManager, "ChooseAgeDialog")
        }
    }

    override fun init() {
        binding.rv.layoutManager = GridLayoutManager(requireContext(), 4)
        binding.tvNextBtn.setOnAntiViolenceClickListener {
            binding.tvNextBtn.enableWithAlpha(false)
            binding.vCover.visible()
            withState(vm) { state ->
                state.age?.let {
                    chooseCallback?.invoke(it)
                }
                dismissAllowingStateLoss()
            }
        }

        vm.onEach(ChooseAgeState::age, ChooseAgeState::ageOptions) { age, ageOptions ->
            binding.tvNextBtn.enableWithAlpha(age != null && ageOptions.any { it.inAgeRange(age) })
        }
    }

    override fun epoxyController(): EpoxyController = buildSelectBirthdayController()

    private fun buildSelectBirthdayController() = simpleController(
        vm,
        ChooseAgeState::age,
        ChooseAgeState::ageOptions
    ) { age, ageOptions ->
        ageOptions.forEach {
            createSelectBirthdayItemV2(it, it.inAgeRange(age ?: -1), selectBirthDayListener)
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        dismissCallback?.invoke()
        super.onDismiss(dialog)
    }

    override fun gravity(): Int {
        return Gravity.BOTTOM
    }

    override fun isClickOutsideDismiss(): Boolean = true

    override fun isBackPressedDismiss(): Boolean = true
}