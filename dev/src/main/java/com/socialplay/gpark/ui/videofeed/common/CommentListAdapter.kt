package com.socialplay.gpark.ui.videofeed.common

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.marginStart
import androidx.recyclerview.widget.DiffUtil
import androidx.viewbinding.ViewBinding
import com.bumptech.glide.RequestManager
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.videofeed.common.Comment
import com.socialplay.gpark.data.model.videofeed.common.PlayerReply
import com.socialplay.gpark.data.model.videofeed.common.Reply
import com.socialplay.gpark.data.model.videofeed.common.CommentUIState
import com.socialplay.gpark.data.model.videofeed.common.ReplyExpandCollapseBar
import com.socialplay.gpark.data.model.videofeed.common.ReplyExpandCollapseBarStatus
import com.socialplay.gpark.databinding.ItemVideoFeedCommentBinding
import com.socialplay.gpark.databinding.ItemVideoFeedCommentReplyExpandbarBinding
import com.socialplay.gpark.databinding.ItemVideofeedCommentReplyBinding
import com.socialplay.gpark.ui.base.adapter.BaseDifferAdapter
import com.socialplay.gpark.ui.base.adapter.BaseVBViewHolder
import com.socialplay.gpark.ui.view.ExpandableTextView
import com.socialplay.gpark.util.DateUtil.formatCommentDate
import com.socialplay.gpark.util.SpannableHelper
import com.socialplay.gpark.util.extension.context
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.setMargin
import com.socialplay.gpark.util.extension.visible
import timber.log.Timber




typealias CommentClickListener = (bindingAdapterPosition: Int, comment: Comment) -> Unit
typealias CommentLongClickListener = (bindingAdapterPosition: Int, comment: Comment) -> Boolean
typealias CommentTextExpandClickListener = (bindingAdapterPosition: Int, comment: Comment, isExpand: Boolean) -> Unit
typealias CommentMoreClickListener = (view: View, bindingAdapterPosition: Int, comment: Comment) -> Unit


typealias ReplyClickListener = (bindingAdapterPosition: Int, reply: Reply) -> Unit
typealias ReplyLongClickListener = (bindingAdapterPosition: Int, reply: Reply) -> Boolean
typealias ReplyTextExpandClickListener = (bindingAdapterPosition: Int, reply: Reply, isExpand: Boolean) -> Unit
typealias ReplyMoreClickListener = (view: View, bindingAdapterPosition: Int, reply: Reply) -> Unit

/**
 *     author : wei.zhu
 *     e-mail : <EMAIL>
 *     time   : 2023/12/28
 *     desc   :
 */
class CommentListAdapter(
    private val glide: RequestManager,
    private val expandMoreRepliesListener: CommentClickListener,
    private val collapseMoreRepliesListener: CommentClickListener,

    private val commentAuthorClickListener: CommentClickListener,
    private val commentLikeListener: CommentClickListener,
    private val commentClickListener: CommentClickListener,
    private val commentTextExpandClickListener: CommentTextExpandClickListener,
    private val commentMoreClickListener: CommentMoreClickListener,
    private val commentLongClickListener: CommentLongClickListener,

    private val replyAuthorClickListener: ReplyClickListener,
    private val replyLikeClickListener: ReplyClickListener,
    private val replyClickListener: ReplyClickListener,
    private val replyMoreClickListener: ReplyMoreClickListener,
    private val replyTextExpandClickListener: ReplyTextExpandClickListener,
    private val replyLongClickListener: ReplyLongClickListener,

    ) : BaseDifferAdapter<CommentUIState, ViewBinding>(DIFF_CALLBACK), LoadMoreModule {

    companion object {

        const val VIEW_TYPE_COMMENT = 1
        const val VIEW_TYPE_REPLY = 2
        const val VIEW_TYPE_REPLY_EXPAND_COLLAPSE_BAR = 3


        const val PAYLOAD_LIKE_STATUS_CHANGED = 1
        const val PAYLOAD_COMMENT_TEXT_EXPAND_STATUS_CHANGED = 2
        const val PAYLOAD_REPLY_TEXT_EXPAND_STATUS_CHANGED = 3
        const val PAYLOAD_REPLY_LIKE_STATUS_CHANGED = 4

        val DIFF_CALLBACK = object : DiffUtil.ItemCallback<CommentUIState>() {
            override fun areItemsTheSame(oldItem: CommentUIState, newItem: CommentUIState): Boolean {
                return if (oldItem is Comment && newItem is Comment) {
                    oldItem.playerComment.commentId == newItem.playerComment.commentId
                } else if (oldItem is Reply && newItem is Reply) {
                    oldItem.playerReply.replyId == newItem.playerReply.replyId
                } else if (oldItem is ReplyExpandCollapseBar && newItem is ReplyExpandCollapseBar) {
                    oldItem.referencedComment.playerComment.commentId == newItem.referencedComment.playerComment.commentId
                } else {
                    oldItem === newItem
                }
            }

            override fun areContentsTheSame(oldItem: CommentUIState, newItem: CommentUIState): Boolean {
                return oldItem == newItem
            }

            override fun getChangePayload(oldItem: CommentUIState, newItem: CommentUIState): Any? {
                val payloads = mutableListOf<Int>()

                if (oldItem is Comment && newItem is Comment) {
                    // 评论对象上面的回复列表发生变化，不要触发任何变更，此字段可以理解为被忽略掉了
                    if (oldItem.playerComment.replyCommonPage != newItem.playerComment.replyCommonPage) {
                        return emptyList<Int>()
                    }
                    if (oldItem.isLiked != newItem.isLiked) {
                        payloads.add(PAYLOAD_LIKE_STATUS_CHANGED)
                    }

                    if (oldItem.isTextExpanded != newItem.isTextExpanded) {
                        payloads.add(PAYLOAD_COMMENT_TEXT_EXPAND_STATUS_CHANGED)
                    }
                } else if (oldItem is Reply && newItem is Reply) {
                    if (oldItem.isTextExpanded != newItem.isTextExpanded) {
                        payloads.add(PAYLOAD_REPLY_TEXT_EXPAND_STATUS_CHANGED)
                    }

                    if (oldItem.isLiked != newItem.isLiked) {
                        payloads.add(PAYLOAD_REPLY_LIKE_STATUS_CHANGED)
                    }
                }


                return payloads.ifEmpty { null }
            }
        }
    }

    override fun getDefItemViewType(position: Int): Int {
        return when (getItem(position)) {
            is Comment -> {
                VIEW_TYPE_COMMENT
            }

            is Reply -> {
                VIEW_TYPE_REPLY
            }

            is ReplyExpandCollapseBar -> {
                VIEW_TYPE_REPLY_EXPAND_COLLAPSE_BAR
            }
        }
    }

    override fun convert(holder: BaseVBViewHolder<ViewBinding>, item: CommentUIState) {
        when (item) {
            is Comment -> {
                setComment(holder, holder.binding as ItemVideoFeedCommentBinding, item)
            }

            is Reply -> {
                setReply(holder, holder.binding as ItemVideofeedCommentReplyBinding, item)
            }

            is ReplyExpandCollapseBar -> {
                setExpandBar(
                    holder,
                    holder.binding as ItemVideoFeedCommentReplyExpandbarBinding,
                    item
                )
            }
        }
    }

    override fun onItemViewHolderCreated(viewHolder: BaseVBViewHolder<ViewBinding>, viewType: Int) {
        when (viewType) {
            VIEW_TYPE_COMMENT      -> {
                bindCommentClick(
                    viewHolder,
                    viewHolder.binding as ItemVideoFeedCommentBinding,
                )
            }
            VIEW_TYPE_REPLY        -> {
                bindReplyClick(
                    viewHolder,
                    viewHolder.binding as ItemVideofeedCommentReplyBinding
                )
            }
            VIEW_TYPE_REPLY_EXPAND_COLLAPSE_BAR     -> {
                bindReplyExpandCollapseBarClick(
                    viewHolder,
                    viewHolder.binding as ItemVideoFeedCommentReplyExpandbarBinding
                )
            }
        }
    }

    private fun bindCommentClick(
        holder: BaseViewHolder,
        binding: ItemVideoFeedCommentBinding
    ) {

        binding.sivUserAvatar.setOnAdapterItemClickListener<Comment>(holder) { view,bindingAdapterPosition, item ->
            commentAuthorClickListener(bindingAdapterPosition, item)
        }

        binding.tvLikeCount.setOnAdapterItemClickListener<Comment>(holder) { view, bindingAdapterPosition, item ->
            commentLikeListener(bindingAdapterPosition, item)
        }
        binding.ivLike.setOnAdapterItemClickListener<Comment>(holder) { view, bindingAdapterPosition, item ->
            commentLikeListener(bindingAdapterPosition, item)
        }

        binding.clComment.setOnAdapterItemClickListener<Comment>(holder) { view,bindingAdapterPosition, item ->
            commentClickListener(bindingAdapterPosition, item)
        }

        binding.tvContent.setOnAdapterItemClickListener<Comment>(holder) { view,bindingAdapterPosition, item ->
            commentClickListener(bindingAdapterPosition, item)
        }

        binding.root.setOnAdapterItemLongClickListener<Comment>(holder) { bindingAdapterPosition, item ->
            commentLongClickListener(bindingAdapterPosition, item)
        }

        binding.tvContent.setOnAdapterItemLongClickListener<Comment>(holder) { bindingAdapterPosition, item ->
            commentLongClickListener(bindingAdapterPosition, item)
        }

        binding.ivMore.setOnAdapterItemClickListener<Comment>(holder) { view, bindingAdapterPosition, item ->
            commentMoreClickListener(view, bindingAdapterPosition, item)
        }

        binding.tvContent.setExpandListener(object : ExpandableTextView.OnExpandListener {
            override fun onExpand(view: ExpandableTextView) {
                Timber.d("ExpandableTextView onExpand")
                val item = getItem(holder.bindingAdapterPosition) as Comment
                commentTextExpandClickListener.invoke(holder.bindingAdapterPosition, item, true)
            }

            override fun onShrink(view: ExpandableTextView) {
                Timber.d("ExpandableTextView onShrink")
                val item = getItem(holder.bindingAdapterPosition) as Comment
                commentTextExpandClickListener.invoke(holder.bindingAdapterPosition, item, false)
            }
        })
    }


    private fun bindReplyClick(
        holder: BaseViewHolder,
        binding: ItemVideofeedCommentReplyBinding,
    ) {

        binding.clReply.setOnAdapterItemClickListener<Reply>(holder) { view,bindingAdapterPosition, reply ->
            replyClickListener(bindingAdapterPosition, reply)
        }

        binding.tvContent.setOnAdapterItemClickListener<Reply>(holder) { view,bindingAdapterPosition, reply ->
            replyClickListener(bindingAdapterPosition, reply)
        }

        binding.sivUserAvatar.setOnAdapterItemClickListener<Reply>(holder) { view,bindingAdapterPosition, reply ->
            replyAuthorClickListener(bindingAdapterPosition, reply)
        }

        binding.tvLikeCount.setOnAdapterItemClickListener<Reply>(holder) { view,bindingAdapterPosition, reply ->
            replyLikeClickListener(bindingAdapterPosition, reply)
        }

        binding.ivLike.setOnAdapterItemClickListener<Reply>(holder) { view,bindingAdapterPosition, reply ->
            replyLikeClickListener(bindingAdapterPosition, reply)
        }


        binding.ivMore.setOnAdapterItemClickListener<Reply>(holder) { view, bindingAdapterPosition, item ->
            replyMoreClickListener(view, bindingAdapterPosition, item)
        }

        binding.root.setOnAdapterItemLongClickListener<Reply>(holder) { bindingAdapterPosition, reply ->
            replyLongClickListener(bindingAdapterPosition, reply)
        }

        binding.tvContent.setOnAdapterItemLongClickListener<Reply>(holder) { bindingAdapterPosition, reply ->
            replyLongClickListener(bindingAdapterPosition, reply)
        }

        binding.tvContent.setExpandListener(object : ExpandableTextView.OnExpandListener {
            override fun onExpand(view: ExpandableTextView) {
                Timber.d("ExpandableTextView onExpand")
                val item = getItem(holder.bindingAdapterPosition) as Reply
                replyTextExpandClickListener.invoke(holder.bindingAdapterPosition, item, true)
            }

            override fun onShrink(view: ExpandableTextView) {
                Timber.d("ExpandableTextView onShrink")
                val item = getItem(holder.bindingAdapterPosition) as Reply
                replyTextExpandClickListener.invoke(holder.bindingAdapterPosition, item, false)
            }
        })

    }

    private fun bindReplyExpandCollapseBarClick(
        holder: BaseVBViewHolder<ViewBinding>,
        binding: ItemVideoFeedCommentReplyExpandbarBinding,
    ){
        binding.tvExpandReply.setOnAdapterItemClickListener<ReplyExpandCollapseBar>(holder) { view,bindingAdapterPosition, item ->
            expandMoreRepliesListener(bindingAdapterPosition, item.referencedComment)
        }
        binding.tvCollapseReply.setOnAdapterItemClickListener<ReplyExpandCollapseBar>(holder) { view,bindingAdapterPosition, item ->
            collapseMoreRepliesListener(bindingAdapterPosition, item.referencedComment)
        }
    }


    override fun convert(
        holder: BaseVBViewHolder<ViewBinding>,
        item: CommentUIState,
        payloads: List<Any>
    ) {
        payloads.filterIsInstance<List<Int>>().flatten().forEach {
            when (it) {
                PAYLOAD_LIKE_STATUS_CHANGED -> {
                    updateCommentLikeStatusInternal(
                        holder,
                        holder.binding as ItemVideoFeedCommentBinding,
                        item as Comment, true
                    )
                }
                PAYLOAD_COMMENT_TEXT_EXPAND_STATUS_CHANGED -> {
                    setCommentExpandStatus(
                        holder,
                        holder.binding as ItemVideoFeedCommentBinding,
                        item as Comment
                    )
                }

                PAYLOAD_REPLY_TEXT_EXPAND_STATUS_CHANGED -> {
                    setReplyExpandStatus(
                        holder,
                        holder.binding as ItemVideofeedCommentReplyBinding,
                        item as Reply
                    )
                }
                PAYLOAD_REPLY_LIKE_STATUS_CHANGED -> {
                    updateReplyLikeStatusInternal(
                        holder,
                        holder.binding as ItemVideofeedCommentReplyBinding,
                        item as Reply
                    )
                }
                else -> {}
            }
        }
    }

    private fun setExpandBar(
        holder: BaseVBViewHolder<ViewBinding>,
        binding: ItemVideoFeedCommentReplyExpandbarBinding,
        item: ReplyExpandCollapseBar
    ) {

        binding.tvExpandReply.visible(item.expandable)
        binding.tvCollapseReply.visible(item.collapsable)

        if(item.expandable && (item.status == ReplyExpandCollapseBarStatus.Default || item.status == ReplyExpandCollapseBarStatus.Collapsed)){
            binding.tvExpandReply.text = context.getString(
                R.string.article_reply_expand_more_formatted,
                item.remainReplyCountToExpand.toString()
            )
            binding.vCenterLine.setMargin(left = 56.dp)
        }else{
            binding.vCenterLine.setMargin(left = 98.dp)
            binding.tvExpandReply.text = context.getString(R.string.article_repaly_expand_more)
        }
    }

    override fun viewBinding(parent: ViewGroup, viewType: Int): ViewBinding {
        when (viewType) {
            VIEW_TYPE_COMMENT -> {
                return ItemVideoFeedCommentBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
            }
            VIEW_TYPE_REPLY -> {
                return ItemVideofeedCommentReplyBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
            }
            VIEW_TYPE_REPLY_EXPAND_COLLAPSE_BAR -> {
                return ItemVideoFeedCommentReplyExpandbarBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
            }
            else -> throw IllegalArgumentException("viewType is not support")
        }

    }

    private fun setReply(
        holder: BaseViewHolder,
        binding: ItemVideofeedCommentReplyBinding,
        reply: Reply
    ) {
        val playerReply: PlayerReply = reply.playerReply

        // 头像
        glide.load(playerReply.avatar)
            .error(R.drawable.icon_default_avatar)
            .into(binding.sivUserAvatar)

        binding.tvUsername.text = playerReply.nickname

        // 时间
        binding.tvTime.text = playerReply.replyTime.formatCommentDate(binding.context)

        // 点赞
        updateReplyLikeStatusInternal(holder, binding, reply)

        //内容
        val content = if (!playerReply.replyUid.isNullOrEmpty()) {
            SpannableHelper.Builder()
                .text("@${playerReply.replyName}: ")
                .color(context.getColorByRes(R.color.aux_color_3))
                .text(playerReply.content)
                .color(context.getColorByRes(R.color.neutral_color_2))
                .build()
        } else {
            playerReply.content
        }

        // 内容
        binding.tvContent.text = content

        setReplyExpandStatus(holder, binding, reply)

        setReplyUserLabel(holder, binding, reply)
    }

    private fun setComment(
        holder: BaseViewHolder,
        binding: ItemVideoFeedCommentBinding,
        comment: Comment
    ) {

        val item = comment.playerComment

        // 头像
        glide.load(item.avatar)
            .error(R.drawable.icon_default_avatar)
            .into(binding.sivUserAvatar)

        // 用户名
        binding.tvUsername.text = item.nickname

        // 认证
        binding.hlvHonor.visible(comment.isShowHonorLabel)

        // 点赞
        updateCommentLikeStatusInternal(holder, binding, comment)

        // 时间
        binding.tvTime.text = item.commentTime.formatCommentDate(binding.context)

        // Log
        Timber.d("commentList  %s", item.replyCount)

        // 内容
        binding.tvContent.text = item.content ?: ""

        setCommentUserLabel(holder, binding, comment)

        setCommentExpandStatus(holder, binding, comment)
    }


    private fun setCommentExpandStatus(
        holder: BaseViewHolder,
        binding: ItemVideoFeedCommentBinding,
        comment: Comment
    ) {

        val expandState = if (comment.isTextExpanded) {
            ExpandableTextView.STATE_EXPAND
        } else {
            ExpandableTextView.STATE_SHRINK
        }

        binding.tvContent.setCurrState(expandState)
    }

    private fun setReplyExpandStatus(
        holder: BaseViewHolder,
        binding: ItemVideofeedCommentReplyBinding,
        reply: Reply
    ) {
        val expandState = if (reply.isTextExpanded) {
            ExpandableTextView.STATE_EXPAND
        } else {
            ExpandableTextView.STATE_SHRINK
        }

        binding.tvContent.setCurrState(expandState)
    }



    private fun setReplyUserLabel(
        holder: BaseViewHolder,
        binding: ItemVideofeedCommentReplyBinding,
        reply: Reply
    ) {
        when {
            reply.isSelf -> {
                binding.tvLabel.visible(true)
                binding.tvLabel.setBackgroundResource(R.drawable.bg_comment_user_label_self)
                binding.tvLabel.setTextColor(context.getColorByRes(R.color.neutral_color_5))
                binding.tvLabel.text = context.getString(R.string.comment_label_self)
            }
            reply.isAuthor -> {
                binding.tvLabel.visible(true)
                binding.tvLabel.setBackgroundResource(R.drawable.bg_comment_user_label_author)
                binding.tvLabel.text = context.getString(R.string.comment_label_author)
                binding.tvLabel.setTextColor(context.getColorByRes(R.color.neutral_color_1))
            }
            else -> {
                binding.tvLabel.visible(false)
            }
        }
    }

    private fun setCommentUserLabel(
        holder: BaseViewHolder,
        binding: ItemVideoFeedCommentBinding,
        comment: Comment
    ) {
        when {
            comment.isSelf -> {
                binding.tvLabel.visible(true)
                binding.tvLabel.setBackgroundResource(R.drawable.bg_comment_user_label_self)
                binding.tvLabel.setTextColor(context.getColorByRes(R.color.neutral_color_5))
                binding.tvLabel.text = context.getString(R.string.comment_label_self)
            }
            comment.isAuthor -> {
                binding.tvLabel.visible(true)
                binding.tvLabel.setBackgroundResource(R.drawable.bg_comment_user_label_author)
                binding.tvLabel.text = context.getString(R.string.comment_label_author)
                binding.tvLabel.setTextColor(context.getColorByRes(R.color.neutral_color_1))
            }
            else -> {
                binding.tvLabel.visible(false)
            }
        }
    }

    private fun updateCommentLikeStatusInternal(
        holder: BaseViewHolder,
        binding: ItemVideoFeedCommentBinding,
        comment: Comment,
        isUpdate: Boolean = false
    ) {
        val tvLikeCount = binding.tvLikeCount
        tvLikeCount.text = comment.playerComment.likeCount.toString()

        val icon = if (comment.isLiked) {
            R.drawable.ic_ugc_comment_like_selected
        } else {
            R.drawable.ic_ugc_comment_like_unselected
        }
        binding.ivLike.setImageResource(icon)
    }


    private fun updateReplyLikeStatusInternal(
        holder: BaseViewHolder,
        binding: ItemVideofeedCommentReplyBinding,
        reply: Reply,
        isUpdate: Boolean = false
    ) {
        val tvLikeCount = binding.tvLikeCount
        tvLikeCount.text = reply.playerReply.likeCount.toString()

        val icon = if (reply.isLiked) {
            R.drawable.ic_ugc_comment_like_selected
        } else {
            R.drawable.ic_ugc_comment_like_unselected
        }
        binding.ivLike.setImageResource(icon)
    }



    private fun <T : CommentUIState> View.setOnAdapterItemClickListener(
        holder: BaseViewHolder,
        callback: (view: View,bindingAdapterPosition: Int, item: T) -> Unit
    ) {
        setOnClickListener {
            val bindingAdapterPosition = holder.bindingAdapterPosition
            val item = getItem(holder.bindingAdapterPosition) as T
            callback(it, bindingAdapterPosition, item)
        }
    }


    private fun <T : CommentUIState> View.setOnAdapterItemLongClickListener(
        holder: BaseViewHolder,
        callback: (bindingAdapterPosition: Int, item: T) -> Boolean
    ) {
        setOnLongClickListener {
            val bindingAdapterPosition = holder.bindingAdapterPosition
            val item = getItem(holder.bindingAdapterPosition) as T
            return@setOnLongClickListener callback(bindingAdapterPosition, item)
        }
    }
}