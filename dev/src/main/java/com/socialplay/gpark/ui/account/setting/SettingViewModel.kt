package com.socialplay.gpark.ui.account.setting

import android.app.Application
import android.content.Context
import androidx.core.app.NotificationManagerCompat
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.MineActionItem
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.ui.notice.NotificationPermissionManager
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.LifecycleCallback
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

/**
 * created by <PERSON><PERSON><PERSON><PERSON> on 2022/7/28 6:50 下午
 * @describe:
 */
class SettingViewModel(
    val repository: com.socialplay.gpark.data.IMetaRepository,
    val accountInteractor: AccountInteractor,
    val h5PageConfigInteractor: H5PageConfigInteractor,
    val application:Application,
    val metaKV: MetaKV
) : ViewModel() {

    val logoutStateCallback: LifecycleCallback<(DataResult<Boolean>) -> Unit> = LifecycleCallback()

    private val _rewardCodeResultFlow = MutableSharedFlow<Pair<Boolean, String?>?>()
    val rewardCodeResultFlow: Flow<Pair<Boolean, String?>?> = _rewardCodeResultFlow

    private val _switch = MutableLiveData<Boolean>()
    val switch: LiveData<Boolean> = _switch

    fun loadNotification() = viewModelScope.launch {
        repository.getNotificationSwitch().collect {
            _switch.postValue(it.data?.get("noticeSwitch") ?: true)
        }
    }

    fun setNotificationSwitch(checked: Boolean) {
        viewModelScope.launch {
            repository.setNotificationSwitch(checked).collect {
                if (it.data != true) {
                    _switch.postValue(!checked)
                    ToastUtil.showShort(it.message)
                }
            }
        }
    }

    fun logout() = viewModelScope.launch {
        accountInteractor.logout(true).collect {
            logoutStateCallback.dispatchOnMainThread { invoke(it) }
        }
    }
    private val _items = MutableStateFlow<PagingData<MineActionItem>?>(null)
    val items: Flow<PagingData<MineActionItem>?> = _items
    init {
        getItemList()
    }

    private fun getItemList() = viewModelScope.launch {
        val list = SettingItemConfigWrapper.getItemList(application, h5PageConfigInteractor, metaKV)
        _items.emit(PagingData.from(list))
    }

    fun receiveBadge(badgeCode: String) = viewModelScope.launch {
        repository.receiveBadge(badgeCode).collect {
            _rewardCodeResultFlow.emit(Pair(it.data == true, it.message))
        }
    }

    fun checkNotification(context: Context) = viewModelScope.launch {
        val enabled = NotificationManagerCompat.from(context).areNotificationsEnabled()
        NotificationPermissionManager.notificationOpen= enabled
        Analytics.track(
            EventConstants.EVENT_NOTIFICATION_APPLICATION,
            "result" to enabled.toString()
        )
        getItemList()
    }

}