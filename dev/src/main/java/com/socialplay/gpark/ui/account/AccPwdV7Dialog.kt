package com.socialplay.gpark.ui.account

import android.os.Parcelable
import android.text.method.HideReturnsTransformationMethod
import android.text.method.PasswordTransformationMethod
import android.view.View
import android.view.WindowManager
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.airbnb.mvrx.args
import com.airbnb.mvrx.asMavericksArgs
import com.airbnb.mvrx.fragmentViewModel
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.databinding.FragmentAccPwdV7Binding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseDialogFragment
import com.socialplay.gpark.ui.dialog.LoadingDialogFragment
import com.socialplay.gpark.util.ClipBoardUtil
import com.socialplay.gpark.util.extension.addTextChangedListener
import com.socialplay.gpark.util.extension.clearFragmentResultByActivity
import com.socialplay.gpark.util.extension.doOnFocusChange
import com.socialplay.gpark.util.extension.enableWithAlpha
import com.socialplay.gpark.util.extension.setFragmentResultByActivity
import com.socialplay.gpark.util.extension.setFragmentResultListenerByActivity
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTextColorByRes
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.parcelize.Parcelize
import org.koin.core.context.GlobalContext

@Parcelize
data class AccPwdV7DialogArgs(
    val source: Int,
) : Parcelable {

    companion object {
        const val SOURCE_PUBLISH_POST = 1
        const val SOURCE_PUBLISH_PGC_COMMENT = 2
        const val SOURCE_PUBLISH_UGC_COMMENT = 3
        const val SOURCE_PUBLISH_REPLY_COMMENT = 4
        const val SOURCE_PUBLISH_UGC_DESIGN_COMMENT = 5
    }
}

class AccPwdV7Dialog : BaseDialogFragment() {

    companion object {
        const val TAG = "AccPwdV7Fragment"

        fun show(fragment: Fragment, source: Int, afterBind: (Boolean) -> Unit) {
            if (!PandoraToggle.enablePublishSetupAccount
                || GlobalContext.get().get<AccountInteractor>().hasBindAccPwd
            ) {
                afterBind(true)
            } else {
                fragment.setFragmentResultListenerByActivity(
                    TAG,
                    fragment.viewLifecycleOwner
                ) { _, bundle ->
                    fragment.clearFragmentResultByActivity(TAG)
                    val result = bundle.getBoolean(TAG)
                    afterBind(result)
                }
                val dialog = AccPwdV7Dialog()
                dialog.arguments = AccPwdV7DialogArgs(source).asMavericksArgs()
                dialog.show(fragment.childFragmentManager, TAG)
                Analytics.track(
                    EventConstants.PUBLISH_CONNECT_DIALOG_SHOW,
                    "source" to source
                )
            }
        }
    }

    override var navColorRes = R.color.white
    override val binding by viewBinding(FragmentAccPwdV7Binding::inflate)
    private val vm: AccPwdV7ViewModel by fragmentViewModel()
    private val accountInteractor: AccountInteractor = GlobalContext.get().get()
    private val args: AccPwdV7DialogArgs by args()

    private var loadingDialogFragment: LoadingDialogFragment? = null

    //    private var focusAcc = false
    private var focusPwd = false
    private var focusPwd2 = false

    private var result = false

    override fun init() {
        viewLifecycleOwner.lifecycleScope.launch {
            delay(10)
            binding.svContentArea.fullScroll(View.FOCUS_DOWN)
        }
        binding.tbl.setOnBackAntiViolenceClickedListener {
            dismissAllowingStateLoss()
        }
        binding.ivPasswordVisibility.setOnAntiViolenceClickListener {
            vm.switchPwdVis()
        }
        binding.ivPasswordVisibility2.setOnAntiViolenceClickListener {
            vm.switchPwdVis2()
        }
        binding.tvSure.setOnAntiViolenceClickListener {
            Analytics.track(
                EventConstants.PUBLISH_CONNECT_DIALOG_CONFIRM,
                "source" to args.source
            )
            if (binding.etPassword.text.toString() != binding.etPassword2.text.toString()) {
                toast(R.string.password_verify_error)
                return@setOnAntiViolenceClickListener
            }
            vm.signup()
        }

//        binding.etAccount.addTextChangedListener(viewLifecycleOwner) {
//            vm.updateAcc(it?.toString())
//        }
        binding.etPassword.addTextChangedListener(viewLifecycleOwner) {
            vm.updatePwd(it?.toString())
        }
        binding.etPassword2.addTextChangedListener(viewLifecycleOwner) {
            vm.updatePwd2(it?.toString())
        }
//        binding.etAccount.doOnFocusChange(viewLifecycleOwner) { _, hasFocus ->
//            focusAcc = hasFocus
//            binding.inputAccount.setHint(
//                if (hasFocus || vm.accNotEmpty) {
//                    R.string.text_account
//                } else {
//                    R.string.enter_account
//                }
//            )
//            binding.tvAccountFormatErrTip.visible(hasFocus || !vm.accVerify)
//        }
        binding.etPassword.doOnFocusChange(viewLifecycleOwner) { _, hasFocus ->
            focusPwd = hasFocus
            binding.inputPassword.setHint(
                if (hasFocus || vm.pwdNotEmpty) {
                    R.string.please_enter_password
                } else {
                    R.string.please_enter_password
                }
            )
            binding.tvPasswordFormatErrTip.visible(hasFocus || !vm.pwdVerify)
        }

        binding.etPassword2.doOnFocusChange(viewLifecycleOwner) { _, hasFocus ->
            focusPwd2 = hasFocus
            binding.inputPassword2.setHint(
                if (hasFocus || vm.pwd2NotEmpty) {
                    R.string.account_enter_password_again
                } else {
                    R.string.account_enter_password_again
                }
            )
            binding.tvPasswordFormatErrTip.visible(hasFocus || !vm.pwdVerify)
        }

        accountInteractor.getUserInfoFromCache()?.let { it ->
            if (it.userNumber.isNullOrEmpty() == false) {
                binding.tvAccountNumber.text = it.userNumber
//                userNumber = it.userNumber
                binding.llAccountNumber.setOnAntiViolenceClickListener { it2 ->
                    lifecycleScope.launch(Dispatchers.IO) {
                        ClipBoardUtil.setClipBoardContent(it.userNumber, requireContext())
                        toast(R.string.copy_success)
                    }
                }
            }
        }

        vm.onEach(
//            AccPwdV7State::acc,
            AccPwdV7State::pwd,
            AccPwdV7State::pwd2,
        ) { pwd, pwd2 ->
//            val accNotEmpty = !acc.isNullOrEmpty()
            val pwdNotEmpty = !pwd.isNullOrEmpty()
            val pwd2NotEmpty = !pwd2.isNullOrEmpty()
//            binding.inputAccount.setHint(
//                if (focusAcc || accNotEmpty) {
//                    R.string.text_account
//                } else {
//                    R.string.enter_account
//                }
//            )
            binding.inputPassword.setHint(
                if (focusPwd || pwdNotEmpty) {
                    R.string.text_password
                } else {
                    R.string.enter_password
                }
            )
            binding.inputPassword2.setHint(
                if (focusPwd2 || pwd2NotEmpty) {
                    R.string.text_password
                } else {
                    R.string.account_enter_password_again
                }
            )
            binding.tvSure.enableWithAlpha(pwdNotEmpty && pwd2NotEmpty)
        }
        vm.onEach(AccPwdV7State::pwdVis) {
            if (it) {
                binding.ivPasswordVisibility.setImageResource(R.drawable.icon_login_visible_password)
                binding.etPassword.transformationMethod =
                    HideReturnsTransformationMethod.getInstance()
            } else {
                binding.ivPasswordVisibility.setImageResource(R.drawable.icon_login_hiden_password)
                binding.etPassword.transformationMethod = PasswordTransformationMethod.getInstance()
            }
            binding.etPassword.setSelection(binding.etPassword.length())
        }
        vm.onEach(AccPwdV7State::pwdVis2) {
            if (it) {
                binding.ivPasswordVisibility2.setImageResource(R.drawable.icon_login_visible_password)
                binding.etPassword2.transformationMethod =
                    HideReturnsTransformationMethod.getInstance()
            } else {
                binding.ivPasswordVisibility2.setImageResource(R.drawable.icon_login_hiden_password)
                binding.etPassword2.transformationMethod = PasswordTransformationMethod.getInstance()
            }
            binding.etPassword2.setSelection(binding.etPassword2.length())
        }
//        vm.onEach(AccPwdV7State::accVerify) {
//            if (!it) {
//                binding.inputAccount.setBackgroundResource(R.drawable.sp_f6f6f6_c12_ff5f42_s1)
//                binding.tvAccountFormatErrTip.setTextColorByRes(R.color.color_F55C45)
//                binding.tvAccountFormatErrTip.visible()
//            } else {
//                binding.inputAccount.setBackgroundResource(R.drawable.selector_bg_sign_up_focus)
//                binding.tvAccountFormatErrTip.setTextColorByRes(R.color.neutral_color_3)
//                binding.tvAccountFormatErrTip.visible(focusAcc)
//            }
//        }
        vm.onEach(AccPwdV7State::pwdVerify) {
            if (!it) {
                binding.inputPassword.setBackgroundResource(R.drawable.sp_f6f6f6_c12_ff5f42_s1)
                binding.tvPasswordFormatErrTip.setTextColorByRes(R.color.color_F55C45)
                binding.tvPasswordFormatErrTip.visible()
            } else {
                binding.inputPassword.setBackgroundResource(R.drawable.selector_bg_sign_up_focus)
                binding.tvPasswordFormatErrTip.setTextColorByRes(R.color.neutral_color_3)
                binding.tvPasswordFormatErrTip.visible(focusPwd)
            }
        }
        vm.onAsync(AccPwdV7State::result, deliveryMode = uniqueOnly(), onLoading = {
            showLoading()
        }, onFail = { _, _ ->
            dismissLoading()
        }) {
            dismissLoading()
            if (it.isValid) {
                dismissAllowingStateLoss()
                result = true
            } else {
                toast(it.description)
            }
        }
        vm.registerAsyncErrorToast(AccPwdV7State::result)
    }

    private fun showLoading() {
        loadingDialogFragment = MetaRouter.Dialog.loading(
            childFragmentManager, msg = getString(R.string.confirming)
        )
    }

    private fun dismissLoading() {
        loadingDialogFragment?.dismissAllowingStateLoss()
        loadingDialogFragment = null
    }

    override fun onDestroyView() {
        dismissLoading()
        if (!result) {
            Analytics.track(
                EventConstants.PUBLISH_CONNECT_DIALOG_CLOSE,
                "source" to args.source
            )
        }
        setFragmentResultByActivity(TAG, bundleOf(TAG to result))
        super.onDestroyView()
    }

    override fun windowHeight() = WindowManager.LayoutParams.MATCH_PARENT

    override fun getStyle() = R.style.DialogStyle_Input_WhiteNavStatusBar
}