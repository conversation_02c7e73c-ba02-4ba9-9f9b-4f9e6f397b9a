package com.socialplay.gpark.ui.main

import android.content.ComponentCallbacks
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.interactor.TTaiInteractor
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.data.model.startup.GuideInfo
import com.socialplay.gpark.data.model.startup.GuideInfoHelper
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.util.extension.collectWithTimeout
import kotlinx.coroutines.launch
import org.koin.android.ext.android.get

data class GuideState(
    val guideConfigs: List<GuideInfo>? = null
) : MavericksState

class GuideViewModel(
    initState: GuideState,
    private val tTaiInteractor: TTaiInteractor
) : BaseViewModel<GuideState>(initState) {

    init {
        fetchGuideConfigs()
    }

    fun fetchGuideConfigs() = viewModelScope.launch {
        val result =
            (tTaiInteractor.getTTaiWithTypeV3<List<GuideInfo>>(TTaiKV.ID_GUIDE_CREATE_THEME_LIST_CONFIG)
                .collectWithTimeout(1000) ?: GuideInfoHelper.defaultList()).filter { it.supported }
        setState { copy(guideConfigs = result) }
    }

    companion object : KoinViewModelFactory<GuideViewModel, GuideState>() {
        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: GuideState
        ): GuideViewModel {
            return GuideViewModel(state, get())
        }
    }
}