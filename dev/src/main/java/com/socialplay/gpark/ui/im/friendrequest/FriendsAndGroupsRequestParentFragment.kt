package com.socialplay.gpark.ui.im.friendrequest

import androidx.navigation.fragment.navArgs
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.CommonTabItem
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.core.BaseTabParentFragment
import com.socialplay.gpark.ui.im.groupchat.GroupsRequestListFragment
import com.socialplay.gpark.ui.im.groupchat.GroupsRequestListFragmentArgs

class FriendsAndGroupsRequestParentFragment : BaseTabParentFragment() {
    companion object {
        const val TYPE_FRIENDS_REQUEST = 1
        const val TYPE_GROUP_REQUEST = 2
    }

    private val args by navArgs<FriendsAndGroupsRequestParentFragmentArgs>()
    override val tabItems: List<CommonTabItem> by lazy {
        val tabs = mutableListOf<CommonTabItem>()
        tabs.add(
            CommonTabItem(
                R.string.request_page_tab_friends,
                TYPE_FRIENDS_REQUEST.toString()
            ) {
                FriendRequestListFragmentV2.newInstance()
            })
        if (PandoraToggle.showGroupRequestJoinTab) {
            tabs.add(
                CommonTabItem(
                    R.string.request_page_tab_group,
                    TYPE_GROUP_REQUEST.toString()
                ) {
                    GroupsRequestListFragment.newInstance(
                        GroupsRequestListFragmentArgs(
                            args.targetUid
                        )
                    )
                })
        }
        tabs
    }
    override val destId: Int = R.id.friendsGroupsRequestPage

    override fun getPageName(): String = PageNameConstants.FRAGMENT_FRIENDS_GROUPS_REQUEST_PARENT
}