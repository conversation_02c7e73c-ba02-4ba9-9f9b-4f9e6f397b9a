package com.socialplay.gpark.ui.view

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View
import com.socialplay.gpark.R
import kotlin.math.*

/**
 * 专注于边框光晕效果的金属边框View
 * 确保光晕主要出现在边框区域
 */
class BorderFocusedMetallicView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private val borderPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val clearPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val borderPath = Path()
    private val innerPath = Path()
    private val borderRect = RectF()
    private val innerRect = RectF()
    
    private var borderThickness: Float = 8f
    private var lightAngle: Float = 45f
    private var cornerRadius: Float = 0f
    private var highlightIntensity: Float = 1.0f
    private var shadowIntensity: Float = 0.8f
    private var debugMode: Boolean = false
    
    init {
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.MetallicBorderView)
        borderThickness = typedArray.getDimension(R.styleable.MetallicBorderView_borderThickness, 8f)
        lightAngle = typedArray.getFloat(R.styleable.MetallicBorderView_lightAngle, 45f)
        cornerRadius = typedArray.getDimension(R.styleable.MetallicBorderView_cornerRadius, 0f)
        highlightIntensity = typedArray.getFloat(R.styleable.MetallicBorderView_highlightIntensity, 1.0f)
        shadowIntensity = typedArray.getFloat(R.styleable.MetallicBorderView_shadowIntensity, 0.8f)
        typedArray.recycle()
        
        clearPaint.xfermode = PorterDuffXfermode(PorterDuff.Mode.CLEAR)
    }
    
    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        updatePaths()
    }
    
    private fun updatePaths() {
        val width = width.toFloat()
        val height = height.toFloat()
        
        if (width <= 0 || height <= 0) return
        
        borderRect.set(0f, 0f, width, height)
        innerRect.set(borderThickness, borderThickness, width - borderThickness, height - borderThickness)
        
        borderPath.reset()
        borderPath.addRoundRect(borderRect, cornerRadius, cornerRadius, Path.Direction.CW)
        
        innerPath.reset()
        val innerCornerRadius = maxOf(0f, cornerRadius - borderThickness)
        innerPath.addRoundRect(innerRect, innerCornerRadius, innerCornerRadius, Path.Direction.CW)
        
        updateBorderShader()
    }
    
    private fun updateBorderShader() {
        val width = width.toFloat()
        val height = height.toFloat()
        
        if (width <= 0 || height <= 0) return
        
        // 计算光照方向
        val angleRad = Math.toRadians(lightAngle.toDouble())
        val lightDx = cos(angleRad).toFloat()
        val lightDy = sin(angleRad).toFloat()
        
        // 创建跨越整个边框的线性渐变
        val centerX = width / 2
        val centerY = height / 2
        val diagonal = sqrt(width * width + height * height)
        
        // 渐变从阴影面到高光面
        val startX = centerX + lightDx * diagonal / 3
        val startY = centerY + lightDy * diagonal / 3
        val endX = centerX - lightDx * diagonal / 3
        val endY = centerY - lightDy * diagonal / 3
        
        // 创建明显的光晕效果
        val baseColor = Color.parseColor("#C0C0C0")
        val shadowColor = Color.argb((255 * shadowIntensity).toInt(), 60, 60, 60)
        val highlightColor = Color.argb((255 * highlightIntensity).toInt(), 255, 255, 255)
        
        val gradient = LinearGradient(
            startX, startY, endX, endY,
            intArrayOf(
                shadowColor,    // 阴影面：暗
                baseColor,      // 中间：基础色
                highlightColor, // 高光面：亮
                baseColor,      // 中间：基础色
                shadowColor     // 阴影面：暗
            ),
            floatArrayOf(0f, 0.2f, 0.5f, 0.8f, 1f),
            Shader.TileMode.CLAMP
        )
        
        borderPaint.shader = gradient
        borderPaint.style = Paint.Style.FILL
    }
    
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        
        if (width <= 0 || height <= 0) return
        
        // 绘制带光晕的边框
        canvas.drawPath(borderPath, borderPaint)
        
        // 挖空中心区域（调试模式下跳过）
        if (!debugMode) {
            canvas.drawPath(innerPath, clearPaint)
        }
    }
    
    // 公共方法
    fun setLightAngle(angle: Float) {
        lightAngle = angle % 360f
        updateBorderShader()
        invalidate()
    }
    
    fun setBorderThickness(thickness: Float) {
        borderThickness = thickness
        updatePaths()
        invalidate()
    }
    
    fun setCornerRadius(radius: Float) {
        cornerRadius = radius
        updatePaths()
        invalidate()
    }
    
    fun setHighlightIntensity(intensity: Float) {
        highlightIntensity = intensity.coerceIn(0f, 1f)
        updateBorderShader()
        invalidate()
    }
    
    fun setShadowIntensity(intensity: Float) {
        shadowIntensity = intensity.coerceIn(0f, 1f)
        updateBorderShader()
        invalidate()
    }
    
    fun setDebugMode(debug: Boolean) {
        debugMode = debug
        invalidate()
    }
    
    fun getLightAngle(): Float = lightAngle
    fun getBorderThickness(): Float = borderThickness
    fun getCornerRadius(): Float = cornerRadius
}
