package com.socialplay.gpark.ui.view

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View
import com.socialplay.gpark.R
import kotlin.math.*

/**
 * 专注于边框光晕效果的金属边框View
 * 确保光晕主要出现在边框区域
 */
class BorderFocusedMetallicView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private val borderPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val glowPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val clearPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val borderPath = Path()
    private val innerPath = Path()
    private val glowPath = Path()
    private val borderRect = RectF()
    private val innerRect = RectF()
    private val glowRect = RectF()

    private var borderThickness: Float = 8f
    private var lightAngle: Float = 45f
    private var cornerRadius: Float = 0f
    private var highlightIntensity: Float = 1.0f
    private var shadowIntensity: Float = 0.8f
    private var debugMode: Boolean = false

    // 外发光属性
    private var glowIntensity: Float = 0.5f
    private var glowColor: Int = Color.parseColor("#0080FF")
    private var glowRadius: Float = 0f
    
    init {
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.MetallicBorderView)
        borderThickness = typedArray.getDimension(R.styleable.MetallicBorderView_borderThickness, 8f)
        lightAngle = typedArray.getFloat(R.styleable.MetallicBorderView_lightAngle, 45f)
        cornerRadius = typedArray.getDimension(R.styleable.MetallicBorderView_cornerRadius, 0f)
        highlightIntensity = typedArray.getFloat(R.styleable.MetallicBorderView_highlightIntensity, 1.0f)
        shadowIntensity = typedArray.getFloat(R.styleable.MetallicBorderView_shadowIntensity, 0.8f)
        typedArray.recycle()
        
        clearPaint.xfermode = PorterDuffXfermode(PorterDuff.Mode.CLEAR)

        glowPaint.style = Paint.Style.FILL
    }
    
    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        updatePaths()
    }
    
    private fun updatePaths() {
        val width = width.toFloat()
        val height = height.toFloat()

        if (width <= 0 || height <= 0) return

        // 计算外发光半径
        glowRadius = borderThickness * 2f

        // 为外发光预留空间，边框在View内部居中
        val glowPadding = glowRadius
        val borderLeft = glowPadding
        val borderTop = glowPadding
        val borderRight = width - glowPadding
        val borderBottom = height - glowPadding

        // 外边框矩形（在View内部，为发光留出空间）
        borderRect.set(borderLeft, borderTop, borderRight, borderBottom)

        // 内部透明区域矩形
        innerRect.set(
            borderLeft + borderThickness,
            borderTop + borderThickness,
            borderRight - borderThickness,
            borderBottom - borderThickness
        )

        borderPath.reset()
        borderPath.addRoundRect(borderRect, cornerRadius, cornerRadius, Path.Direction.CW)

        innerPath.reset()
        val innerCornerRadius = maxOf(0f, cornerRadius - borderThickness)
        innerPath.addRoundRect(innerRect, innerCornerRadius, innerCornerRadius, Path.Direction.CW)

        updateBorderShader()
    }
    
    private fun updateBorderShader() {
        val width = width.toFloat()
        val height = height.toFloat()
        
        if (width <= 0 || height <= 0) return
        
        // 计算光照方向
        val angleRad = Math.toRadians(lightAngle.toDouble())
        val lightDx = cos(angleRad).toFloat()
        val lightDy = sin(angleRad).toFloat()
        
        // 创建跨越整个边框的线性渐变
        val centerX = width / 2
        val centerY = height / 2
        val diagonal = sqrt(width * width + height * height)
        
        // 渐变从阴影面到高光面
        val startX = centerX + lightDx * diagonal / 3
        val startY = centerY + lightDy * diagonal / 3
        val endX = centerX - lightDx * diagonal / 3
        val endY = centerY - lightDy * diagonal / 3
        
        // 创建明显的光晕效果
        val baseColor = Color.parseColor("#C0C0C0")
        val shadowColor = Color.argb((255 * shadowIntensity).toInt(), 60, 60, 60)
        val highlightColor = Color.argb((255 * highlightIntensity).toInt(), 255, 255, 255)
        
        val gradient = LinearGradient(
            startX, startY, endX, endY,
            intArrayOf(
                shadowColor,    // 阴影面：暗
                baseColor,      // 中间：基础色
                highlightColor, // 高光面：亮
                baseColor,      // 中间：基础色
                shadowColor     // 阴影面：暗
            ),
            floatArrayOf(0f, 0.2f, 0.5f, 0.8f, 1f),
            Shader.TileMode.CLAMP
        )
        
        borderPaint.shader = gradient
        borderPaint.style = Paint.Style.FILL
    }


    
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        if (width <= 0 || height <= 0) return

        val width = width.toFloat()
        val height = height.toFloat()

        // 使用离屏缓冲区避免混合问题
        val layerId = canvas.saveLayer(0f, 0f, width, height, null)

        // 1. 绘制外发光（如果强度大于0）
        if (glowIntensity > 0f && borderThickness > 0f) {
            drawOuterGlow(canvas, width, height)
        }

        // 2. 绘制带光晕的边框
        canvas.drawPath(borderPath, borderPaint)

        // 3. 挖空中心区域（调试模式下跳过）
        if (!debugMode) {
            val clearPaintFixed = Paint(Paint.ANTI_ALIAS_FLAG).apply {
                xfermode = PorterDuffXfermode(PorterDuff.Mode.SRC_OUT)
                color = Color.BLACK
            }
            canvas.drawPath(innerPath, clearPaintFixed)
        }

        canvas.restoreToCount(layerId)
    }

    private fun drawOuterGlow(canvas: Canvas, width: Float, height: Float) {
        val glowAlpha = (255 * glowIntensity).toInt()

        // 计算边框的实际位置（考虑发光预留空间）
        val glowPadding = glowRadius

        // 创建多层发光效果
        for (i in 1..5) {
            val layerRadius = glowRadius * i / 5f
            val layerAlpha = (glowAlpha * (6 - i) / 5f).toInt()

            if (layerAlpha <= 0) continue

            val layerColor = Color.argb(
                layerAlpha,
                Color.red(glowColor),
                Color.green(glowColor),
                Color.blue(glowColor)
            )

            val glowPaintLayer = Paint(Paint.ANTI_ALIAS_FLAG).apply {
                color = layerColor
                style = Paint.Style.STROKE
                strokeWidth = layerRadius / 2f
                maskFilter = BlurMaskFilter(layerRadius / 3f, BlurMaskFilter.Blur.NORMAL)
            }

            // 绘制发光边框，从边框位置向外扩散
            val glowRect = RectF(
                glowPadding - layerRadius / 4f,
                glowPadding - layerRadius / 4f,
                width - glowPadding + layerRadius / 4f,
                height - glowPadding + layerRadius / 4f
            )

            canvas.drawRoundRect(glowRect, cornerRadius + layerRadius / 4f, cornerRadius + layerRadius / 4f, glowPaintLayer)
        }
    }
    
    // 公共方法
    fun setLightAngle(angle: Float) {
        lightAngle = angle % 360f
        updateBorderShader()
        invalidate()
    }
    
    fun setBorderThickness(thickness: Float) {
        borderThickness = thickness
        updatePaths()
        invalidate()
    }
    
    fun setCornerRadius(radius: Float) {
        cornerRadius = radius
        updatePaths()
        invalidate()
    }
    
    fun setHighlightIntensity(intensity: Float) {
        highlightIntensity = intensity.coerceIn(0f, 1f)
        updateBorderShader()
        invalidate()
    }
    
    fun setShadowIntensity(intensity: Float) {
        shadowIntensity = intensity.coerceIn(0f, 1f)
        updateBorderShader()
        invalidate()
    }
    
    fun setDebugMode(debug: Boolean) {
        debugMode = debug
        invalidate()
    }
    
    fun setGlowIntensity(intensity: Float) {
        glowIntensity = intensity.coerceIn(0f, 1f)
        invalidate()
    }

    fun setGlowColor(color: Int) {
        glowColor = color
        invalidate()
    }

    fun getLightAngle(): Float = lightAngle
    fun getBorderThickness(): Float = borderThickness
    fun getCornerRadius(): Float = cornerRadius
    fun getGlowIntensity(): Float = glowIntensity
    fun getGlowColor(): Int = glowColor
}
