package com.socialplay.gpark.ui.mgs.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.module.LoadMoreModule
import com.meta.biz.mgs.data.model.UgcGame
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.GameRoomDetail
import com.socialplay.gpark.databinding.ItemDetailRoomListBinding
import com.socialplay.gpark.databinding.ItemPlayerWorkBinding
import com.socialplay.gpark.ui.base.adapter.BaseAdapter
import com.socialplay.gpark.ui.base.adapter.BaseVBViewHolder
import com.socialplay.gpark.ui.base.adapter.BasicQuickAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder
import com.socialplay.gpark.util.extension.dp

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2023/02/15
 *     desc   :
 *
 */

class PlayerWorkAdapter : BasicQuickAdapter<UgcGame, ItemPlayerWorkBinding>(),
    LoadMoreModule {

    override fun convert(holder: BaseVBViewHolder<ItemPlayerWorkBinding>, item: UgcGame) {
        Glide.with(holder.binding.ivBuilding)
            .load(item.banner)
            .placeholder(R.drawable.placeholder_corner_13)
            .transform(CenterCrop(), RoundedCorners(14.dp))
            .into(holder.binding.ivBuilding)
        holder.binding.tvBuildName.text = item.ugcGameName
        Glide.with(holder.binding.ivBuilding)
            .load(item.pvTagIconUrl)
            .into(holder.binding.imgLogo)
    }

    override fun viewBinding(parent: ViewGroup, viewType: Int): ItemPlayerWorkBinding {
        return ItemPlayerWorkBinding.inflate(LayoutInflater.from(parent.context), parent, false)
    }
}