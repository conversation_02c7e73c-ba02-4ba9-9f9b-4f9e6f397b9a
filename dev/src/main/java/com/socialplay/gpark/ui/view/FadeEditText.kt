package com.socialplay.gpark.ui.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Shader
import android.text.TextPaint
import android.util.AttributeSet
import androidx.annotation.AttrRes
import com.socialplay.gpark.util.extension.argb


class FadeEditText : androidx.appcompat.widget.AppCompatEditText {

    private var paint: TextPaint? = null // 文本画笔
    private var shader: Shader? = null // 默认Shader
    private var gradient: LinearGradient? = null // 文本过长渐隐效果
    var isTextGradient: Boolean = true // 默认有渐隐效果
        set(value) {
            if (value != field) {
                field = value
                postInvalidate()
            }
        }

    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, @AttrRes defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    override fun onSizeChanged(w: Int, h: Int, oldW: Int, oldH: Int) {
        super.onSizeChanged(w, h, oldW, oldH)
        val measuredWidth = measuredWidth
        if (measuredWidth > 0) {
            val gradientRatio = 0.5f // 渐隐比例，默认从gradientRatio位置开始，可修改
            val gradientStart = measuredWidth * gradientRatio // 渐隐开始位置
            gradient = LinearGradient(
                gradientStart,
                0.0f,
                measuredWidth.toFloat(),
                0.0f,
                intArrayOf(
                    argb(0xFF, currentTextColor),
                    currentTextColor,
                    Color.TRANSPARENT
                ),
                floatArrayOf(0.0f, gradientRatio, 1.0f),
                Shader.TileMode.CLAMP
            ) // LinearGradient效果是受文本颜色透明度影响的，于是做出兼容措施。从左到右按比例：无透明文本颜色@0，文本颜色@gradientRatio，完全透明文本颜色@1。
            val newPaint = getPaint()
            paint = newPaint
            shader = newPaint.shader
        }
    }

    override fun onDraw(canvas: Canvas) {
        val paint = paint
        val gradient = gradient
        if (paint != null && gradient != null) {
            if (isTextGradient && paint.measureText(text.toString()) > measuredWidth) {
                paint.setShader(gradient)
            } else {
                paint.setShader(shader)
            }
        }
        super.onDraw(canvas)
    }
}