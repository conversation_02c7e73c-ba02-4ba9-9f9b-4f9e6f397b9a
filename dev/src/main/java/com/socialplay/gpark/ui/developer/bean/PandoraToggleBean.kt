package com.socialplay.gpark.ui.developer.bean

import kotlin.reflect.KClass

/**
 * xingxiu.hou
 * 2021/6/7
 */
data class PandoraToggleBean(
    val key: String,
    val name: String,
    val desc: String,
    val localValue: String,
    val onlineValue: String,
    val selectArray: Array<String>,
    var valueType: KClass<*> = String::class
) {
    override fun toString(): String {
        return "PandoraToggleBean(key='$key', name='$name', desc='$desc', localValue='$localValue', selectArray=${selectArray.contentToString()}, valueType=$valueType)"
    }
}
