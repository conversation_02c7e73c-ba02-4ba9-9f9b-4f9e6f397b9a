package com.socialplay.gpark.ui.editorschoice.header.friends

import android.util.TypedValue
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.choice.ChoiceFriendInfo
import com.socialplay.gpark.databinding.ViewChoiceFriendsViewBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.analytics.resid.ResIdUtils
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.editorschoice.ChoiceHomeCardAdapter
import com.socialplay.gpark.ui.editorschoice.header.ChoiceHomeAdapterHeaderIndex
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.isPad
import com.socialplay.gpark.util.extension.screenWidth
import com.socialplay.gpark.util.extension.setOnAntiViolenceItemClickListener
import org.koin.androidx.viewmodel.ext.android.viewModel
import timber.log.Timber
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 2023/8/11
 */
class ChoiceHomeHeaderFriends(private val fragment: Fragment) {

    private val addItemMarginStart = 16.dp
    private val userItemMarginEnd = 12.dp

    private val homeFriendsViewModel by fragment.viewModel<ChoiceHomeFriendsViewModel>()

    private val binding by lazy { ViewChoiceFriendsViewBinding.inflate(fragment.layoutInflater) }

    private val adapter by lazy { ChoiceHomeHeaderFriendsAdapter(Glide.with(fragment), getFriendItemWidth()) }

    private val resIdBean by lazy {
        ResIdBean.newInstance().setCategoryID(CategoryId.HOME_FLOW_ROOM)
    }

    private fun getFriendItemWidth(): Int {
        return if (fragment.isPad) {
            fragment.dp(74)
        } else {
            val screenNum = kotlin.runCatching {
                val out = TypedValue()
                fragment.resources.getValue(R.dimen.header_friend_screen_items, out, true)
                out.float
            }.getOrElse {
                4.2F
            }
            ((fragment.screenWidth - addItemMarginStart - (userItemMarginEnd * screenNum.toInt())) / screenNum).toInt()
        }
    }

    fun addToHeadView(adapter: ChoiceHomeCardAdapter) {
        if (binding.root.parent != null) {
            (binding.root.parent as? ViewGroup)?.removeView(binding.root)
        }
        adapter.setHeaderView(binding.root, ChoiceHomeAdapterHeaderIndex.HEAD_PRIORITY_FRIEND)
    }

    fun getView(): View {
        return binding.root
    }

    fun initialize() {
        // 写死，没有首页跟房，需求来自 -> 宋津瑶
        if (PandoraToggle.openHomeFlowRoom) {
            binding.root.isVisible = true
            initView()
            initData()
        } else {
            binding.root.isVisible = false
        }
    }

    fun destroy() {
        try {
            binding?.rvFriendsList?.clearOnScrollListeners()
            adapter.setOnItemClickListener(null)
            adapter.setOnItemShowListener(null)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun initView() {
        binding.rvFriendsList.layoutManager = LinearLayoutManager(fragment.requireContext(), LinearLayoutManager.HORIZONTAL, false)
        binding.rvFriendsList.adapter = adapter

        val isScrolling = AtomicBoolean(false)
        binding.rvFriendsList.clearOnScrollListeners()
        binding.rvFriendsList.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    if (isScrolling.compareAndSet(true, false)) {
                        Analytics.track(EventConstants.EVENT_PARTY_FRIENDS_SLIDE) {
                            putAll(ResIdUtils.getAnalyticsMap(resIdBean))
                        }
                    }
                } else {
                    isScrolling.set(true)
                }
            }
        })

        adapter.setOnAntiViolenceItemClickListener { adapter, _, position ->
            if (fragment.fragmentManager == null) {
                Timber.e("onAntiViolenceItemClick return. position = $position . because FragmentManager is null. isAdded = ${fragment.isAdded} isVisible = ${fragment.isVisible} fragmentManager = ${fragment.fragmentManager}")
                return@setOnAntiViolenceItemClickListener
            }
            adapter.getItem(position).also {
                when (it.type) {
                    ChoiceFriendInfo.TYPE_ADD -> {
                        Analytics.track(EventConstants.EVENT_ADD_FRIENDS_CLICK) {
                            putAll(ResIdUtils.getAnalyticsMap(resIdBean))
                            put("source", "party")
                        }
                        if (fragment.isAdded && fragment.isVisible && fragment.fragmentManager != null) {
                            MetaRouter.IM.goSearchFriend(fragment)
                        }
                    }

                    ChoiceFriendInfo.TYPE_USER -> {
                        Analytics.track(EventConstants.EVENT_PARTY_FRIENDS_USER_CLICK) {
                            putAll(ResIdUtils.getAnalyticsMap(resIdBean))
                            put(
                                "state", when {
                                    it.isInGame() -> "ingame"
                                    it.isOnline() -> "online"
                                    else -> "offline"
                                }
                            )
                        }
                        if (fragment.isVisible) {
                            HomeFriendsActionDialog.show(fragment, it, resIdBean)
                        }
                    }
                }
            }
        }
        //上报显示埋点
        adapter.setOnItemShowListener { _, position ->
            adapter.getItem(position).also {
                if (it.type != ChoiceFriendInfo.TYPE_ADD) {
                    Analytics.track(EventConstants.EVENT_PARTY_FRIENDS_SHOW) {
                        putAll(ResIdUtils.getAnalyticsMap(resIdBean))
                        put("online", if (it.userOnline) 1 else 0)
                        put("offline", if (!it.userOnline) 1 else 0)
                        put("ingame", if (it.gameName.isNotEmpty()) 1 else 0)
                        put("userid", it.userId)
                    }
                }
            }
        }
    }

    private fun initData() {
        homeFriendsViewModel.fetchFriends().observe(fragment.viewLifecycleOwner) { friendList ->
            // 控制两种布局的显示
            val hasRealFriends = friendList.any { it.type == ChoiceFriendInfo.TYPE_USER }
            
            if (hasRealFriends) {
                // 有朋友时显示朋友列表
                binding.layoutHasFriends.isVisible = true
                binding.layoutEmptyFriends.isVisible = false
                adapter.submitData(fragment.viewLifecycleOwner.lifecycle, friendList)
            } else {
                // 没有朋友时显示空状态
                binding.layoutHasFriends.isVisible = false
                binding.layoutEmptyFriends.isVisible = true
                // 为空状态布局设置点击事件
                binding.layoutEmptyFriends.setOnClickListener {
                    // 点击空状态时跳转到添加朋友页面
                    Analytics.track(EventConstants.EVENT_ADD_FRIENDS_CLICK) {
                        putAll(ResIdUtils.getAnalyticsMap(resIdBean))
                        put("source", "party_empty_state")
                    }
                    if (fragment.isAdded && fragment.isVisible && fragment.fragmentManager != null) {
                        MetaRouter.IM.goSearchFriend(fragment)
                    }
                }
            }
        }
    }

}