package com.socialplay.gpark.ui.profile

import androidx.fragment.app.Fragment
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModel
import androidx.lifecycle.asFlow
import androidx.lifecycle.viewModelScope
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.code
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.BaseAccountInteractor
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor
import com.socialplay.gpark.data.interactor.TTaiInteractor
import com.socialplay.gpark.data.interactor.UserMemberInteractor
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.data.model.account.ProfileLinkInfo
import com.socialplay.gpark.data.model.account.UserFollowEvent
import com.socialplay.gpark.data.model.member.MemberInfo
import com.socialplay.gpark.data.model.outfit.ProfileCurrentCloth
import com.socialplay.gpark.data.model.outfit.ProfileCurrentClothesRequest
import com.socialplay.gpark.data.model.pay.IAPConstants
import com.socialplay.gpark.data.model.pay.SubscribeProductInfo
import com.socialplay.gpark.data.model.pay.UserBalance
import com.socialplay.gpark.data.model.profile.UserProfileInfo
import com.socialplay.gpark.data.model.profile.request.RelationCountRequest
import com.socialplay.gpark.data.model.qrcode.QrCodeCreateRequest
import com.socialplay.gpark.data.model.share.ShareRawData
import com.socialplay.gpark.data.model.user.BadgeInfo
import com.socialplay.gpark.data.model.user.RelationType
import com.socialplay.gpark.data.repository.UgcRepository
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.pay.IPayInteractor
import com.socialplay.gpark.function.pay.PayProvider
import com.socialplay.gpark.ui.outfit.ProfileUgcDesignFragment
import com.socialplay.gpark.ui.post.feed.profile.ProfileCommunityFeedFragment
import com.socialplay.gpark.ui.post.feed.profile.ProfileCommunityFeedFragmentArgs
import com.socialplay.gpark.ui.profile.outfit.ProfileTabOutfitFragment
import com.socialplay.gpark.ui.profile.recent.ProfileTabRecentFragment
import com.socialplay.gpark.ui.profile.ugc.ProfilePublishedUgcFragment
import com.socialplay.gpark.ui.profile.ugc.ProfilePublishedUgcFragmentArgs
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.SingleLiveData
import com.socialplay.gpark.util.extension.LifecycleCallback
import com.socialplay.gpark.util.extension.registerHermes
import com.socialplay.gpark.util.extension.unregisterHermes
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe

/**
 * created by liyanfeng on 2022/7/25 4:08 下午
 * @describe:
 */
class BaseProfileViewModel(
    private val repository: com.socialplay.gpark.data.IMetaRepository,
    private val payInteractor: IPayInteractor,
    private val memberInteractor: UserMemberInteractor,
    val tTaiInteractor: TTaiInteractor,
    private val accountInteractor: AccountInteractor,
    private val ugcRepository: UgcRepository,
    private val h5PageConfigInteractor: H5PageConfigInteractor,
) : ViewModel() {

    private val _tabItems = MutableLiveData<ArrayList<Pair<HomepageTab, () -> Fragment>>?>()
    val tabItems: LiveData<ArrayList<Pair<HomepageTab, () -> Fragment>>?> get() = _tabItems

    private val _selectedItemLiveData = MutableLiveData(0)
    val selectedItemLiveData: LiveData<Int> = _selectedItemLiveData

    private val _userProfile = MutableLiveData<UserProfileInfo?>()
    val userProfile: LiveData<UserProfileInfo?> = _userProfile

    private val _otherUuidFlow = MutableStateFlow("")

    val followLifeCallback: LifecycleCallback<(Pair<DataResult<Boolean>, Boolean>) -> Unit> = LifecycleCallback()

    val toastLifeCallback: LifecycleCallback<(Int?, String?) -> Unit> = LifecycleCallback()
    val userBalance: Flow<UserBalance?> = payInteractor.userBalance.asFlow()
    val memberInfo:MutableLiveData<MemberInfo?> = MutableLiveData()
    //当前的订阅商品信息
    private val _subscribeProductLiveData = MutableLiveData<SubscribeProductInfo>()
    val subscribeProductLiveData = _subscribeProductLiveData
    private val memberChangeListener = Observer<MemberInfo?> {
        memberInfo.value = it
    }

    private val _shareProfileData = SingleLiveData<ShareRawData.UserExtra?>()
    val shareProfileData: LiveData<ShareRawData.UserExtra?> = _shareProfileData

    private val _currentClothesLiveData = MutableLiveData<List<ProfileCurrentCloth>?>()
    val currentClothesLiveData: LiveData<List<ProfileCurrentCloth>?> = _currentClothesLiveData

    val nickname get() = if (isMe) {
        accountInteractor.accountLiveData.value?.nickname ?: _userProfile.value?.nickname
    } else {
        _userProfile.value?.nickname
    }

    val sparkH5Url get() = h5PageConfigInteractor.getH5PageUrl(
        H5PageConfigInteractor.SPARK_ACCOUNT
    )

    init {
        memberInteractor.vipPlusLiveData.observeForever(memberChangeListener)
        registerHermes()
    }

    private var otherUuid: String = ""
    private var isMe: Boolean = false
    private var isMineTab: Boolean = false

    fun initArgs(
        otherUuid: String,
        isMe: Boolean,
        isMineTab: Boolean,
        callback: () -> Unit
    ) {
        updateIfDifferent(otherUuid, callback)
        this.isMe = isMe
        this.isMineTab = isMineTab
    }

    fun updateIfDifferent(otherUuid: String, callback: () -> Unit) {
        if (this.otherUuid != "" && isMe && this.otherUuid != otherUuid) {
            this.otherUuid = otherUuid
            _tabItems.value = null
            _shareProfileData.postValue(null)
            callback()
            getUserProfile(otherUuid, true)
        } else {
            this.otherUuid = otherUuid
        }
    }

    private fun configFragments(userProfileInfo: UserProfileInfo?) {
        if (PandoraToggle.enableProfileCurrentClothes && !isMe && userProfileInfo?.canTryOn() == true) {
            viewModelScope.launch {
                ugcRepository.getProfileCurrentClothesFlow(ProfileCurrentClothesRequest(otherUuid))
                    .collect {
                        if (it.succeeded && !it.data.isNullOrEmpty()) {
                            _currentClothesLiveData.value = it.data
                        }
                    }
            }
        }
        if (isMe && userProfileInfo != null) {
            if (isMineTab) {
                accountInteractor.updatePrivacySwitch(
                    ootdPrivateSwitch = userProfileInfo.ootdPrivateSwitch,
                    updateCache = true
                )
            } else {
                accountInteractor.updatePrivacySwitch(
                    ootdPrivateSwitch = userProfileInfo.ootdPrivateSwitch,
                    updateCache = false
                )
            }
            accountInteractor.updateSparkBalance(userProfileInfo.userRemainLightUpQuantity)
        }
        if (userProfileInfo == null || _tabItems.value != null) return

        val items = ArrayList<Pair<HomepageTab, () -> Fragment>>()
        items.add(HomepageTab.RECENT to { ProfileTabRecentFragment.newInstance(isMe, otherUuid) })
        if (PandoraToggle.openProfileUgc) {
            items.add(HomepageTab.UGC to {
                ProfilePublishedUgcFragment.newInstance(
                    ProfilePublishedUgcFragmentArgs(
                        isMe,
                        otherUuid
                    )
                )
            })
        }
        items.add(HomepageTab.POST to {
            ProfileCommunityFeedFragment.newInstance(
                ProfileCommunityFeedFragmentArgs(otherUuid, isMe)
            )
        })
        if ((isMe || isMineTab || userProfileInfo.canTryOn()) && PandoraToggle.enableStyleCommunity) {
            items.add(HomepageTab.CLOTHES to {
                ProfileTabOutfitFragment.newInstance(isMe || isMineTab, otherUuid)
            })
        }
        if (PandoraToggle.enableProfileLibraryTab) {
            items.add(HomepageTab.ASSETS to {
                ProfileUgcDesignFragment.newInstance(
                    isMe,
                    otherUuid
                )
            })
        }
        _tabItems.value = items
    }

    fun changeSelectTab(position: Int) {
        _selectedItemLiveData.value = position
    }

    fun getUserProfile(otherUuid: String, isMe: Boolean) = viewModelScope.launch {
        val data = tTaiInteractor.getConfig(TTaiKV.ID_LINK_PROFILE)
        val list = if (data != null) {
            GsonUtil.gsonSafeParseCollection<List<ProfileLinkInfo>>(data.value) ?: emptyList()
        } else {
            emptyList()
        }

        if (PandoraToggle.isOpenAiBot) {
            //有aiBot的数据，查询AiBot的关注数
            repository.queryUserProfile(otherUuid).combine(
                repository.getFriendCount(
                    RelationCountRequest(
                        otherUuid,
                        RelationType.AiBot.value
                    )
                )
            ) { userInfo, b ->
                userInfo.data?.followCount = (userInfo.data?.followCount ?: 0) + (b.data?.forwardCount?:0)
                userInfo
            }.collect {
                val linkList = it.data?.externalLinks?.map { item ->
                    val linkInfo = list.find { it.type == item.type }
                    item.copy(icon = linkInfo?.icon.orEmpty())
                }
                it.data?.externalLinks = linkList?: emptyList()
                _userProfile.value = it.data
                configFragments(it.data)
            }
        } else {
            repository.queryUserProfile(otherUuid).collect {
                val linkList = it.data?.externalLinks?.map { item ->
                    val linkInfo = list.find { it.type == item.type }
                    item.copy(icon = linkInfo?.icon.orEmpty())
                }
                it.data?.externalLinks = linkList?: emptyList()
                _userProfile.value = it.data
                configFragments(it.data)
            }
        }
    }

    fun changeFollow(uuid: String, toFollow: Boolean) = viewModelScope.launch {
        Analytics.track(EventConstants.EVENT_FOLLOW_CLICK) {
            put(EventParamConstants.KEY_USERID, uuid)
            put(EventParamConstants.KEY_LOCATION, EventParamConstants.LOCATION_FOLLOW_PROFILE)
            put(
                EventParamConstants.KEY_TYPE,
                if (toFollow) EventParamConstants.TYPE_FOLLOW else EventParamConstants.TYPE_UNFOLLOW
            )
        }
        if (toFollow) {
            repository.relationAdd(uuid, RelationType.Follow.value)
        } else {
            repository.relationDel(uuid, RelationType.Follow.value)
        }.collect {
            if (it.succeeded) {
                _userProfile.value?.apply {
                    isFollow = toFollow
                    if (toFollow) fansCount++ else fansCount--
                }

                EventBus.getDefault().post(UserFollowEvent(uuid, toFollow, UserFollowEvent.FROM_PROFILE))

                followLifeCallback.dispatchOnMainThread {
                    invoke(it to toFollow)
                }
            } else {
                toastLifeCallback.dispatchOnMainThread {
                    it.message?.let { it1 -> invoke(null, it1) }
                }
            }
        }
    }
    fun isWalletEntranceEnabled(): Boolean {
        val data = PandoraToggle.walletEntrance.split(",")
        return  data.contains("2")
    }
    fun getSubsProduct() = viewModelScope.launch {
        if (PayProvider.ENABLE_PREMIUM) {
            repository.getSubsProduct(IAPConstants.IAP_PRODUCT_TYPE_MEMBER_AWARD).collect() {
                if (it.succeeded) {
                    if (it.data?.isNotEmpty() == true) {
                        _subscribeProductLiveData.value = it.data?.get(0)
                    }
                }
            }
        }
    }

    fun getUserExtra4Share() = viewModelScope.launch {
        repository.getRecentPlayGameListV4(otherUuid, 1, 20).combine(
            repository.qrCodeCreateFlow(
                QrCodeCreateRequest.homePage()
            )
        ) { recentGames, qrCode ->
            val url = qrCode.data?.url
            if (recentGames.code != 200 || !qrCode.succeeded || url.isNullOrBlank()) {
                null
            } else {
                ShareRawData.UserExtra(
                    recentGames.data?.validList.orEmpty(),
                    url
                )
            }
        }.collect {
            if (it != null) {
                _shareProfileData.postValue(it)
            } else {
                toastLifeCallback.dispatchOnMainThread { invoke(R.string.common_error, null) }
            }
        }
    }

    fun clearNewFollowerRecord() = viewModelScope.launch {
        accountInteractor.clearRedBadge(BaseAccountInteractor.TYPE_NEW_FOLLOWER) {
            it?.copy(newFollower = BadgeInfo(false, 0, null))
        }
    }

    @Subscribe
    fun onUserFollowEvent(event: UserFollowEvent) {
        if (isMe) return
        if (otherUuid == event.uuid && _userProfile.value?.isFollow != event.followStatus) {
            _userProfile.value?.apply {
                isFollow = event.followStatus
                if (event.followStatus) fansCount++ else fansCount--
            }

            followLifeCallback.dispatchOnMainThread {
                invoke(DataResult.Success(true) to event.followStatus)
            }
        }
    }

    override fun onCleared() {
        unregisterHermes()
        memberInteractor.vipPlusLiveData.removeObserver(memberChangeListener)
        super.onCleared()
    }
}