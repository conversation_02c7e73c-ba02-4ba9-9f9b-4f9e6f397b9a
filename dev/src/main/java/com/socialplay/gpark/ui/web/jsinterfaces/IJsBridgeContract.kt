package com.socialplay.gpark.ui.web.jsinterfaces

import android.app.Activity
import android.content.Context
import android.content.Intent
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleCoroutineScope
import androidx.lifecycle.LifecycleOwner
import com.socialplay.gpark.data.model.event.PayResult
import com.socialplay.gpark.function.analytics.resid.ResIdBean

interface IJsBridgeContract {

    val context: Context?
    val viewLifecycleOwner: LifecycleOwner
    val lifecycle: Lifecycle
    val lifecycleScope: LifecycleCoroutineScope
    val lifecycleOwner: LifecycleOwner
    val activity: Activity?

    fun requireContext(): Context
    fun requireActivity(): FragmentActivity

    fun isFromGame(): Boolean
    fun fromTab(): Boolean
    fun webTs(): Long?
    fun gamePackageName(): String?
    fun gameId(): String?
    fun source(): String?
    fun style(): String?

    fun onPaySuccess()
    fun onPayFailed()

    fun notifyBackToWebFromWeb()

    fun onResumeGame()

    fun isToolbarVisible(): Boolean?
    fun setStatusBarVisible(visible: Boolean)
    fun setToolbarVisible(visible: Boolean)
    fun setStatusBarColor(colorStr: String): Boolean?
    fun setStatusBarTextColor(dark: Boolean)
    fun routerToLogin(source: String)
    fun routerToLogin(popUpId: Int)
    fun routerToAuthList(hasPasswordItem: Boolean)
    fun routerToLoginBind(hasPasswordItem: Boolean)
    fun routerToAccountSetting(source: LoginSource)
    fun gameToLogin(source: String)
    fun hasFragment(): Boolean
    fun isWebFragment(): Boolean
    fun goBack():Boolean
    fun closeAll(removeWebView: Boolean = true)
    fun closeWebView(removeWebView: Boolean = true)
    fun getResId(): ResIdBean?
    fun requireFragment(): Fragment
    fun send2Ue(amount: Int, code: Int, errorMessage: String?)
    fun startActivity(createChooser: Intent)
    fun isWebViewDialog(): Boolean
    fun toast(res: Int)
    fun fragmentManager(): FragmentManager
    fun onPayResultToGame(payResult: PayResult)
    fun closeLoading()
    fun setPageSource(source: String)
    fun addRefreshFragmentListenerByReason(reason: String)
    fun preOpenNativePayPage(data: String?)

}

enum class LoginSource(val value: Int, val description: String) {
    OTHER(-1, "Other");

    companion object {
        fun fromValue(value: Int): LoginSource {
            return entries.find { it.value == value } ?: OTHER
        }
    }
}