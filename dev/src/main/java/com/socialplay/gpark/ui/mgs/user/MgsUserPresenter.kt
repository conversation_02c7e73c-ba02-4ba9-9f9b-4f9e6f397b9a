package com.socialplay.gpark.ui.mgs.user

import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.MgsInteractor
import com.socialplay.gpark.data.model.mgs.MgsSceneConfig
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.mgs.listener.OnMgsSceneListener
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import org.koin.core.context.GlobalContext
import java.util.concurrent.atomic.AtomicBoolean

/**
 * @author: ning.wang
 * @date: 2022-03-11 9:42 上午
 * @desc:
 */
class MgsUserPresenter(private val listener: OnMgsSceneListener) : KoinComponent {

    private val metaRepository = GlobalContext.get().get<IMetaRepository>()
    private val mgsInteractor: MgsInteractor by inject()

    private var isLike: Boolean = false
    private var isOpenDanmu: AtomicBoolean = AtomicBoolean(true)

    private var _currentShowViewOpenId = ""

    fun getMgsSceneConfig() {
        val gameId = mgsInteractor.getGameId() ?: return
        if (PandoraToggle.isMWMgs) return
        MainScope().launch {
            metaRepository.getMgsSceneConfig(gameId).collect {
                if (!it.succeeded) {
                    listener.setUserInfo(false, "", "")
                    return@collect
                }
                it.data?.let { config ->
                    mgsInteractor.updateMgsScene(config)
                    _currentShowViewOpenId = config.openId ?: ""
                    listener.setUserInfo(config.userShowSwitch, config.nickname, config.avatar)
                    listener.setLikeState(config.likeShowSwitch, config.liked)
                    isLike = config.liked ?: false
                    listener.setInputVisibility(config.boxChatSwitch ?: false)
                }
            }
        }
    }

    fun clickLike() {
        if (isLike) unLike() else like()
    }

    fun getCurrentShowViewOpenId(): String {
        return _currentShowViewOpenId
    }

    private fun like() {
        val gameId = mgsInteractor.getGameId() ?: return
        MainScope().launch {
            metaRepository.mgsSceneLike(gameId).collect {
                if (it.succeeded && it.data == true) {
                    isLike = true
                    listener.setLikeState(true, isLike = true)
                }
            }
        }
    }

    private fun unLike() {
        val gameId = mgsInteractor.getGameId() ?: return
        MainScope().launch {
            metaRepository.mgsSceneUnLike(gameId).collect {
                if (it.succeeded && it.data == true) {
                    isLike = false
                    listener.setLikeState(true, isLike = false)
                }
            }
        }
    }

    fun canShowBallAudio(): Boolean {
        return mgsInteractor.canShowBallAudio()
    }
    fun setDanmuStatus() {
        isOpenDanmu.set(!isOpenDanmu.get())
    }

    fun getDanmuStatus(): Boolean {
        return isOpenDanmu.get()
    }
}