package com.socialplay.gpark.ui.recommend.choice

import android.text.TextUtils
import android.view.Gravity
import android.view.View
import com.airbnb.epoxy.Carousel
import com.airbnb.epoxy.VisibilityState
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.choice.ChoiceCardInfo
import com.socialplay.gpark.data.model.choice.ChoiceGameInfo
import com.socialplay.gpark.databinding.AdapterChoiceCardItemTemplateBinding
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaModelCollector
import com.socialplay.gpark.ui.core.views.carouselNoSnapWrapBuilder
import com.socialplay.gpark.ui.core.views.textItem
import com.socialplay.gpark.util.GradientTransformation
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.context
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.getString
import com.socialplay.gpark.util.extension.setMargin
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener

fun MetaModelCollector.templateCardList(
    card: ChoiceCardInfo,
    cardPosition: Int,
    spanSize: Int,
    listener: IChoiceListener
) {
    val games = card.gameList ?: return
    // 先添加标题和更多按钮
    add(
        ChoiceTitleMoreItem(card, spanSize, listener)
            .id("TemplateCardTitle-$cardPosition")
            .spanSizeOverride { _, _, _ -> spanSize }
    )
    carouselNoSnapWrapBuilder {
        id("TemplateCardList-$cardPosition")
        padding(Carousel.Padding.dp(8, 0, 8, 0, 0))
        hasFixedSize(true)
        initialPrefetchItemCount(3)
        onVisibilityStateChanged { _, _, visibilityState ->
            if (visibilityState == VisibilityState.VISIBLE) {
                listener.onCardShow(cardPosition, card)
            }
        }
        spanSizeOverride { _, _, _ ->
            spanSize
        }
        games.forEachIndexed { index, game ->
            add(
                TemplateCardItem(
                    item = game,
                    position = index,
                    card = card,
                    cardPosition = cardPosition,
                    spanSize = spanSize,
                    isTemplate = index == 0,
                    listener = listener
                ).id("TemplateCardItem-$cardPosition-$index-${game.code}")
            )
        }
    }
}

data class TemplateCardItem(
    val item: ChoiceGameInfo,
    val position: Int,
    val card: ChoiceCardInfo,
    val cardPosition: Int,
    val spanSize: Int,
    val isTemplate: Boolean,
    val listener: IChoiceListener
) : ViewBindingItemModel<AdapterChoiceCardItemTemplateBinding>(
    R.layout.adapter_choice_card_item_template,
    AdapterChoiceCardItemTemplateBinding::bind
) {
    override fun AdapterChoiceCardItemTemplateBinding.onBind() {
//        root.setMargin(right = dp(10))

        if (ScreenUtil.isPad(context)) {
            val layoutParams = root.layoutParams
            layoutParams.width = ScreenUtil.dp2px(context, 212f)
            root.layoutParams = layoutParams
        } else {
            val layoutParams = root.layoutParams
            layoutParams.width = ScreenUtil.dp2px(context, 212f)
            root.layoutParams = layoutParams
        }
        // 封面
        listener.getGlideOrNull()?.run {
            load(item.iconUrl).placeholder(R.drawable.placeholder_corner_12)
                .transform(CenterCrop())
                .into(ivGameIcon)

            load(item.iconUrl)
                .transform(
                    GradientTransformation(ivLikeBg), RoundedCorners(12.dp)
                )
                .into(ivLikeBg)
        }
        // 点赞
        likeView.setLikeText(UnitUtil.formatKMCount(item.localLikeCount ?: item.likeCount ?: 0))
        // 标题
        tvGameTitle.text = item.displayName
        // 星星数量
        val stars = listOf(ivStar1, ivStar2, ivStar3)
        val rating = item.difficulty?.toInt() ?: 3

        // 先全部隐藏
        tagContainer.visibility = android.view.View.GONE
        btnCreate.visibility = android.view.View.GONE
        sivCreatorAvatar.visibility = android.view.View.GONE
        tvCreatorNickname.visibility = android.view.View.GONE
        playersView.visibility = android.view.View.GONE

        if (isTemplate) {
            // 第一位 模板数据
            tagContainer.visibility = android.view.View.VISIBLE
            btnCreate.visibility = android.view.View.VISIBLE
            layoutTemplateTag.setBackgroundResource(R.drawable.bg_home_template_item)
            tvTemplate.text = getString(R.string.game_template)
            // 星星
            for (i in stars.indices) {
                stars[i].visibility = android.view.View.VISIBLE
                stars[i].alpha = if (i < rating) 1f else 0.3f
            }
            // 标签
            tagContainer.setTags(item.tagList ?: emptyList())
            // 按钮
            btnCreate.setOnAntiViolenceClickListener { listener.onMoreClick(card) }

            root.setOnClickListener {

            }
        } else {
            // 作品数据
            sivCreatorAvatar.visibility = android.view.View.VISIBLE
            tvCreatorNickname.visibility = android.view.View.VISIBLE
            playersView.visibility = android.view.View.VISIBLE

            layoutTemplateTag.setBackgroundResource(R.drawable.bg_home_template_child_item)
            tvTemplate.text = getString(R.string.game_creation)
            // 星星
            for (i in stars.indices) {
                stars[i].visibility = android.view.View.GONE
            }
            listener.getGlideOrNull()?.run {
                load(item.avatar).placeholder(R.drawable.placeholder_round)
                    .transform(CenterCrop())
                    .into(sivCreatorAvatar)
            }
            tvCreatorNickname.text = item.nickname
            playersView.setLikeText(getString(R.string.played_x_times, UnitUtil.formatKMCount(item.playCount ?: 0)))

            btnCreate.setOnClickListener { }
            // 普通作品可点击整个卡片
            root.setOnAntiViolenceClickListener { listener.onItemClick(cardPosition, card, position, item, false) }
        }
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        if (visibilityState == VisibilityState.VISIBLE) {
            listener.onItemShow(cardPosition, card, position, item, false)
        }
    }
}