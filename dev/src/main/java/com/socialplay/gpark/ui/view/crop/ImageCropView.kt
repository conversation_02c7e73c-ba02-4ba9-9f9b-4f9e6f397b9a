package com.socialplay.gpark.ui.view.crop

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.Region
import android.util.AttributeSet
import android.util.SparseArray
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.View
import android.view.ViewGroup
import android.view.animation.DecelerateInterpolator
import androidx.core.content.ContextCompat
import com.socialplay.gpark.R
import timber.log.Timber
import kotlin.math.abs

/***
 * 图片裁剪
 */
class ImageCropView : View {
    private var centerX: Float = 0.toFloat()
    private var centerY: Float = 0.toFloat()

    //view显示的图片
    private var showBitmap: Bitmap? = null
    private var showBitmapRectF: RectF? = null

    //控制图片绘制的矩阵
    private var showBitmapMatrix: Matrix? = null
    private var showBitmapPaint: Paint? = null

    //图片可放大的最大倍数
    private var maxScale = 3f

    //双击图片放大倍数
    private var doubleClickScale = 1.8f
    private var doubleClickX: Float = 0.toFloat()
    private var doubleClickY: Float = 0.toFloat()

    //随着圆形区域的变小而变小,用来限制圆形的缩小倍数
    private val minCircleScale = 1f

    //初始化图片缩放和平移，保证图片在view中心显示
    private var initScale = 1f
    private var initTranslateX: Float = 0.toFloat()
    private var initTranslateY: Float = 0.toFloat()
    private var circleRectFMatrix: Matrix? = null
    private var initRectangleRectF: RectF? = null

    //圆形所在矩阵
    private var rectangleRectF: RectF? = null

    //包裹圆形可触摸矩阵
    private var bigCircleRectF: RectF? = null

    //通过path在view中显示出方框
    private var rectPath: Path? = null

    //给圆形path内部绘制一个边框(like qq)
    private var circleBorderPath: Path? = null
    private var circleBorderPaint: Paint? = null

    //圆形之外所有区域
    private var outsidePath: Path? = null
    private var paint: Paint? = null
    private var bgPaint: Paint? = null
    private var touchRegion: Region? = null //(暂时没用)

    //用于放大圆形(暂时没用)
    private var bigRectPath: Path? = null

    //是否可以放大圆形(暂时没用)
    private var canZoomRect: Boolean = false

    //是否可以移动图片(点击图片内部区域才能移动位置)
    private var canMoveBitmap: Boolean = false

    // 允许双指放大缩小
    private var enableZoom: Boolean = true

    // 允许双击放大缩小
    private var enableDoubleClick: Boolean = true

    // 裁剪尺寸与原图保持一致
    private var clipUseBitmapRatio: Boolean = true

    private var gestureDetector: GestureDetector? = null
    private var scaleGestureDetector: ScaleGestureDetector? = null
    private var valueAnimator: ValueAnimator? = null

    private var bgColor = Color.WHITE
    private var maskColor: Int = 0
    private var ratioWidth: Float = 9F
    private var ratioHeight: Float = 16F
    private var cropBoxHorPadding: Float = 0F
    private var borderColor: Int = 0
    private var sizeChanged: Boolean = false

    private val screenWidth: Int
        get() = context.resources.displayMetrics.widthPixels

    private val pathInterval: Float
        get() = dip2px(context, 0.5f).toFloat()

    private val touchAreaWidth: Int
        get() = dip2px(context, 10f)

    private //加了水平翻转功能进去，所以这里取绝对值
    val currentScale: Float
        get() {
            val temp = FloatArray(9)
            showBitmapMatrix!!.getValues(temp)
            return abs(temp[Matrix.MSCALE_X])
        }

    constructor(context: Context) : super(context) {
        initGesture()
        initAttr(null)
    }


    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        initGesture()
        initAttr(attrs)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        initGesture()
        initAttr(attrs)
    }

    private fun initAttr(attrs: AttributeSet?) {
        maskColor = Color.parseColor("#60000000")
        borderColor = ContextCompat.getColor(context, android.R.color.white)
        if (attrs == null) {
            return
        }
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.ImageCropView)
        maskColor =
            typedArray.getColor(R.styleable.ImageCropView_maskColor, Color.parseColor("#60000000"))
        bgColor =
            typedArray.getColor(R.styleable.ImageCropView_bgColor, Color.parseColor("#00000000"))
        borderColor = typedArray.getColor(
            R.styleable.ImageCropView_borderColor,
            ContextCompat.getColor(context, android.R.color.white)
        )
//        radius = typedArray.getDimension(R.styleable.ImageCropView_radius, -1f)

        maxScale = typedArray.getFloat(R.styleable.ImageCropView_maxScale, 3f)
        doubleClickScale = typedArray.getFloat(R.styleable.ImageCropView_doubleClickScale, 1.8f)
        ratioWidth = typedArray.getFloat(R.styleable.ImageCropView_ratioWidth, 9F)
        ratioHeight = typedArray.getFloat(R.styleable.ImageCropView_ratioHeight, 16F)
        enableZoom = typedArray.getBoolean(R.styleable.ImageCropView_enableZoom, enableZoom)
        cropBoxHorPadding = typedArray.getDimension(R.styleable.ImageCropView_cropBoxHorPadding, 0F)
        enableDoubleClick =
            typedArray.getBoolean(R.styleable.ImageCropView_enableDoubleClick, enableDoubleClick)
        if (maxScale < 1) {
            maxScale = 1f
        }
        if (doubleClickScale < 1) {
            doubleClickScale = 1f
        }
        if (doubleClickScale > maxScale) {
            doubleClickScale = maxScale
        }
        typedArray.recycle()
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val widthSize = MeasureSpec.getSize(widthMeasureSpec)
        val heightSize = MeasureSpec.getSize(heightMeasureSpec)
        val width = screenWidth / 2
        val height = screenWidth / 2

        if (ViewGroup.LayoutParams.WRAP_CONTENT == layoutParams.width && ViewGroup.LayoutParams.WRAP_CONTENT == layoutParams.height) {
            setMeasuredDimension(width, height)
        } else if (ViewGroup.LayoutParams.WRAP_CONTENT == layoutParams.width) {
            setMeasuredDimension(width, heightSize)
        } else if (ViewGroup.LayoutParams.WRAP_CONTENT == layoutParams.height) {
            setMeasuredDimension(widthSize, height)
        } else {
            super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        }

    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        touchRegion = Region()
        paint = Paint(Paint.ANTI_ALIAS_FLAG)
        paint?.style = Paint.Style.STROKE
        paint?.strokeWidth = 2f
        circleBorderPaint = Paint(Paint.ANTI_ALIAS_FLAG)
        circleBorderPaint?.color = borderColor
        circleBorderPaint?.style = Paint.Style.STROKE
        circleBorderPaint?.strokeWidth = dip2px(context, 1f).toFloat()
        bgPaint = Paint(Paint.ANTI_ALIAS_FLAG)
        bgPaint?.color = maskColor
        showBitmapPaint = Paint(Paint.ANTI_ALIAS_FLAG)
        showBitmapPaint?.style = Paint.Style.STROKE
        showBitmapPaint?.strokeWidth = 2f
        init()
        sizeChanged = true
    }

    private fun init() {
        centerX = (width / 2).toFloat()
        centerY = (height / 2).toFloat()
        circleBorderPath = Path()
        rectPath = Path()
        outsidePath = Path()
        bigRectPath = Path()
        val tempBitmap = showBitmap ?: return
//        if (tempBitmap.height < height && tempBitmap.width < width) {
        //如果图片宽高均小于屏幕宽高，只需要计算图片位移到中心的距离
        val bmRatio = tempBitmap.width / tempBitmap.height.toFloat()
        //根据图片矩阵获取圆形矩阵
        rectangleRectF = getRectangleRectFByBitmapRectF()
        val tempRectangleRectF = rectangleRectF ?: return
        val rectFWidth = getRectWidth(tempRectangleRectF)
        val rectFHeight = getRectHeight(tempRectangleRectF)
        val rectFRatio = rectFWidth / rectFHeight
        initScale =
            if (bmRatio > rectFRatio) {
                rectFHeight / tempBitmap.height
            } else {
                rectFWidth / tempBitmap.width
            }
        initTranslateX = (width - tempBitmap.width * initScale) / 2F
        initTranslateY = (height - tempBitmap.height * initScale) / 2F

//        } else if ((tempBitmap.width.toFloat() / tempBitmap.height) > (width.toFloat() / height)) {
//            //如果图片宽(高)大于屏幕宽(高)，需要计算图片缩小倍数和位移到中心的距离
//            if (tempBitmap.width > tempBitmap.height) {
//                //宽大于高
//                initScale = if (tempBitmap.height >= width) {
//                    1f
//                } else {
//                    (width / tempBitmap.height).toFloat()
//                }
//            }
//            initTranslateX = 0f
//            initTranslateY = (height - tempBitmap.height * initScale) / 2
//        } else {
//            //高大于宽
//            initScale = 1f
//            initTranslateX = (width - tempBitmap.width * initScale) / 2
//            initTranslateY = 0f
//        }
        circleRectFMatrix = Matrix()
        //图片未缩放的矩阵
        showBitmapRectF = RectF(0f, 0f, tempBitmap.width.toFloat(), tempBitmap.height.toFloat())
        showBitmapMatrix = Matrix().apply {
            postScale(initScale, initScale)
            postTranslate(initTranslateX, initTranslateY)
            //图片缩放之后的矩阵
            mapRect(showBitmapRectF)
        }
        //记录初始化的圆形矩阵
        initRectangleRectF = rectangleRectF
        refreshPath()

    }

    private fun refreshPath() {
        if (outsidePath == null || circleBorderPath == null || bigRectPath == null || rectangleRectF == null || rectPath == null) {
            return
        }
        if (!outsidePath!!.isEmpty) {
            outsidePath?.reset()
        }
        //圆形之外所有区域
        outsidePath?.addRect(RectF(0f, 0f, width.toFloat(), height.toFloat()), Path.Direction.CW)

        if (!rectPath!!.isEmpty) {
            rectPath?.reset()
        }
        //圆形之内所有区域
        rectPath?.addRect(
            rectangleRectF!!,
//            radius, radius,
            Path.Direction.CW
        )
        if (!circleBorderPath!!.isEmpty) {
            circleBorderPath?.reset()
        }
        val circleBorderRectF = RectF(
            rectangleRectF!!.left + pathInterval,
            rectangleRectF!!.top + pathInterval,
            rectangleRectF!!.right - pathInterval,
            rectangleRectF!!.bottom - pathInterval
        )
        circleBorderPath?.addRect(
            circleBorderRectF,
//            radius * getRectLength(circleBorderRectF) / getRectLength(circleRectF!!),
//            radius * getRectLength(circleBorderRectF) / getRectLength(circleRectF!!),
            Path.Direction.CW
        )

        //获取圆形之外所有区域
        outsidePath?.op(rectPath!!, Path.Op.XOR)

        this.bigCircleRectF = getBigCircleRectF(rectangleRectF)
        if (!bigRectPath!!.isEmpty) {
            bigRectPath?.reset()
        }
        bigRectPath?.addRect(
            this.bigCircleRectF!!,
//            (this.bigCircleRectF!!.right - this.bigCircleRectF!!.left) / 2,
//            (this.bigCircleRectF!!.right - this.bigCircleRectF!!.left) / 2,
            Path.Direction.CW
        )

        bigRectPath?.op(rectPath!!, Path.Op.XOR)
    }

    /**
     * 生成裁剪后的bitmap，如果不需要裁剪，则返回null
     * @return 裁剪后的bitmap
     */
    fun clip(): Bitmap? {
        if (!sizeChanged || showBitmap == null || rectangleRectF == null) {
            return null
        }
        val paint = Paint(Paint.ANTI_ALIAS_FLAG)

        val matrix = Matrix()
        showBitmapMatrix?.invert(matrix)
        val rectF = RectF()
        val tempRectangleRectF = rectangleRectF ?: return null
        rectF.set(tempRectangleRectF)
        matrix.mapRect(rectF)
        var needCropBitmap: Bitmap = runCatching {
            Bitmap.createBitmap(
                showBitmap!!,
                rectF.left.toInt(),
                rectF.top.toInt(),
                (rectF.right - rectF.left).toInt(),
                (rectF.bottom - rectF.top).toInt()
            )
        }.getOrNull() ?: return null
        val newBitmap = runCatching {
            Bitmap.createBitmap(
                getRectWidth(tempRectangleRectF).toInt(),
                getRectHeight(tempRectangleRectF).toInt(),
                Bitmap.Config.ARGB_8888
            )
        }.getOrElse {
            return null
        }
        val canvas = Canvas(newBitmap)
        canvas.drawColor(bgColor)
        val saveCount = canvas.saveLayer(null, null, Canvas.ALL_SAVE_FLAG)
        val path = Path()
        path.addRect(
            RectF(0f, 0f, getRectWidth(tempRectangleRectF), getRectHeight(tempRectangleRectF)),
            Path.Direction.CW
        )

        path.moveTo(0f, 0f)
        path.moveTo(getRectWidth(tempRectangleRectF), getRectHeight(tempRectangleRectF))

        canvas.drawPath(path, paint)
        paint.xfermode = PorterDuffXfermode(PorterDuff.Mode.SRC_IN)

        canvas.drawBitmap(
            needCropBitmap,
            Rect(0, 0, needCropBitmap.width, needCropBitmap.height),
            RectF(0f, 0f, getRectWidth(tempRectangleRectF), getRectHeight(tempRectangleRectF)),
            paint
        )
        paint.xfermode = null
        canvas.restoreToCount(saveCount)
        needCropBitmap.recycle()
        return newBitmap
    }

    /*还原*/
    private fun reset() {
        if (sizeChanged) {
            init()
            invalidate()
        } else {
            post {
                init()
                invalidate()
            }
        }
    }

    //根据图片缩放之后的矩阵获取圆形裁剪框的矩阵
    private fun getRectangleRectFByBitmapRectF(): RectF {
        val ratio = ratioWidth / ratioHeight
//        val bmRatio = showBitmapRectF.width() / showBitmapRectF.height()
        val viewRatio = width / height.toFloat()
//        val rectFW = showBitmapRectF.right - showBitmapRectF.left
//        val rectFH = showBitmapRectF.bottom - showBitmapRectF.top
        //圆形所在矩阵边长
        val sumHorPadding = cropBoxHorPadding * 2
        val circleRectFWidth = if (ratio > viewRatio) (width.toFloat() - cropBoxHorPadding) else ratio * height.toFloat()
        val circleRectFHeight = if (ratio > viewRatio) (1 / ratio) * (width.toFloat() - cropBoxHorPadding) else height.toFloat()
        //计算出圆形所在矩阵的left top
        val circleRectFLeft = centerX - circleRectFWidth / 2
        val circleRectFTop = centerY - circleRectFHeight / 2
        //圆形矩阵
        return RectF(
            circleRectFLeft,
            circleRectFTop,
            circleRectFWidth + circleRectFLeft,
            circleRectFHeight + circleRectFTop
        )
    }

    private fun getRectWidth(rectF: RectF): Float {
        return abs(rectF.right - rectF.left)
    }

    private fun getRectHeight(rectF: RectF): Float {
        return abs(rectF.bottom - rectF.top)
    }

    private fun getBigCircleRectF(circleRectF: RectF?): RectF {
        val rectF = RectF()
        if (circleRectF != null) {
            rectF.set(circleRectF)
        }
        rectF.left = rectF.left - touchAreaWidth
        rectF.top = rectF.top - touchAreaWidth
        rectF.right = rectF.right + touchAreaWidth
        rectF.bottom = rectF.bottom + touchAreaWidth
        return rectF
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        if (showBitmap == null || showBitmapMatrix == null || outsidePath == null || bgPaint == null || circleBorderPaint == null || circleBorderPaint == null) {
            return
        }
        canvas.drawColor(bgColor)
        canvas.drawBitmap(showBitmap!!, showBitmapMatrix!!, null)
        canvas.drawPath(outsidePath!!, bgPaint!!)
        canvas.drawPath(circleBorderPath!!, circleBorderPaint!!)
    }

    private fun dip2px(context: Context, dipValue: Float): Int {
        val scale = context.resources.displayMetrics.density
        return (dipValue * scale + 0.5f).toInt()
    }

    private fun initGesture() {
        gestureDetector =
            GestureDetector(context, object : GestureDetector.SimpleOnGestureListener() {
                override fun onScroll(
                    e1: MotionEvent?,
                    e2: MotionEvent,
                    distanceX: Float,
                    distanceY: Float
                ): Boolean {
                    var distanceX = distanceX
                    var distanceY = distanceY
                    //通过移动来缩放圆形裁剪框
                    if (canZoomRect) {
                        val distance = if (abs(distanceX) > abs(distanceY)) distanceX else distanceY
                        val rectHeight = rectangleRectF!!.bottom - rectangleRectF!!.top
                        val scaleFactory = (-distance * 2 + rectHeight) / rectHeight
                        showBitmapMatrix?.postScale(scaleFactory, scaleFactory, centerX, centerY)
                        showBitmapRectF = RectF(
                            0f,
                            0f,
                            showBitmap!!.width.toFloat(),
                            showBitmap!!.height.toFloat()
                        )
                        showBitmapMatrix?.mapRect(showBitmapRectF)
                        rectangleRectF = RectF()
                        rectangleRectF?.set(getRectangleRectFByBitmapRectF())
                        bigCircleRectF = getBigCircleRectF(rectangleRectF)
                        refreshPath()
                        invalidate()
                    }
                    //移动图片
                    if (canMoveBitmap && !canZoomRect) {
                        //从左往右滑动图片(防止图片滑出裁剪框外)
                        if (distanceX < 0) {
                            val rectDistance = rectangleRectF!!.left - showBitmapRectF!!.left
                            if (rectDistance < abs(distanceX)) {
                                distanceX = -rectDistance
                            }
                        }
                        //从右往左滑动图片(防止图片滑出裁剪框外)
                        if (distanceX > 0) {
                            val rectDistance = showBitmapRectF!!.right - rectangleRectF!!.right
                            if (rectDistance < abs(distanceX)) {
                                distanceX = rectDistance
                            }
                        }
                        //从上往下滑动图片(防止图片滑出裁剪框外)
                        if (distanceY < 0) {
                            val rectDistance = rectangleRectF!!.top - showBitmapRectF!!.top
                            if (rectDistance < abs(distanceY)) {
                                distanceY = -rectDistance
                            }
                        }

                        //从下往上滑动图片(防止图片滑出裁剪框外)
                        if (distanceY > 0) {
                            val rectDistance = showBitmapRectF!!.bottom - rectangleRectF!!.bottom
                            if (rectDistance < abs(distanceY)) {
                                distanceY = rectDistance
                            }
                        }
                        showBitmapRectF = RectF(
                            0f,
                            0f,
                            showBitmap!!.width.toFloat(),
                            showBitmap!!.height.toFloat()
                        )
                        showBitmapMatrix?.postTranslate(-distanceX, -distanceY)
                        showBitmapMatrix?.mapRect(showBitmapRectF)
                        invalidate()
                    }
                    return true
                }

                override fun onDoubleTapEvent(e: MotionEvent): Boolean {
                    if (!enableZoom) {
                        return super.onDoubleTapEvent(e)
                    }
                    when (e.action) {
                        MotionEvent.ACTION_UP -> if (showBitmapRectF!!.contains(e.x, e.y)) {
                            if (currentScale > initScale) {
                                //用于双击图片放大缩小,获取动画间隔缩放系数
                                val sparseArray = SparseArray<Float>()
                                sparseArray.put(0, -1f)
                                sparseArray.put(1, -1f)
                                valueAnimator = ValueAnimator.ofFloat(currentScale, initScale)
                                valueAnimator!!.addUpdateListener { animation ->
                                    val value = animation.animatedValue as Float
                                    var tempScale = 1f
                                    if (sparseArray.get(0) == -1f && sparseArray.get(1) == -1f) {
                                        sparseArray.put(0, value)
                                    } else if (sparseArray.get(1) == -1f) {
                                        sparseArray.put(1, value)
                                        tempScale = sparseArray.get(1) / sparseArray.get(0)
                                    } else {
                                        sparseArray.put(0, sparseArray.get(1))
                                        sparseArray.put(1, value)
                                        tempScale = sparseArray.get(1) / sparseArray.get(0)
                                    }
                                    zoomBitmap(tempScale, centerX, centerX)
                                    invalidate()
                                }
                                valueAnimator?.interpolator = DecelerateInterpolator()
                                valueAnimator?.duration = 300
                                valueAnimator?.start()
                            } else {
                                doubleClickX = e.x
                                doubleClickY = e.y
                                //用于双击图片放大缩小,获取动画间隔缩放系数
                                val sparseArray = SparseArray<Float>()
                                sparseArray.put(0, -1f)
                                sparseArray.put(1, -1f)
                                valueAnimator =
                                    ValueAnimator.ofFloat(currentScale, doubleClickScale)
                                valueAnimator?.addUpdateListener { animation ->
                                    val value = animation.animatedValue as Float
                                    var tempScale = 1f
                                    if (sparseArray.get(0) == -1f && sparseArray.get(1) == -1f) {
                                        sparseArray.put(0, value)
                                    } else if (sparseArray.get(1) == -1f) {
                                        sparseArray.put(1, value)
                                        tempScale = sparseArray.get(1) / sparseArray.get(0)
                                    } else {
                                        sparseArray.put(0, sparseArray.get(1))
                                        sparseArray.put(1, value)
                                        tempScale = sparseArray.get(1) / sparseArray.get(0)
                                    }
                                    showBitmapMatrix?.postScale(
                                        tempScale,
                                        tempScale,
                                        doubleClickX,
                                        doubleClickY
                                    )
                                    showBitmapRectF = RectF(
                                        0f,
                                        0f,
                                        showBitmap!!.width.toFloat(),
                                        showBitmap!!.height.toFloat()
                                    )
                                    showBitmapMatrix?.mapRect(showBitmapRectF)
                                    invalidate()
                                }
                                valueAnimator?.interpolator = DecelerateInterpolator()
                                valueAnimator?.duration = 300
                                valueAnimator?.start()
                            }
                        }
                    }
                    return super.onDoubleTapEvent(e)
                }
            })
        scaleGestureDetector = ScaleGestureDetector(
            context,
            object : ScaleGestureDetector.SimpleOnScaleGestureListener() {
                override fun onScale(detector: ScaleGestureDetector): Boolean {
                    if (!enableZoom) {
                        return super.onScale(detector)
                    }
                    val currentScale = currentScale
                    var scaleFactor = detector.scaleFactor

                    //防止过度缩小
                    if (currentScale * scaleFactor < initScale) {
                        scaleFactor = initScale / currentScale
                    }

                    zoomBitmap(scaleFactor, detector.focusX, detector.focusY)
                    invalidate()
                    return true
                }

                override fun onScaleBegin(detector: ScaleGestureDetector): Boolean {
                    //如果缩放中心在图片范围内就可以缩放
                    return showBitmapRectF?.contains(detector.focusX, detector.focusY) ?: false
                }
            })
    }

    private fun zoomBitmap(scaleFactor: Float, focusX: Float, focusY: Float) {
        var scaleFactor = scaleFactor
        if (scaleFactor > 1 && currentScale * scaleFactor > maxScale) {
            scaleFactor = maxScale / currentScale
        }
        showBitmapMatrix?.postScale(scaleFactor, scaleFactor, focusX, focusY)
        showBitmapRectF = RectF(0f, 0f, showBitmap!!.width.toFloat(), showBitmap!!.height.toFloat())
        showBitmapMatrix?.mapRect(showBitmapRectF)

        //如果缩小需要检查圆形框是否包含图片，如果不包含，缩小之后需要平移
        if (scaleFactor < 1) { //小于1缩小动作，大于1放大动作
            val leftLength = showBitmapRectF!!.left - rectangleRectF!!.left
            if (leftLength > 0) {
                showBitmapMatrix!!.postTranslate(-leftLength, 0f)
            }
            val topLength = showBitmapRectF!!.top - rectangleRectF!!.top
            if (topLength > 0) {
                showBitmapMatrix!!.postTranslate(0f, -topLength)
            }
            val rightLength = rectangleRectF!!.right - showBitmapRectF!!.right
            if (rightLength > 0) {
                showBitmapMatrix!!.postTranslate(rightLength, 0f)
            }
            val bottomLength = rectangleRectF!!.bottom - showBitmapRectF!!.bottom
            if (bottomLength > 0) {
                showBitmapMatrix!!.postTranslate(0f, bottomLength)
            }
            showBitmapRectF =
                RectF(0f, 0f, showBitmap!!.width.toFloat(), showBitmap!!.height.toFloat())
            showBitmapMatrix!!.mapRect(showBitmapRectF)
        }

    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        scaleGestureDetector?.onTouchEvent(event)
        gestureDetector?.onTouchEvent(event)
        when (event.action) {
            MotionEvent.ACTION_DOWN -> if (showBitmapRectF!!.contains(event.x, event.y)) {
                canMoveBitmap = true
            }

            MotionEvent.ACTION_UP -> {
                canMoveBitmap = false
                canZoomRect = false
            }
        }
        return true
    }

    private fun resetBitmap() {
        reset()
    }

    fun setRatio(ratioW: Float, ratioH: Float) {
        ratioWidth = ratioW
        ratioHeight = ratioH
        reset()
    }

    fun setBitmapForWidth(
        ratioW: Float?,
        ratioH: Float?,
        useBitmapRatio: Boolean,
        bitmap: Bitmap,
        reqWidth: Int
    ): ImageCropView {
        if (ratioW != null && ratioH != null && ratioW > 0 && ratioH > 0) {
            ratioWidth = ratioW
            ratioHeight = ratioH
        }
        if (useBitmapRatio) {
            ratioWidth = bitmap.width.toFloat()
            ratioHeight = bitmap.height.toFloat()
        }
        showBitmap = bitmap
        Timber.d("setBitmapForWidth ${bitmap.width},${bitmap.width}")
        resetBitmap()
        return this
    }

    fun recyclerBm() {
        showBitmap?.recycle()
        showBitmap = null
    }

}
