package com.socialplay.gpark.ui.editorschoice.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.data.model.choice.ChoiceGameInfo
import com.socialplay.gpark.databinding.AdapterChoiceCardItemHorizontalBinding
import com.socialplay.gpark.ui.base.adapter.BaseVBViewHolder
import com.socialplay.gpark.util.SmallCardViewUtil

/**
 * 横向滑动适配器
 * <AUTHOR> Assistant
 * @date 2024/12/19
 */
class HorizontalItemAdapter(
    data: MutableList<ChoiceGameInfo>?,
    private val glide: RequestManager
) :
    BaseEditorsChoiceItemAdapter<AdapterChoiceCardItemHorizontalBinding>(data) {

    override fun viewBinding(
        parent: ViewGroup,
        viewType: Int
    ): AdapterChoiceCardItemHorizontalBinding {
        return AdapterChoiceCardItemHorizontalBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    }

    override fun convert(
        holder: BaseVBViewHolder<AdapterChoiceCardItemHorizontalBinding>,
        item: ChoiceGameInfo
    ) {
        // 使用SmallCardViewUtil设置基本内容
        SmallCardViewUtil.setupCardView(context, glide, item, holder.binding, false)
        
        // 显示价格标签（如果有价格）
        holder.binding.tvPrice.apply {
            isVisible = item.price != null && item.price > 0
            if (isVisible) {
                text = "${item.price}币"
            }
        }
    }
} 