package com.socialplay.gpark.ui.room.all

import android.graphics.Rect
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.LoadStatus
import com.socialplay.gpark.data.model.LoadType
import com.socialplay.gpark.data.model.room.ChatRoomInfo
import com.socialplay.gpark.databinding.FragmentHomeAllRoomListBinding
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.ui.editorschoice.adapter.RoomItemAdapter
import com.socialplay.gpark.ui.room.HomeRoomAnalytics
import com.socialplay.gpark.ui.room.RoomDetailDialog
import com.socialplay.gpark.ui.view.EmptyLoadMoreView
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setOnAntiViolenceItemClickListener
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.launch
import org.koin.androidx.viewmodel.ext.android.viewModel

/**
 * @des:
 * @author: lijunjia
 * @date: 2023/7/12 18:03
 */
class HomeAllRoomListFragment : BaseFragment<FragmentHomeAllRoomListBinding>() {
    private val viewModel by viewModel<HomeAllRoomViewModel>()
    private val mAdapter by lazy {
        RoomItemAdapter(Glide.with(this), true)
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentHomeAllRoomListBinding? {
        return FragmentHomeAllRoomListBinding.inflate(inflater, container, false)
    }

    override fun init() {
        HomeRoomAnalytics.trackAllRoomPageShow()
        initView()
        initData()
    }

    private fun initView() {
        binding.tvCreate.setOnAntiViolenceClickListener {
            HomeRoomAnalytics.trackAllRoomPageCreateRoomClick()
            MetaRouter.HomeRoom.goCreateRoom(this)
        }
        binding.loadingView.apply {
            setRetry {
                viewModel.loadData()
            }
        }
        binding.titleBar.setOnBackClickedListener {
            findNavController().popBackStack()
        }
        binding.refresh.setOnRefreshListener {
            viewModel.loadData()
        }

        binding.rv.removeItemDecoration(spaceItemDecoration)
        binding.rv.addItemDecoration(spaceItemDecoration)
        binding.rv.layoutManager = GridLayoutManager(requireContext(), 2)
        binding.rv.adapter = mAdapter.apply {
            loadMoreModule.loadMoreView = EmptyLoadMoreView()
            loadMoreModule.setOnLoadMoreListener {
                viewModel.loadMore()
            }
            loadMoreModule.isEnableLoadMore = false
        }
        mAdapter.setOnAntiViolenceItemClickListener { adapter, _, position ->
            val chatRoomInfo = adapter.getItemOrNull(position)
            chatRoomInfo?.let {
                HomeRoomAnalytics.trackRoomClick(it, CategoryId.HOME_ROOM_LIST)
                showRoomDetail(it)
            }
        }
        mAdapter.setOnItemShowListener { item, _ ->
            HomeRoomAnalytics.trackRoomShow(item, CategoryId.HOME_ROOM_LIST)
        }
    }

    /**
     * 展示房间详情
     */
    private fun showRoomDetail(chatRoomInfo: ChatRoomInfo) {
        RoomDetailDialog.show(
            this,
            chatRoomInfo,
            ResIdBean().setGameId(chatRoomInfo.platformGameId)
                .setCategoryID(CategoryId.HOME_ROOM_LIST)
        )
    }

    private val spaceItemDecoration = object : RecyclerView.ItemDecoration() {
        private var spanCount = 2
        private var dp8 = 8.dp
        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: RecyclerView.State
        ) {
            val position = parent.getChildAdapterPosition(view)
            val currentRow = position / spanCount
            val currentColumn = position % spanCount
            if (currentRow == 0) {
                outRect.top = dp8 * 2
            } else {
                outRect.top = dp8
            }
            outRect.bottom = dp8
            if (currentColumn == 0) {
                outRect.left = dp8 * 2
                outRect.right = dp8
            } else {
                outRect.left = dp8
                outRect.right = dp8 * 2
            }
        }
    }

    private fun initData() {
        viewModel.roomListLiveData.observe(viewLifecycleOwner) {
            viewLifecycleOwner.lifecycleScope.launch {
                updateList(it)
            }
        }
    }

    private fun getLoadingView() = binding.loadingView

    private fun updateList(it: Pair<LoadStatus, MutableList<ChatRoomInfo>?>) {
        val status = it.first
        val list = it.second
        binding.refresh.isRefreshing = false
        when (status.status) {
            LoadType.Refresh, LoadType.RefreshEnd -> {
                // 可能：Refresh、RefreshEmpty、RefreshFailed、RefreshEnd
                mAdapter.submitData(viewLifecycleOwner.lifecycle, list, true)
                when {
                    list.isNullOrEmpty() && !status.message.isNullOrEmpty() -> {
                        // RefreshFailed
                        getLoadingView().showError()
                    }

                    list.isNullOrEmpty() -> {
                        // RefreshEmpty
                        getLoadingView().showEmpty(getString(R.string.no_data))
                    }

                    else -> {
                        // Refresh、RefreshEnd
                        getLoadingView().hide()
                        if (status.status == LoadType.RefreshEnd) {
                            mAdapter.loadMoreModule.loadMoreEnd()
                        } else {
                            mAdapter.resetLoadMore()
                        }
                    }
                }
            }

            LoadType.LoadMore -> {
                mAdapter.submitData(viewLifecycleOwner.lifecycle, list)
                mAdapter.loadMoreModule.loadMoreComplete()
                getLoadingView().hide()
            }

            LoadType.End -> {
                // LoadMoreEnd
                mAdapter.submitData(viewLifecycleOwner.lifecycle, list)
                mAdapter.loadMoreModule.loadMoreEnd()
                getLoadingView().hide()
            }

            LoadType.Fail -> {
                // LoadMoreFailed、不能判断RefreshFailed（可能有数据但是RefreshFailed）
                mAdapter.loadMoreModule.loadMoreFail()
                getLoadingView().hide()
            }

            LoadType.Update -> {
                mAdapter.submitData(viewLifecycleOwner.lifecycle, list)
            }

            else -> {
                getLoadingView().hide()
            }
        }
    }

    override fun loadFirstData() {
        getLoadingView().showLoading()
        viewModel.loadData()
    }

    override fun getFragmentName(): String = PageNameConstants.FRAGMENT_NAME_HOME_ALL_ROOM
}