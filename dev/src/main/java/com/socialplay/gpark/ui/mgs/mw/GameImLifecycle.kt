package com.socialplay.gpark.ui.mgs.mw

import com.bin.cpbus.CpEventBus
import com.meta.biz.mgs.data.model.MGSJoinErrorEvent
import com.meta.biz.mgs.data.model.MGSSendErrorEvent
import com.meta.biz.mgs.data.mw.MWCallBackImMessage
import com.meta.biz.mgs.im.MWIMBackType
import com.meta.biz.mgs.im.MWIMConstant
import com.meta.biz.mgs.im.MWMessageRegistry
import com.meta.biz.mgs.im.listener.MwImListener
import com.meta.biz.ugc.model.MWImMessage
import com.meta.biz.ugc.protocol.UGCProtocolReceiver
import com.meta.biz.ugc.protocol.UGCProtocolSender
import com.meta.biz.ugc.protocol.constants.ProtocolReceiveConstants
import com.meta.biz.ugc.protocol.constants.ProtocolSendConstant
import com.meta.biz.ugc.protocol.listener.SingleProtocolListener
import com.socialplay.gpark.function.mw.lifecycle.MWLifecycle
import timber.log.Timber


/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/05/24
 *     desc   : mw IM消息中间层处理
 *
 */
object GameImLifecycle : MWLifecycle(), MwImListener {
    fun init(isHost: Boolean): MWLifecycle {
        UGCProtocolReceiver.addProtocolObserver(object :
            SingleProtocolListener<MWImMessage>(ProtocolReceiveConstants.PROTOCOL_ACTION_GAME_IM_SDK) {
            override fun handleProtocol(message: MWImMessage?, messageId: Int) {
                //收到mw的消息
                val data = com.meta.biz.mgs.data.mw.MWImMessage(
                    message?.ImType,
                    message?.ClassName,
                    message?.FunctionName,
                    message?.params
                )
                MWMessageRegistry.deliverCmdMessage(data, messageId, isHost)

            }
        })
        MWMessageRegistry.register(isHost, this)
        return this
    }

    override fun sendToMwImMessage(messageId: Int, message: MWCallBackImMessage) {
        val data = com.meta.biz.ugc.model.MWCallBackImMessage(
            message.ImType,
            message.ClassName,
            message.FunctionName,
            message.callback,
            message.params
        )
        UGCProtocolSender.sendProtocol(
            ProtocolSendConstant.PROTOCOL_ACTION_GAME_IM_SDK_BACK,
            messageId,
            data
        )
        checkErrorMessage(data)
    }

    /**
     * 处理mw发送消息或者加入房间失败回调
     */
    private fun checkErrorMessage(data: com.meta.biz.ugc.model.MWCallBackImMessage) {
        try {
            if (data.params != null && data.ImType == MWIMBackType.Callback) {
                val params = data.params as MutableMap<String, Any?>
                val code = params["error_code"] as String? ?: return
                if (code == "200" || code == "400") {
                    //成功或者找不到的方法不需要重试
                    return
                }
                val desc = params["error_message"] as String?
                val groupID = params["group_id"] as String?
                val functionName = params["function_name"] as String?
                when (functionName) {
                    MWIMConstant.JoinGroup -> {
                        //加入房间
                        CpEventBus.post(MGSJoinErrorEvent("", groupID ?: "", code.toInt(), desc))
                    }

                    MWIMConstant.QuitGroup -> {
                        //不处理
                    }

                    else                   -> {
                        //其他消息操作
                        CpEventBus.post(MGSSendErrorEvent("", groupID ?: "", code.toInt(), desc))
                    }
                }

            }

        } catch (e: Exception) {
            Timber.e("checkErrorMessage %s", e.printStackTrace())
        }
    }
}