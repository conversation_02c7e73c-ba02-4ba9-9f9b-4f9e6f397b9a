package com.socialplay.gpark.ui.suggestion

import android.view.View
import androidx.viewpager2.widget.ViewPager2
import com.socialplay.gpark.util.extension.dp
import timber.log.Timber
import kotlin.math.abs

class SuggestionViewPageTransformer(private val offsetY: Int) : ViewPager2.PageTransformer {

    companion object {
        private const val DEFAULT_TRANSLATION_X = .0f
        private const val DEFAULT_TRANSLATION_Y = .0f
        private const val TRANSLATION_FACTOR = 1F

        private const val SCALE_FACTOR = 0.12f
        private const val DEFAULT_SCALE = 1f
        private const val MAX_ROTATION = 15f
    }

    override fun transformPage(page: View, position: Float) {
        page.apply {
            elevation = -abs(position)
            if (position <= 0f) {
                translationX = DEFAULT_TRANSLATION_X
                rotation = MAX_ROTATION * position
                scaleX = DEFAULT_SCALE
                scaleY = DEFAULT_SCALE
                translationX = width / 6 * position
                translationY = DEFAULT_TRANSLATION_Y
                translationZ = 1F
                alpha = 1.0f
            } else {
                val scale = -SCALE_FACTOR * position + DEFAULT_SCALE
                scaleX = scale
                scaleY = scale
                rotation = 0f
                translationX = -width * position
                translationY = (page.height * (1.0f - scale) / 2 + offsetY) * position
                translationZ = 0F
                alpha = 1.0f - position * 0.25f
            }

        }
    }

}