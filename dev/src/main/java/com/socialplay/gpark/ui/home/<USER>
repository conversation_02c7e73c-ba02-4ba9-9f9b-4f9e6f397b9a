package com.socialplay.gpark.ui.home

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import com.meta.lib.mwbiz.MWBiz
import com.socialplay.gpark.app.initialize.PlayedGameInit
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.data.model.LoadStatus
import com.socialplay.gpark.data.model.LoadType
import com.socialplay.gpark.data.model.MyPlayedGame
import com.socialplay.gpark.data.model.asset.AssetType
import com.socialplay.gpark.data.model.choice.ChoiceCardInfo
import com.socialplay.gpark.data.model.choice.ChoiceCardListApiResult
import com.socialplay.gpark.data.model.choice.ChoiceCardType
import com.socialplay.gpark.data.model.choice.ChoiceContentType
import com.socialplay.gpark.data.model.choice.ChoiceGameInfo
import com.socialplay.gpark.data.model.choice.ChoiceResultEvent
import com.socialplay.gpark.data.model.entity.GameDetailEntity
import com.socialplay.gpark.data.model.room.ChatRoomInfo
import com.socialplay.gpark.data.model.room.House
import com.socialplay.gpark.data.model.room.RoomStyle
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.exoplayer.VideoFeedPreloadInteractor
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.dialog.DialogShowManager
import com.socialplay.gpark.util.GsonUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.lastOrNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.single
import kotlinx.coroutines.flow.singleOrNull
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/09/12
 * desc   :
 * </pre>
 */


class HomeViewModel(
    private val repository: com.socialplay.gpark.data.IMetaRepository,
    private val videoFeedPreloadInteractor: VideoFeedPreloadInteractor,
) : ViewModel() {
    private val gameSet = hashSetOf<String>()
    private val placeholderStateFlow = MutableStateFlow(true)
    private val metaKV: MetaKV by lazy { GlobalContext.get().get() }
    val playedGamePlaceholder: Flow<PagingData<MyPlayedGame>> = placeholderStateFlow
        .map {
            if (it) {
                val list = ArrayList<MyPlayedGame>()
                for (i in 1..9) {
                    list.add(createPlaceholderItem(i.toString()))
                }
                PagingData.from(list)
            } else {
                PagingData.empty()
            }
        }.cachedIn(viewModelScope)

    val playedGames: Flow<PagingData<MyPlayedGame>> = repository.fetchPlayedGame(PlayedGameInit.PLAYED_GAME_PAGE_SIZE)
        .cachedIn(viewModelScope)
    val recommendGames = repository.fetchRecommendGames(20).cachedIn(viewModelScope)

    private val accountInteractor by GlobalContext.get().inject<AccountInteractor>()

    private var cardRoomInfo: Pair<ChoiceCardInfo, Long?>? = null

    private val _roomListLiveData: MutableLiveData<List<ChatRoomInfo>> = MutableLiveData()
    val roomListLiveData: LiveData<List<ChatRoomInfo>> = _roomListLiveData

    private var needRefreshRoomList = false

    private val oldDataList get() = oldDataListOrNull ?: ArrayList()
    private val oldDataListOrNull get() = _cardListLiveData.value?.originalDataList?.toMutableList()

    private var isHomeRecommend: Boolean = PandoraToggle.isHomeRecommend

    private var inited = false

    companion object {
        const val UPDATE_TYPE_INSERT_TRENDING = "insert_trending"
    }

    fun init() {
        if (inited) return
        inited = true
        refreshData()
    }

    fun setPlaceHolderEnable(enable: Boolean) {
        placeholderStateFlow.value = enable
    }

    fun getRoomCardIndex() = cardListLiveData.value?.originalDataList?.indexOfFirst {
        ChoiceCardType.isRoomCardType(it.cardType)
    } ?: -1

    private fun createPlaceholderItem(id: String): MyPlayedGame {
        return MyPlayedGame(
            id,
            System.currentTimeMillis(),
            "",
            "",
            0,
            null,
            1F,
            "",
            0,
            0,
            gameTags = null,
            true
        )
    }

    suspend fun onLaunchGame(gameInfo: GameDetailEntity) {
        repository.insertGameDetailEntityIfNoExists(gameInfo)
    }

    private var pageNum: Int = 1
    private val _cardListLiveData = MutableLiveData<ChoiceResultEvent>()
    val cardListLiveData: LiveData<ChoiceResultEvent> = _cardListLiveData
    private val _cottageRoomList: MutableLiveData<List<House>?> = MutableLiveData()
    val cottageRoomList = _cottageRoomList
    var offset: Int? = null

    fun refreshData() {
        loadData(true)
    }

    fun loadMore() {
        loadData(false)
    }

    private var group: Int = 0
    private fun loadData(isRefresh: Boolean) = viewModelScope.launch {
        val isRecommend = (PandoraToggle.isRecommendOpen() || PandoraToggle.isChoiceRecommend()) && !isHomeRecommend
        if (isRefresh) {
            offset = null
            gameSet.clear()
            val cardList = repository.getChoiceCardList().single()
            refreshCardList(true, cardList, 1)
            if (!isHomeRecommend) appendVideoFeeds()
        }

        if (isRecommend) {
            val recommend = repository.getRecommend(offset).single()
            offset = recommend.data?.offset
            if (recommend.succeeded) {
                val list = oldDataList
                if (isRefresh && (recommend.data?.dataList?.size ?: 0) > 0) {
                    list.add(ChoiceCardInfo(System.currentTimeMillis(), null, ChoiceCardType.RECOMMEND_HEADER, null))
                }
                recommend.data?.dataList?.forEach {
                    if (gameSet.add(it.code ?: "")) {
                        list.add(ChoiceCardInfo(it.hashCode().toLong(), null, ChoiceCardType.RECOMMEND, it))
                    }
                }
                _cardListLiveData.value = ChoiceResultEvent(
                    loadStatus = LoadStatus(status = if (recommend.data?.end == true) LoadType.End else LoadType.LoadMore),
                    originalDataList = list
                )
            }
        }
    }

    private suspend fun appendVideoFeeds() {
        val videoFeedApiResult = repository.getRecommendVideoList(1, 20, categoryId = CategoryId.HOME_RECOMMEND_NEWEST_GAME).catch {
            Timber.w("getRecommendVideoFeedList error: ${it.message}")
        }.lastOrNull()

        if (videoFeedApiResult != null) {

            videoFeedPreloadInteractor.preloadPartyVideoFeedsIfNeed(videoFeedApiResult)

            val list = oldDataList

            val choiceCardInfo = ChoiceCardInfo(
                videoFeedApiResult.hashCode().toLong(),
                null,
                ChoiceCardType.RECOMMEND_VIDEO_FEED_LIST
            ).apply {
                videoFeeds = videoFeedApiResult
            }

            list.add(choiceCardInfo)

            val event = ChoiceResultEvent(
                loadStatus = LoadStatus(status = LoadType.Refresh),
                isUpdateAdapter = false,
                originalDataList = list
            )

            _cardListLiveData.value = event
        }

    }

    private suspend fun HomeViewModel.refreshCardList(
        isRefresh: Boolean,
        cardlist: DataResult<ChoiceCardListApiResult>,
        nextPage: Int
    ) {
        val event = ChoiceResultEvent(
            loadStatus = LoadStatus(status = if (isRefresh) LoadType.Refresh else LoadType.LoadMore),
            message = cardlist.message,
            isUpdateAdapter = false
        )
        val dataResultList = oldDataList
        if (cardlist.succeeded) {
            pageNum = nextPage
            //过滤不符合当前版本的卡片类型
            cardlist.data?.apply {
                dataList
                    ?.filter { cardInfo ->
                        //过滤当前版本支持的卡片类型
                        ChoiceCardType.isSupportCardType(cardInfo.cardType)
                    }
                    ?.run {
                        if (isRefresh) {
                            dataResultList.clear()
                        }
                        dataResultList.addAll(this)
                    }
                //是否是最后一页
                if (end) {
                    event.loadStatus.status = LoadType.End
                }
            }
            handleRoomData(dataResultList)
            //                getCottageCardInfo(dataResultList)
            if (pageNum == 1) {
                group = cardlist.data?.group ?: 0
                //                    group = (System.currentTimeMillis() % 3).toInt()
            }
            event.group = group
            event.originalDataList = dataResultList
        } else {
            event.loadStatus.status = LoadType.Fail
        }

        // 插入Mock数据：模板和资源卡片
        if (isRefresh && pageNum == 1) {
            insertMockTemplateAndAssetData(dataResultList)
        }

        _cardListLiveData.value = event
        Timber.tag(DialogShowManager.TAG).d("card list finish ${event}")
    }

    /**
     * 插入Mock模板和资源数据
     */
    private fun insertMockTemplateAndAssetData(dataResultList: MutableList<ChoiceCardInfo>) {
        // 插入模板卡片
        val templateCard = ChoiceCardInfo(
            cardId = System.currentTimeMillis(),
            cardName = "热门模板",
            cardType = ChoiceCardType.TEMPLATE,
        ).apply {
            gameList = mutableListOf(
                createMockTemplateGame("template_001", "冒险世界模板", "https://picsum.photos/200/300?random=1", 4.8, 1250, 8900),
                createMockTemplateGame("template_002", "科幻主题模板", "https://picsum.photos/200/300?random=2", 4.6, 980, 6700),
                createMockTemplateGame("template_003", "童话故事模板", "https://picsum.photos/200/300?random=3", 4.9, 2100, 15600),
                createMockTemplateGame("template_004", "城市建造模板", "https://picsum.photos/200/300?random=4", 4.7, 1560, 11200)
            )
        }

        // 插入资源卡片（包含模组和服装）
        val assetCard = ChoiceCardInfo(
            cardId = System.currentTimeMillis() + 1,
            cardName = "精选资源",
            cardType = ChoiceCardType.ASSETS,
        ).apply {
            gameList = mutableListOf(
                // 模组资源
                createMockAssetGame("module_001", "超级跑车模组", "https://picsum.photos/200/300?random=5", 4.9, 890, 12300, AssetType.MODULE, 500),
                createMockAssetGame("module_002", "魔法武器模组", "https://picsum.photos/200/300?random=6", 4.7, 650, 8900, AssetType.MODULE, 300),
                // 服装资源
                createMockAssetGame("clothes_001", "公主裙装", "https://picsum.photos/200/300?random=7", 4.8, 1200, 15600, AssetType.CLOTHES),
                createMockAssetGame("clothes_002", "骑士盔甲", "https://picsum.photos/200/300?random=8", 4.6, 980, 11200, AssetType.CLOTHES),
                createMockAssetGame("clothes_003", "魔法师袍", "https://picsum.photos/200/300?random=9", 4.9, 2100, 18900, AssetType.CLOTHES),
                createMockAssetGame("module_003", "飞行器模组", "https://picsum.photos/200/300?random=10", 4.8, 750, 9800, AssetType.MODULE, 800)
            )
        }

        // 在合适的位置插入Mock数据（在第一个卡片之后插入）
        if (dataResultList.isNotEmpty()) {
            dataResultList.add(1, templateCard)
            dataResultList.add(2, assetCard)
        } else {
            dataResultList.add(templateCard)
            dataResultList.add(assetCard)
        }
    }

    /**
     * 创建Mock模板游戏数据
     */
    private fun createMockTemplateGame(
        code: String,
        title: String,
        imageUrl: String,
        rating: Double,
        likeCount: Long,
        playCount: Long
    ): ChoiceGameInfo {
        return ChoiceGameInfo(
            description = "这是一个精彩的$title，包含丰富的游戏元素和精美的视觉效果",
            displayName = title,
            iconUrl = imageUrl,
            code = code,
            imageUrl = imageUrl,
            gcMode = "UGC",
            packageName = "com.socialplay.template.$code",
            rating = rating,
            rate = rating / 5.0,
            uid = "creator_${code}",
            nickname = "模板创作者",
            avatar = "https://picsum.photos/50/50?random=${code.hashCode()}",
            type = ChoiceContentType.GAME,
            tagList = listOf("模板", "热门", "推荐"),
            startupExtension = null,
            likeCount = likeCount,
            score = rating.toFloat(),
            playCountTotal = playCount,
            playCount = playCount,
            isNewGame = false,
            playedFriendCount = (playCount * 0.1).toLong(),
            operatingPosition = null,
            ignoreThis = false,
            difficulty = 2.5 // 中等难度
        )
    }

    /**
     * 创建Mock资源游戏数据
     */
    private fun createMockAssetGame(
        code: String,
        title: String,
        imageUrl: String,
        rating: Double,
        likeCount: Long,
        playCount: Long,
        assetType: AssetType,
        price: Long? = null
    ): ChoiceGameInfo {
        val typeName = when (assetType) {
            AssetType.MODULE -> "模组"
            AssetType.CLOTHES -> "服装"
        }

        return ChoiceGameInfo(
            description = "精美的$typeName：$title，让你的游戏更加精彩",
            displayName = title,
            iconUrl = imageUrl,
            code = code,
            imageUrl = imageUrl,
            gcMode = "UGC",
            packageName = "com.socialplay.asset.$code",
            rating = rating,
            rate = rating / 5.0,
            uid = "creator_${code}",
            nickname = "资源创作者",
            avatar = "https://picsum.photos/50/50?random=${code.hashCode()}",
            type = ChoiceContentType.GAME,
            tagList = listOf(typeName, "热门", "精选"),
            startupExtension = null,
            likeCount = likeCount,
            score = rating.toFloat(),
            playCountTotal = playCount,
            playCount = playCount,
            isNewGame = false,
            playedFriendCount = (playCount * 0.1).toLong(),
            operatingPosition = null,
            ignoreThis = false,
            price = price // 模组有价格，服装免费
        )
    }

    private suspend fun handleRoomData(dataResultList: MutableList<ChoiceCardInfo>) {
        val indexRoomCard = dataResultList.indexOfFirst { cardInfo -> ChoiceCardType.isRoomCardType(cardInfo.cardType) }
        if (indexRoomCard >= 0) {
            val preCardId = dataResultList.getOrNull(indexRoomCard - 1)?.cardId
            cardRoomInfo = dataResultList[indexRoomCard] to preCardId
            val roomList = checkRequestRoomList()
            if (roomList.isNullOrEmpty()) {
                dataResultList.removeAt(indexRoomCard)
            } else {
                dataResultList[indexRoomCard].roomList = roomList
            }
        }
    }

    /**
     * 获取T台配置的要显示在首页的房间对应的游戏
     */
    private suspend fun getTTaiConfigGames(): List<String>? {
        var roomGameJson: String? = metaKV.tTaiKV.homeRoomGame
        val jsonTmp = if (roomGameJson.isNullOrEmpty()) {
            val config = repository.getTTaiConfigById(TTaiKV.ID_HOME_ROOM_GAME).singleOrNull()?.data
            config?.let {
                metaKV.tTaiKV.saveConfig(it)
            }
            config?.value
        } else {
            roomGameJson
        }
        val listGameStyle =
            GsonUtil.gsonSafeParseCollection<List<RoomStyle>>(jsonTmp)
        return listGameStyle?.map { it.gameId }
    }


    private suspend fun checkRequestRoomList(): List<ChatRoomInfo> {
        return if (accountInteractor.checkAccountInit() && MWBiz.isAvailable()) {
            requestRoomList()
        } else {
            needRefreshRoomList = true
            mutableListOf()
        }
    }

    private suspend fun requestRoomListActual(gameIds: List<String>?): List<ChatRoomInfo> {
        val dataResult = repository.getHomeRoomList(
            gameIds,
            1,
            12
        )
        return dataResult.data?.dataList ?: mutableListOf()
    }

    private suspend fun requestRoomList(): List<ChatRoomInfo> {
        val homeRoomGameIds = getTTaiConfigGames()
        return if (homeRoomGameIds.isNullOrEmpty()) {
            mutableListOf()
        } else {
            requestRoomListActual(homeRoomGameIds)
        }
    }

    fun checkRefreshRoomList() {
        Timber.d("checkRefreshRoomList $needRefreshRoomList")
        if (needRefreshRoomList) {
            needRefreshRoomList = false
            refreshRoomList()
        }
    }


    fun refreshRoomList() = viewModelScope.launch(Dispatchers.IO) {
        refreshChatRoomList()
        refreshCottageRoomList()
    }

    /**
     * 酒吧房间
     */
    private suspend fun refreshChatRoomList() {
        val localRoomCardInfo = cardRoomInfo ?: return
        val resultEventList = _cardListLiveData.value ?: return
        val dataResultList = oldDataListOrNull ?: return
        val roomList = requestRoomList()
        val indexCardRoom =
            dataResultList.indexOfFirst { ChoiceCardType.isRoomCardType(it.cardType) }
        if (roomList.isNullOrEmpty()) {
            if (indexCardRoom >= 0) {
                dataResultList.removeAt(indexCardRoom)
                _cardListLiveData.postValue(resultEventList.apply {
                    originalDataList = dataResultList
                })
            }
            localRoomCardInfo.first.roomList = null
        } else {
            localRoomCardInfo.first.roomList = roomList
            if (indexCardRoom < 0) {
                val indexRoomCard =
                    dataResultList.indexOfFirst { it.cardId == localRoomCardInfo.second } + 1
                _cardListLiveData.postValue(
                    ChoiceResultEvent(
                        loadStatus = LoadStatus(status = LoadType.Update),
                        originalDataList = dataResultList.apply {
                            kotlin.runCatching { add(indexRoomCard, localRoomCardInfo.first) }
                        })
                )
            } else {
                _roomListLiveData.postValue(roomList)
            }
        }
    }

    //小屋房间列表
    private suspend fun refreshCottageRoomList() {
        if (!PandoraToggle.openUgcHomeEntrance) return
        val dataResultList = oldDataListOrNull ?: return
        val indexRoomCard = dataResultList.indexOfFirst { cardInfo -> ChoiceCardType.isHomeRoomCardType(cardInfo.cardType) }
        if (indexRoomCard >= 0) {
            repository.getCottageRoomList(1).collect { data ->
                val dataResultList = oldDataListOrNull ?: return@collect
                val indexRoomCard = dataResultList.indexOfFirst { cardInfo -> ChoiceCardType.isHomeRoomCardType(cardInfo.cardType) }
                val roomList = data?.houses
                if (roomList.isNullOrEmpty()) {
                    if (indexRoomCard >= 0) {
                        //移除
                        dataResultList.removeAt(indexRoomCard)
                        _cardListLiveData.postValue(_cardListLiveData.value?.apply {
                            originalDataList = dataResultList
                        })
                    }
                } else {
                    //更新
                    _cottageRoomList.postValue(roomList)
                }
            }
        }
    }

    fun getCottageRoomIndex(): Int {
        val dataResultList = oldDataListOrNull ?: return -1
        return dataResultList.indexOfFirst { cardInfo ->
            ChoiceCardType.isHomeRoomCardType(
                cardInfo.cardType
            )
        }
    }

    fun insertTrending() = viewModelScope.launch {
        val current = _cardListLiveData.value ?: return@launch
        oldDataListOrNull?.let {
            val newList = repository.addTrendingInsertList(it)

            withContext(Dispatchers.Main) {
                _cardListLiveData.value = current.apply {
                    loadStatus = LoadStatus(status = LoadType.Update, message = UPDATE_TYPE_INSERT_TRENDING)
                    originalDataList = newList
                }
            }
        }
    }
}