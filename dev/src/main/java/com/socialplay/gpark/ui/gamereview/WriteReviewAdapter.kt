package com.socialplay.gpark.ui.gamereview

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import com.socialplay.gpark.databinding.ItemWriteReviewsBinding
import com.socialplay.gpark.ui.base.adapter.BaseAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder

/**
 * Created by bo.li
 * Date: 2022/7/14
 * Desc:
 */
class WriteReviewAdapter : BaseAdapter<<PERSON>olean, ItemWriteReviewsBinding>() {

    override fun createViewBinding(layoutInflater: LayoutInflater, parent: ViewGroup, viewType: Int): ItemWriteReviewsBinding {
        return ItemWriteReviewsBinding.inflate(LayoutInflater.from(context), parent, false)
    }

    override fun convert(holder: BindingViewHolder<ItemWriteReviewsBinding>, item: <PERSON><PERSON><PERSON>, position: Int) {
        holder.binding.root.isVisible = true
    }
}