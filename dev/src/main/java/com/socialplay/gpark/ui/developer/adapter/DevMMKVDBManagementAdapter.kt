package com.socialplay.gpark.ui.developer.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import com.socialplay.gpark.databinding.AdapterDeveloperMmkvDbBinding
import com.socialplay.gpark.ui.base.adapter.BaseAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder
import com.socialplay.gpark.ui.developer.bean.MMKVDB

class DevMMKVDBManagementAdapter : BaseAdapter<MMKVDB, AdapterDeveloperMmkvDbBinding>() {

    override fun createViewBinding(
        layoutInflater: LayoutInflater,
        parent: ViewGroup,
        viewType: Int
    ): AdapterDeveloperMmkvDbBinding {
        return AdapterDeveloperMmkvDbBinding.inflate(layoutInflater, parent, false)
    }

    override fun convert(
        holder: BindingViewHolder<AdapterDeveloperMmkvDbBinding>,
        item: MMKVDB,
        position: Int
    ) {
        holder.binding.run {
            tvTitle.text = item.name
        }
    }
}