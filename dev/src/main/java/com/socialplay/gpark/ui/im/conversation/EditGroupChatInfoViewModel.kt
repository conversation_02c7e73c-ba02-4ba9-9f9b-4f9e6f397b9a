package com.socialplay.gpark.ui.im.conversation

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.model.groupchat.GroupChatDetailInfo
import com.socialplay.gpark.data.model.groupchat.MgsGroupChatEditRequest
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import kotlinx.coroutines.launch
import org.koin.android.ext.android.get

data class RemoveMemberResult(
    val groupId: Long,
    val memberId: String,
    val result: DataResult<Boolean>,
)

data class EditGroupChatInfoModelState(
    val editResult: Async<DataResult<GroupChatDetailInfo>> = Uninitialized,
    val removeMemberResult: Async<RemoveMemberResult> = Uninitialized,
) : MavericksState

class EditGroupChatInfoViewModel(
    initialState: EditGroupChatInfoModelState,
    val metaRepository: com.socialplay.gpark.data.IMetaRepository,
) : BaseViewModel<EditGroupChatInfoModelState>(initialState) {

    fun editGroupChatName(groupId: Long, name: String) {
        editGroupChatInfo(
            MgsGroupChatEditRequest(
                chatGroupId = groupId,
                name = name,
            )
        )
    }

    fun editGroupChatDesc(groupId: Long, desc: String) {
        editGroupChatInfo(
            MgsGroupChatEditRequest(
                chatGroupId = groupId,
                describe = desc,
            )
        )
    }

    fun editGroupChatJoinInviteRule(groupId: Long, joinType: Int, inviteType: Int) {
        editGroupChatInfo(
            MgsGroupChatEditRequest(
                chatGroupId = groupId,
                joinType = joinType,
                inviteType = inviteType
            )
        )
    }

    private fun editGroupChatInfo(request: MgsGroupChatEditRequest) {
        if (oldState.editResult is Loading || request.chatGroupId == null) {
            return
        }
        setState {
            copy(
                editResult = Loading()
            )
        }
        viewModelScope.launch {
            val dataResult = metaRepository.editGroupChat(request)
            setState {
                copy(
                    editResult = Success(dataResult)
                )
            }
        }
    }

    fun removeGroupChatMember(chatGroupId: Long, memberId: String) {
        if (oldState.removeMemberResult is Loading) {
            return
        }
        setState {
            copy(
                removeMemberResult = Loading()
            )
        }
        viewModelScope.launch {
            val dataResult = metaRepository.removeGroupChatMember(chatGroupId, memberId)
            setState {
                copy(
                    removeMemberResult = Success(
                        RemoveMemberResult(
                            groupId = chatGroupId,
                            memberId = memberId,
                            result = dataResult
                        )
                    )
                )
            }
        }
    }

    companion object :
        KoinViewModelFactory<EditGroupChatInfoViewModel, EditGroupChatInfoModelState>() {
        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: EditGroupChatInfoModelState
        ): EditGroupChatInfoViewModel {
            return EditGroupChatInfoViewModel(state, get())
        }
    }
}