package com.socialplay.gpark.ui.web.jsinterfaces.model

import com.meta.biz.ugc.model.IPlatformMsg

data class JSTransToData(
    val feature: String,
    val gameId: String,
    val params: Map<String, Any>,
) : IPlatformMsg() {
    override fun useMessageChannel(): Boolean = true

    override fun addJsonData(data: MutableMap<String, Any>) {
        data["feature"] = feature
        data["gameId"] = gameId
        data["params"] = params
    }
}
