package com.socialplay.gpark.ui.fragment

import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.GridLayoutManager
import com.airbnb.mvrx.args
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.choice.ChoiceCardInfo
import com.socialplay.gpark.data.model.choice.ChoiceGameInfo
import com.socialplay.gpark.data.model.template.OutstandingWork
import com.socialplay.gpark.data.model.template.TemplateDetail
import com.socialplay.gpark.databinding.FragmentTemplateListDetailBinding
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.ui.adapter.OutstandingWorkAdapter
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.viewmodel.TemplateDetailState
import com.socialplay.gpark.ui.viewmodel.TemplateDetailViewModel
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import kotlinx.parcelize.Parcelize
import kotlinx.parcelize.RawValue

@Parcelize
data class TemplateListDetailFragmentArgs(
    val templateCard: @RawValue ChoiceCardInfo? = null,
    val templateId: String? = null
) : Parcelable

/**
 * 模板详情二级页面
 */
class TemplateListDetailFragment : BaseFragment<FragmentTemplateListDetailBinding>(R.layout.fragment_template_list_detail) {

    private val viewModel: TemplateDetailViewModel by fragmentViewModel()
    private lateinit var outstandingWorkAdapter: OutstandingWorkAdapter

    private val args by args<TemplateListDetailFragmentArgs>()

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentTemplateListDetailBinding? {
        return FragmentTemplateListDetailBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupViews()
        setupObservers()
        loadData()
    }

    private fun setupViews() {
        // 设置标题栏
        binding.titleBarLayout.setTitle(args.templateCard?.cardName ?: "")
        binding.titleBarLayout.setOnBackClickedListener {
            navigateUp()
        }

        // 设置优秀作品列表为网格布局
        outstandingWorkAdapter = OutstandingWorkAdapter()
        binding.rvOutstandingWorks.apply {
            layoutManager = GridLayoutManager(context, 2)
            adapter = outstandingWorkAdapter
        }

        // 设置创建按钮点击事件
        binding.btnCreate.setOnAntiViolenceClickListener {
            // TODO: 处理创建按钮点击
        }
    }

    private fun setupObservers() {
        viewModel.onEach(TemplateDetailState::templateDetail) { templateDetail ->
            templateDetail?.let {
                bindTemplateDetail(it)
            }
        }

        viewModel.onEach(TemplateDetailState::isLoading) { isLoading ->
            // TODO: 显示或隐藏loading状态
        }

        viewModel.onEach(TemplateDetailState::error) { error ->
            // TODO: 处理错误状态
        }
    }

    private fun bindTemplateDetail(detail: TemplateDetail) {
        // 更新优秀作品列表
        outstandingWorkAdapter.submitList(detail.outstandingWorks)
    }

    private fun loadData() {
        // 如果有从首页传递的数据，转换并使用
        args.templateCard?.let { card ->
            // 获取第一个模板数据（模板本身）
            val templateGame = card.gameList?.firstOrNull()
            templateGame?.let { gameInfo ->
                bindTemplateData(gameInfo)
            }

            // 获取其他作品数据（从第二个开始）
            val worksList = card.gameList?.drop(1)?.map { gameInfo ->
                convertToOutstandingWork(gameInfo)
            } ?: emptyList()

            // 更新优秀作品列表
            outstandingWorkAdapter.submitList(worksList)
        } ?: run {
            // 如果没有传递数据，使用原来的方式从ViewModel加载
            val templateId = args.templateId ?: "1"
            viewModel.loadTemplateDetail(templateId)
        }
    }

    /**
     * 绑定模板数据到UI
     */
    private fun bindTemplateData(templateGame: ChoiceGameInfo) {
        // 设置模板封面
        glide?.run {
            load(templateGame.iconUrl).placeholder(R.drawable.placeholder_corner_12)
                .transform(CenterCrop())
                .into(binding.ivTemplateCover)
        }

        // 设置模板标题
        binding.tvTemplateTitle.text = templateGame.displayName

        // 设置模板标签
        binding.tagContainer.setTags(templateGame.tagList ?: emptyList())

        // 设置模板描述
        binding.tvDescription.text = templateGame.description

        // 设置点赞数
        binding.likeView.setLikeText(UnitUtil.formatKMCount(templateGame.localLikeCount ?: templateGame.likeCount ?: 0))

        // 设置游玩数量
//        val playCount = templateGame.playCount ?: 0
//        if (playCount > 0) {
//            binding.playersView.visibility = View.VISIBLE
//            binding.playersView.setLikeText(getString(R.string.played_x_times, UnitUtil.formatKMCount(playCount)))
//        } else {
        binding.playersView.visibility = View.GONE
//        }

        // 设置难度星级
        val stars = listOf(binding.ivStar1, binding.ivStar2, binding.ivStar3)
        val rating = templateGame.difficulty?.toInt() ?: 3

        for (i in stars.indices) {
            stars[i].visibility = View.VISIBLE
            stars[i].alpha = if (i < rating) 1f else 0.3f
        }
    }

    /**
     * 将ChoiceGameInfo转换为OutstandingWork
     */
    private fun convertToOutstandingWork(gameInfo: ChoiceGameInfo): OutstandingWork {
        return OutstandingWork(
            id = gameInfo.code ?: "",
            title = gameInfo.displayName ?: "",
            coverImageUrl = gameInfo.iconUrl ?: "",
            authorName = gameInfo.nickname ?: "",
            authorAvatarUrl = gameInfo.avatar ?: "",
            likeCount = (gameInfo.likeCount ?: 0).toInt(),
            playCount = (gameInfo.playCount ?: 0).toInt()
        )
    }

    override fun getPageName(): String {
        return PageNameConstants.FRAGMENT_TEMPLATE_LIST_DETAIL
    }
} 