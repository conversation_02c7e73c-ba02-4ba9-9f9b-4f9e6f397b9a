package com.socialplay.gpark.ui.share

import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.share.SharePlatform
import com.socialplay.gpark.databinding.ItemShareBinding
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaModelCollector
import com.socialplay.gpark.util.extension.compoundDrawables
import com.socialplay.gpark.util.extension.unsetOnClick

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/04/10
 *     desc   :
 *
 */
interface IShareListener {
    fun share(item: SharePlatform)
}

data class ShareItem(
    val item: SharePlatform,
    val iShareListener: IShareListener
) : ViewBindingItemModel<ItemShareBinding>(
    R.layout.item_share,
    ItemShareBinding::bind
) {

    override fun ItemShareBinding.onBind() {
        tvShare.setText(item.titleRes)
        tvShare.compoundDrawables(top = item.iconRes)
        root.setOnClickListener { iShareListener.share(item) }
    }

    override fun ItemShareBinding.onUnbind() {
        root.unsetOnClick()
    }
}

fun MetaModelCollector.createShareItem(
    item: SharePlatform,
    listener: IShareListener,
    id: Int
) {
    add {
        ShareItem(
            item,
            listener
        ).id(id)
    }
}