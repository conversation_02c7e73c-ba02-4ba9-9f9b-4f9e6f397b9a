package com.socialplay.gpark.ui.base.adapter

import androidx.lifecycle.Lifecycle
import androidx.lifecycle.coroutineScope
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListUpdateCallback
import androidx.viewbinding.ViewBinding
import com.chad.library.adapter.base.module.LoadMoreModule
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.concurrent.atomic.AtomicInteger

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/06/30
 * desc   :
 * </pre>
 */


abstract class BaseDifferAdapter<T : Any, VB : ViewBinding>(diffCallback: DiffUtil.ItemCallback<T>, data: MutableList<T>? = null) : BasicQuickAdapter<T, VB>(data) {

    private val differ = AsyncPagingDataDiffer(diffCallback, AdapterListUpdateCallback())

    suspend fun submitData(list: MutableList<T>?, isRefresh: Boolean = false, finishCallback: (() -> Unit)? = null) {
        if (hasEmptyView()) {
            setNewInstance(list)
            return
        }
        differ.submitData(list, isRefresh, finishCallback)
    }

    fun submitData(lifecycle: Lifecycle, list: MutableList<T>?, isRefresh: Boolean = false, finishCallback: (() -> Unit)? = null) {
        if (hasEmptyView()) {
            setNewInstance(list)
            return
        }
        differ.submitData(lifecycle, list, isRefresh, finishCallback)
    }

    /**
     * 有空视图且liveData的list引用和adapter的list一致时，用[setNewInstance]会有潜在问题
     * 尝试用[setList]代替
     */
    suspend fun submitDataV2(list: MutableList<T>?, isRefresh: Boolean = false, finishCallback: (() -> Unit)? = null) {
        if (hasEmptyView()) {
            setList(list)
            return
        }
        differ.submitData(list, isRefresh, finishCallback)
    }

    fun submitDataV2(lifecycle: Lifecycle, list: MutableList<T>?, isRefresh: Boolean = false, finishCallback: (() -> Unit)? = null) {
        if (hasEmptyView()) {
            setList(list)
            return
        }
        differ.submitData(lifecycle, list, isRefresh, finishCallback)
    }

    fun resetLoadMore() {
        if (this@BaseDifferAdapter is LoadMoreModule) {
            loadMoreModule.isEnableLoadMore = false
            loadMoreModule.isEnableLoadMore = true
        }
    }

    private inner class AsyncPagingDataDiffer<T : Any>(
        private val diffCallback: DiffUtil.ItemCallback<T>,
        private val updateCallback: ListUpdateCallback,

        ) {
        private val submitDataId = AtomicInteger(0)

        suspend fun submitData(list: MutableList<T>?, isRefresh: Boolean, finishCallback: (() -> Unit)? = null) {
            collectFrom(list, submitDataId.incrementAndGet(), isRefresh, finishCallback)
        }

        fun submitData(lifecycle: Lifecycle, list: MutableList<T>?, isRefresh: Boolean, finishCallback: (() -> Unit)?) {
            val id = submitDataId.incrementAndGet()
            lifecycle.coroutineScope.launch {
                if (submitDataId.get() == id) {
                    collectFrom(list, id, isRefresh, finishCallback)
                }
            }
        }

        private suspend fun collectFrom(newList: MutableList<T>?, submitDataId: Int, isRefresh: Boolean, finishCallback: (() -> Unit)? = null) {
            withContext(Dispatchers.Main) {
                val oldList = data as MutableList<T>
                when {
                    <EMAIL>() != submitDataId -> {
                        Timber.e("collectFrom error submitDataId changed ($submitDataId,${<EMAIL>()})")
                    }
                    newList === oldList                                           -> {
                        Timber.e("collectFrom error newList === oldList")
                    }
                    isRefresh                                                     -> {
                        replaceList(newList)
                        notifyDataSetChanged()
                    }
                    newList.isNullOrEmpty()                                       -> {
                        val oldSize = oldList.size
                        replaceList(newList)
                        updateCallback.onRemoved(0, oldSize)
                    }
                    oldList.isEmpty()                                             -> {
                        replaceList(newList)
                        updateCallback.onInserted(0, newList.size)
                    }
                    else                                                          -> {
                        val result = withContext(Dispatchers.Default) {
                            DiffUtil.calculateDiff(object : DiffUtil.Callback() {
                                override fun getOldListSize() = oldList.size

                                override fun getNewListSize() = newList.size

                                private fun safeGet(list: MutableList<T>?, itemPosition: Int): T? {
                                    if (list.isNullOrEmpty()) return null
                                    if (itemPosition < 0 || itemPosition > list.lastIndex) return null
                                    return list[itemPosition]
                                }

                                override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
                                    val oldItem = safeGet(oldList, oldItemPosition)
                                    val newItem = safeGet(newList, newItemPosition)
                                    return when {
                                        oldItem == null || newItem == null -> false
                                        oldItem === newItem                -> true
                                        else                               -> diffCallback.areItemsTheSame(oldItem, newItem)
                                    }
                                }

                                override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
                                    val oldItem = safeGet(oldList, oldItemPosition)
                                    val newItem = safeGet(newList, newItemPosition)
                                    return when {
                                        oldItem == null || newItem == null -> false
                                        oldItem === newItem                -> true
                                        else                               -> diffCallback.areContentsTheSame(oldItem, newItem)
                                    }
                                }

                                override fun getChangePayload(oldItemPosition: Int, newItemPosition: Int): Any? {
                                    val oldItem = safeGet(oldList, oldItemPosition)
                                    val newItem = safeGet(newList, newItemPosition)
                                    return when {
                                        oldItem == null || newItem == null -> null
                                        oldItem === newItem                -> null
                                        else                               -> diffCallback.getChangePayload(oldItem, newItem)
                                    }
                                }
                            })
                        }
                        if (<EMAIL>() == submitDataId) {
                            replaceList(newList)
                            result.dispatchUpdatesTo(updateCallback)
                        }
                    }
                }
                finishCallback?.invoke()
            }
        }

        private fun replaceList(newList: MutableList<T>?) {
            data = arrayListOf()
            if (!newList.isNullOrEmpty()) {
                (data as MutableList<T>).addAll(newList)
            }
        }
    }

    private inner class AdapterListUpdateCallback : ListUpdateCallback {
        override fun onInserted(position: Int, count: Int) {
            Timber.d("onInserted, position:$position, count:$count")
            notifyItemRangeInserted(position + headerLayoutCount, count)
        }

        override fun onRemoved(position: Int, count: Int) {
            Timber.d("onRemoved, position:$position, count:$count")
            if (this@BaseDifferAdapter is LoadMoreModule && loadMoreModule.hasLoadMoreView() && itemCount == 0) {
                // 如果注册了加载更多，并且当前itemCount为0，则需要加上loadMore所占用的一行
                notifyItemRangeRemoved(position + headerLayoutCount, count + 1)
            } else {
                notifyItemRangeRemoved(position + headerLayoutCount, count)
            }
        }

        override fun onMoved(fromPosition: Int, toPosition: Int) {
            Timber.d("onMoved, fromPosition:$fromPosition, toPosition:$toPosition")
            notifyItemMoved(fromPosition + headerLayoutCount, toPosition + headerLayoutCount)
        }

        override fun onChanged(position: Int, count: Int, payload: Any?) {
            Timber.d("onChanged, position:$position, count:$count, payload:$payload")
            notifyItemRangeChanged(position + headerLayoutCount, count, payload)
        }

    }

}


