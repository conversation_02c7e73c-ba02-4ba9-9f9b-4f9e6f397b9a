package com.socialplay.gpark.ui.profile.link

import android.annotation.SuppressLint
import android.graphics.Rect
import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.View.OnTouchListener
import android.view.ViewGroup
import android.widget.HorizontalScrollView
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.epoxy.EpoxyController
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.mvrx.fragmentViewModel
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.SimpleListData
import com.socialplay.gpark.data.model.account.ProfileLinkInfo
import com.socialplay.gpark.databinding.FragmentEditLinkBinding
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.core.views.spacer
import com.socialplay.gpark.ui.dialog.ListDialog

import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.navigateUp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible
import kotlinx.android.parcel.Parcelize
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/08/16
 *     desc   :
 *
 */
@Parcelize
data class EditLinkFragmentArgs(
    val data: String
) : Parcelable

class EditLinkFragment  : com.socialplay.gpark.ui.core.BaseRecyclerViewFragment<FragmentEditLinkBinding>(R.layout.fragment_edit_link) {
    companion object{
        const val REQ_ADD="link_add_result"
        const val MAX_COUNT = 5
    }
    private val touchListener:OnTouchListener =  OnTouchListener { v, event ->
        val holder: RecyclerView.ViewHolder? = binding.ryLink.findContainingViewHolder(v)
        if (holder != null) {
            val horizontalScrollView: HorizontalScrollView = holder.itemView.findViewById(R.id.horScroll)
            val rect = Rect()
            horizontalScrollView.getHitRect(rect)
            if (rect.contains(event.x.toInt(), event.y.toInt())) {
                // 在 HorizontalScrollView 内，让 HorizontalScrollView 处理触摸事件
                horizontalScrollView.onTouchEvent(event)
                return@OnTouchListener true
            } else {
                // 在 RecyclerView 的其他部分，让 RecyclerView 处理触摸事件
                return@OnTouchListener false
            }
        }
        false
    }


    private val viewModel: EditLinkViewModel by fragmentViewModel()
    override val recyclerView: EpoxyRecyclerView
        get() = binding.ryLink as EpoxyRecyclerView

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentEditLinkBinding? {
        return FragmentEditLinkBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
    }
    private fun initView(){
        binding.iclTitleBar.ibBack.setOnAntiViolenceClickListener { navigateUp() }
        binding.iclTitleBar.tvEdit.setOnAntiViolenceClickListener {
            if (viewModel.oldState.list.isNullOrEmpty()){
                ToastUtil.showShort(
                    getString(
                        R.string.profile_link_add_url_empty_tips
                    )
                )
                return@setOnAntiViolenceClickListener
            }
            if (viewModel.oldState.isEditMode) {
                viewModel.changeEditMode(false)
            } else {
                viewModel.changeEditMode(true)
            }
        }
        binding.tvAdd.setOnAntiViolenceClickListener {
            if (viewModel.oldState.list.size == MAX_COUNT) {
                ToastUtil.showShort(
                    getString(
                        R.string.profile_link_add_url_count,
                        MAX_COUNT.toString()
                    )
                )
                return@setOnAntiViolenceClickListener
            }
            //点击添加按钮退出编辑模式
            viewModel.changeEditMode(false)
            MetaRouter.Account.addLink(this, REQ_ADD) {
                if (it != null) {
                    viewModel.addLink(it)
                    ToastUtil.showShort(R.string.profile_link_add_url_success)
                }
            }
        }
        recyclerView.setOnTouchListener(touchListener)

        viewModel.onEach(EditLinkState::isEditMode) { editMode ->
            //进入编辑模式，列表所有item展示删除按钮
            if (editMode) {
                binding.iclTitleBar.tvEdit.text = getString(R.string.profile_link_edit_exit)
            } else {
                binding.iclTitleBar.tvEdit.text = getString(R.string.profile_link_edit)
            }
        }

    }

    override fun epoxyController(): EpoxyController = simpleController(
        viewModel,
        EditLinkState::list,
        EditLinkState::isEditMode,
        ) { list, isEditMode ->
        list.forEachIndexed { index, item ->
            addLinkItem(
                item.id + index,
                item,
                ScreenUtil.screenWidth - 32.dp,
                isEditMode,
                ::glide
            ) {
                showDeleteDialog(it)
            }
            spacer(height = 12.dp)
        }
        viewLifecycleOwner.lifecycleScope.launch(Dispatchers.Main) {
            if (isVisible && !isStateSaved && !isDetached) {
                if (list.size < MAX_COUNT) {
                    binding.tvAdd.alpha = 1f
                } else {
                    binding.tvAdd.alpha = 0.5F
                }
                binding.viewEmpty.root.visible(list.isNullOrEmpty())
            }
        }

    }

    /**
     * 展示删除本地工程弹窗
     */
    private fun showDeleteDialog(item: ProfileLinkInfo) {
        val confirm = SimpleListData(
            getString(R.string.text_confirm),
            bgResource = R.drawable.bg_common_dialog_confirm,
            textColor = R.color.white
        )
        val cancel = SimpleListData(getString(R.string.dialog_cancel))
        ListDialog()
            .list(mutableListOf(confirm, cancel))
            .content(getString(R.string.profile_link_delete_tip))
            .image(R.drawable.icon_delete)
            .onViewCreateCallback {

            }.clickCallback {
                when (it) {
                    confirm -> {
                        // 直接删除
                        context?.let {
                            viewModel.deleteItem(item)
                        }
                    }

                    else -> {

                    }
                }
            }.show(childFragmentManager, "CreationDeleteDialog")
    }

    override fun onDestroyView() {
        recyclerView.setOnTouchListener(null)
        super.onDestroyView()
    }



    override fun getPageName(): String = PageNameConstants.FRAGMENT_EDIT_LINK

}