package com.socialplay.gpark.ui.profile.recent

import android.view.View
import com.airbnb.epoxy.VisibilityState
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.profile.recent.RecentPlayListV2Response
import com.socialplay.gpark.databinding.ItemProfileTabRecentBinding
import com.socialplay.gpark.ui.core.IBaseEpoxyItemListener
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaModelCollector
import com.socialplay.gpark.util.UnitUtilWrapper
import com.socialplay.gpark.util.extension.context
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setPaddingEx
import com.socialplay.gpark.util.extension.unsetOnClick

interface IProfileTabRecentListener : IBaseEpoxyItemListener {
    fun click(item: RecentPlayListV2Response.Game, position: Int)
    fun show(item: RecentPlayListV2Response.Game, position: Int)
}

fun MetaModelCollector.profileTabRecentItem(
    item: RecentPlayListV2Response.Game,
    position: Int,
    listener: IProfileTabRecentListener
) {
    add(
        ProfileTabRecentItem(
            item,
            position,
            listener
        ).id("ProfileTabRecent-${item.gameId}")
    )
}

data class ProfileTabRecentItem(
    val item: RecentPlayListV2Response.Game,
    val position: Int,
    val listener: IProfileTabRecentListener
) : ViewBindingItemModel<ItemProfileTabRecentBinding>(
    R.layout.item_profile_tab_recent,
    ItemProfileTabRecentBinding::bind
) {

    override fun ItemProfileTabRecentBinding.onBind() {
        if (position == 0) {
            root.setPaddingEx(top = 0)
        } else {
            root.setPaddingEx(top = dp(12))
        }
        listener.getGlideOrNull()?.run {
            load(item.gamePic).placeholder(R.drawable.placeholder_corner_14)
                .transform(CenterCrop(), RoundedCorners(14.dp))
                .into(ivIcon)
        }
        tvGameName.text = item.name
        tvPlayTime.text = UnitUtilWrapper.formatPlayTime(context, item.duration)
        root.setOnAntiViolenceClickListener {
            listener.click(item, position)
        }
    }

    override fun ItemProfileTabRecentBinding.onUnbind() {
        root.unsetOnClick()
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        if (visibilityState == VisibilityState.VISIBLE) {
            listener.show(item, position)
        }
    }
}