package com.socialplay.gpark.ui.editor.home

import android.app.Activity
import android.content.res.Configuration
import android.os.Handler
import android.os.Looper
import android.os.SystemClock
import android.view.View
import android.view.ViewGroup
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import android.view.animation.DecelerateInterpolator
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.OnBackPressedCallback
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.asFlow
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.lifecycle.viewModelScope
import androidx.viewbinding.ViewBinding
import com.bumptech.glide.Glide
import com.meta.biz.ugc.model.GameTransform
import com.meta.lib.mwbiz.MWBizConst
import com.meta.lib.mwbiz.MWBizProxy
import com.meta.lib.mwbiz.bean.game.GameViewParams
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.EditorGameLifecycleInteractor
import com.socialplay.gpark.data.interactor.EditorGameLoadInteractor
import com.socialplay.gpark.data.interactor.EditorInteractor
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor
import com.socialplay.gpark.data.interactor.MVCoreProxyInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.LoginSource
import com.socialplay.gpark.data.model.editor.AvatarLoadingStatus
import com.socialplay.gpark.data.model.feedback.SubmitNewFeedbackRequest
import com.socialplay.gpark.databinding.IncludeRoleBtnsBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.analytics.resid.ResIdUtils
import com.socialplay.gpark.function.editor.EditorGameInteractHelper
import com.socialplay.gpark.function.editor.FakeProgressStatus
import com.socialplay.gpark.function.editor.RoleGameTryOn
import com.socialplay.gpark.function.mw.launch.TSLaunch
import com.socialplay.gpark.function.mw.launch.bean.TSLaunchParams
import com.socialplay.gpark.function.mw.launch.ui.TSLaunchFailedWrapper
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.pay.IPayInteractor
import com.socialplay.gpark.function.pay.PayProvider
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.function.share.screenshot.ScreenshotMonitor
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.ui.editor.AvatarAnalyticsV2
import com.socialplay.gpark.ui.editor.home.popup.AvatarPopupDialog
import com.socialplay.gpark.ui.editor.tab.AvatarLoadingErrorDialog
import com.socialplay.gpark.ui.editor.tab.FullScreenEditorActivityArgs
import com.socialplay.gpark.ui.feedback.FeedbackTypeWrapper
import com.socialplay.gpark.ui.main.MainBottomNavigationItem
import com.socialplay.gpark.ui.main.MainViewModel
import com.socialplay.gpark.util.DateUtil
import com.socialplay.gpark.util.UnitUtilWrapper
import com.socialplay.gpark.util.extension.collectIn
import com.socialplay.gpark.util.extension.collectWithLifecycleOwner
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.isPortrait
import com.socialplay.gpark.util.extension.setMargin
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.distinctUntilChangedBy
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import okhttp3.HttpUrl.Companion.toHttpUrl
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.sharedViewModel
import org.koin.androidx.viewmodel.ext.android.viewModel
import org.koin.core.context.GlobalContext
import timber.log.Timber
import java.util.concurrent.TimeUnit


abstract class BaseEditorHomeFragment<T: ViewBinding> : BaseFragment<T>() {

    override fun getFragmentName() = PageNameConstants.FRAGMENT_NAME_EDITOR_HOME

    protected val viewModel by sharedViewModel<EditorHomeViewModel>()
    protected  val fullAvatarViewModel by viewModel<HomeFullAvatarViewModel>()

    protected val accountInteractor: AccountInteractor by inject()
    private val mvCoreProxyInteractor: MVCoreProxyInteractor by inject()

    private val tsLaunch by lazy { TSLaunch() }
    private val editorInteractor: EditorInteractor by inject()
    private val metaKV by inject<MetaKV>()
    private val h5PageConfig by inject<H5PageConfigInteractor>()
    private val payInteractor: IPayInteractor = GlobalContext.get().get()

    private var avatarLoadSuccessEventJob: Job? = null
    private var enterTime: Long = 0

    private var babyTipsFlag = 0b000

    protected var isEnterAvatarLoadFinish: Boolean = false

    // 记录在当前页面的重试次数（包括自动或手动）
    protected var retryCnt: Int = 0


    protected val mainViewModel by sharedViewModel<MainViewModel>()
    private val WALLETE_SHOW_MESSAGE= 100
    private val handler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: android.os.Message) {
            super.handleMessage(msg)
            when (msg.what) {
                WALLETE_SHOW_MESSAGE -> {
                    updateRechargeTipView()
                }
            }
        }
    }


    private val backPressCallback by lazy {
        object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                val lifecycleInteractor = GlobalContext.get().get<EditorGameLifecycleInteractor>()
                lifecycleInteractor.dispatchNavigateUp {
                    fullAvatarViewModel.exitFullAvatar()
                }
            }
        }
    }

    override fun init() {
        initView()
        initData()
        initTSGame()
    }

    private fun initTSGame() {
        tsLaunch.onLaunchListener(viewLifecycleOwner) {
            onLaunchGameEnd { params, e ->
                TSLaunchFailedWrapper.show(this@BaseEditorHomeFragment, params, e)
            }
        }
    }

    private fun initView() {
        ivFeedBack?.visible(PandoraToggle.enableAvatarFeedback)
        ivFeedBack?.setOnAntiViolenceClickListener {
            val gameId = editorInteractor.gameConfigLiveData.value?.data?.roleViewGameId
            MetaRouter.Feedback.feedback(
                this,
                gameId,
                SubmitNewFeedbackRequest.SOURCE_AVATAR_NUMBER,
                FeedbackTypeWrapper.avatar.id,
                false,
                needBackGame = false,
                fromGameId = null
            )
        }

        includeBtns?.let { includeBtns ->
            val hasBabyBtn = PandoraToggle.openAvatarBaby
            initBtn(includeBtns.tvBaby, includeBtns.shadowBaby, hasBabyBtn) {
                val raiseChildGameId = viewModel.gameConfigFlow.value?.raiseChildGameId ?: return@initBtn
                Analytics.track(
                    EventConstants.C_ENTER_TAB,
                    "gameid" to raiseChildGameId
                )
                handlePgcClick(raiseChildGameId) {
                    if (babyTipsFlag.and(0b010) == 0b010) {
                        metaKV.userStatusKV.firstRaiseChildGame = false
                    }
                    viewModel.dismissBabyTips(false)
                    babyTipsFlag = 0b001
                }
            }

            val hasPlotBtn = PandoraToggle.openPlotHalfHomeEntrance
            initBtn(includeBtns.tvPlot, includeBtns.shadowPlot, hasPlotBtn) {
                Analytics.track(EventConstants.EVENT_VIEW_CLICK_MOMENTS)
                MetaRouter.Plot.list(this, roleCategoryId())
            }

            val hasDesignBtn = PandoraToggle.openUgcClothesEntrance
            initBtn(includeBtns.tvDesign, includeBtns.shadowDesign, hasDesignBtn) {
                Analytics.track(EventConstants.EVENT_AVATAR_DESIGN)
                handleGoFullScreenRole(GameTransform.STATUS_EDIT_CLOTHES)
            }

            initBtn(includeBtns.tvDress, includeBtns.shadowDress, true) {
                onClickDressBtn()
            }

            val hasBuildBtn = !MainBottomNavigationItem.hasCreateTab
            val hasHomeBtn = PandoraToggle.openUgcHomeEntrance
            initBtn(includeBtns.tvBuild, includeBtns.shadowBuild, hasBuildBtn && !hasHomeBtn) {
                MetaRouter.MobileEditor.creation(this)
            }

            initBtn(includeBtns.tvParty, includeBtns.shadowParty, false) {
                val plazaGameId = viewModel.gameConfigFlow.value?.plazaGameId ?: return@initBtn
                handlePgcClick(plazaGameId)
            }
            if (hasHomeBtn) {
                Analytics.track(EventConstants.EVENT_DSHOME_ENTRY_SHOW, mapOf("show_categoryid" to "1"))
            }
            initBtn(includeBtns.tvHome, includeBtns.shadowHome, hasHomeBtn) {
                viewModel.setHomeVisitorCount(0)
                editorInteractor.launchCottageGame(this, accountInteractor.curUuid, "1", CategoryId.COTTAGE_ROOM_LIST, this.getFragmentName())
            }

            val dp4 = 4.dp
            val dp16 = 16.dp
            includeBtns.shadowHome.setMargin(0, 0, dp4, if (hasHomeBtn) 0 else -dp16)
            includeBtns.shadowParty.setMargin(0, 0, dp4, if (hasHomeBtn) -dp16 else 0)
            includeBtns.shadowBuild.setMargin(0, 0, dp4, if (hasBuildBtn && !hasHomeBtn) 0 else -dp16)

            viewModel.fakeProgressStatus.filterNotNull()
                .filter {
                    Timber.d("fakeProgressStatus targetStatus:${it.targetStatus} currentFakeProgressStatus:${it.currentFakeProgressStatus}")
                    it.targetStatus.isPortrait() &&
                            it.currentFakeProgressStatus == FakeProgressStatus.WaitingToDo
                }
                .collectWithLifecycleOwner(
                    viewLifecycleOwner,
                    Lifecycle.State.STARTED
                ) {
                    viewModel.makeFakeProgress(500)
                }

            if (PandoraToggle.ugcCreateEntry) {
                includeBtns.shadowBuild.gone()
            }
        }

        initWalletEntranceView()

        fullAvatarViewModel.fullAvatarStatusFlow
            .map { it.isFullMode }
            .distinctUntilChanged()
            .collectWithLifecycleOwner(viewLifecycleOwner,Lifecycle.State.RESUMED){
                ScreenshotMonitor.inRoleTabFull = it

                backPressCallback.isEnabled = it

                val lifecycleInteractor = GlobalContext.get().get<EditorGameLifecycleInteractor>()
                if (it) {
                    lifecycleInteractor.dispatchEnterEditMode(viewLifecycleOwner, requireActivity())
                } else {
                    lifecycleInteractor.dispatchEnterViewMode(viewLifecycleOwner, requireActivity())
                }
        }

        mvCoreProxyInteractor.engineAvailableFlow.collectIn(viewLifecycleOwner.lifecycleScope){
            if(it && fullAvatarViewModel.fullAvatarStatusFlow.value.isFullMode){
                val lifecycleInteractor = GlobalContext.get().get<EditorGameLifecycleInteractor>()
                lifecycleInteractor.dispatchEnterEditMode(viewLifecycleOwner, requireActivity())
            }
        }

        // 仅在当前页面的时候收到游戏加载成功才处理横竖屏切换
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.RESUMED) {

                fullAvatarViewModel.fullAvatarStatusFlow.combine(viewModel.gameStartedStateFlow) { a, b ->
                    a to b
                }.collect {
                    // 加载成功的情况下才发送横竖屏切换协议
                    if (it.second) {
                        val avatarData = it.first
                        if (avatarData.isFullMode) {
                            val internalStatus = EditorGameInteractHelper.gameInternalTransformStatus.value

                            var needTryOnData = false
                            val targetStatus = if (internalStatus?.status != null &&
                                internalStatus.status != GameTransform.STATUS_ROLE_VIEW
                            ) {
                                internalStatus.status
                            } else if (avatarData.tryOnData != null) {
                                needTryOnData = true
                                if (avatarData.tryOnData.sendWithTransform) {
                                    GameTransform.STATUS_TRY_ON
                                } else {
                                    GameTransform.STATUS_ROLE_EDIT
                                }
                            } else {
                                GameTransform.STATUS_ROLE_EDIT
                            }

                            EditorGameInteractHelper.sendTsGameTransform(
                                targetStatus,
                                false,
                                avatarData.opacityData,
                                customData = getCustomData(targetStatus),
                                tryOnData = if (needTryOnData) {
                                    avatarData.tryOnData
                                } else {
                                    null
                                }
                            )

                        } else {
                            EditorGameInteractHelper.sendTsGameTransform(
                                GameTransform.STATUS_ROLE_VIEW,
                                false,
                                customData = getCustomData(GameTransform.STATUS_ROLE_VIEW)
                            )
                        }
                    }
                }
            }
        }


        // 一进来就先根据状态设置一下全屏View会操作的View的状态，不使用动画
        setFullAvatarRelatedViewStatus(!fullAvatarViewModel.isFullAvatar(), false)

        // TODO 放到fullAvatarViewModel里面去，防止状态丢失
        if (fullAvatarViewModel.isFullAvatar()) {
            mainViewModel.hideTabBar(false)
        } else {
            mainViewModel.showTabBar(false)
        }

        fullAvatarViewModel.fullAvatarStatusFlow
            .map { it.isFullMode }
            .distinctUntilChanged()
            .collectIn(viewModel.viewModelScope) {
                val animDelay = getTabBarAnimationDelay(it)
                if (it) {
                    mainViewModel.hideTabBar(viewModel.isAvatarGameLoaded(), 80, animDelay)
                } else {
                    mainViewModel.showTabBar(viewModel.isAvatarGameLoaded(), 0, animDelay)
                }
            }

        fullAvatarViewModel.fullAvatarStatusEvent
            .map { it.isFullMode }
            .distinctUntilChanged()
            .collectIn(viewLifecycleOwner.lifecycleScope) {
                // 如果没有加载成功则不走动画逻辑
                setFullAvatarRelatedViewStatus(!it, viewModel.isAvatarGameLoaded())
            }

        backPressCallback.isEnabled = fullAvatarViewModel.isFullAvatar()
    }

    protected open fun getTabBarAnimationDelay(isHide:Boolean): Long {
        return if(isHide) 80 else 250
    }

    private fun initBabyTips() {
        includeBtns?.let { includeBtns ->
            if (!PandoraToggle.openAvatarBaby || babyTipsFlag.and(0b001) == 0b001) return
            babyTipsFlag = 0b001
            if (!metaKV.userStatusKV.firstRaiseChildGame) return
            babyTipsFlag = 0b011
            val today = DateUtil.getTodayString()
            if (metaKV.userStatusKV.lastRaiseChildDay == today || !viewModel.canShowBabyTips()) return
            babyTipsFlag = 0b111
            metaKV.userStatusKV.lastRaiseChildDay = today
            includeBtns.llBabyTips.visible()
            val anim = AnimationUtils.loadAnimation(requireContext(), R.anim.popup_in_right_to_left)
            includeBtns.llBabyTips.clearAnimation()
            includeBtns.llBabyTips.startAnimation(anim)
            viewModel.dismissBabyTips(true)
        }
    }

    private fun initWalletEntranceView(){
        rlRechargeEntrance.visible(showWallet && (viewModel.isWalletEntranceEnabled() || PandoraToggle.enableLightUp))
        tvBalance.visible(showWallet && viewModel.isWalletEntranceEnabled())
        tvBalance.setOnAntiViolenceClickListener {
            if (PayProvider.ENABLE_RECHARGE) {
                payInteractor.checkConnect()
                MetaRouter.Pay.goBuyCoinsPage(
                    requireContext(),
                    this,
                    "avatar"
                )
            } else {
                toast(R.string.under_development)
            }
        }
        updateRechargeTipView()
    }
    private fun updateRechargeTipView() {
        if (PayProvider.ENABLE_RECHARGE && showWallet && viewModel.isWalletEntranceEnabled() && !metaKV.appKV.isWalletGuidShow) {
            clRechargeFirstTip.visible()
            metaKV.appKV.isWalletGuidShow = true
            handler.sendEmptyMessageDelayed(WALLETE_SHOW_MESSAGE, 3000)
        } else {
            clRechargeFirstTip.gone()
        }
    }

    private fun initData() {
        viewLifecycleOwner.lifecycleScope.launch {
            // 在每次Resume的时候都能重新接管Surface
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED){
                viewModel.ueViewStatusFlow.collect{
                    Timber.d("ueViewStatusFlow collected:$it")
                    if (it) {
                        installUEView()
                    } else {
                        removeUEView()
                    }
                }
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.RESUMED) {
                viewModel.loadingStatusFlow
                    .distinctUntilChangedBy { it }
                    .collect {
                        Timber.d("Received avatar loading status:$it")
                        when (it) {
                            is AvatarLoadingStatus.Loading -> {
                                dismissAvatarGameLoadFailDialog()
                                fcvLoadingLayout.animate().cancel()
                                fcvLoadingLayout.alpha = 1F
                                fcvLoadingLayout.visible()
                            }

                            is AvatarLoadingStatus.Success -> {
                                AvatarAnalyticsV2.finish(
                                    AvatarAnalyticsV2.FinishReason.Success,
                                    0,
                                    "",
                                    viewModel.isAvatarViewVisible(),
                                    retryCnt
                                )

                                val fcvLoadingLayout = fcvLoadingLayout
                                if (it.isFake) {
                                    fcvLoadingLayout
                                        .animate()
                                        .alpha(0F)
                                        .setInterpolator(DecelerateInterpolator())
                                        .setDuration(400)
                                        .withEndAction {
                                            viewModel.makeFakeProgressFinish()
                                            fcvLoadingLayout.gone()
                                        }
                                        .start()
                                } else {
                                    fcvLoadingLayout.gone()
                                }
                            }

                            is AvatarLoadingStatus.Error -> {
                                Timber.d("Avatar load failed autoLoadFailCnt:${viewModel.autoLoadFailCnt}")
                                if (viewModel.autoLoadFailCnt++ < EditorHomeViewModel.AVATAR_LOAD_FAIL_AUTO_RETRY_MAX_COUNT) {
                                    retryCnt++
                                    if (PandoraToggle.avatarLoadingStrategy.progressiveBackOff) {
                                        // 每次重试在超时1分钟的基础上加一分钟，最多三分钟
                                        // 第一次加载 超时为1分钟
                                        // 第一次重试 超时为2分钟
                                        // 第二次重试 超时为3分钟
                                        // 第三次重试 超时为3分钟
                                        val timeoutInMinutes = (1 + viewModel.autoLoadFailCnt.toLong()).coerceAtMost(3)
                                        viewModel.reload(TimeUnit.MINUTES.toMillis(timeoutInMinutes))
                                    } else {
                                        viewModel.reload()
                                    }
                                } else {
                                    fcvLoadingLayout.visible()
                                    showAvatarGameLoadFailDialog(it.errorCode, it.message)
                                }
                            }
                        }

                        if (editorInteractor.isDebugHideRoleMask) {
                            fcvLoadingLayout.gone()
                        }
                    }
            }
        }

        includeBtns?.let { includeBtns ->
            if (PandoraToggle.openUgcHomeEntrance) {
                viewModel.homeVisitorCountFlow.collectWithLifecycleOwner(viewLifecycleOwner) {
                    includeBtns.llHomeVisitor.visible(!it.isNullOrEmpty())
                    includeBtns.tvHomeVisitorCount.text = (it?.size?:0).toString()
                    includeBtns.vHomeRedDot.visible(!it.isNullOrEmpty())
                    includeBtns.ivHomeVisitor1.visible(true)
                    if (!it.isNullOrEmpty()) {
                        Glide.with(requireContext()).load(it[0].avatar).error(R.drawable.icon_default_avatar)
                            .into(includeBtns.ivHomeVisitor1)
                    }
                    if ((it?.size ?: 0) >= 2) {
                        includeBtns.ivHomeVisitor2.visible(true)
                        Glide.with(requireContext()).load(it?.get(1)?.avatar).error(R.drawable.icon_default_avatar)
                            .into(includeBtns.ivHomeVisitor2)
                    }
                }
            }
            accountInteractor.badgeLiveData.asFlow().collectWithLifecycleOwner(viewLifecycleOwner) {
                includeBtns.vRoleRedDot.visible(it?.dressResource?.hasNew ?: false)
                includeBtns.llRoleUpdate.visible(it?.dressResource?.hasNew ?: false)
            }
        }

        initWalletEntranceData()
        viewModel.dismissBabyTipsLiveData.observe(viewLifecycleOwner) {
            if (it) {
                dismissBabyTips()
            }
        }
        viewModel.hideBabyTipsLiveData.observe(viewLifecycleOwner) {
            if (it) {
                hideBabyTips()
            }
        }

        mainViewModel.tabPendingConsumeDataFlow.collectWithLifecycleOwner(viewLifecycleOwner) {
            if (it.targetTabId == MainBottomNavigationItem.EDITOR_HOME.itemId) {
                val categoryId = it.data.getInt("category_id")
                val opacityData = it.data.getString("data")
                fullAvatarViewModel.enterFullAvatar(opacityData)
                mainViewModel.clearTabPendingConsumeData()
            }
        }

        viewModel.avatarPopupFlow.collectWithLifecycleOwner(viewLifecycleOwner) {
            AvatarPopupDialog.show(this, it)
        }
    }

    private fun dismissBabyTips() {
        includeBtns?.let { includeBtns ->
            if (babyTipsFlag.and(0b100) != 0b100 || includeBtns.llBabyTips.isGone) return
            val anim = AnimationUtils.loadAnimation(requireContext(), R.anim.popup_out_left_to_right)
            anim.setAnimationListener(object : Animation.AnimationListener {
                override fun onAnimationStart(animation: Animation) {}

                override fun onAnimationEnd(animation: Animation) {
                    viewModel.hideBabyTips()
                }

                override fun onAnimationRepeat(animation: Animation) {}
            })
            includeBtns.llBabyTips.clearAnimation()
            includeBtns.llBabyTips.startAnimation(anim)
            babyTipsFlag = 0b011
        }
    }

    private fun hideBabyTips() {
        includeBtns?.let { includeBtns ->
            includeBtns.llBabyTips.clearAnimation()
            includeBtns.llBabyTips.gone()
        }
    }

    private fun initWalletEntranceData(){
        if(showWallet && viewModel.isWalletEntranceEnabled()){
            viewModel.userBalance.collectWithLifecycleOwner(viewLifecycleOwner) {
                tvBalance.text = UnitUtilWrapper.formatCoinCont(it?.leCoinNum ?: 0)
            }
        }


//        //设计说如果加载失败了，用户返回回来的时候自动重新加载
//        mainViewModel.selectedItemLiveData.asFlow()
//            .filterNotNull()
//            .distinctUntilChangedBy { it.itemId }
//            .filter { it.itemId == MainBottomNavigationItem.EDITOR_HOME.itemId }
//            .collectWithLifecycleOwner(viewLifecycleOwner){
//                // 用户切换到当前页面后则重置加载失败次数
//                viewModel.autoLoadFailCnt = 0
//
//                if (viewModel.loadingStatusFlow.value is AvatarLoadingStatus.Error) {
//                    AvatarLoadingErrorDialog.dismiss(childFragmentManager)
//                    viewModel.reload()
//                }
//            }
    }

    private fun dismissAvatarGameLoadFailDialog() {
        if (AvatarLoadingErrorDialog.isShowing(childFragmentManager)) {
            AvatarLoadingErrorDialog.dismiss(childFragmentManager)
        }
    }

    private fun showAvatarGameLoadFailDialog(errorCode: Int, message: String) {
        if (!AvatarLoadingErrorDialog.isShowing(childFragmentManager)) {
            val showLoginTip = PandoraToggle.avatarLoadingStrategy.loginGuide &&
                    errorCode == EditorGameLoadInteractor.ERR_CODE_GET_USER_INFO_FAILED

            AvatarLoadingErrorDialog.show(
                requireContext(),
                fragmentManager = childFragmentManager,
                title = getString(R.string.failed_to_load),
                content = if (showLoginTip) getString(R.string.editor_role_game_failed_to_get_user_info_login_tip) else message,
                confirmText = if (showLoginTip) getString(R.string.editor_role_game_to_login) else null,
                errorCode,
                lifecycleOwner = this
            ) {
                if (it == AvatarLoadingErrorDialog.CONFIRM) {
                    if(showLoginTip){
                        MetaRouter.Login.login(this, LoginSource.AvatarLogin)
                    } else {
                        retryCnt++
                        viewModel.reload()
                    }
                } else if (it == AvatarLoadingErrorDialog.LEAVE) {
                    // 选择了离开则退出全屏角色,如果当前再全屏角色状态
                    if (fullAvatarViewModel.isFullAvatar()) {
                        fullAvatarViewModel.exitFullAvatar()
                    }
                }
            }
        }
    }

    abstract fun setFullAvatarRelatedViewStatus(viewMode: Boolean, isAnim: Boolean)

    private fun installUEView() {

        val ueView = MWBizProxy.initializeUEView(
            requireActivity(), MWBizConst.GVT.SURFACE, GameViewParams(
                IsPortrait = isPortrait(),
                InterceptEvents = true,
                LifecycleController = viewLifecycleOwner
            )
        )

        flAvatarContainer.removeAllViews()

        flAvatarContainer.addView(
            ueView, ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
        )
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        Timber.d("onConfigurationChanged isBindingAvailable:${isBindingAvailable()} newConfig:${newConfig}")
        if (isBindingAvailable()) {
            removeUEView()
            installUEView()
        }
    }

    private fun removeUEView() {
        flAvatarContainer.removeAllViews()
    }

    private fun handleGoFullScreenRole(status: String) {
        Analytics.track(EventConstants.EVENT_GAME_AVATAR_LAUNCH) {
            put("from", "editor")
            putAll(ResIdUtils.getAnalyticsMap(ResIdBean().setCategoryID(roleCategoryId())))
        }
        MetaRouter.MobileEditor.fullScreenRole(
            requireActivity(),
            FullScreenEditorActivityArgs(status = status, categoryId = roleCategoryId())
        )
    }

    private fun handlePgcClick(gameId: String, onSuccess: () -> Unit = {}) {
        viewLifecycleOwner.lifecycleScope.launch {
            val info = viewModel.featGameInfoFromCache(gameId)
            if (info != null) {
                val resIdBean = ResIdBean.newInstance().setCategoryID(roleCategoryId())
                if (tsLaunch.isLaunching(info.id)) {
                    toast("Launching")
                } else {
                    metaKV.analytic.saveClickLaunchTime(info.packageName, System.currentTimeMillis())
                    val params = TSLaunchParams(info, resIdBean)
                    tsLaunch.launch(requireContext(), params)
                    viewModel.setHomeVisitorCount(0)
                    onSuccess()
                }
            } else {
                toast(R.string.loading_failed_click_to_retry)
            }
        }
    }

    protected fun initBtn(clickBtn: View, visibleBtn: View, hasBtn: Boolean, btn2Listener: (View) -> Unit) {
        visibleBtn.isVisible = hasBtn
        if (hasBtn) {
            clickBtn.setOnAntiViolenceClickListener(btn2Listener)
        }
    }

    override fun onResume() {
        super.onResume()

        EditorGameInteractHelper.saveRoleResIdBean(
            roleCategoryId(),
            EditorGameInteractHelper.getRoleGameId()
        )

        if (!fullAvatarViewModel.isFullAvatar()) {
            EditorGameInteractHelper.sendTsGameTransform(
                GameTransform.STATUS_ROLE_VIEW,
                false,
                customData = getCustomData(GameTransform.STATUS_ROLE_VIEW)
            )
        }

        this.isEnterAvatarLoadFinish = viewModel.loadingStatusFlow.value is AvatarLoadingStatus.Success
        retryCnt = 0

        val avatarGameLoaded = viewModel.isAvatarGameLoaded()
        val isReceivedUserLoadEventThisLoadCycle = viewModel.isReceivedUserLoadEventThisLoadCycle()


        Analytics.track(EventConstants.C_EVENT_SHOW_AVATARTAB) {
            put("state", "${if (avatarGameLoaded) 1 else 0}")
            put("userload", "${if (isReceivedUserLoadEventThisLoadCycle) 1 else 0}")
        }

        enterTime = SystemClock.elapsedRealtime()

        if (!avatarGameLoaded) {
            avatarLoadSuccessEventJob = viewModel.loadingStatusEvent
                .filter { (it is AvatarLoadingStatus.Success && !it.isFake) || (it is AvatarLoadingStatus.Error && !it.isFake) }
                .collectWithLifecycleOwner(viewLifecycleOwner, Lifecycle.State.RESUMED) {
                    val isLoadSuccess = it is AvatarLoadingStatus.Success
                    Timber.d("Collected avatar load finish isLoadSuccess:${isLoadSuccess}")

                    val errorCode = if(it is AvatarLoadingStatus.Error) it.errorCode else 0
                    val subErrorCode = if(it is AvatarLoadingStatus.Error) it.subErrorCode else ""

                    Analytics.track(EventConstants.C_EVENT_SHOW_AVATARTAB_RESULT) {
                        put("playtime", "${SystemClock.elapsedRealtime() - enterTime}")
                        put("state", "${if (isLoadSuccess) 1 else 0}")
                        put("errorcode", errorCode)
                        put("sub_errorcode", subErrorCode)
                        put("userload", if (viewModel.isReceivedUserLoadEventThisLoadCycle()) 1 else 0)
                    }

                    avatarLoadSuccessEventJob?.let {
                        if(!it.isCancelled){
                            it.cancel()
                        }
                        avatarLoadSuccessEventJob = null
                    }
                }
        }

        // 放到Resume这里来注册，用默认的自动注册/解注册会在OnStart操作，但是Fragment的OnStart会比MainActivity的早
        // 会导致MainActivity注册在后，这里无法拦截到back事件
        requireActivity().onBackPressedDispatcher.addCallback(backPressCallback)

        // 进页面的时候，如果已经是加载失败，切重试次数耗尽，则直接显示加载失败弹窗
        val (isLoadFail, errorCode, message) = viewModel.getAvatarGameLoadFailMessage()
        if (isLoadFail && viewModel.autoLoadFailCnt >= EditorHomeViewModel.AVATAR_LOAD_FAIL_AUTO_RETRY_MAX_COUNT) {
            showAvatarGameLoadFailDialog(errorCode, message)
        }

        viewModel.notifyAvatarViewVisibilityChanged(true)

        initBabyTips()

        val isPreloadCycle = viewModel.isPreloadCycleState()


        AvatarAnalyticsV2.start(
            isPreloadCycle,
            "1",
            isReceivedUserLoadEventThisLoadCycle,
            false,
            null,
            !viewModel.isEngineValid()
        )

        if (isReceivedUserLoadEventThisLoadCycle) {
            AvatarAnalyticsV2.end(AvatarAnalyticsV2.EndReason.Success, 0, "", true)
        }

        viewModel.checkShowAvatarPopupDialog()
    }

    override fun onPause() {
        super.onPause()
        ScreenshotMonitor.inRoleTabFull = false
        backPressCallback.remove()
        viewModel.notifyAvatarViewVisibilityChanged(false)

        avatarLoadSuccessEventJob?.let {
            if(!it.isCancelled){
                it.cancel()
            }
            avatarLoadSuccessEventJob = null
        }

        val avatarGameLoaded = viewModel.isAvatarGameLoaded()
        val stayTime = if (enterTime <= 0) 0 else SystemClock.elapsedRealtime() - enterTime

        Analytics.track(EventConstants.C_EVENT_END_AVATARTAB) {
            put("state", "${if (avatarGameLoaded) 1 else 0}")
            put("playtime", "$stayTime")
            put("userload", if (viewModel.isReceivedUserLoadEventThisLoadCycle()) 1 else 0)
        }


        val avatarLoadingStatus = viewModel.loadingStatusFlow.value

        if(avatarLoadingStatus is AvatarLoadingStatus.Loading){
            AvatarAnalyticsV2.end(AvatarAnalyticsV2.EndReason.Leave, 0, "", true)
        }

        if (avatarLoadingStatus is AvatarLoadingStatus.Error) {
            AvatarAnalyticsV2.finish(
                AvatarAnalyticsV2.FinishReason.Fail,
                0,
                "",
                true,
                retryCnt
            )
        } else {
            AvatarAnalyticsV2.finish(
                AvatarAnalyticsV2.FinishReason.Leave,
                0,
                "",
                true,
                retryCnt
            )
        }

    }

    override fun onDestroyView() {
        Timber.d("setHomeVisitorCount_onDestroyView  ")
        viewModel.setHomeVisitorCount(0)
        handler.removeCallbacksAndMessages(null)
        viewModel.hideBabyTips()
        super.onDestroyView()
    }

    override fun loadFirstData() {
        viewModel.load(roleCategoryId())
    }

    protected fun onClickDressBtn(needTrack: Boolean = true, tryOnData: RoleGameTryOn? = null) {
        if (needTrack) {
            Analytics.track(EventConstants.EVENT_VIEW_CLICK_DRESS)
            Analytics.track(EventConstants.NBLAND_AVATAR_CLICK)
        }

        // 如果再失败的情况下点击进入编辑状态，则显示错误弹窗
        val (isLoadFailed, errorCode, errorMessage) = viewModel.getAvatarGameLoadFailMessage()
        if (isLoadFailed) {
            showAvatarGameLoadFailDialog(errorCode, errorMessage)
        } else {
            if (needTrack) {
                viewModel.updateRoleUpdateRecord()
            }
            fullAvatarViewModel.enterFullAvatar(fromDress = true, tryOnData = tryOnData)
        }
    }

    abstract val ivFeedBack: ImageView?
    abstract val flAvatarContainer: FrameLayout
    abstract val includeBtns: IncludeRoleBtnsBinding?
    abstract val fcvLoadingLayout: ViewGroup
    abstract val tvBalance: TextView
    abstract val rlRechargeEntrance: ViewGroup
    abstract val clRechargeFirstTip: ViewGroup

    protected open val showWallet: Boolean = true
    protected open fun getCustomData(status: String): String? = null
    protected open fun roleCategoryId(): Int = CategoryId.MOBILE_EDITOR_MAIN
}