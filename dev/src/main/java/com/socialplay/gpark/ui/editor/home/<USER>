package com.google.android.material.appbar

import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.ViewConfiguration
import androidx.coordinatorlayout.widget.CoordinatorLayout
import com.socialplay.gpark.R
import com.socialplay.gpark.util.ViewGroupUtils
import timber.log.Timber

class NoDraggableAppBarBehavior(context: Context, attrs: AttributeSet?) :
    CustomAppBarBehavior(context, attrs) {

    private var isBeggingDragging = false

    private var collapsingDownX: Int = -1
    private var collapsingDownY: Int = -1

    private val scaledTouchSlop = ViewConfiguration.get(context).scaledTouchSlop

    private fun isTouchedInScrollableView(
        parent: CoordinatorLayout,
        child: AppBarLayout, ev: MotionEvent
    ): Boolean {

        Timber.d("isLifted ${child.isLifted()}")

        when (ev.actionMasked) {
            MotionEvent.ACTION_DOWN -> {
                val handleView:View? = child.findViewById(R.id.scollView)

                val rect = Rect()

                var isTouchedInHandleView = false

                if (handleView != null) {
                    ViewGroupUtils.getDescendantRect(parent, handleView, rect)
                    isTouchedInHandleView = rect.contains(ev.x.toInt(), ev.y.toInt())
                }

                isBeggingDragging = isTouchedInHandleView
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                isBeggingDragging = false
            }
        }

        return isBeggingDragging
    }

    override fun onInterceptTouchEvent(
        parent: CoordinatorLayout,
        child: AppBarLayout,
        ev: MotionEvent
    ): Boolean {
        if (isTouchedInScrollableView(parent, child, ev)) {
            return super.onInterceptTouchEvent(parent, child, ev)
        }
        return shouldInterceptForLiftClick(parent, child, ev)
    }

    override fun onTouchEvent(
        parent: CoordinatorLayout,
        child: AppBarLayout,
        ev: MotionEvent
    ): Boolean {
        if (isTouchedInScrollableView(parent, child, ev)) {
            return super.onTouchEvent(parent, child, ev)
        }
        return shouldInterceptForLiftClick(parent, child, ev)
    }



    private fun shouldInterceptForLiftClick(
        parent: CoordinatorLayout,
        child: AppBarLayout,
        ev: MotionEvent
    ): Boolean {
        if (child.isLifted) {
            val rect = Rect()
            ViewGroupUtils.getDescendantRect(parent, child, rect)

            val x = ev.x.toInt()
            val y = ev.y.toInt()

            if ((rect.contains(x, y))) {
                when (ev.actionMasked) {
                    MotionEvent.ACTION_DOWN -> {
                        collapsingDownX = x
                        collapsingDownY = y
                        return true
                    }
                    MotionEvent.ACTION_UP -> {
                        if(collapsingDownX != 0 && collapsingDownY != 0){
                            child.postOnAnimation { child.setExpanded(true) }
                            return true
                        }
                    }

                    MotionEvent.ACTION_CANCEL -> {
                        collapsingDownX = 0
                        collapsingDownY = 0
                    }

                    MotionEvent.ACTION_MOVE -> {
                        if (Math.abs(x - collapsingDownX) <= scaledTouchSlop && Math.abs(y - collapsingDownY) <= scaledTouchSlop) {
                            return true
                        } else {
                            collapsingDownX = 0
                            collapsingDownY = 0
                        }
                    }
                }
            }
        }
        // 防止用户快速双击，Expand状态未完成导致的穿透
        return child.pendingAction != AppBarLayout.PENDING_ACTION_NONE || isOffsetAnimatorRunning
    }
}