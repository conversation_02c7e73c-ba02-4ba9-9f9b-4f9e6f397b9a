package com.socialplay.gpark.ui.videofeed.recommend

import android.app.Application
import android.content.ComponentCallbacks
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.model.videofeed.RecommendVideoFeedArgs
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import org.koin.android.ext.android.get

data class RecommendVideoFeedViewModelState(
    val args: RecommendVideoFeedArgs,
) : MavericksState

class RecommendVideoFeedViewModel(
    private val app: Application,
    private val initialState: RecommendVideoFeedViewModelState,
) : BaseViewModel<RecommendVideoFeedViewModelState>(initialState) {

    companion object :
        KoinViewModelFactory<RecommendVideoFeedViewModel, RecommendVideoFeedViewModelState>() {

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: RecommendVideoFeedViewModelState
        ): RecommendVideoFeedViewModel {
            return RecommendVideoFeedViewModel(get(), state)
        }
    }

}