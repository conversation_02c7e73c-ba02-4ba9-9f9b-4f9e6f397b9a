package com.socialplay.gpark.ui.cottage.detail

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.meta.biz.mgs.data.model.MgsBriefRoomInfo
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.GameDetailInfo
import com.socialplay.gpark.data.model.room.CottageRoomInfo
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.mgs.MgsGameRoomLauncher
import com.socialplay.gpark.ui.cottage.CottageAllRoomFragment
import com.socialplay.gpark.util.SingleLiveData
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.launch
import org.koin.core.context.GlobalContext

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2023/10/10
 *     desc   :
 *
 */
class CottageRoomDetailViewModel(
    val repository: IMetaRepository,
    val accountInteractor: AccountInteractor,
) : ViewModel(){
    private val _roomInfoLieData = MutableLiveData<CottageRoomInfo?>()
    val roomInfoLieData = _roomInfoLieData
    private val _pkgLiveData = SingleLiveData<String?>()
    val pkgLiveData= _pkgLiveData
    fun getRoomInfo(roomId: String) = viewModelScope.launch {
        repository.getCottageRoomInfo(roomId).collect{
            _roomInfoLieData.value = it
        }
    }
    fun getGamePkg(gameId :String) = viewModelScope.launch {
       repository.fetchGameInfoByIdFromRemoteWithCache(gameId).collect{
           _pkgLiveData.setValue(it.data?.packageName)
       }
    }


}