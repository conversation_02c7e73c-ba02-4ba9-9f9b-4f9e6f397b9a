package com.socialplay.gpark.ui.room

import android.content.Context
import android.graphics.Rect
import android.text.TextPaint
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.room.ChatRoomInfo
import com.socialplay.gpark.databinding.AdapterItemRoomBinding
import com.socialplay.gpark.function.cdnview.CacheCdnImageTask
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.visible
import org.koin.core.context.GlobalContext

/**
 * Created by bo.li
 * Date: 2024/4/9
 * Desc: 抽取语音房卡片的公共部分
 */
object RoomCardUtil {

    private val textPaint by lazy {
        val textPaint = TextPaint()
        textPaint.textSize = 16.dp.toFloat()
        textPaint
    }

    fun getRoomCardWidth(): Int {
        val context = GlobalContext.get().get<Context>()
        return if (ScreenUtil.isPad(context)) {
            context.dp(164)
        } else {
            ((ScreenUtil.getScreenWidth(context) - context.dp(12) - context.dp(10) - context.dp(10)) / 2.1).toInt()
        }
    }

    fun setImageBg(glide: RequestManager?, binding: AdapterItemRoomBinding, item: ChatRoomInfo) {
        glide?.run {
            if (item.image.isNullOrEmpty()) {
                load(CacheCdnImageTask.CDN_ROOM_ITEM_BG).into(binding.ivRoomItemBg)
            } else {
                load(item.image).error(load(CacheCdnImageTask.CDN_ROOM_ITEM_BG))
                    .into(binding.ivRoomItemBg)
            }
        }
    }

    /**
     */
    fun setRoomName(
        matchParent: Boolean,
        cardWidth: Int,
        context: Context,
        binding: AdapterItemRoomBinding,
        item: ChatRoomInfo
    ) {
        val heightRoomName: Int = cardWidth * 164 / 190 - 102.dp
        val roomName =
            if (item.roomName.isNullOrEmpty()) context.getString(R.string.default_room_name) else item.roomName
        if (matchParent) {
            binding.tvRoomName.maxLines = 3
        } else {
            val rect = Rect()
            textPaint.getTextBounds(roomName, 0, roomName.length, rect)
            val maxLines = if (rect.height() == 0) 2 else heightRoomName / rect.height()
            binding.tvRoomName.maxLines = maxLines.coerceAtLeast(2)
        }
        binding.tvRoomName.text = roomName
    }

    fun setRoomTag(context: Context, binding: AdapterItemRoomBinding, item: ChatRoomInfo) {
        binding.tvRoomTag.text =
            if (item.tag.isNullOrEmpty()) context.getString(R.string.default_room_tag) else item.tag
    }

    fun setRoomStyle(binding: AdapterItemRoomBinding, item: ChatRoomInfo) {
        binding.tvRoomStyle.text = item.getRoomStyle()
    }

    fun setRoomPlayerCount(
        context: Context,
        binding: AdapterItemRoomBinding,
        item: ChatRoomInfo
    ) {
        binding.tvPlayerCount.text =
            context.getString(R.string.x_player, UnitUtil.formatKMCount(item.number.toLong()))
    }

    fun setRoomMember(
        glide: RequestManager?,
        binding: AdapterItemRoomBinding,
        item: ChatRoomInfo
    ) {
        val memberList = item.members?.take(3)
        val memberFirst = memberList?.getOrNull(0)
        val memberSecond = memberList?.getOrNull(1)
        val memberThird = memberList?.getOrNull(2)
        if (memberFirst == null) {
            binding.roomMember1.gone()
        } else {
            binding.roomMember1.visible()
            glide?.run {
                load(memberFirst.avatar).placeholder(R.drawable.icon_default_avatar)
                    .into(binding.roomMember1)
            }
        }
        if (memberSecond == null) {
            binding.roomMember2.gone()
        } else {
            binding.roomMember2.visible()
            glide?.run {
                load(memberSecond.avatar).placeholder(R.drawable.icon_default_avatar)
                    .into(binding.roomMember2)
            }
        }
        if (memberThird == null) {
            binding.roomMember3.gone()
        } else {
            binding.roomMember3.visible()
            glide?.run {
                load(memberThird.avatar).placeholder(R.drawable.icon_default_avatar)
                    .into(binding.roomMember3)
            }
        }
    }
}