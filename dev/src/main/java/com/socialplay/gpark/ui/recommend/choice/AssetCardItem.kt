package com.socialplay.gpark.ui.recommend.choice

import android.text.TextUtils
import android.view.Gravity
import android.view.View
import com.airbnb.epoxy.Carousel
import com.airbnb.epoxy.VisibilityState
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.choice.ChoiceCardInfo
import com.socialplay.gpark.data.model.choice.ChoiceGameInfo
import com.socialplay.gpark.databinding.AdapterChoiceCardItemBuyBinding
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaModelCollector
import com.socialplay.gpark.ui.core.views.carouselNoSnapWrapBuilder
import com.socialplay.gpark.ui.core.views.textItem
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.context
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setMargin
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener

fun MetaModelCollector.assetCardList(
    card: ChoiceCardInfo,
    cardPosition: Int,
    spanSize: Int,
    listener: IChoiceListener
) {
    val games = card.gameList ?: return
    // 先添加标题和更多按钮
    add(
        ChoiceTitleMoreItem(card, spanSize, listener)
            .id("AssetCardTitle-$cardPosition")
            .spanSizeOverride { _, _, _ -> spanSize }
    )
    carouselNoSnapWrapBuilder {
        id("AssetCardList-$cardPosition")
        padding(Carousel.Padding.dp(8, 0, 8, 0, 0))
        hasFixedSize(true)
        initialPrefetchItemCount(3)
        onVisibilityStateChanged { _, _, visibilityState ->
            if (visibilityState == VisibilityState.VISIBLE) {
                listener.onCardShow(cardPosition, card)
            }
        }
        spanSizeOverride { _, _, _ ->
            spanSize
        }
        games.forEachIndexed { index, game ->
            add(
                BuyCardItem(
                    item = game,
                    position = index,
                    card = card,
                    cardPosition = cardPosition,
                    spanSize = spanSize,
                    listener = listener
                ).id("BuyCardItem-$cardPosition-$index-${game.code}")
            )
        }
    }
}

data class BuyCardItem(
    val item: ChoiceGameInfo,
    val position: Int,
    val card: ChoiceCardInfo,
    val cardPosition: Int,
    val spanSize: Int,
    val listener: IChoiceListener
) : ViewBindingItemModel<AdapterChoiceCardItemBuyBinding>(
    R.layout.adapter_choice_card_item_buy,
    AdapterChoiceCardItemBuyBinding::bind
) {
    override fun AdapterChoiceCardItemBuyBinding.onBind() {
//        root.setMargin(right = dp(10))
        if (ScreenUtil.isPad(context)) {
            val layoutParams = root.layoutParams
            layoutParams.width = ScreenUtil.dp2px(context, 212f)
            root.layoutParams = layoutParams
        } else {
            val layoutParams = root.layoutParams
            layoutParams.width = ScreenUtil.dp2px(context, 212f)
            root.layoutParams = layoutParams
        }

        // 顶部图片
        listener.getGlideOrNull()?.run {
            load(item.iconUrl).placeholder(R.drawable.placeholder_corner_12)
                .transform(CenterCrop())
                .into(ivGameIcon)
        }
        // 点赞
        likeView.setLikeText(UnitUtil.formatKMCount(item.likeCount ?: 0))
        // 名称
        tvGameTitle.text = item.displayName
        // 标签
        tagContainer.setTags(item.tagList ?: emptyList())
        // 价格
        tvPrice.text = "${item.price ?: 0L}"
        // 金币icon
        ivCoin.setImageResource(R.drawable.icon_item_buy_coins_g)
        // 购买按钮
        btnBuy.setOnAntiViolenceClickListener { listener.onMoreClick(card) }
        // 整卡点击
        root.setOnAntiViolenceClickListener { listener.onItemClick(cardPosition, card, position, item, false) }
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        if (visibilityState == VisibilityState.VISIBLE) {
            listener.onItemShow(cardPosition, card, position, item, false)
        }
    }
} 