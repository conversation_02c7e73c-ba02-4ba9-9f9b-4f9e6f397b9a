package com.socialplay.gpark.ui.view

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View
import com.socialplay.gpark.R
import kotlin.math.*

/**
 * 简化版金属边框View，专注于解决高光显示问题
 */
class SimpleMetallicBorderView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val glowPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val borderRect = RectF()
    private val innerRect = RectF()

    private var borderThickness: Float = 8f
    private var lightAngle: Float = 45f
    private var cornerRadius: Float = 0f
    private var highlightIntensity: Float = 1.0f
    private var shadowIntensity: Float = 0.8f
    private var glowIntensity: Float = 0.5f
    private var glowColor: Int = Color.parseColor("#0080FF")
    
    init {
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.MetallicBorderView)
        borderThickness = typedArray.getDimension(R.styleable.MetallicBorderView_borderThickness, 8f)
        lightAngle = typedArray.getFloat(R.styleable.MetallicBorderView_lightAngle, 45f)
        cornerRadius = typedArray.getDimension(R.styleable.MetallicBorderView_cornerRadius, 0f)
        highlightIntensity = typedArray.getFloat(R.styleable.MetallicBorderView_highlightIntensity, 1.0f)
        shadowIntensity = typedArray.getFloat(R.styleable.MetallicBorderView_shadowIntensity, 0.8f)
        typedArray.recycle()
    }
    
    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        updateRects()
    }
    
    private fun updateRects() {
        val width = width.toFloat()
        val height = height.toFloat()

        if (width <= 0 || height <= 0) return

        // 为外发光预留空间
        val glowRadius = borderThickness * 2f
        val glowPadding = glowRadius

        borderRect.set(glowPadding, glowPadding, width - glowPadding, height - glowPadding)
        innerRect.set(
            glowPadding + borderThickness,
            glowPadding + borderThickness,
            width - glowPadding - borderThickness,
            height - glowPadding - borderThickness
        )
    }
    
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        if (width <= 0 || height <= 0) return

        val width = width.toFloat()
        val height = height.toFloat()

        // 使用离屏缓冲区
        val layerId = canvas.saveLayer(0f, 0f, width, height, null)

        // 1. 绘制外发光（如果强度大于0且边框厚度大于0）
        if (glowIntensity > 0f && borderThickness > 0f) {
            drawOuterGlow(canvas, width, height)
        }

        // 2. 计算光照方向和渐变
        val angleRad = Math.toRadians(lightAngle.toDouble())
        val lightDx = cos(angleRad).toFloat()
        val lightDy = sin(angleRad).toFloat()

        val centerX = width / 2
        val centerY = height / 2
        val diagonal = sqrt(width * width + height * height)

        val startX = centerX + lightDx * diagonal / 3
        val startY = centerY + lightDy * diagonal / 3
        val endX = centerX - lightDx * diagonal / 3
        val endY = centerY - lightDy * diagonal / 3

        val shadowColor = Color.argb((255 * shadowIntensity).toInt(), 80, 80, 80)
        val baseColor = Color.parseColor("#C0C0C0")
        val highlightColor = Color.argb((255 * highlightIntensity).toInt(), 255, 255, 255)

        val gradient = LinearGradient(
            startX, startY, endX, endY,
            intArrayOf(shadowColor, baseColor, highlightColor, baseColor, shadowColor),
            floatArrayOf(0f, 0.3f, 0.5f, 0.7f, 1f),
            Shader.TileMode.CLAMP
        )

        paint.shader = gradient
        paint.style = Paint.Style.FILL

        // 3. 绘制边框
        canvas.drawRoundRect(borderRect, cornerRadius, cornerRadius, paint)

        // 4. 清除内部区域
        paint.shader = null
        paint.color = Color.BLACK
        paint.xfermode = PorterDuffXfermode(PorterDuff.Mode.SRC_OUT)

        val innerCornerRadius = maxOf(0f, cornerRadius - borderThickness)
        canvas.drawRoundRect(innerRect, innerCornerRadius, innerCornerRadius, paint)

        paint.xfermode = null
        canvas.restoreToCount(layerId)
    }

    private fun drawOuterGlow(canvas: Canvas, width: Float, height: Float) {
        val glowRadius = borderThickness * 2f
        val glowAlpha = (255 * glowIntensity).toInt()

        // 计算边框的实际位置（考虑发光预留空间）
        val glowPadding = glowRadius

        // 创建多层发光效果
        for (i in 1..5) {
            val layerRadius = glowRadius * i / 5f
            val layerAlpha = (glowAlpha * (6 - i) / 5f).toInt()

            if (layerAlpha <= 0) continue

            val layerColor = Color.argb(
                layerAlpha,
                Color.red(glowColor),
                Color.green(glowColor),
                Color.blue(glowColor)
            )

            val glowPaintLayer = Paint(Paint.ANTI_ALIAS_FLAG).apply {
                color = layerColor
                style = Paint.Style.STROKE
                strokeWidth = layerRadius / 2f
                maskFilter = BlurMaskFilter(layerRadius / 3f, BlurMaskFilter.Blur.NORMAL)
            }

            // 绘制发光边框，从边框位置向外扩散
            val glowRect = RectF(
                glowPadding - layerRadius / 4f,
                glowPadding - layerRadius / 4f,
                width - glowPadding + layerRadius / 4f,
                height - glowPadding + layerRadius / 4f
            )

            canvas.drawRoundRect(glowRect, cornerRadius + layerRadius / 4f, cornerRadius + layerRadius / 4f, glowPaintLayer)
        }
    }
    
    fun setLightAngle(angle: Float) {
        lightAngle = angle % 360f
        invalidate()
    }
    
    fun setBorderThickness(thickness: Float) {
        borderThickness = thickness
        updateRects()
        invalidate()
    }
    
    fun setCornerRadius(radius: Float) {
        cornerRadius = radius
        invalidate()
    }
    
    fun setHighlightIntensity(intensity: Float) {
        highlightIntensity = intensity.coerceIn(0f, 1f)
        invalidate()
    }
    
    fun setShadowIntensity(intensity: Float) {
        shadowIntensity = intensity.coerceIn(0f, 1f)
        invalidate()
    }

    fun setGlowIntensity(intensity: Float) {
        glowIntensity = intensity.coerceIn(0f, 1f)
        invalidate()
    }

    fun setGlowColor(color: Int) {
        glowColor = color
        invalidate()
    }

    fun getLightAngle(): Float = lightAngle
    fun getBorderThickness(): Float = borderThickness
    fun getCornerRadius(): Float = cornerRadius
    fun getGlowIntensity(): Float = glowIntensity
    fun getGlowColor(): Int = glowColor
}
