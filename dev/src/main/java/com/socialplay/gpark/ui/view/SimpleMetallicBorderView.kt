package com.socialplay.gpark.ui.view

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View
import com.socialplay.gpark.R
import kotlin.math.*

/**
 * 简化版金属边框View，专注于解决高光显示问题
 */
class SimpleMetallicBorderView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val borderRect = RectF()
    private val innerRect = RectF()
    
    private var borderThickness: Float = 8f
    private var lightAngle: Float = 45f
    private var cornerRadius: Float = 0f
    private var highlightIntensity: Float = 1.0f
    private var shadowIntensity: Float = 0.8f
    
    init {
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.MetallicBorderView)
        borderThickness = typedArray.getDimension(R.styleable.MetallicBorderView_borderThickness, 8f)
        lightAngle = typedArray.getFloat(R.styleable.MetallicBorderView_lightAngle, 45f)
        cornerRadius = typedArray.getDimension(R.styleable.MetallicBorderView_cornerRadius, 0f)
        highlightIntensity = typedArray.getFloat(R.styleable.MetallicBorderView_highlightIntensity, 1.0f)
        shadowIntensity = typedArray.getFloat(R.styleable.MetallicBorderView_shadowIntensity, 0.8f)
        typedArray.recycle()
    }
    
    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        updateRects()
    }
    
    private fun updateRects() {
        val width = width.toFloat()
        val height = height.toFloat()
        
        if (width <= 0 || height <= 0) return
        
        borderRect.set(0f, 0f, width, height)
        innerRect.set(borderThickness, borderThickness, width - borderThickness, height - borderThickness)
    }
    
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        
        if (width <= 0 || height <= 0) return
        
        val width = width.toFloat()
        val height = height.toFloat()
        
        // 计算光照方向
        // 光照角度：0度=右，90度=下，180度=左，270度=上
        // 45度表示光从左上角照射，渐变应该从左上到右下
        val angleRad = Math.toRadians(lightAngle.toDouble())
        val lightDx = cos(angleRad).toFloat()
        val lightDy = sin(angleRad).toFloat()

        // 创建线性渐变，从阴影到高光
        // 渐变方向应该与光照方向一致：从背光面（阴影）到受光面（高光）
        val centerX = width / 2
        val centerY = height / 2
        val diagonal = sqrt(width * width + height * height)

        // 渐变起点：背光面（阴影位置）
        val startX = centerX + lightDx * diagonal / 3
        val startY = centerY + lightDy * diagonal / 3
        // 渐变终点：受光面（高光位置）
        val endX = centerX - lightDx * diagonal / 3
        val endY = centerY - lightDy * diagonal / 3
        
        // 创建明显的渐变效果
        val shadowColor = Color.argb((255 * shadowIntensity).toInt(), 80, 80, 80)
        val baseColor = Color.parseColor("#C0C0C0")
        val highlightColor = Color.argb((255 * highlightIntensity).toInt(), 255, 255, 255)
        
        val gradient = LinearGradient(
            startX, startY, endX, endY,
            intArrayOf(shadowColor, baseColor, highlightColor, baseColor, shadowColor),
            floatArrayOf(0f, 0.3f, 0.5f, 0.7f, 1f),
            Shader.TileMode.CLAMP
        )
        
        paint.shader = gradient
        paint.style = Paint.Style.FILL
        
        // 绘制外边框
        canvas.drawRoundRect(borderRect, cornerRadius, cornerRadius, paint)
        
        // 清除内部区域
        paint.shader = null
        paint.color = Color.TRANSPARENT
        paint.xfermode = PorterDuffXfermode(PorterDuff.Mode.CLEAR)
        
        val innerCornerRadius = maxOf(0f, cornerRadius - borderThickness)
        canvas.drawRoundRect(innerRect, innerCornerRadius, innerCornerRadius, paint)
        
        paint.xfermode = null
    }
    
    fun setLightAngle(angle: Float) {
        lightAngle = angle % 360f
        invalidate()
    }
    
    fun setBorderThickness(thickness: Float) {
        borderThickness = thickness
        updateRects()
        invalidate()
    }
    
    fun setCornerRadius(radius: Float) {
        cornerRadius = radius
        invalidate()
    }
    
    fun setHighlightIntensity(intensity: Float) {
        highlightIntensity = intensity.coerceIn(0f, 1f)
        invalidate()
    }
    
    fun setShadowIntensity(intensity: Float) {
        shadowIntensity = intensity.coerceIn(0f, 1f)
        invalidate()
    }
    
    fun getLightAngle(): Float = lightAngle
    fun getBorderThickness(): Float = borderThickness
    fun getCornerRadius(): Float = cornerRadius
}
