package com.socialplay.gpark.ui.base.adapter

import android.view.View
import androidx.annotation.IdRes
import androidx.recyclerview.widget.RecyclerView
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import java.util.LinkedHashSet

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/09/12
 * desc   :
 * </pre>
 */

typealias ItemClickListener = (view: View, position: Int) -> Unit
typealias ItemLongClickListener = (view: View, position: Int) -> Boolean

interface IAdapterClickListener {

    fun getOnItemClickListener(): ItemClickListener?

    fun setOnItemClickListener(listener: ItemClickListener?)

    fun getOnItemLongClickListener(): ItemLongClickListener?

    fun setOnItemLongClickListener(listener: ItemLongClickListener?)

    fun getOnItemChildClickListener(): ItemClickListener?

    fun setOnItemChildClickListener(listener: ItemClickListener?)

    fun getOnItemChildLongClickListener(): ItemLongClickListener?

    fun setOnItemChildLongClickListener(listener: ItemLongClickListener?)

    fun getOnItemShowListener(): ItemClickListener?

    fun setOnItemShowListener(listener: ItemClickListener?)

    fun addChildClickViewIds(@IdRes vararg viewIds: Int)

    fun addChildLongClickViewIds(@IdRes vararg viewIds: Int)

    fun bindViewClickListener(holder: BindingViewHolder<*>)
}

class AdapterClickListener : IAdapterClickListener {

    private var itemClickListener: ItemClickListener? = null
    private var itemLongClickListener: ItemLongClickListener? = null
    private var itemChildClickListener: ItemClickListener? = null
    private var itemChildLongClickListener: ItemLongClickListener? = null
    private var itemShowListener: ItemClickListener? = null

    private var childClickViewIds: LinkedHashSet<Int>? = null
    private var childLongClickViewIds: LinkedHashSet<Int>? = null

    override fun getOnItemClickListener(): ItemClickListener? {
        return itemClickListener
    }

    override fun setOnItemClickListener(listener: ItemClickListener?) {
        itemClickListener = listener
    }

    override fun getOnItemLongClickListener(): ItemLongClickListener? {
        return itemLongClickListener
    }

    override fun setOnItemLongClickListener(listener: ItemLongClickListener?) {
        itemLongClickListener = listener
    }

    override fun getOnItemChildClickListener(): ItemClickListener? {
        return itemChildClickListener
    }

    override fun setOnItemChildClickListener(listener: ItemClickListener?) {
        itemChildClickListener = listener
    }

    override fun getOnItemShowListener(): ItemClickListener? {
        return itemShowListener
    }

    override fun setOnItemShowListener(listener: ItemClickListener?) {
        itemShowListener = listener
    }

    override fun getOnItemChildLongClickListener(): ItemLongClickListener? {
        return itemChildLongClickListener
    }

    override fun setOnItemChildLongClickListener(listener: ItemLongClickListener?) {
        itemChildLongClickListener = listener
    }

    override fun addChildClickViewIds(vararg viewIds: Int) {
        if (childClickViewIds == null) {
            childClickViewIds = LinkedHashSet()
        }
        for (viewId in viewIds) {
            childClickViewIds?.add(viewId)
        }
    }

    override fun addChildLongClickViewIds(vararg viewIds: Int) {
        if (childLongClickViewIds == null) {
            childLongClickViewIds = LinkedHashSet()
        }
        for (viewId in viewIds) {
            childLongClickViewIds?.add(viewId)
        }
    }

    override fun bindViewClickListener(holder: BindingViewHolder<*>) {
        itemClickListener?.let { listener ->
            holder.itemView.setOnAntiViolenceClickListener {
                val position = holder.bindingAdapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    listener(it, position)
                }
            }
        }
        itemLongClickListener?.let { listener ->
            holder.itemView.setOnLongClickListener {
                val position = holder.bindingAdapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    return@setOnLongClickListener listener(it, position)
                }
                return@setOnLongClickListener false
            }
        }

        itemChildClickListener?.let { listener ->
            childClickViewIds?.forEach { id ->
                holder.itemView.findViewById<View>(id)?.let { view ->
                    view.setOnAntiViolenceClickListener {
                        val position = holder.bindingAdapterPosition
                        if (position != RecyclerView.NO_POSITION) {
                            listener(view, position)
                        }
                    }
                }
            }
        }

        itemChildLongClickListener?.let { listener ->
            childLongClickViewIds?.forEach { id ->
                holder.itemView.findViewById<View>(id)?.let { view ->
                    view.setOnLongClickListener {
                        val position = holder.bindingAdapterPosition
                        if (position != RecyclerView.NO_POSITION) {
                            return@setOnLongClickListener listener(view, position)
                        }
                        return@setOnLongClickListener false
                    }
                }
            }
        }
    }
}