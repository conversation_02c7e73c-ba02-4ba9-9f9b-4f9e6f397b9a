package com.socialplay.gpark.ui.outfit

import android.content.Context
import android.graphics.Color
import android.graphics.Outline
import android.os.SystemClock
import android.text.TextUtils
import android.util.AttributeSet
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.ViewOutlineProvider
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.core.view.setPadding
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.airbnb.epoxy.VisibilityState
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.outfit.UgcAssetNotice
import com.socialplay.gpark.data.model.outfit.UgcDesignFeed
import com.socialplay.gpark.databinding.ItemGameDetailOperationBinding
import com.socialplay.gpark.databinding.ItemUgcAssetFeedNoticesBinding
import com.socialplay.gpark.databinding.ItemUgcAssetTagBinding
import com.socialplay.gpark.databinding.ItemUgcDesignFeedBinding
import com.socialplay.gpark.ui.base.adapter.createViewBinding
import com.socialplay.gpark.ui.core.IBaseEpoxyItemListener
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.ViewItemModel
import com.socialplay.gpark.ui.core.views.MetaEpoxyController
import com.socialplay.gpark.ui.view.FlowLayout
import com.socialplay.gpark.ui.view.MetaTextView
import com.socialplay.gpark.ui.view.SquareImageView
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.backgroundTintListByColor
import com.socialplay.gpark.util.extension.compoundDrawables
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.goneIfValueEmpty
import com.socialplay.gpark.util.extension.setBackgroundColorByRes
import com.socialplay.gpark.util.extension.setFontWeight400
import com.socialplay.gpark.util.extension.setFontWeight500
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTextColorByRes
import com.socialplay.gpark.util.extension.unsetOnClick
import com.socialplay.gpark.util.extension.unsetOnLongClickAndLongClickable
import com.socialplay.gpark.util.extension.visible
import timber.log.Timber

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/11/06
 *     desc   :
 * </pre>
 */

interface IUgcDesignFeedListener : IBaseEpoxyItemListener {
    fun clickOutfit(item: UgcDesignFeed, position: Int)
    fun clickLike(item: UgcDesignFeed, position: Int)
    fun more(item: UgcDesignFeed, position: Int)
    fun showDesign(item: UgcDesignFeed, position: Int)
    fun clickOperation(item: UgcAssetNotice, position: Int) {}
}

fun MetaEpoxyController.ugcDesignFeedItem(
    item: UgcDesignFeed,
    position: Int,
    uuid: String,
    uniqueTag: Int,
    prefix: String,
    listener: IUgcDesignFeedListener
) {
    add(
        UgcDesignFeedItem(
            item,
            position,
            uuid,
            listener
        ).id("${prefix}-${uniqueTag}-${item.feedId}")
    )
//    add(
//        UgcDesignFeedItemTest(
//            item,
//            position,
//            uuid,
//            listener
//        ).id("${prefix}-${uniqueTag}-${item.feedId}-test")
//    )
}

//data class UgcDesignFeedItemTest(
//    val item: UgcDesignFeed,
//    val position: Int,
//    val uuid: String,
//    val listener: IUgcDesignFeedListener
//) : ViewItemModel<UgcDesignFeedItemView>() {
//
//    companion object {
//        var totalTs = 0L
//    }
//
//    override fun UgcDesignFeedItemView.onBind() {
//        if (item.isUgcModule) {
//            ivOutfit.setPadding(0)
//            listener.getGlideOrNull()?.run {
//                load(item.cover)
//                    .centerCrop()
//                    .into(ivOutfit)
//            }
//        } else {
//            ivOutfit.setPadding(dp(23))
//            listener.getGlideOrNull()?.run {
//                load(item.cover)
//                    .fitCenter()
//                    .into(ivOutfit)
//            }
//        }
//        tvTitle.goneIfValueEmpty(item.title)
//        listener.getGlideOrNull()?.run {
//            load(item.userIcon)
//                .placeholder(R.drawable.placeholder_circle)
//                .circleCrop()
//                .into(ivAvatar)
//        }
//        tvUsername.text = item.userName
//        tvLikeCount.text = UnitUtil.formatKMCount(item.favorites)
//        tvLikeCount.isSelected = item.isFavorite
//
//        this.setOnAntiViolenceClickListener {
//            listener.clickOutfit(item, position)
//        }
//        if (item.uuid == uuid) {
//            this.unsetOnLongClickAndLongClickable()
//        } else {
//            this.setOnLongClickListener {
//                listener.more(item, position)
//                true
//            }
//        }
//        tvLikeCount.setOnAntiViolenceClickListener {
//            listener.clickLike(item, position)
//        }
//
//        flTags.removeAllViews()
//        flTags.visible(!item.tags.isNullOrEmpty())
//        item.tags?.take(3)?.forEach {
//            val b = this.createViewBinding(ItemUgcAssetTagBinding::inflate)
//            b.root.text = it
//            flTags.addView(b.root)
//        }
//    }
//
//    override fun UgcDesignFeedItemView.onUnbind() {
//        this.unsetOnClick()
//        this.unsetOnLongClickAndLongClickable()
//        tvLikeCount.unsetOnClick()
//        flTags.removeAllViews()
//    }
//
//    override fun onVisibilityStateChanged(visibilityState: Int, view: UgcDesignFeedItemView) {
//        super.onVisibilityStateChanged(visibilityState, view)
//        if (visibilityState == VisibilityState.VISIBLE) {
//            listener.showDesign(item, position)
//        }
//    }
//
//    override fun createView(parent: ViewGroup, context: Context): UgcDesignFeedItemView {
////        val ts = SystemClock.elapsedRealtimeNanos()
//        val view = UgcDesignFeedItemView(context)
////        totalTs += SystemClock.elapsedRealtimeNanos() - ts
////        Timber.d("WTF 2 $totalTs")
//        return view
//    }
//}
//
//class UgcDesignFeedItemView @JvmOverloads constructor(
//    context: Context,
//    attrs: AttributeSet? = null,
//    defStyleAttr: Int = 0
//) : LinearLayout(context, attrs, defStyleAttr) {
//
//    val ivOutfit: SquareImageView
//    val tvTitle: MetaTextView
//    val flTags: FlowLayout
//    val ivAvatar: ImageView
//    val tvUsername: MetaTextView
//    val tvLikeCount: MetaTextView
//
//    init {
//        addView(SquareImageView(context).apply {
//            ivOutfit = this
//        })
//        addView(MetaTextView(context).apply {
//            textSize = 13.0f
//            setFontWeight500()
//            maxLines = 2
//            ellipsize = TextUtils.TruncateAt.END
//            layoutParams = LayoutParams(
//                LayoutParams.MATCH_PARENT,
//                LayoutParams.WRAP_CONTENT
//            ).apply {
//                leftMargin = dp(8)
//                rightMargin = dp(8)
//                topMargin = dp(8)
//            }
//            tvTitle = this
//        })
//        addView(FlowLayout(context).apply {
//            setHorizontalSpacing(4)
//            setVerticalSpacing(4)
//            setMaxLines(1)
//            layoutParams = LayoutParams(
//                LayoutParams.MATCH_PARENT,
//                LayoutParams.WRAP_CONTENT
//            ).apply {
//                leftMargin = dp(8)
//                rightMargin = dp(8)
//                topMargin = dp(8)
//            }
//            flTags = this
//        })
//        addView(LinearLayout(context).apply {
//            addView(ImageView(context).apply {
//                layoutParams = LayoutParams(
//                    dp(18),
//                    dp(18)
//                ).apply {
//                    leftMargin = dp(8)
//                    topMargin = dp(8)
//                }
//                ivAvatar = this
//            })
//            addView(MetaTextView(context).apply {
//                textSize = 12.0f
//                setTextColorByRes(R.color.neutral_color_3)
//                setFontWeight400()
//                maxLines = 1
//                ellipsize = TextUtils.TruncateAt.END
//                layoutParams = LayoutParams(
//                    0,
//                    LayoutParams.WRAP_CONTENT,
//                    1.0f
//                ).apply {
//                    leftMargin = dp(4)
//                }
//                tvUsername = this
//            })
//            addView(MetaTextView(context).apply {
//                textSize = 12.0f
//                setTextColorByRes(R.color.color_ugc_comment_like)
//                setFontWeight400()
//                setPadding(dp(8))
//                gravity = Gravity.CENTER_VERTICAL
//                compoundDrawables(left = R.drawable.selector_ugc_comment_like)
//                compoundDrawablePadding = dp(2)
//                layoutParams = LayoutParams(
//                    LayoutParams.WRAP_CONTENT,
//                    LayoutParams.WRAP_CONTENT
//                )
//                tvLikeCount = this
//            })
//            layoutParams = LayoutParams(
//                LayoutParams.MATCH_PARENT,
//                LayoutParams.WRAP_CONTENT
//            ).apply {
//                gravity = Gravity.CENTER_VERTICAL
//            }
//        })
//        orientation = VERTICAL
//        setBackgroundColorByRes(R.color.white)
//        layoutParams = RecyclerView.LayoutParams(
//            RecyclerView.LayoutParams.MATCH_PARENT,
//            RecyclerView.LayoutParams.WRAP_CONTENT
//        ).apply {
//            leftMargin = dp(6)
//            rightMargin = dp(6)
//            bottomMargin = dp(12)
//        }
//        outlineProvider = object : ViewOutlineProvider() {
//            override fun getOutline(view: View, outline: Outline) {
//                outline.setRoundRect(0, 0, view.width, view.height, view.dp(12).toFloat())
//            }
//        }
//        clipToOutline = true
//    }
//}

data class UgcDesignFeedItem(
    val item: UgcDesignFeed,
    val position: Int,
    val uuid: String,
    val listener: IUgcDesignFeedListener
) : ViewBindingItemModel<ItemUgcDesignFeedBinding>(
    R.layout.item_ugc_design_feed,
    ItemUgcDesignFeedBinding::bind
) {

    override fun ItemUgcDesignFeedBinding.onBind() {
        if (item.isUgcModule) {
            ivOutfit.setPadding(0)
            listener.getGlideOrNull()?.run {
                load(item.cover)
                    .centerCrop()
                    .into(ivOutfit)
            }
        } else {
            ivOutfit.setPadding(dp(23))
            listener.getGlideOrNull()?.run {
                load(item.cover)
                    .fitCenter()
                    .into(ivOutfit)
            }
        }
        tvTitle.goneIfValueEmpty(item.title)
        listener.getGlideOrNull()?.run {
            load(item.userIcon)
                .placeholder(R.drawable.placeholder_circle)
                .circleCrop()
                .into(ivAvatar)
        }
        tvUsername.text = item.userName
        tvLikeCount.text = UnitUtil.formatKMCount(item.favorites)
        tvLikeCount.isSelected = item.isFavorite

        root.setOnAntiViolenceClickListener {
            listener.clickOutfit(item, position)
        }
        if (item.uuid == uuid) {
            root.unsetOnLongClickAndLongClickable()
        } else {
            root.setOnLongClickListener {
                listener.more(item, position)
                true
            }
        }
        tvLikeCount.setOnAntiViolenceClickListener {
            listener.clickLike(item, position)
        }

        flTags.removeAllViews()
        flTags.visible(!item.tags.isNullOrEmpty())
        item.tags?.take(3)?.forEach {
            val b = root.createViewBinding(ItemUgcAssetTagBinding::inflate)
            b.root.text = it
            flTags.addView(b.root)
        }
    }

    override fun ItemUgcDesignFeedBinding.onUnbind() {
        root.unsetOnClick()
        root.unsetOnLongClickAndLongClickable()
        tvLikeCount.unsetOnClick()
        flTags.removeAllViews()
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        super.onVisibilityStateChanged(visibilityState, view)
        if (visibilityState == VisibilityState.VISIBLE) {
            listener.showDesign(item, position)
        }
    }
}

fun MetaEpoxyController.ugcAssetFeedNoticesItem(
    items: List<UgcAssetNotice>,
    spanSize: Int,
    listener: IUgcDesignFeedListener
) {
    add(
        UgcAssetFeedNoticesItem(
            items,
            spanSize,
            listener
        ).id("UgcAssetFeedNotices").spanSizeOverride { totalSpanCount, _, _ ->
            spanSize.coerceAtMost(totalSpanCount)
        }
    )
}

data class UgcAssetFeedNoticesItem(
    val items: List<UgcAssetNotice>,
    val spanSize: Int,
    val listener: IUgcDesignFeedListener
) : ViewBindingItemModel<ItemUgcAssetFeedNoticesBinding>(
    R.layout.item_ugc_asset_feed_notices,
    ItemUgcAssetFeedNoticesBinding::bind
) {

    override fun ItemUgcAssetFeedNoticesBinding.onBind() {
        if (root.layoutParams is StaggeredGridLayoutManager.LayoutParams) {
            root.layoutParams =
                (root.layoutParams as? StaggeredGridLayoutManager.LayoutParams)?.apply {
                    isFullSpan = true
                }
        }

        includeOp1.bindOperation(items.getOrNull(0), 0)
        includeOp2.bindOperation(items.getOrNull(1), 1)
        includeOp3.bindOperation(items.getOrNull(2), 2)
    }

    override fun ItemUgcAssetFeedNoticesBinding.onUnbind() {
        includeOp1.root.unsetOnClick()
        includeOp2.root.unsetOnClick()
        includeOp3.root.unsetOnClick()
    }

    private fun ItemGameDetailOperationBinding.bindOperation(item: UgcAssetNotice?, position: Int) {
        if (item == null) {
            root.gone()
            return
        }
        root.visible()
        if (item.tagName.isNullOrEmpty()) {
            tvTag.gone()
        } else {
            tvTag.visible()
            val color = kotlin.runCatching {
                item.tagColor?.let {
                    Color.parseColor(it)
                } ?: getColorByRes(R.color.color_4AB4FF)
            }.getOrDefault(getColorByRes(R.color.color_4AB4FF))
            tvTag.text = item.tagName
            tvTag.backgroundTintListByColor(color)
        }
        tvTitle.text = item.noticeTitle
        root.setOnAntiViolenceClickListener {
            listener.clickOperation(item, position)
        }
    }
}