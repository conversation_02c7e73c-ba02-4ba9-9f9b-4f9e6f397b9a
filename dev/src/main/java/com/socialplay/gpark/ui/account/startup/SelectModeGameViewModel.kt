package com.socialplay.gpark.ui.account.startup

import android.content.ComponentCallbacks
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/05/24
 *     desc   :
 * </pre>
 */
data class SelectModeGameState(
    val hide: Boolean = false
) : MavericksState

class SelectModeGameViewModel(
    initialState: SelectModeGameState
) : BaseViewModel<SelectModeGameState>(initialState) {

    companion object : KoinViewModelFactory<SelectModeGameViewModel, SelectModeGameState>() {
        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: SelectModeGameState
        ): SelectModeGameViewModel {
            return SelectModeGameViewModel(state)
        }
    }

    fun hide(delay: Boolean = false) = viewModelScope.launch {
        if (delay) {
            delay(3_000)
        }
        setState { copy(hide = true) }
    }
}