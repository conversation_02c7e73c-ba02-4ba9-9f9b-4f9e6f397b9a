package com.socialplay.gpark.ui.im.conversation

import android.content.Context
import androidx.core.app.NotificationManagerCompat
import androidx.core.net.toUri
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ly123.metacloud.ext.CustomMessageType
import com.ly123.tes.mgs.metacloud.IResultListener
import com.ly123.tes.mgs.metacloud.ISendMediaMessageListener
import com.ly123.tes.mgs.metacloud.ISendSystemMessageListener
import com.ly123.tes.mgs.metacloud.ISendTextMessageListener
import com.ly123.tes.mgs.metacloud.ITypingStatusListener
import com.ly123.tes.mgs.metacloud.MetaCloud
import com.ly123.tes.mgs.metacloud.message.ImageMessage
import com.ly123.tes.mgs.metacloud.message.InformationMultiTypeMessage
import com.ly123.tes.mgs.metacloud.message.InformationNotificationMessage
import com.ly123.tes.mgs.metacloud.message.OverseaPgcGameCardMessage
import com.ly123.tes.mgs.metacloud.message.OverseaUgcGameCardMessage
import com.ly123.tes.mgs.metacloud.message.PostCardMessage
import com.ly123.tes.mgs.metacloud.message.ProfileCardMessage
import com.ly123.tes.mgs.metacloud.message.UgcDesignCardMessage
import com.ly123.tes.mgs.metacloud.message.VideoFeedCardMessage
import com.ly123.tes.mgs.metacloud.model.Conversation
import com.ly123.tes.mgs.metacloud.model.Message
import com.ly123.tes.mgs.metacloud.model.SentMessageErrorCode
import com.ly123.tes.mgs.metacloud.model.TypingStatus
import com.ly123.tes.mgs.metacloud.model.UserInfo
import com.meta.box.biz.friend.FriendBiz
import com.meta.box.biz.friend.internal.model.FriendStatus
import com.meta.box.biz.friend.model.FriendInfo
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.BanBlockInteractor
import com.socialplay.gpark.data.interactor.ImInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.LoadStatus
import com.socialplay.gpark.data.model.LoadType
import com.socialplay.gpark.data.model.editor.UgcDetailInfo
import com.socialplay.gpark.data.model.editor.UgcGameDetail
import com.socialplay.gpark.data.model.event.OnReceiveMessageProgressEvent
import com.socialplay.gpark.data.model.im.LocalMessageInfo
import com.socialplay.gpark.data.model.outfit.UgcDesignDetail
import com.socialplay.gpark.data.model.post.PostShareDetail
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.data.model.post.request.AddPvRequest
import com.socialplay.gpark.data.model.profile.UserProfileInfo
import com.socialplay.gpark.data.model.share.ShareContent
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.im.conversation.provider.InfoNotificationMuLtiTypeMsgItemProvider
import com.socialplay.gpark.ui.notice.NotificationPermissionManager
import com.socialplay.gpark.data.model.share.ShareRawData
import com.socialplay.gpark.data.model.videofeed.VideoFeedItem
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.NetUtil
import com.socialplay.gpark.util.SingleLiveData
import com.socialplay.gpark.util.ToastUtil
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.koin.core.context.GlobalContext
import timber.log.Timber
import java.io.File


/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2021/06/22
 *     desc   :
 *
 */
class ConversationViewModel(val metaRepository: IMetaRepository, private val banBlockInteractor: BanBlockInteractor,
                            val metaKV: MetaKV, val application: Context) : ViewModel() {
    private val accountInteractor by lazy { GlobalContext.get().get<AccountInteractor>() }
    private val imInteractor by lazy { GlobalContext.get().get<ImInteractor>() }
    private val _localMessageLiveData by lazy { MutableLiveData<Pair<LocalMessageInfo?, List<Message?>?>?>() }
    val localMessageLiveData: LiveData<Pair<LocalMessageInfo?, List<Message?>?>?> = _localMessageLiveData
    private val _remoteLiveData by lazy { MutableLiveData<Pair<LoadStatus?, List<Message?>?>?>() }
    val remoteLiveData = _remoteLiveData
    private val _lastMentionedLiveData by lazy { MutableLiveData<List<Message?>?>() }
    val lastMentionLiveData = _lastMentionedLiveData
    private val _friendStatusLiveData by lazy { MutableLiveData<FriendStatus?>() }
    val friendStatusLiveData = _friendStatusLiveData
    private val _typingStatusInfo by lazy { MutableLiveData<Message.MessageType?>() }
    val typingStatusLiveData = _typingStatusInfo
    private val _onDeleteMessageLiveData by lazy { MutableLiveData<Message?>() }
    val onDeleteMessageLiveData = _onDeleteMessageLiveData
    private val _sendMessageResultLiveData by lazy { SingleLiveData<Boolean>() }
    val sendMessageResultLiveData = _sendMessageResultLiveData
    var blocked: Boolean = false
    private var haveTipNotification = false

    private val typingStatusListener = object : ITypingStatusListener {
        override fun onTypingStatusChanged(
            conversationType: Conversation.ConversationType?,
            targetId: String?,
            typingStatusSet: MutableCollection<TypingStatus>?
        ) {
            if (typingStatusSet.isNullOrEmpty()) {
                _typingStatusInfo.postValue(null)
                return
            }
            typingStatusSet.forEach { status ->
                _typingStatusInfo.postValue(status.messageType)
            }
        }

    }

    //好友信息
    private val _friendInfoLiveData: MutableLiveData<FriendInfo?> = MutableLiveData()
    val friendInfoLiveData: LiveData<FriendInfo?> = _friendInfoLiveData

    private lateinit var targetUUID: String

    private val _showIMNotificationLiveData = MutableStateFlow<Boolean>(false)
    private val _openNotification = MutableStateFlow<Boolean?>(null)
    val openNotification = _openNotification
    private val _unReadCountFlow = MutableStateFlow<Int?>(null)
    private val _firstSend = MutableStateFlow<Boolean>(false)

    val showIMNotificationRecord =
        combine(_showIMNotificationLiveData, _unReadCountFlow, _firstSend) { a, b, c ->
            Timber.d("_showIMNotificationLiveData- %s %s %s", a,b,c)
            if (a && ((b ?: 0) > 0)) {
                Timber.d("_showIMNotificationLiveData-true")
                //当前周第一次收到消息
                NotificationPermissionManager.updateImPermissionTime()
                sendNotification()
                true
            } else if (a && c) {
                //当周第一次发送消息
                NotificationPermissionManager.updateImPermissionTime()
                sendNotification()
                Timber.d("_showIMNotificationLiveData-true")
                true
            } else {
                Timber.d("_showIMNotificationLiveData-false")
                false
            }
        }
    private val friendInfoUpdateObserver: suspend (friendInfo: FriendInfo) -> Unit = {
        _friendInfoLiveData.value = it
        _friendStatusLiveData.postValue(_friendInfoLiveData.value?.status)
    }

    init {
        checkImNotification()
    }

    private fun checkImNotification() = viewModelScope.launch {
        _showIMNotificationLiveData.value = NotificationPermissionManager.imNeedPermission(application)
    }

    /**
     * 好友的资料
     */
    fun getFriendInfo(uuid: String, title: String?) = viewModelScope.launch {

        if (NetUtil.isNetworkAvailable()) {
            _friendInfoLiveData.value = queryUserInfo(uuid).data
            _friendStatusLiveData.postValue(_friendInfoLiveData.value?.status)
        }
    }

    private suspend fun queryUserInfo(uuid: String) = metaRepository.queryFriendInfo(uuid)


    /**
     * 输入状态监听
     */
    fun getTypingStatus(){
        MetaCloud.registerTypingStatusListener(typingStatusListener)
    }

    /**
     *清除未读消息
     */
    fun clearMessageUnReadStatus(type: Conversation.ConversationType, targetId: String) = viewModelScope.launch {
        imInteractor.clearMessageUnReadStatus(type, targetId)
    }

    /**
     *同步未读消息
     */
    fun syncConversationReadStatus(type: Conversation.ConversationType, targetId: String, timestamp: Long){
        MetaCloud.syncConversationReadStatus(type, targetId,timestamp) {
        }
    }

    /**
     * 发送文本消息
     */
    fun sendTextMessageWithRiskReview(
        otherUid: String,
        conversationType: Conversation.ConversationType,
        text: String,
        systemStr: String,
    ) = viewModelScope.launch {
        if (checkFriendStatus(otherUid, conversationType, systemStr)) {
            metaRepository.reviewPrivateMessageRisk(content = text, null).collect {
                if (!it.succeeded || it.data?.checkPass() != true) {
                    _sendMessageResultLiveData.postValue(false)
                } else {
                    _sendMessageResultLiveData.postValue(true)
                    sendText(otherUid, text, conversationType)
                }
            }
        }
    }


    private fun sendText(otherUid: String, text: String, conversationType: Conversation.ConversationType){
        val userInfo = accountInteractor.accountLiveData.value?:return
        MetaCloud.sendTextMessage(
            otherUid, text, conversationType, null, null, UserInfo(
                userInfo.uuid, userInfo.nickname,
                userInfo.portrait
            ), "", object : ISendTextMessageListener {

            override fun onError(imMessage: Message, errorCode: Int, desc: String?) {
                EventBus.getDefault().postSticky(imMessage)
                Timber.d("metaCloud  onError imMessage: %s  errorCode:%s",imMessage, errorCode)
            }

            override fun onSuccess(imMessage: Message) {
                Timber.d("metaCloud  onSuccess  %s", imMessage)
                EventBus.getDefault().postSticky(imMessage)
                if (!_firstSend.value) {
                    viewModelScope.launch {
                        _firstSend.emit(true)
                    }
                }
            }

        })

    }


    private fun checkFriendStatus(otherUid: String, conversationType: Conversation.ConversationType, systemStr: String): Boolean {
        val friend = _friendInfoLiveData.value
        val isFriend = friend?.bothFriend == true
        val isOfficial = accountInteractor.isOfficial || friend?.isOfficial() == true
        if (!isFriend && !isOfficial) {
            //不是双向好友
            accountInteractor.accountLiveData.value?.uuid?.let {
                sendSystem(targetId = otherUid, it, conversationType, systemStr)
            }
        }
        return isFriend || isOfficial
    }

    /**
     * 系统消息提示不是自己的好友
     */
    private fun sendSystem(targetId: String, uuid: String, conversationType: Conversation.ConversationType, systemStr: String) {
        MetaCloud.insertIncomingMessage(targetId, uuid, conversationType, systemStr, object :ISendSystemMessageListener{
            override fun onError(errorCode: Int, desc: String?) {
                Timber.d("metaCloud %s", errorCode)
            }

            override fun onSuccess(imMessage: Message) {
                EventBus.getDefault().postSticky(imMessage)
            }

        })
    }
    private fun sendNotification() =viewModelScope.launch {
        _showIMNotificationLiveData.value = false
        _firstSend.value = false
        Analytics.track(EventConstants.EVENT_CHAT_PUSH_POST_SHOW)
        accountInteractor.accountLiveData.value?.uuid?.let {
            val message = InformationMultiTypeMessage.InformationMultiType(
                InfoNotificationMuLtiTypeMsgItemProvider.TYPE_IM_NOTIFICATION,
                application.getString(R.string.notification_im_title)
            )
            MetaCloud.insertLocalInformationMultiTypeMessage(
                targetUUID,
                it,
                Conversation.ConversationType.PRIVATE,
                message,
                object : ISendSystemMessageListener {
                    override fun onError(errorCode: Int, desc: String?) {

                    }

                    override fun onSuccess(imMessage: Message) {
                        (imMessage.content as InformationMultiTypeMessage).show = true
                        EventBus.getDefault().postSticky(imMessage)
                    }

                }
            )
        }
    }

        /**
     * 获取远程消息
     */
    fun getRemoteHistoryMessages(conversationType: Conversation.ConversationType, targetId: String, message: Message?, reqCount: Int) {
        MetaCloud.getRemoteHistoryMessages(targetId,conversationType, message, reqCount,object :
            IResultListener{
            override fun onError(errorMsg: String?) {
                _remoteLiveData.value = LoadStatus(status = LoadType.Fail) to null
            }

            override fun onSuccess(messages: List<Message?>?) {

                _remoteLiveData.value = LoadStatus(
                    updateSize = reqCount,
                    status = LoadType.LoadMore
                ) to messages
            }

        })
    }

    fun getHistoryMessage(conversationType: Conversation.ConversationType, targetId: String, localMessageInfo: LocalMessageInfo) {
        MetaCloud.getRemoteHistoryMessages(targetId, conversationType, localMessageInfo.eldestMessage, ConversationFragment.REQ_COUNT,  object : IResultListener{
                override fun onError(errorMsg: String?) {
                    localMessageInfo.isSuccess = false
                    _localMessageLiveData.value = localMessageInfo to null
                }

                override fun onSuccess(messages: List<Message?>?) {
                    val newList = filterNotificationMessage(messages,localMessageInfo)
                    localMessageInfo.isSuccess = true
                    Timber.d("getHistoryMessage"+messages?.size +"  newList"+ newList.size)
                    _localMessageLiveData.value = localMessageInfo to messages
                }
            })
    }

    /**
     * 过滤出系统通知消息，只展示最后一条
     */
    fun filterNotificationMessage(messages: List<Message?>?,localMessageInfo: LocalMessageInfo):ArrayList<Message>{
        val list = messages?.filterNotNull()?: emptyList()
        val newList = ArrayList<Message>()
        newList.addAll(list)
        if (localMessageInfo.eldestMessage == null) {
            //刷新数据时，提示也需要更新
            haveTipNotification = false
        }
        if (PandoraToggle.isChatPushNotification) {
            val removeidList = ArrayList<String>()
            for (i in newList.lastIndex.downTo(0)) {
                val item = newList[i]
                if (item.content is InformationMultiTypeMessage) {
                    val data = (item.content as InformationMultiTypeMessage)
                    val result =
                        GsonUtil.gsonSafeParse<InformationMultiTypeMessage.InformationMultiType>(
                            data.data
                        )
                    if (result?.type.equals(InfoNotificationMuLtiTypeMsgItemProvider.TYPE_IM_NOTIFICATION)) {
                        if (haveTipNotification) {
                            //已经有展示的了
                            data.show = false
                            removeidList.add(item.messageId)
                        } else {
                            haveTipNotification = true
                            data.show = true
                        }
                    }
                }
            }
            deleteMessageList(removeidList)
        }
        return newList
    }

    private fun getUnreadCount(conversationType: Conversation.ConversationType, targetId: String)= viewModelScope.launch {
        MetaCloud.getUnReadCount(conversationType, targetId) {
            this.launch { _unReadCountFlow.emit(it) }
            clearMessageUnReadStatus(conversationType, targetId)
        }
    }

    fun sendReadReceiptMessage(conversationType: Conversation.ConversationType, targetId: String) {
       MetaCloud.sendReadReceiptMessage(conversationType, targetId)
    }

    private fun deleteMessageList(list: List<String>) {
        val removeList = ArrayList<String>()
        removeList.addAll(list)
        GlobalScope.launch {
            removeList.forEach {
                MetaCloud.deleteMessages(targetUUID, it, null)
            }
        }
    }

    /**
     * 好友在线状态监听
     */
    fun addMessageListener(targetId: String) = viewModelScope.launch {
        targetUUID = targetId
        FriendBiz.observeFriend(targetId, null, friendInfoUpdateObserver)
        getUnreadCount(Conversation.ConversationType.PRIVATE, targetUUID)
    }

    /**
     * 清楚缓存信息
     */
    fun cleanAllMessage() {
        if (_localMessageLiveData.value?.first != null) {
            val first: LocalMessageInfo? = _localMessageLiveData.value?.first
            first?.isSuccess = true
            first?.isClean = true
            _localMessageLiveData.value = first to null
        }
        if (_remoteLiveData.value?.first != null) {
            _remoteLiveData.value = _remoteLiveData.value?.first to null
        }
        if (_onDeleteMessageLiveData.value != null) {
            _onDeleteMessageLiveData.value = null
        }
    }

    override fun onCleared() {
        FriendBiz.removeFriendObserver(targetUUID, friendInfoUpdateObserver)
        MetaCloud.unRegisterTypingStatusListener(typingStatusListener)
        super.onCleared()
    }

    /**
     *删除消息
     */
    fun deleteMessages(data: Message) {
        MetaCloud.deleteMessages(data.targetId, data.messageId) {
            _onDeleteMessageLiveData.postValue(data)
            data.messageId = "0"
            onResendItemClick(data)
        }
    }

    /**
     * 消息重发
     */
    private fun onResendItemClick(message: Message) {
        if (message.content is ImageMessage) {
            val imageMessage = message.content as ImageMessage
            if (imageMessage.remoteUri != null && !imageMessage.remoteUri.toString().startsWith("file")) {
                MetaCloud.sendMessage(message,  "",object :ISendTextMessageListener{
                    override fun onError(imMessage: Message, errorCode: Int, desc: String?) {
                        EventBus.getDefault().postSticky(imMessage)
                    }

                    override fun onSuccess(imMessage: Message) {
                        EventBus.getDefault().postSticky(imMessage)
                    }

                })
            } else {
                MetaCloud.sendImageMessage(message, object :ISendMediaMessageListener{
                    override fun onSend(message: Message?, progress: Int) {
                        Timber.d("metacloud image onSend  %s %s",message,progress)
                        val result =
                            OnReceiveMessageProgressEvent()
                        result.message = message
                        result.progress = progress
                        EventBus.getDefault().postSticky(result)

                    }

                    override fun onSendError(message: Message?, messageErrorCode: SentMessageErrorCode?, desc: String) {
                        EventBus.getDefault().postSticky(message)
                    }

                    override fun onSendUpdate(message: Message?) {
                        EventBus.getDefault().postSticky(message)
                        Timber.d("metacloud image onSendUpdate  %s ",message)
                    }

                    override fun onStartSend(message: Message?) {
                        EventBus.getDefault().postSticky(message)
                    }

                    override fun onSuccess(message: Message) {
                        EventBus.getDefault().postSticky(message)
                        Timber.d("metacloud image onSuccess  %s ",message)
                    }
                } )
            }
        } else {
            MetaCloud.sendMessage(message,  "",object :ISendTextMessageListener{

                override fun onError(imMessage: Message, errorCode: Int, desc: String?) {
                    EventBus.getDefault().postSticky(imMessage)
                }

                override fun onSuccess(imMessage: Message) {
                    EventBus.getDefault().postSticky(imMessage)
                }

            })
        }
    }

    fun isSelfSendMessage(senderUserId: String?): Boolean {
       return accountInteractor.accountLiveData.value?.uuid.equals(senderUserId)
    }

    fun checkBlockRelation(otherUuid: String) = viewModelScope.launch {
        blocked = banBlockInteractor.checkBeingBlocked(otherUuid)
    }

    fun getUserInfo(): UserInfo {
        return UserInfo(
            accountInteractor.accountLiveData.value?.uuid,
            accountInteractor.accountLiveData.value?.nickname,
            accountInteractor.accountLiveData.value?.portrait
        )
    }

    fun quickSharing(shareContent: String?, targetId: String) {
        shareContent ?: return
        val shareData = GsonUtil.gsonSafeParse<ShareContent>(shareContent)
        when (shareData?.type) {
            ShareContent.TYPE_TOPIC -> {
                val postTag = GsonUtil.gsonSafeParse<PostTag>(shareData.content)?:return
                MetaCloud.sendCustomMessage(
                    targetId, Conversation.ConversationType.PRIVATE, GsonUtil.safeToJson(postTag,""),
                    CustomMessageType.CUSTOM_SHARE_TOPIC,null,object :ISendTextMessageListener{
                        override fun onError(imMessage: Message, errorCode: Int, desc: String?) {
                            Timber.d("metaCloud  onError imMessage: %s  errorCode:%s",imMessage, errorCode)
                            EventBus.getDefault().postSticky(imMessage)
                        }

                        override fun onSuccess(imMessage: Message) {
                            EventBus.getDefault().postSticky(imMessage)
                            ToastUtil.showShort(R.string.share_successfully)
                            Timber.d("metaCloud  onSuccess imMessage: %s  ",imMessage)
                        }

                    })

            }

            ShareContent.TYPE_POST  -> {
                val post = GsonUtil.gsonSafeParse<PostShareDetail>(shareData.content) ?: return
                viewModelScope.launch { metaRepository.commonAddPvCount(AddPvRequest.RES_TYPE_POST_SHARE, post.postId).collect {} }
                MetaCloud.sendPostCardMessage(
                    targetId,
                    Conversation.ConversationType.PRIVATE,
                    PostCardMessage.PostInfo(post.postId,null,post.image ?: post.video, post.content), "",object :ISendTextMessageListener{
                        override fun onError(imMessage: Message, errorCode: Int, desc: String?) {
                            Timber.d("metaCloud  onError imMessage: %s  errorCode:%s",imMessage, errorCode)
                            EventBus.getDefault().postSticky(imMessage)
                        }

                        override fun onSuccess(imMessage: Message) {
                            EventBus.getDefault().postSticky(imMessage)
                            ToastUtil.showShort(R.string.share_successfully)
                            Timber.d("metaCloud  onSuccess imMessage: %s  ",imMessage)
                        }

                    })
            }

            ShareContent.TYPE_IMAGE -> {
                val image = shareData.content
                MetaCloud.sendImages(
                    Conversation.ConversationType.PRIVATE,
                    targetId,
                    arrayListOf(File(image).toUri()),
                    true,
                    getUserInfo(),
                    "",
                    object : ISendMediaMessageListener {
                        override fun onSend(message: Message?, progress: Int) {
                            message ?: return
                            Timber.d("Chat image onSend %s %s", message, progress)
                            val result = OnReceiveMessageProgressEvent()
                            result.message = message
                            result.progress = progress
                            EventBus.getDefault().postSticky(result)
                        }

                        override fun onSendError(
                            message: Message?,
                            messageErrorCode: SentMessageErrorCode?,
                            desc: String
                        ) {
                            message ?: return
                            Timber.d(
                                "Chat image onSendError %s %s %s",
                                message,
                                messageErrorCode,
                                desc
                            )
                            EventBus.getDefault().postSticky(message)
                        }

                        override fun onSendUpdate(message: Message?) {
                            message ?: return
                            Timber.d("Chat image onSendUpdate %s", message)
                            EventBus.getDefault().postSticky(message)
                        }

                        override fun onStartSend(message: Message?) {
                            message ?: return
                            Timber.d("Chat image onStartSend %s", message)
                            MetaCloud.sendTypingStatus(
                                Conversation.ConversationType.PRIVATE,
                                targetId,
                                Message.MessageType.IMAGE
                            )
                            EventBus.getDefault().postSticky(message)
                        }

                        override fun onSuccess(message: Message) {
                            Timber.d("Chat image onSuccess %s", message)
                            ToastUtil.showShort(R.string.share_successfully)
                            EventBus.getDefault().postSticky(message)
                        }
                    }
                )
            }

            ShareContent.TYPE_PROFILE  -> {
                val profile = GsonUtil.gsonSafeParse<UserProfileInfo>(shareData.content) ?: return
                MetaCloud.sendProfileCardMessage(
                    targetId,
                    Conversation.ConversationType.PRIVATE,
                    ProfileCardMessage.ProfileCardInfo(
                        uid = profile.uid,
                        userNumber = profile.userNumber,
                        nickname = profile.nickname,
                        gender = profile.gender,
                        birth = profile.birth,
                        city = profile.city,
                        portrait = profile.portrait,
                        signature = profile.signature,
                        commentTotal = profile.commentTotal,
                        friendTotal = profile.friendTotal,
                        followCount = profile.followCount,
                        fansCount = profile.fansCount,
                        likeCount = profile.likeCount
                    ),
                    "",
                    object : ISendTextMessageListener {
                        override fun onError(imMessage: Message, errorCode: Int, desc: String?) {
                            Timber.d(
                                "metaCloud  onError imMessage: %s  errorCode:%s",
                                imMessage,
                                errorCode
                            )
                            EventBus.getDefault().postSticky(imMessage)
                        }

                        override fun onSuccess(imMessage: Message) {
                            EventBus.getDefault().postSticky(imMessage)
                            ToastUtil.showShort(R.string.share_successfully)
                            Timber.d("metaCloud  onSuccess imMessage: %s  ", imMessage)
                        }
                    }
                )
            }

            ShareContent.TYPE_PGC_DETAIL  -> {
                val pgc = GsonUtil.gsonSafeParse<ShareRawData.Game>(shareData.content) ?: return
                MetaCloud.sendOverseaPgcGameCardMessage(
                    targetId,
                    Conversation.ConversationType.PRIVATE,
                    OverseaPgcGameCardMessage.PgcCardInfo(
                        id = pgc.id,
                        name = pgc.name,
                        icon = pgc.icon,
                        description = pgc.description,
                        pkg = pgc.pkg,
                        shareCount = pgc.shareCount,
                        authorId = pgc.authorId,
                        authorName = pgc.authorName,
                        authorAvatar = pgc.authorAvatar,
                        avgScore = pgc.avgScore,
                        playerCount = pgc.playerCount,
                        likeCount = pgc.likeCount,
                        isBannerHor = pgc.isBannerHor
                    ),
                    "",
                    object : ISendTextMessageListener {
                        override fun onError(imMessage: Message, errorCode: Int, desc: String?) {
                            Timber.d(
                                "metaCloud  onError imMessage: %s  errorCode:%s",
                                imMessage,
                                errorCode
                            )
                            EventBus.getDefault().postSticky(imMessage)
                        }

                        override fun onSuccess(imMessage: Message) {
                            EventBus.getDefault().postSticky(imMessage)
                            ToastUtil.showShort(R.string.share_successfully)
                            Timber.d("metaCloud  onSuccess imMessage: %s  ", imMessage)
                        }
                    }
                )
            }

            ShareContent.TYPE_UGC_DETAIL  -> {
                val ugc = GsonUtil.gsonSafeParse<UgcGameDetail>(shareData.content) ?: return
                MetaCloud.sendOverseaUgcGameCardMessage(
                    targetId,
                    Conversation.ConversationType.PRIVATE,
                    OverseaUgcGameCardMessage.UgcCardInfo(
                        id = ugc.id,
                        packageName = ugc.packageName,
                        ugcGameName = ugc.ugcGameName,
                        gameCode = ugc.gameCode,
                        banner = ugc.banner,
                        ugcGameDesc = ugc.ugcGameDesc,
                        userName = ugc.author?.name,
                        userUuid = ugc.author?.id ?: ugc.userUuid,
                        userIcon = ugc.author?.avatar,
                        userReleaseCount = ugc.userReleaseCount,
                        loveQuantity = ugc.loveQuantity,
                        pageView = ugc.pvCount,
                        parentIcon = null,
                        shareCount = 0
                    ),
                    "",
                    object : ISendTextMessageListener {
                        override fun onError(imMessage: Message, errorCode: Int, desc: String?) {
                            Timber.d(
                                "metaCloud  onError imMessage: %s  errorCode:%s",
                                imMessage,
                                errorCode
                            )
                            EventBus.getDefault().postSticky(imMessage)
                        }

                        override fun onSuccess(imMessage: Message) {
                            EventBus.getDefault().postSticky(imMessage)
                            ToastUtil.showShort(R.string.share_successfully)
                            Timber.d("metaCloud  onSuccess imMessage: %s  ", imMessage)
                        }
                    }
                )
            }

            ShareContent.TYPE_VIDEO_FEED  -> {
                val videoFeed = GsonUtil.gsonSafeParse<VideoFeedItem>(shareData.content) ?: return
                MetaCloud.sendVideoFeedCardMessage(
                    targetId,
                    Conversation.ConversationType.PRIVATE,
                    VideoFeedCardMessage.VideoFeedCardInfo(
                        videoId = videoFeed.videoId,
                        videoUrl = videoFeed.videoUrl,
                        videoCover = videoFeed.videoCover,
                        videoLikeCount = videoFeed.videoLikeCount,
                        videoCommentCount = videoFeed.videoCommentCount,
                        videoContent = videoFeed.videoContent,
                        videoWidth = videoFeed.videoWidth,
                        videoHeight = videoFeed.videoHeight,
                        videoShareCount = videoFeed.shareCount,
                        videoAuthorUuid = videoFeed.author.uuid,
                        videoAuthorName = videoFeed.author.name,
                        videoAuthorAvatar = videoFeed.author.avatar
                    ),
                    "",
                    object : ISendTextMessageListener {
                        override fun onError(imMessage: Message, errorCode: Int, desc: String?) {
                            Timber.d(
                                "metaCloud  onError imMessage: %s  errorCode:%s",
                                imMessage,
                                errorCode
                            )
                            EventBus.getDefault().postSticky(imMessage)
                        }

                        override fun onSuccess(imMessage: Message) {
                            EventBus.getDefault().postSticky(imMessage)
                            ToastUtil.showShort(R.string.share_successfully)
                            Timber.d("metaCloud  onSuccess imMessage: %s  ", imMessage)
                        }
                    }
                )
            }

            ShareContent.TYPE_UGC_DESIGN_DETAIL -> {
                val ugcDesignDetail = GsonUtil.gsonSafeParseCollection<UgcDesignDetail>(shareData.content) ?: return
                MetaCloud.sendUgcDesignCardMessage(
                    targetId,
                    Conversation.ConversationType.PRIVATE,
                    UgcDesignCardMessage.UgcDesignInfo(
                        icon = ugcDesignDetail.cover,
                        title = ugcDesignDetail.title,
                        itemId = ugcDesignDetail.feedId
                    ),
                    "",
                    object : ISendTextMessageListener {
                        override fun onSuccess(imMessage: Message) {
                            EventBus.getDefault().postSticky(imMessage)
                            ToastUtil.showShort(R.string.share_successfully)
                            Timber.d("metaCloud  onSuccess imMessage: %s  ", imMessage)
                        }

                        override fun onError(imMessage: Message, errorCode: Int, desc: String?) {
                            Timber.d(
                                "metaCloud  onError imMessage: %s  errorCode:%s",
                                imMessage,
                                errorCode
                            )
                            EventBus.getDefault().postSticky(imMessage)
                        }
                    }
                )
            }
        }
    }
    fun checkNotification() = viewModelScope.launch {
        if (PandoraToggle.isChatPushNotification) {
            val enabled = NotificationManagerCompat.from(application).areNotificationsEnabled()
            NotificationPermissionManager.notificationOpen = enabled
            _openNotification.emit(enabled)
            Analytics.track(
                EventConstants.EVENT_CHAT_PUSH_POST_CLICK,
                map = mapOf("result" to if (enabled) "0" else "1")
            )
        }
    }
}
