package com.socialplay.gpark.ui.developer.viewmodel

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.R
import com.socialplay.gpark.data.repository.DemoRepository
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.maverick.map
import com.socialplay.gpark.ui.core.maverick.plus
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.ui.developer.DemoListFragmentArgs
import com.socialplay.gpark.util.ToastData
import org.koin.android.ext.android.get
import timber.log.Timber

data class DemoListViewModelState(
    val key: String,
    val refresh: Async<List<DemoListItem>> = Uninitialized,
    val loadMore: Async<LoadMoreState> = Uninitialized,
    val msg: String = "",
    val toastMsg: ToastData = ToastData.EMPTY,
    val test: Test = Test(),
    val testCount: Async<Int> = Uninitialized,
    val myGameIds: Async<List<String>> = Uninitialized,
) : MavericksState {

    val list: List<DemoListItem> = refresh.invoke() ?: emptyList()

    constructor(args: DemoListFragmentArgs) : this(args.key)
}

data class Test(val inner: NestTest = NestTest())

data class NestTest(val flag: Boolean = true)

data class DemoListItem(val title: String, val id: Int, val isGroup: Boolean)


class DemoListViewModel(
    private val demoRepository: DemoRepository,
    initialState: DemoListViewModelState
) :
    BaseViewModel<DemoListViewModelState>(initialState) {

    init {
        refresh()

        demoRepository.testStateFlow.execute {
            copy(testCount = it)
        }

        onAsync(DemoListViewModelState::testCount) {
            Timber.d("anxindebug testCount:$it")
            setState {
                copy(
                    toastMsg = if (it % 5 == 0) {
                        toastMsg.toResMsg(R.string.develop_test_toast_mgs)
                    } else if (it % 5 == 1) {
                        toastMsg.toResMsg(R.string.develop_test_toast_mgs_param, it)
                    } else if (it % 5 == 2) {
                        toastMsg.toResMsg(R.string.develop_test_toast_mgs_params, it, it)
                    } else if (it % 5 == 3) {
                        toastMsg.toResMsg(R.string.develop_test_toast_mgs_params, listOf(it, it))
                    } else {
                        toastMsg.toMsg("ok----$it")
                    }
                )
            }
        }

        test()
    }

    fun test() {
        demoRepository.testGetMyGames().execute {
            copy(myGameIds = it)
        }
    }

    fun refresh(pageSize: Int = 20) = withState { oldState ->
        Timber.d("anxindebug refresh")
        if (oldState.refresh is Loading) return@withState
        demoRepository.getDemoList(oldState.key, pageSize, 0)
            .execute(retainValue = DemoListViewModelState::refresh) {
                copy(refresh = it, loadMore = if (it is Success) Uninitialized else loadMore)
            }
    }

    fun loadMore(pageSize: Int = 20) = withState { oldState ->
        Timber.d("anxindebug loadMore")
        if (oldState.list.isEmpty()) return@withState
        if (oldState.loadMore is Loading) return@withState
        demoRepository.getDemoListSuspend(oldState.key, pageSize, oldState.list.last().id + 1)
            .execute { result ->
                copy(
                    refresh = oldState.refresh + result,
                    loadMore = result.map { LoadMoreState(it.isEmpty()) }
                )
            }
    }

    companion object : KoinViewModelFactory<DemoListViewModel, DemoListViewModelState>() {

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: DemoListViewModelState
        ): DemoListViewModel {
            return DemoListViewModel(get(), state)
        }

    }
}