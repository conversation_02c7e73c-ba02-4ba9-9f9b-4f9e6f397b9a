package com.socialplay.gpark.ui.view

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import kotlin.math.abs


/**
 *  <AUTHOR> taiyuan
 *  time   : 2021/7/4
 *  desc   :
 */
open class ElasticConstraintLayout(context: Context, attrs: AttributeSet?) :
    ConstraintLayout(context, attrs) {


    override fun setLayoutParams(params: ViewGroup.LayoutParams?) {
        super.setLayoutParams(params)
    }
}