package com.socialplay.gpark.ui.recommend

import android.view.LayoutInflater
import android.view.ViewGroup
import com.socialplay.gpark.databinding.AdapterAiBotTagBinding
import com.socialplay.gpark.ui.base.adapter.BaseAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder

class AiBotTagItemAdapter:BaseAdapter<String, AdapterAiBotTagBinding>() {
    override fun createViewBinding(
        layoutInflater: LayoutInflater,
        parent: ViewGroup,
        viewType: Int
    ): AdapterAiBotTagBinding {
        return AdapterAiBotTagBinding.inflate(layoutInflater)
    }

    override fun convert(
        holder: BindingViewHolder<AdapterAiBotTagBinding>,
        item: String,
        position: Int
    ) {
        holder.binding.tv.text = item
    }
}