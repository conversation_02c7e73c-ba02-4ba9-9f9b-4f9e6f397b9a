package com.socialplay.gpark.ui.kol.creator

import android.content.Context
import android.view.View
import com.airbnb.epoxy.VisibilityState
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.creator.KolCreatorInfo
import com.socialplay.gpark.databinding.ItemKolMoreCreatorBinding
import com.socialplay.gpark.ui.core.IBaseEpoxyItemListener
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaModelCollector
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTextColorByRes

/**
 * Created by bo.li
 * Date: 2024/8/9
 * Desc: kol 更多创作者列表
 */
interface IKolMoreCreatorAction : IBaseEpoxyItemListener {
    fun goProfile(uuid: String)
    fun changeFollow(uuid: String, toFollow: Boolean)
    fun onItemShow(uuid: String)
}

fun MetaModelCollector.kolMoreCreatorItem(
    pageKey: String,
    item: KolCreatorInfo,
    useFollowFunc: <PERSON><PERSON><PERSON>,
    listener: IKolMoreCreatorAction?
) {
    add(
        KolMoreCreatorItem(
            item,
            useFollowFunc,
            listener
        ).id("${pageKey}_kolMoreCreatorItem_${item.uuid}")
    )
}

/**
 * @param item 用户数据
 * @param useFollowFunc 使用关注功能
 */
data class KolMoreCreatorItem(
    val item: KolCreatorInfo,
    val useFollowFunc: Boolean,
    val listener: IKolMoreCreatorAction?
) : ViewBindingItemModel<ItemKolMoreCreatorBinding>(
    R.layout.item_kol_more_creator,
    ItemKolMoreCreatorBinding::bind
) {

    override fun ItemKolMoreCreatorBinding.onBind() {
        val context = getItemView().context
        includeItem.tvName.text = item.nickname
        includeItem.tvId.text =
            context.getString(R.string.creation_num_param, item.releaseCount.toString())
        listener?.getGlideOrNull()?.run {
            load(item.avatar).placeholder(R.drawable.icon_default_avatar)
                .into(includeItem.ivPortrait)
        }
        root.setOnAntiViolenceClickListener {
            listener?.goProfile(item.uuid)
        }
        initActionBtn(context)
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        super.onVisibilityStateChanged(visibilityState, view)
        if (visibilityState == VisibilityState.VISIBLE) {
            listener?.onItemShow(item.uuid)
        }
    }

    private fun ItemKolMoreCreatorBinding.initActionBtn(context: Context) {
        if (useFollowFunc) {
            if (item.followUser) {
                tvAction.text = context.getString(R.string.following_cap)
                tvAction.setBackgroundResource(R.drawable.bg_round_16_666666_stroke_05)
                tvAction.setTextColorByRes(R.color.textColorPrimary)
            } else {
                tvAction.text = context.getString(R.string.follow)
                tvAction.setBackgroundResource(R.drawable.bg_b884ff_round_16)
                tvAction.setTextColorByRes(R.color.white)
            }
            tvAction.setOnAntiViolenceClickListener {
                listener?.changeFollow(item.uuid, !item.followUser)
            }
        } else {
            tvAction.text = context.getString(R.string.view_cap)
            tvAction.setBackgroundResource(R.drawable.bg_b884ff_round_16)
            tvAction.setTextColorByRes(R.color.white)
            tvAction.setOnAntiViolenceClickListener {
                listener?.goProfile(item.uuid)
            }
        }
    }
}