package com.socialplay.gpark.ui.profile.ugc

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import com.airbnb.epoxy.VisibilityState
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.editor.ProfileMapTab
import com.socialplay.gpark.data.model.editor.UgcGameInfo
import com.socialplay.gpark.databinding.ItemProfileMapsBinding
import com.socialplay.gpark.databinding.ItemProfileMapsTabBinding
import com.socialplay.gpark.ui.core.IBaseEpoxyItemListener
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaEpoxyController
import com.socialplay.gpark.ui.view.MetaTextView
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTextWithArgs
import com.socialplay.gpark.util.extension.unsetOnClick
import com.socialplay.gpark.util.extension.visible

interface IProfileUgcItemAction : IBaseEpoxyItemListener {
    fun showPopupMore(item: UgcGameInfo.Games, position: Int, anchorView: View)
    fun goGameDetail(item: UgcGameInfo.Games, position: Int)
    fun onItemVisibilityChange(item: UgcGameInfo.Games, position: Int)
    fun onClickTab(item: ProfileMapTab, position: Int)
}

fun MetaEpoxyController.profileMapTab(
    item: ProfileMapTab,
    gameType: Int,
    position: Int,
    listListener: IProfileUgcItemAction?
) {
    add {
        ProfileMapTabItem(item, gameType, position, listListener).id("ProfileMapTab-${item.type}")
    }
}

data class ProfileMapTabItem(
    val item: ProfileMapTab,
    val gameType: Int,
    val position: Int,
    val listListener: IProfileUgcItemAction?
) : ViewBindingItemModel<ItemProfileMapsTabBinding>(
    R.layout.item_profile_maps_tab,
    ItemProfileMapsTabBinding::bind
) {

    override fun ItemProfileMapsTabBinding.onBind() {
        root.setText(item.titleRes)
        root.isSelected = item.type == gameType
        root.setOnAntiViolenceClickListener {
            listListener?.onClickTab(item, position)
        }
    }

    override fun ItemProfileMapsTabBinding.onUnbind() {
        root.unsetOnClick()
    }
}

fun MetaEpoxyController.publishedUgc(
    item: UgcGameInfo.Games,
    position: Int,
    isMe: Boolean,
    listListener: IProfileUgcItemAction?,
) {
    add(PublishedUgc(item, position, isMe, listListener).id("publishedUgc_${item.id}"))
}

data class PublishedUgc(
    val item: UgcGameInfo.Games,
    val position: Int,
    val isMe: Boolean,
    val listListener: IProfileUgcItemAction?,
) : ViewBindingItemModel<ItemProfileMapsBinding>(
    R.layout.item_profile_maps,
    ItemProfileMapsBinding::bind
) {

    override fun ItemProfileMapsBinding.onBind() {
        listListener?.getGlideOrNull()?.run {
            load(item.banner).placeholder(R.drawable.placeholder_corner_12)
                .into(ivBanner)
        }
        ivPinLabel.visible(item.topOn)
        ivMoreBtn.visible(isMe)
        ivMoreBtn.setOnAntiViolenceClickListener {
            listListener?.showPopupMore(item, position, it)
        }
        tvPv.text = UnitUtil.formatPlayerCount(item.pvCount)
        tvName.text = item.ugcGameName
        initLabelList(item)
        root.setOnAntiViolenceClickListener {
            listListener?.goGameDetail(item, position)
        }
    }

    override fun ItemProfileMapsBinding.onUnbind() {
        root.unsetOnClick()
        flLabel.removeAllViews()
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        super.onVisibilityStateChanged(visibilityState, view)
        if (visibilityState == VisibilityState.VISIBLE || visibilityState == VisibilityState.INVISIBLE) {
            listListener?.onItemVisibilityChange(item, position)
        }
    }

    private fun ItemProfileMapsBinding.initLabelList(
        item: UgcGameInfo.Games
    ) {
        flLabel.removeAllViews()
        item.gameTagList?.mapNotNull { it.name }?.take(2)?.forEach {
            flLabel.addView(getLabelView(root.context, it))
        }
    }

    private fun getLabelView(context: Context, genre: String): View {
        val tv = LayoutInflater.from(context).inflate(R.layout.item_profile_maps_tag_genre, null)
        (tv as? MetaTextView)?.apply {
            text = genre
        }
        return tv
    }
}