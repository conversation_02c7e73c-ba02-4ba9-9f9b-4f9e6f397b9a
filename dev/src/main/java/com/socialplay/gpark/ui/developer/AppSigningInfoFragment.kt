package com.socialplay.gpark.ui.developer

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.os.Bundle
import android.util.Base64
import android.view.LayoutInflater
import android.view.ViewGroup
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.FragmentAppSigningInfoBinding
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.util.SigningUtil
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.property.viewBinding
import timber.log.Timber

class AppSigningInfoFragment : BaseFragment<FragmentAppSigningInfoBinding>() {

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentAppSigningInfoBinding? {
        return FragmentAppSigningInfoBinding.inflate(inflater, container, false)
    }

    override fun init() {
        binding.etPackageName.setText(requireActivity().packageName)
        binding.generate.setOnAntiViolenceClickListener {
            try {
                generate()
            } catch (e: Exception) {
                e.printStackTrace()
                ToastUtil.showLong(requireContext(),e.message)
            }
        }
        binding.btnCopySha1.setOnClickListener { copyPlainText(binding.tvSha1.text.toString()) }
        binding.btnCopySha1Base64.setOnClickListener { copyPlainText(binding.tvSha1Base64.text.toString()) }
        binding.btnCopyMd5.setOnClickListener { copyPlainText(binding.tvMd5.text.toString()) }

    }

    private fun generate() {
        val packageName = binding.etPackageName.text.toString()
        val signature: ByteArray = SigningUtil.getSignature(requireContext(), packageName)



        val sha1 = signature.joinToString(":") {String.format("%02X", it)}
        val base64Sha1 = Base64.encodeToString(signature, Base64.DEFAULT)

        Timber.i("SHA1:%s", sha1)
        Timber.i("SHA1 base64:%s", base64Sha1)

        var md5 = SigningUtil.getSignatureString(requireContext(), packageName, SigningUtil.MD5)
        Timber.i("MD5:%s", md5)
        Timber.i("SHA256:%s", SigningUtil.getSignatureString(requireContext(), packageName, SigningUtil.SHA256))

        binding.tvMd5.text = md5
        binding.tvSha1.text = sha1
        binding.tvSha1Base64.text = base64Sha1
    }

    private fun copyPlainText(text: String) {
        val clipboardManager = requireContext().getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager?
        val clipData = ClipData.newPlainText("CampaignWebLabel", text)
        clipboardManager?.let {
            it.setPrimaryClip(clipData)
            ToastUtil.showShort(requireContext(), R.string.content_copied)
        }
    }

    override fun loadFirstData() {
    }


    override fun getFragmentName() = PageNameConstants.FRAGMENT_NAME_DEV_APP_SIG_INFO
}