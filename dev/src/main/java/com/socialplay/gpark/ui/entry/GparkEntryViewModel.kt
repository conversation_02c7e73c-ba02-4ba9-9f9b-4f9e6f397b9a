package com.socialplay.gpark.ui.entry
import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.SdkInteractor
import com.socialplay.gpark.data.model.sdk.AuthAppInfo
import com.socialplay.gpark.data.model.sdk.resp.AuthResp
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import org.koin.core.context.GlobalContext
import timber.log.Timber

class GparkEntryViewModel(private val accountInteractor: AccountInteractor,
                          private val repository: IMetaRepository,
                          private val sdkInteractor: SdkInteractor
) : ViewModel() {

    private val _appVerifyResult = MutableStateFlow<VerifyAppKeyState?>(null)
    val appVerifyResult: StateFlow<VerifyAppKeyState?> = _appVerifyResult

    private val _authResp = MutableStateFlow<AuthResp?>(null)
    val authResp: StateFlow<AuthResp?> = _authResp

    var authAppInfo: AuthAppInfo? = null

    fun verifyAppKey() {
        viewModelScope.launch {
            Timber.i("verify appkey")
            repository.authAppCheck(sdkInteractor.sdkRequestParams.appKey ?: "").collect {
                if (it.succeeded) {
                    authAppInfo = it.data
                    _appVerifyResult.value = VerifyAppKeyState.Success
                } else {
                    _appVerifyResult.value = VerifyAppKeyState.Failed(it.message ?: GlobalContext.get().get<Context>().getString(R.string.unknown_error))
                }
            }
        }
    }

    fun notifyAuthResp(resp: AuthResp) {
        _authResp.value = resp
    }

    fun isRealLogin() = accountInteractor.isRealLoginPermissionButVisitor()

    sealed class VerifyAppKeyState {
        object Success : VerifyAppKeyState()
        data class Failed(val message: String) : VerifyAppKeyState()
    }
}
