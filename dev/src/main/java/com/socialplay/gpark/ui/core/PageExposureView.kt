package com.socialplay.gpark.ui.core


import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import org.koin.core.context.GlobalContext
import timber.log.Timber

private const val TAG = "PageExposureTracker"

interface PageExposureView : LifecycleOwner {

    fun registerPageExposureTracker() {
        PageExposureLifecycleTracker.startReceiver(this)
    }

    fun getPageName(): String

    fun onNewDuration(duration: Long) {

    }

    fun isEnableTrackPageExposure(): Boolean = true

    fun getPageExtraParams(): Map<String, String> = emptyMap()
}

private class PageExposureLifecycleTracker(val pageName: String, val pageExposureView: PageExposureView) : DefaultLifecycleObserver {

    companion object {
        private val viewsWithRegisteredReceivers = mutableSetOf<PageExposureView>()

        fun startReceiver(pageExposureView: PageExposureView) {
            val pageName = pageExposureView.getPageName()
            val enableTrackPageExposure = pageExposureView.isEnableTrackPageExposure()
            Timber.tag(TAG).d("startReceiver pageName:%s, enable:%s", pageName, enableTrackPageExposure)
            if (enableTrackPageExposure) {
                PageExposureLifecycleTracker(pageName, pageExposureView)
            }
        }
    }

    init {
        if (viewsWithRegisteredReceivers.add(pageExposureView)) {
            pageExposureView.lifecycle.addObserver(this)
        }
    }


    private var resumeTime = 0L
    private val metaKV by lazy { GlobalContext.get().get<MetaKV>() }

    override fun onResume(owner: LifecycleOwner) {
        Timber.tag(TAG).d("onResume pageName:%s", pageName)
        resumeTime = System.currentTimeMillis()
        //页面展示
        Analytics.track(EventConstants.EVENT_SHOW_PAGE){
            put("pageName", pageName)
        }
    }

    override fun onPause(owner: LifecycleOwner) {
        metaKV.analytic.setFirstAppOpenTime()

        val duration = System.currentTimeMillis() - resumeTime
        Analytics.track(EventConstants.EVENT_APP_PAGE_TIME){
            put("playtime", duration)
            put("pagename", pageName)
        }
        pageExposureView.onNewDuration(duration)

        // 上报 次留 3日留存 7日留存
        val appDayCount = metaKV.analytic.getAppDayCount()

        val analytics = listOf(
            2 to EventConstants.EVENT_AF_APP_TIME_1,
            3 to EventConstants.EVENT_AF_APP_TIME_2,
            4 to EventConstants.EVENT_AF_APP_TIME_3,
            8 to EventConstants.EVENT_AF_APP_TIME_7,
        )

        analytics.forEach {
            val day = it.first
            val event = it.second

            if (appDayCount >= day && !metaKV.analytic.isDayCountTracked(day)) {
                Analytics.track(event)
                metaKV.analytic.setDayCountTracked(day)
            }
        }
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        viewsWithRegisteredReceivers.remove(pageExposureView)
    }

}