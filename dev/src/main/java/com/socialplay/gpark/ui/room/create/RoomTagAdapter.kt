package com.socialplay.gpark.ui.room.create

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import com.chad.library.adapter.base.BaseQuickAdapter
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.ItemTagRoomBinding
import com.socialplay.gpark.ui.base.adapter.BaseAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder

/**
 * @des:
 * @author: lijunjia
 * @date: 2023/7/13 16:49
 */
class RoomTagAdapter : BaseAdapter<HomeCreateRoomViewModel.RoomTag, ItemTagRoomBinding>() {
    override fun createViewBinding(
        layoutInflater: LayoutInflater,
        parent: ViewGroup,
        viewType: Int
    ): ItemTagRoomBinding {
        return ItemTagRoomBinding.inflate(layoutInflater, parent, false)
    }

    override fun convert(
        holder: BindingViewHolder<ItemTagRoomBinding>,
        item: HomeCreateRoomViewModel.RoomTag,
        position: Int
    ) {
        holder.binding.tvRoomTag.apply {
            text = item.tag
            if (item.isSelected) {
                setTextColor(ContextCompat.getColor(context, R.color.color_8c3ff5))
                setBackgroundResource(R.drawable.shape_stroke_8c3ff5_round)
            } else {
                setTextColor(ContextCompat.getColor(context, R.color.textColorPrimary))
                setBackgroundResource(R.drawable.shape_stroke_e6e6e6_round)
            }

        }

    }
}