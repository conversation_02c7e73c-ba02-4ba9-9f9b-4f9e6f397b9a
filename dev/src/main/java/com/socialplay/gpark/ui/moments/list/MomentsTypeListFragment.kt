package com.socialplay.gpark.ui.moments.list

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.GridLayoutManager
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.withState
import com.chad.library.adapter.base.loadmore.LoadMoreStatus
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.moments.PlotTemplate
import com.socialplay.gpark.databinding.FragmentMomentTypeBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.ui.core.maverick.map
import com.socialplay.gpark.ui.moments.base.BaseMomentsFragment

/**
 * 2024/1/31
 */
class MomentsTypeListFragment :
    BaseMomentsFragment<FragmentMomentTypeBinding>(R.layout.fragment_moment_type) {

    private val momentsListViewModel: MomentsListViewModel by fragmentViewModel()
    private val adapter by lazy { MomentsListAdapter() }
    private val args by navArgs<MomentsTypeListFragmentArgs>()

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentMomentTypeBinding? {
        return FragmentMomentTypeBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.titleOC.setTitle(args.title)
        binding.titleOC.setOnBackClickedListener {
            findNavController().popBackStack()
        }

        binding.rvList.layoutManager = GridLayoutManager(requireContext(), 2)
        binding.rvList.adapter = adapter
        adapter.setOnItemClickListener { adapter, view, position ->
            val item = adapter.getItem(position)
            if (item is PlotTemplate) {
                Analytics.track(EventConstants.EVENT_MOMENTS_TEMPLATE_CLICK) {
                    put("show_categoryid", args.categoryId)
                    put("templateid", item.templateId)
                    put("templatetype", item.templateType)
                    put("templatename", item.templateName)
                }
                momentsListViewModel.plotTemplateLoveDo(
                    "${item.templateId}",
                    MomentsListUiState::allList
                ) {
                    copy(allList = it.map { item -> item.map { temp -> temp } })
                }
                startPlotGame(
                    item.gameId,
                    item.templateName,
                    "${item.templateId}",
                    item.extraConfig
                )
            }
        }

        adapter.loadMoreModule.apply {
            isEnableLoadMore = true
            setOnLoadMoreListener {
                momentsListViewModel.loadMorePlotAllList(args.type)
            }
        }
        momentsListViewModel.setupRefreshLoading(
            MomentsListUiState::allList,
            binding.loadingOC,
            null,
            onRefresh = {
                momentsListViewModel.featPlotAllList(args.type)
            })
        momentsListViewModel.onEach(
            MomentsListUiState::allList, MomentsListUiState::allListLoadStatus
        ) { allList, status ->
            val list = allList.invoke()
            adapter.setList(list)
            when (status) {
                LoadMoreStatus.Loading -> adapter.loadMoreModule.loadMoreEnd(false)
                LoadMoreStatus.Complete -> adapter.loadMoreModule.loadMoreComplete()
                LoadMoreStatus.End -> adapter.loadMoreModule.loadMoreEnd(true)
                LoadMoreStatus.Fail -> adapter.loadMoreModule.loadMoreFail()
            }
        }

        momentsListViewModel.featPlotAllList(args.type)
    }

    override fun invalidate() = withState(momentsListViewModel) {
    }

    override fun getPageName(): String = PageNameConstants.FRAGMENT_MOMENTS_LIST
}