package com.socialplay.gpark.ui.kol.list.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.creator.label.KolCreatorLabel
import com.socialplay.gpark.databinding.AdapterKolCreatorUgcLabelBinding
import com.socialplay.gpark.ui.base.adapter.BaseDifferAdapter
import com.socialplay.gpark.ui.base.adapter.BaseVBViewHolder
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.setTextColorByRes

/**
 * Created by bo.li
 * Date: 2024/8/5
 * Desc: Kol创作者标签选择器
 */
class KolCreatorLabelAdapter(
    private val glide: RequestManager
) : BaseDifferAdapter<KolCreatorLabel, AdapterKolCreatorUgcLabelBinding>(DIFF_CALLBACK) {

    companion object {

        private val DIFF_CALLBACK = object : DiffUtil.ItemCallback<KolCreatorLabel>() {
            override fun areItemsTheSame(
                oldItem: KolCreatorLabel,
                newItem: KolCreatorLabel
            ): Boolean {
                return oldItem.tagId == newItem.tagId
            }

            override fun areContentsTheSame(
                oldItem: KolCreatorLabel,
                newItem: KolCreatorLabel
            ): Boolean {
                return oldItem == newItem
            }
        }
    }

    override fun convert(
        holder: BaseVBViewHolder<AdapterKolCreatorUgcLabelBinding>,
        item: KolCreatorLabel
    ) {
        if (item.localSelected) {
            holder.binding.llLabel.setBackgroundResource(R.drawable.bg_corner_360_black)
            holder.binding.tvLabel.setTextColorByRes(R.color.white)
        } else {
            holder.binding.llLabel.setBackgroundResource(R.drawable.bg_f0f0f0_corner_360)
            holder.binding.tvLabel.setTextColorByRes(R.color.neutral_color_3)
        }
        holder.binding.tvLabel.text = item.title
    }

    override fun viewBinding(parent: ViewGroup, viewType: Int): AdapterKolCreatorUgcLabelBinding {
        return AdapterKolCreatorUgcLabelBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    }

}