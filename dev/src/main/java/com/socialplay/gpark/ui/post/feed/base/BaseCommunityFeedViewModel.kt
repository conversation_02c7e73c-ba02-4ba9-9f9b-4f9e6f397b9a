package com.socialplay.gpark.ui.post.feed.base

import android.util.SparseArray
import androidx.core.util.forEach
import androidx.core.util.isNotEmpty
import com.airbnb.mvrx.Success
import com.meta.box.biz.friend.internal.model.FriendStatus
import com.meta.box.biz.friend.model.FriendInfo
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.ApiDataException
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.post.CommunityFeedInfo
import com.socialplay.gpark.data.model.post.OpinionRequestBody
import com.socialplay.gpark.data.model.post.PostStyleCard
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.util.ToastError
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * @exception [java.lang.reflect.InvocationTargetException] 继承BaseCommunityFeedViewModel时，init代码块里加东西，可能会导致fragmentViewModel找不到类
 */
abstract class BaseCommunityFeedViewModel<S>(
    private val repository: IMetaRepository,
    private val accountInteractor: AccountInteractor,
    initialState: S
) : BaseViewModel<S>(initialState) where S : ICommunityFeedModelState {

    fun updateFriendState(friendInfoList: List<FriendInfo>) {
        val feedInfoList = oldState.refresh.invoke() ?: return

        val map: MutableMap<String, FriendInfo> = mutableMapOf()
        friendInfoList.forEach {
            map[it.uuid] = it
        }
        val updateRecord: SparseArray<FriendStatus> = SparseArray()
        feedInfoList.forEachIndexed { index, communityFeedInfo ->
            val friendStatus = map[communityFeedInfo.uid]?.status
            val friendStatusFlag = friendStatus?.status
            if (friendStatus != null) {
                if (friendStatusFlag != communityFeedInfo.userStatus?.status
                    || friendStatus.gameStatus != communityFeedInfo.userStatus?.gameStatus
                ) {
                    updateRecord.put(index, friendStatus)
                }
            }
        }
        if (updateRecord.isNotEmpty()) {
            val newFeedInfoList = feedInfoList.toMutableList()
            updateRecord.forEach { index, friendStatus ->
                newFeedInfoList[index] = newFeedInfoList[index].copy(
                    userStatus = friendStatus
                )
            }
            setState {
                updateFeedData(newFeedInfoList) as S
            }
        }
    }
    /**
     * 点赞，取消点赞
     */
    fun changePostLike(toLike: Boolean, postId: String, location: String, tagList: List<PostTag>?) {
        Analytics.track(EventConstants.EVENT_POST_LIKE_CLICK) {
            put(EventParamConstants.KEY_POSTID, postId)
            put(EventParamConstants.KEY_LOCATION, location)
            put(EventParamConstants.KEY_TYPE, if (toLike) EventParamConstants.V_LIKE else EventParamConstants.V_UNLIKE)
            tagList?.map { it.tagId }?.let { put(EventParamConstants.KEY_TAG_LIST, it) }
        }
        viewModelScope.launch {
            val oldState = awaitState()
            if (oldState.refresh !is Success) return@launch
            try {
                val succeed =
                    repository.saveOpinion(OpinionRequestBody.postLike(postId, toLike)).invoke()
                if (succeed) {
                    val operateList = ArrayList(oldState.refresh.invoke() ?: emptyList())
                    val oldIndex = operateList.indexOf(operateList.find { it.postId == postId })
                    if (oldIndex in 0 until operateList.size) {
                        val oldItem = operateList[oldIndex]
                        operateList[oldIndex] = oldItem.copy(
                            likeCount = oldItem.likeCount + if (toLike) 1 else -1,
                            opinion = CommunityFeedInfo.getOpinion(toLike)
                        )
                    }
                    setState {
                        updateFeedData(operateList.toList()) as S
                    }
                } else {
                    throw ApiDataException(Boolean::class)
                }
            } catch (e: Throwable) {
                setState {
                    toast(ToastError(e)) as S
                }
            }
        }
    }

    /**
     * 从帖子详情回来，更新feed数据
     */
    fun handleChangeFromDetail(
        postId: String,
        deleted: Boolean,
        opinion: Int,
        likeCount: Long,
        commentCount: Long,
        shareCount: Long
    ) {
        withState { oldState ->
            if (oldState.refresh !is Success || oldState.refresh().isNullOrEmpty()) return@withState
            val operateList = ArrayList(oldState.refresh.invoke())
            if (deleted) {
                operateList.removeAll { it.postId == postId }
                setState {
                    updateFeedData(operateList.toList()) as S
                }
            } else {
                val oldIndex = operateList.indexOf(operateList.find { it.postId == postId })
                if (oldIndex in 0 until operateList.size) {
                    val oldItem = operateList[oldIndex]
                    if (oldItem.likeCount != likeCount || oldItem.opinion != opinion || oldItem.commentCount != commentCount || oldItem.shareCount != shareCount) {
                        operateList[oldIndex] = oldItem.copy(
                            likeCount = likeCount,
                            opinion = opinion,
                            commentCount = commentCount,
                            shareCount = shareCount
                        )
                        setState {
                            updateFeedData(operateList.toList()) as S
                        }
                    }
                }
            }
        }
    }

    fun visitOutfitCard(item: CommunityFeedInfo, outfit: PostStyleCard) {
        Analytics.track(
            EventConstants.POST_LIST_OUTFIT_CLICK,
            "uuid" to item.uid,
            "resid" to item.postId,
            "tag" to item.tagList?.map { it.tagId }?.joinToString(",").orEmpty(),
            "shareid" to outfit.roleId,
            "source" to "0"
        )
        if (!accountInteractor.isMe(item.uid)) {
            GlobalScope.launch {
                runCatching {
                    repository.visitPostOutfitCard(item.postId, outfit.roleId).invoke()
                }
            }
        }
    }

    fun addShareCount(postId: String) = viewModelScope.launch {
        val operateList = ArrayList(oldState.refresh.invoke() ?: emptyList())
        val oldIndex = operateList.indexOf(operateList.find { it.postId == postId })
        if (oldIndex in 0 until operateList.size) {
            val oldItem = operateList[oldIndex]
            operateList[oldIndex] = oldItem.copy(
                shareCount = oldItem.shareCount + 1
            )
            setState {
                updateFeedData(operateList.toList()) as S
            }
        }
    }

    /**
     * 通知检查feed播放视频状态
     */
    fun notifyCheckVideo() {
        suspend {
            delay(1000)
            System.currentTimeMillis()
        }.execute {
            checkVideo(it) as S
        }
    }
}