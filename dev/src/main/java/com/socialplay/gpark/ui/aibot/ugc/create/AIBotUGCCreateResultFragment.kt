package com.socialplay.gpark.ui.aibot.ugc.create

import android.os.Bundle
import android.os.CountDownTimer
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.addCallback
import androidx.core.view.isVisible
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.model.aibot.AIBotCreateImageInfo
import com.socialplay.gpark.data.model.aibot.AiBotSelectResultEvent
import com.socialplay.gpark.databinding.FragmentAiBotCreateResultBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.aibot.ugc.AIBotUGCCreateViewModel
import com.socialplay.gpark.ui.aibot.ugc.AIBotUGCCreateViewModel.Companion.SELECT_IMAGE
import com.socialplay.gpark.ui.aibot.ugc.adapter.AIBotBgAdapter
import com.socialplay.gpark.ui.aibot.ugc.adapter.AIBotCreateImageAdapter
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.ui.view.center.CenterLayoutManager
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.collectWithLifecycleOwner
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.navigateUp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.property.viewBinding
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.koin.androidx.viewmodel.ext.android.viewModel
import timber.log.Timber

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/08/26
 *     desc   :
 *
 */
class AIBotUGCCreateResultFragment : BaseFragment<FragmentAiBotCreateResultBinding>() {
    private val viewModel by viewModel<AIBotUGCCreateResultViewModel>()
    private val adapter: AIBotCreateImageAdapter by lazy { AIBotCreateImageAdapter() }
    private val bgAdapter by lazy { AIBotBgAdapter() }
    private var indexPosition = 0
    val args = navArgs<AIBotUGCCreateResultFragmentArgs>()

    private val adapterBigPageChangeCallback = object : ViewPager2.OnPageChangeCallback() {

        override fun onPageSelected(position: Int) {
            // 大图选中后，小图需要定位
            viewModel.changeResultSelect(adapter.getItem(position),position)
            onRvSelect(position, true)
        }

        override fun onPageScrolled(
            position: Int,
            positionOffset: Float,
            positionOffsetPixels: Int
        ) {

        }

        override fun onPageScrollStateChanged(state: Int) {

        }
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentAiBotCreateResultBinding? {
        return FragmentAiBotCreateResultBinding.inflate(inflater, container, false)
    }

    override fun init() {
        initView()
        initData()
    }

    private fun initView() {
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner) {
            // 拦截
            if (viewModel.generateCreateInfo.value != null) {
                if (viewModel.generateCreateInfo.value?.succeeded == true || viewModel.generateCreateInfo.value?.succeeded == false) {
                    goBack()
                }
            }
        }
        binding.imgBack.setOnAntiViolenceClickListener {
            goBack()
        }
        binding.clLoading.setOnAntiViolenceClickListener {

        }
        Glide.with(this).load(BuildConfig.CND_AB_BOT_UGC_TOP_BG).into(binding.bgLineAiBotUgcBack)
        Glide.with(this).load(BuildConfig.CND_AB_BOT_UGC_BOTTOM_BG).transform(CenterCrop(), RoundedCorners(10.dp)).into(binding.bgLineAiBotUgcLoading)
        binding.tvGenerate.setOnAntiViolenceClickListener {
            val data = viewModel.resultSelect.value ?: return@setOnAntiViolenceClickListener
            data.gender = (viewModel.aiBotCreateRequest.value?.userInfo?.gender ?: "0").toInt()
            data.selectType = viewModel.aiBotCreateRequest.value?.type
            MetaRouter.AiBot.gotoAIBotSelect(
                this,
                GsonUtil.safeToJson(data, "")
            )
            val info = getAllInfo()
            Analytics.track(EventConstants.EVENT_AI_BOT_AVATAR__GENERATE_CLICK,
                map =
                 mapOf(
                     "url" to (data.backgroundImage?:""),
                     "capacity" to (data.capacity?:""),
                      "sel_model_name" to (data.modelName?:""),
                      "un_sel_url" to info.first,
                      "un_sel_model_name" to info.second,
                      "generate_type" to getGenerateType(data.selectType)
                 ))
        }
        binding.ryImage.layoutManager = CenterLayoutManager(
            requireContext(),
            LinearLayoutManager.HORIZONTAL, false
        )
        binding.ryImage.adapter = adapter
        binding.imgBg.adapter = bgAdapter
        binding.imgBg.registerOnPageChangeCallback(adapterBigPageChangeCallback)
        adapter.setOnItemClickListener { view, position ->
            viewModel.changeResultSelect(adapter.getItem(position), position)
            onRvSelect(position, false)
        }

    }
    private fun goBack(){
        navigateUp()
        val type = viewModel.aiBotCreateRequest.value?.type
        Analytics.track(
            EventConstants.EVENT_AI_BOT_GENERATE_BACK,
            mapOf("generate_type" to getGenerateType(type))
        )
    }
    private fun getAllInfo(): Pair<String, String> {
        val unSelUrl = StringBuilder("")
        val unSelModel = StringBuilder("")
        viewModel.generateCreateInfo.value?.data?.forEachIndexed { index, aiBotCreateImageInfo ->
            if (aiBotCreateImageInfo.backgroundImage != viewModel.resultSelect.value?.backgroundImage) {
                unSelUrl.append(aiBotCreateImageInfo.backgroundImage).append(",")
                unSelModel.append(aiBotCreateImageInfo.modelName).append(",")
            }
        }
        return unSelUrl.toString() to unSelModel.toString()
    }
    private fun getGenerateType(type: Int?): String {
        return if (type == SELECT_IMAGE) {
            "image 2 image"
        } else {
            "text 2 image"
        }
    }

    private fun onRvSelect(position: Int, fromVp: Boolean) {
        val layoutManager = binding.ryImage.layoutManager as CenterLayoutManager
        layoutManager.smoothScrollToPosition(
            binding.ryImage,
            RecyclerView.State(), indexPosition, position
        )
        if (indexPosition != position) {
            indexPosition = position
        }
        if (!fromVp) {
            setSelectFragmentByPosition(position)
        }
    }

    private fun setSelectFragmentByPosition(position: Int) {
        binding.imgBg.setCurrentItem(position, true)
    }

    private fun initData() {
        viewModel.generateCreateInfo.observe(viewLifecycleOwner) {
            if (it != null) {
                if (it.succeeded) {
                    updateResult(it.data, true)
                } else {
                    ToastUtil.showShort(it.message)
                    this.findNavController().popBackStack()
                    updateErrorStatus()
                }
            }
        }
        viewModel.generateProgress.observe(viewLifecycleOwner) {
            binding.tvProgress.text =
                getString(R.string.ai_bot_generate_loading) + " " + it + "%"
        }
        viewModel.bigImageList.observe(viewLifecycleOwner) {
            bgAdapter.setList(it)
        }
        viewModel.smallImageList.observe(viewLifecycleOwner) {
            adapter.setList(it)
        }

    }
    @Subscribe
    fun onEvent(event: AiBotSelectResultEvent) {
        Timber.d("AiBotCreateResultEvent")
        this.findNavController().popBackStack()
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

    override fun loadFirstData() {
        updateResult(null, false)
        viewModel.generate(args.value.data ?: "", requireContext())

    }


    private fun updateResult(data: List<AIBotCreateImageInfo>?, isFinish: Boolean) {
        if (isFinish) {
            binding.clLoading.visibility = View.GONE
            binding.loadBg.visibility = View.GONE
            binding.result.visibility = View.VISIBLE
            binding.tvGenerate.text = getString(R.string.pickerview_submit)
            binding.tvGenerate.isEnabled = true
            binding.tvGenerate.alpha = 1f
            binding.imgBack.alpha = 1f
            binding.imgBack.isEnabled = true
            binding.tvProgress.text = getString(R.string.ai_bot_generate_loading)
        } else {
            binding.tvProgress.text = getString(R.string.ai_bot_generate_loading) + " 1%"
            binding.clLoading.visibility = View.VISIBLE
            binding.loadBg.visibility = View.VISIBLE
            binding.result.visibility = View.GONE
            binding.imgBack.alpha = 0.5f
            binding.imgBack.isEnabled = false
            binding.tvGenerate.text = getString(R.string.ai_bot_generate)
            binding.tvGenerate.isEnabled = false
            binding.tvGenerate.alpha = 0.5f
        }
    }
    private fun updateErrorStatus(){
        binding.clLoading.visibility = View.VISIBLE
        binding.loadBg.visibility = View.VISIBLE
        binding.result.visibility = View.GONE
        binding.imgBack.alpha = 1f
        binding.imgBack.isEnabled = true
        binding.tvProgress.text = getString(R.string.ai_bot_generate_loading)
    }

    override fun onDestroyView() {
        if( binding.imgLoading.isAnimating){
            binding.imgLoading.cancelAnimation()
        }
        super.onDestroyView()
    }


    override fun getFragmentName(): String = PageNameConstants.FRAGMENT_AI_BOT_CREATE_RESULT

}