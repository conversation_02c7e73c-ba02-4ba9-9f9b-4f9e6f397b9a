package com.socialplay.gpark.ui.manage

import com.bumptech.glide.RequestManager
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.MyPlayedGame
import com.socialplay.gpark.databinding.ItemContinueManageBinding
import com.socialplay.gpark.ui.core.GlideGetter
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener

/**
 * 2024/7/30
 */
data class MyPlayGameFeed(
    private val glide: GlideGetter,
    val item: MyPlayedGame,
    val onDelete: (MyPlayedGame) -> Unit,
) : ViewBindingItemModel<ItemContinueManageBinding>(
    R.layout.item_continue_manage,
    ItemContinueManageBinding::bind
) {
    override fun ItemContinueManageBinding.onBind() {
        tvGameDelete.setOnAntiViolenceClickListener { onDelete.invoke(item) }
        glide()?.run {
            load(item.icon)
                .transform(CenterCrop(), RoundedCorners(ScreenUtil.dp2px(root.context, 16F)))
                .into(ivGameIcon)
        }
        tvGameName.text = item.name
    }
}