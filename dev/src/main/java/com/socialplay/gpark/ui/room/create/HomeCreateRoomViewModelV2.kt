package com.socialplay.gpark.ui.room.create

import android.content.ComponentCallbacks
import android.content.Context
import android.os.SystemClock
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.airbnb.mvrx.withState
import com.meta.biz.ugc.model.EditorConfigJsonEntity
import com.meta.biz.ugc.model.EditorTemplate
import com.meta.biz.ugc.model.UgcDraftInfo
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.PagingApiResult
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.EditorInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.data.model.editor.EditorCreationShowInfo
import com.socialplay.gpark.data.model.room.RoomStyle
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.maverick.copyEx
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.extension.dropAtWithResult
import com.socialplay.gpark.util.extension.insertAt
import com.socialplay.gpark.util.extension.replaceAt
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.singleOrNull
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.koin.android.ext.android.get
import org.koin.core.context.GlobalContext
import java.io.File

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/12/18
 *     desc   :
 * </pre>
 */
data class HomeCreateRoomState(
    val styles: Async<List<RoomStyle>> = Uninitialized,
    val curStyle: RoomStyle? = null,
    val rooms: Async<PagingApiResult<EditorCreationShowInfo>> = Uninitialized,
    val roomsLoadMore: Async<LoadMoreState> = Uninitialized,
    val curRoom: EditorCreationShowInfo? = null,
    val lastRoomId: String? = null,
    val draftPair: Pair<UgcDraftInfo?, Long> = null to 0,
    val templateId: String? = null,
    val template: Async<EditorTemplate> = Uninitialized,
    val scrollToFirst: Long = -1
) : MavericksState

class HomeCreateRoomViewModelV2(
    initialState: HomeCreateRoomState,
    private val repo: IMetaRepository,
    private val metaKV: MetaKV,
    private val editorInteractor: EditorInteractor
) : BaseViewModel<HomeCreateRoomState>(initialState) {

    private val gameSet = HashSet<String>()
    private val fileIdSet = HashSet<String>()

    val curStyle: RoomStyle?
        get() = withState(this) { it.curStyle ?: it.curRoom?.ugcInfo?.toRoomStyle() }

    val uuid: String
        get() = metaKV.account.uuid

    val oldRoomsResultAsync
        get() = withState(this) { it.rooms }
    val oldRoomsResult
        get() = oldRoomsResultAsync.invoke()

    init {
        getStyles()
        if (PandoraToggle.enableUgcLiveRoom) {
            getTemplateId()
            getDrafts()
        }
    }

    fun getStyles() = withState { s ->
        if (s.styles is Loading) return@withState
        val cache = metaKV.tTaiKV.homeRoomGame
        if (cache.isNotBlank()) {
            val styles = convert2Style(cache)
            setState {
                copy(
                    styles = Success(styles),
                    curStyle = styles.firstOrNull()
                )
            }
        } else {
            repo.getTTaiConfigById(TTaiKV.ID_HOME_ROOM_GAME)
                .map {
                    it.data?.let { tt ->
                        metaKV.tTaiKV.saveConfig(tt)
                        convert2Style(tt.value)
                    } ?: emptyList()
                }
                .execute {
                    if (it is Success) {
                        copy(
                            styles = it,
                            curStyle = it.invoke().firstOrNull()
                        )
                    } else {
                        copy(styles = it)
                    }
                }
        }
    }

    fun convert2Style(json: String?): List<RoomStyle> {
        return GsonUtil.gsonSafeParseCollection<List<RoomStyle>>(json) ?: emptyList()
    }

    fun selectStyle(style: RoomStyle) = withState { s ->
        if (s.curStyle == style) return@withState
        setState { copy(curStyle = style, curRoom = null) }
    }

    fun getDrafts(fromGame: Boolean = false, fileId: String? = null) = viewModelScope.launch {
        var scroll = -1L
        val isEdit = !fileId.isNullOrBlank()
        val drafts = editorInteractor.getLocalCreationListFlow(
            gameSet,
            type = EditorConfigJsonEntity.TYPE_CHAT_ROOM,
            filter = {
                val curFileId = it.jsonConfig.fileId
                !fileIdSet.contains(curFileId) || (isEdit && curFileId == fileId)
            }
        ).singleOrNull()?.data?.list
        drafts?.forEach {
            val curFileId = it.draftInfo?.jsonConfig?.fileId
            if (!curFileId.isNullOrBlank()) {
                fileIdSet.add(curFileId)
            }
        }
        val oldRoomsResultAsync = oldRoomsResultAsync
        val oldRoomsResult = oldRoomsResultAsync.invoke()
        val newRoomsResultAsync = if (oldRoomsResult != null) {
            val newRoomsResult = if (isEdit && !drafts.isNullOrEmpty()) {
                var replaceDraft: EditorCreationShowInfo? = null
                val filterDrafts = drafts.filter {
                    val ok = it.draftInfo?.jsonConfig?.fileId != fileId
                    if (!ok) {
                        replaceDraft = it
                    }
                    ok
                }
                val replaceDrafts = if (replaceDraft != null) {
                    val idx = oldRoomsResult.dataList?.indexOfFirst {
                        it.draftInfo?.jsonConfig?.fileId == fileId
                    } ?: -1
                    if (idx != -1) {
                        oldRoomsResult.dataList.replaceAt(idx, replaceDraft!!)
                    } else {
                        oldRoomsResult.dataList
                    }
                } else {
                    oldRoomsResult.dataList
                }
                oldRoomsResult.copy(
                    dataList = filterDrafts.insertAt(-1, replaceDrafts)
                )
            } else {
                oldRoomsResult.copy(
                    dataList = drafts.insertAt(-1, oldRoomsResult.dataList)
                )
            }
            scroll = SystemClock.elapsedRealtime()
            oldRoomsResultAsync.copyEx(newRoomsResult)
        } else {
            Loading(PagingApiResult(dataList = drafts, total = 0, end = false))
        }
        setState { copy(rooms = newRoomsResultAsync, scrollToFirst = scroll) }
        if (!fromGame) {
            getRooms()
        }
    }

    fun editDraft(room: EditorCreationShowInfo) {
        val ts = SystemClock.elapsedRealtime()
        setState { copy(draftPair = room.draftInfo to ts) }
    }

    fun getRooms() = withState { s ->
        if (s.roomsLoadMore is Loading) return@withState
        repo.fetchMyChatRoomPublishedList(s.lastRoomId).map {
            val lastRoomId = it.games?.lastOrNull()?.id
            val oldRoomsResult = oldRoomsResult
            if (oldRoomsResult == null) {
                val newRooms = it.games?.map { game ->
                    EditorCreationShowInfo(
                        draftInfo = null,
                        ugcInfo = game
                    )
                }
                PagingApiResult(dataList = newRooms, total = 0, end = it.end)
            } else {
                val newRooms = ArrayList(oldRoomsResult.dataList.orEmpty())
                val rawNewRooms = it.games?.filter { published ->
                    val old =
                        newRooms.indexOfFirst { local -> local.draftInfo?.ugid == published.id }
                    if (old != -1) {
                        newRooms[old] = newRooms[old].copy(ugcInfo = published)
                        false
                    } else {
                        true
                    }
                }?.map { game ->
                    EditorCreationShowInfo(
                        draftInfo = null,
                        ugcInfo = game
                    )
                }
                if (!rawNewRooms.isNullOrEmpty()) {
                    newRooms.addAll(rawNewRooms)
                }
                oldRoomsResult.copy(dataList = newRooms, end = it.end)
            } to lastRoomId
        }.execute { result ->
            when (result) {
                is Success -> {
                    val (newRooms, lastRoomsId) = result.invoke()
                    copy(
                        rooms = Success(newRooms),
                        lastRoomId = lastRoomsId,
                        roomsLoadMore = Success(LoadMoreState(newRooms.end))
                    )
                }

                is Fail -> {
                    copy(roomsLoadMore = Fail(result.error))
                }

                else -> {
                    copy(roomsLoadMore = Loading())
                }
            }
        }
    }

    fun selectRoom(room: EditorCreationShowInfo) = withState { s ->
        if (s.curRoom == room) return@withState
        setState { copy(curStyle = null, curRoom = room) }
    }

    fun deleteRoom(room: EditorCreationShowInfo, position: Int) {
        suspend {
            val path = room.draftInfo?.path
            if (!path.isNullOrBlank()) {
                assert(withContext(Dispatchers.IO) { File(path).deleteRecursively() }) {
                    GlobalContext.get().get<Context>().getString(R.string.delete_template_failed)
                }
                val fileId = room.draftInfo?.jsonConfig?.fileId
                if (!fileId.isNullOrBlank()) {
                    fileIdSet.remove(fileId)
                }
            }
            val ugcId = room.ugcInfo?.id ?: room.draftInfo?.ugid
            if (!ugcId.isNullOrBlank()) {
                assert(repo.deleteEditorPublished(ugcId).singleOrNull()?.data == true) {
                    GlobalContext.get().get<Context>().getString(R.string.delete_template_failed)
                }
                gameSet.remove(ugcId)
            }
            Analytics.track(
                EventConstants.EVENT_CHATROOM_UGC_TEMPLATE_DELETE_SUCCESS,
                "ugcid" to ugcId.orEmpty()
            )
        }.map {
            val oldRoomsResult = oldRoomsResult!!
            val oldRooms = oldRoomsResult.dataList
            val (result, _, newList) = oldRooms.dropAtWithResult(position, room)
            if (result) {
                oldRoomsResult.copy(dataList = newList)
            } else {
                oldRoomsResult
            }
        }.execute { result ->
            when (result) {
                is Success -> {
                    copy(rooms = result)
                }

                else -> this
            }
        }
    }

    fun getTemplateId() {
        repo.fetchChatRoomTemplateList().map {
            it.firstTemplateId!!
        }.execute { result ->
            when (result) {
                is Success -> {
                    copy(templateId = result.invoke())
                }

                else -> this
            }
        }
    }

    fun getGameTemplate() = withState { s ->
        if (s.template is Loading) return@withState
        val templateId = s.templateId ?: return@withState
        repo.getGameTemplate(templateId, 6).map {
            assert(it.succeeded) {
                it.message ?: GlobalContext.get().get<Context>().getString(R.string.fetch_template_info_failed)
            }
            it.data!!.apply {
                type = EditorConfigJsonEntity.TYPE_CHAT_ROOM
            }
        }.execute {
            copy(template = it)
        }
    }

    override fun onCleared() {
        super.onCleared()
        gameSet.clear()
        fileIdSet.clear()
    }

    companion object : KoinViewModelFactory<HomeCreateRoomViewModelV2, HomeCreateRoomState>() {
        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: HomeCreateRoomState
        ): HomeCreateRoomViewModelV2 {
            return HomeCreateRoomViewModelV2(state, get(), get(), get())
        }
    }
}