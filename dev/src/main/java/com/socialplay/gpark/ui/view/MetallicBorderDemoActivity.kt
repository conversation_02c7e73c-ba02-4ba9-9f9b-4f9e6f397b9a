package com.socialplay.gpark.ui.view

import android.animation.ValueAnimator
import android.graphics.Color
import android.os.Bundle
import android.widget.Button
import androidx.appcompat.app.AppCompatActivity
import com.socialplay.gpark.R
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener

/**
 * MetallicBorderView 演示Activity
 * 展示如何使用金属边框自定义View
 */
class MetallicBorderDemoActivity : AppCompatActivity() {

    private lateinit var metallicBorder1: MetallicBorderView
    private lateinit var metallicBorder2: MetallicBorderView
    private lateinit var metallicBorder3: MetallicBorderView
    private lateinit var metallicBorder4: MetallicBorderView
    
    private lateinit var btnRotateLight: Button
    private lateinit var btnChangeBorder: Button
    
    private var currentAngle = 45f
    private var currentBorderThickness = 2f
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.sample_metallic_border_view)
        
        initViews()
        setupClickListeners()
        startAutoRotation()
    }
    
    private fun initViews() {
        metallicBorder1 = findViewById(R.id.metallicBorder1)
        metallicBorder2 = findViewById(R.id.metallicBorder2)
        metallicBorder3 = findViewById(R.id.metallicBorder3)
        metallicBorder4 = findViewById(R.id.metallicBorder4)
        
        btnRotateLight = findViewById(R.id.btnRotateLight)
        btnChangeBorder = findViewById(R.id.btnChangeBorder)
    }
    
    private fun setupClickListeners() {
        // 旋转光照按钮
        btnRotateLight.setOnAntiViolenceClickListener {
            rotateLight()
        }
        
        // 改变边框按钮
        btnChangeBorder.setOnAntiViolenceClickListener {
            changeBorderStyle()
        }
    }
    
    /**
     * 旋转光照角度
     */
    private fun rotateLight() {
        val targetAngle = currentAngle + 45f
        
        val animator = ValueAnimator.ofFloat(currentAngle, targetAngle).apply {
            duration = 1000
            addUpdateListener { animation ->
                val angle = animation.animatedValue as Float
                metallicBorder1.setLightAngle(angle)
                metallicBorder2.setLightAngle(angle + 90f)
                metallicBorder3.setLightAngle(angle + 180f)
                metallicBorder4.setLightAngle(angle + 270f)
            }
        }
        
        animator.start()
        currentAngle = targetAngle % 360f
    }
    
    /**
     * 改变边框样式
     */
    private fun changeBorderStyle() {
        // 循环改变边框厚度
        currentBorderThickness = when {
            currentBorderThickness < 2f -> 2f
            currentBorderThickness < 4f -> 4f
            currentBorderThickness < 6f -> 6f
            else -> 1f
        }
        
        val density = resources.displayMetrics.density
        val thicknessPx = currentBorderThickness * density
        
        // 应用到所有边框
        metallicBorder1.setBorderThickness(thicknessPx)
        metallicBorder2.setBorderThickness(thicknessPx)
        metallicBorder3.setBorderThickness(thicknessPx)
        metallicBorder4.setBorderThickness(thicknessPx)
        
        // 同时改变一些颜色效果
        when (currentBorderThickness.toInt()) {
            1 -> {
                metallicBorder1.setMetallicBaseColor(Color.parseColor("#C0C0C0")) // 银色
                metallicBorder1.setHighlightIntensity(0.8f)
            }
            2 -> {
                metallicBorder1.setMetallicBaseColor(Color.parseColor("#FFD700")) // 金色
                metallicBorder1.setHighlightIntensity(0.9f)
            }
            4 -> {
                metallicBorder1.setMetallicBaseColor(Color.parseColor("#CD853F")) // 铜色
                metallicBorder1.setHighlightIntensity(0.7f)
            }
            6 -> {
                metallicBorder1.setMetallicBaseColor(Color.parseColor("#9370DB")) // 紫色
                metallicBorder1.setHighlightIntensity(0.6f)
            }
        }
    }
    
    /**
     * 启动自动旋转动画
     */
    private fun startAutoRotation() {
        val autoRotateAnimator = ValueAnimator.ofFloat(0f, 360f).apply {
            duration = 10000 // 10秒一圈
            repeatCount = ValueAnimator.INFINITE
            addUpdateListener { animation ->
                val angle = animation.animatedValue as Float
                
                // 让不同的边框以不同的速度和方向旋转
                metallicBorder2.setLightAngle(angle)
                metallicBorder3.setLightAngle(-angle * 0.5f)
                metallicBorder4.setLightAngle(angle * 1.5f)
            }
        }
        
        autoRotateAnimator.start()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        // 清理动画资源
    }
}

/**
 * 扩展函数：创建预设的金属边框样式
 */
object MetallicBorderPresets {
    
    /**
     * 银色边框预设
     */
    fun applySilverStyle(view: MetallicBorderView) {
        view.apply {
            setMetallicBaseColor(Color.parseColor("#C0C0C0"))
            setHighlightIntensity(0.8f)
            setShadowIntensity(0.3f)
        }
    }
    
    /**
     * 金色边框预设
     */
    fun applyGoldStyle(view: MetallicBorderView) {
        view.apply {
            setMetallicBaseColor(Color.parseColor("#FFD700"))
            setHighlightIntensity(0.9f)
            setShadowIntensity(0.4f)
        }
    }
    
    /**
     * 铜色边框预设
     */
    fun applyCopperStyle(view: MetallicBorderView) {
        view.apply {
            setMetallicBaseColor(Color.parseColor("#CD853F"))
            setHighlightIntensity(0.7f)
            setShadowIntensity(0.5f)
        }
    }
    
    /**
     * 钢铁边框预设
     */
    fun applySteelStyle(view: MetallicBorderView) {
        view.apply {
            setMetallicBaseColor(Color.parseColor("#708090"))
            setHighlightIntensity(0.6f)
            setShadowIntensity(0.4f)
        }
    }
}
