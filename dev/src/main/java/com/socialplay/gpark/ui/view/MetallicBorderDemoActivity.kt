package com.socialplay.gpark.ui.view

import android.animation.ValueAnimator
import android.graphics.Color
import android.os.Bundle
import android.widget.Button
import android.widget.SeekBar
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import com.socialplay.gpark.R
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener

/**
 * MetallicBorderView 演示Activity
 * 展示如何使用金属边框自定义View
 */
class MetallicBorderDemoActivity : AppCompatActivity() {

    private lateinit var metallicBorder1: MetallicBorderView
    private lateinit var metallicBorder2: MetallicBorderView
    private lateinit var metallicBorder3: MetallicBorderView
    private lateinit var metallicBorder4: MetallicBorderView
    private lateinit var simpleMetallicBorder: SimpleMetallicBorderView
    private lateinit var borderFocusedMetallic: BorderFocusedMetallicView

    private lateinit var btnReset: Button
    private lateinit var btnDebugMode: Button

    // 光照角度调节控件
    private lateinit var seekBarLightAngle: SeekBar
    private lateinit var tvLightAngle: TextView

    // 圆角半径调节控件
    private lateinit var seekBarCornerRadius: SeekBar
    private lateinit var tvCornerRadius: TextView

    // 边框厚度调节控件
    private lateinit var seekBarBorderThickness: SeekBar
    private lateinit var tvBorderThickness: TextView

    // 主光晕调节控件
    private lateinit var seekBarHighlightIntensity: SeekBar
    private lateinit var tvHighlightIntensity: TextView
    private lateinit var seekBarHighlightAngle: SeekBar
    private lateinit var tvHighlightAngle: TextView

    // 补光光晕调节控件
    private lateinit var seekBarFillLightIntensity: SeekBar
    private lateinit var tvFillLightIntensity: TextView
    private lateinit var seekBarFillLightAngle: SeekBar
    private lateinit var tvFillLightAngle: TextView

    // 外发光调节控件
    private lateinit var seekBarGlowIntensity: SeekBar
    private lateinit var tvGlowIntensity: TextView
    private lateinit var btnGlowColorRed: Button
    private lateinit var btnGlowColorBlue: Button
    private lateinit var btnGlowColorGreen: Button
    private lateinit var btnGlowColorPurple: Button
    private lateinit var btnGlowColorWhite: Button

    private var currentAngle = 45f
    private var currentCornerRadius = 12f
    private var currentBorderThickness = 6f
    private var currentHighlightIntensity = 0.9f
    private var currentHighlightAngle = 45f
    private var currentFillLightIntensity = 0.3f
    private var currentFillLightAngle = 225f
    private var currentGlowIntensity = 0.5f
    private var currentGlowColor = Color.parseColor("#0080FF")
    private var isDebugMode = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.sample_metallic_border_view)

        initViews()
        setupClickListeners()
        setupSeekBars()
    }

    private fun initViews() {
        metallicBorder1 = findViewById(R.id.metallicBorder1)
        metallicBorder2 = findViewById(R.id.metallicBorder2)
        metallicBorder3 = findViewById(R.id.metallicBorder3)
        metallicBorder4 = findViewById(R.id.metallicBorder4)
        simpleMetallicBorder = findViewById(R.id.simpleMetallicBorder)
        borderFocusedMetallic = findViewById(R.id.borderFocusedMetallic)

        btnReset = findViewById(R.id.btnReset)
        btnDebugMode = findViewById(R.id.btnDebugMode)

        // 光照角度调节控件
        seekBarLightAngle = findViewById(R.id.seekBarLightAngle)
        tvLightAngle = findViewById(R.id.tvLightAngle)

        // 圆角半径调节控件
        seekBarCornerRadius = findViewById(R.id.seekBarCornerRadius)
        tvCornerRadius = findViewById(R.id.tvCornerRadius)

        // 边框厚度调节控件
        seekBarBorderThickness = findViewById(R.id.seekBarBorderThickness)
        tvBorderThickness = findViewById(R.id.tvBorderThickness)

        // 主光晕调节控件
        seekBarHighlightIntensity = findViewById(R.id.seekBarHighlightIntensity)
        tvHighlightIntensity = findViewById(R.id.tvHighlightIntensity)
        seekBarHighlightAngle = findViewById(R.id.seekBarHighlightAngle)
        tvHighlightAngle = findViewById(R.id.tvHighlightAngle)

        // 补光光晕调节控件
        seekBarFillLightIntensity = findViewById(R.id.seekBarFillLightIntensity)
        tvFillLightIntensity = findViewById(R.id.tvFillLightIntensity)
        seekBarFillLightAngle = findViewById(R.id.seekBarFillLightAngle)
        tvFillLightAngle = findViewById(R.id.tvFillLightAngle)

        // 外发光调节控件
        seekBarGlowIntensity = findViewById(R.id.seekBarGlowIntensity)
        tvGlowIntensity = findViewById(R.id.tvGlowIntensity)
        btnGlowColorRed = findViewById(R.id.btnGlowColorRed)
        btnGlowColorBlue = findViewById(R.id.btnGlowColorBlue)
        btnGlowColorGreen = findViewById(R.id.btnGlowColorGreen)
        btnGlowColorPurple = findViewById(R.id.btnGlowColorPurple)
        btnGlowColorWhite = findViewById(R.id.btnGlowColorWhite)
    }

    private fun setupClickListeners() {
        // 重置效果按钮
        btnReset.setOnAntiViolenceClickListener {
            resetEffects()
        }

        // 调试模式按钮
        btnDebugMode.setOnAntiViolenceClickListener {
            toggleDebugMode()
        }

        // 外发光颜色按钮
        btnGlowColorRed.setOnAntiViolenceClickListener {
            setGlowColor(Color.parseColor("#FF0000"))
        }

        btnGlowColorBlue.setOnAntiViolenceClickListener {
            setGlowColor(Color.parseColor("#0080FF"))
        }

        btnGlowColorGreen.setOnAntiViolenceClickListener {
            setGlowColor(Color.parseColor("#00FF00"))
        }

        btnGlowColorPurple.setOnAntiViolenceClickListener {
            setGlowColor(Color.parseColor("#FF00FF"))
        }

        btnGlowColorWhite.setOnAntiViolenceClickListener {
            setGlowColor(Color.parseColor("#FFFFFF"))
        }

    }

    /**
     * 设置SeekBar
     */
    private fun setupSeekBars() {
        // 初始化光照角度SeekBar
        seekBarLightAngle.progress = currentAngle.toInt()
        updateAngleDisplay(currentAngle)

        // 初始化圆角半径SeekBar
        seekBarCornerRadius.progress = currentCornerRadius.toInt()
        updateCornerRadiusDisplay(currentCornerRadius)

        // 初始化边框厚度SeekBar
        seekBarBorderThickness.progress = currentBorderThickness.toInt()
        updateBorderThicknessDisplay(currentBorderThickness)

        // 初始化主光晕SeekBar
        seekBarHighlightIntensity.progress = (currentHighlightIntensity * 100).toInt()
        updateHighlightIntensityDisplay(currentHighlightIntensity)
        seekBarHighlightAngle.progress = currentHighlightAngle.toInt()
        updateHighlightAngleDisplay(currentHighlightAngle)

        // 初始化补光光晕SeekBar
        seekBarFillLightIntensity.progress = (currentFillLightIntensity * 100).toInt()
        updateFillLightIntensityDisplay(currentFillLightIntensity)
        seekBarFillLightAngle.progress = currentFillLightAngle.toInt()
        updateFillLightAngleDisplay(currentFillLightAngle)

        // 初始化外发光SeekBar
        seekBarGlowIntensity.progress = (currentGlowIntensity * 100).toInt()
        updateGlowIntensityDisplay(currentGlowIntensity)

        setupSeekBarListeners()
    }

    private fun setupSeekBarListeners() {
        // 设置光照角度SeekBar监听器
        seekBarLightAngle.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    currentAngle = progress.toFloat()
                    updateAngleDisplay(currentAngle)
                    updateAllBordersAngle(currentAngle)
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        // 设置圆角半径SeekBar监听器
        seekBarCornerRadius.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    currentCornerRadius = progress.toFloat()
                    updateCornerRadiusDisplay(currentCornerRadius)
                    updateAllBordersCornerRadius(currentCornerRadius)
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        // 设置边框厚度SeekBar监听器
        seekBarBorderThickness.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    currentBorderThickness = progress.toFloat()
                    updateBorderThicknessDisplay(currentBorderThickness)
                    updateAllBordersBorderThickness(currentBorderThickness)
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        // 设置主光晕强度SeekBar监听器
        seekBarHighlightIntensity.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    currentHighlightIntensity = progress / 100f
                    updateHighlightIntensityDisplay(currentHighlightIntensity)
                    updateAllBordersHighlightIntensity(currentHighlightIntensity)
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        // 设置主光晕角度SeekBar监听器
        seekBarHighlightAngle.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    currentHighlightAngle = progress.toFloat()
                    updateHighlightAngleDisplay(currentHighlightAngle)
                    updateAllBordersHighlightAngle(currentHighlightAngle)
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        // 设置补光光晕强度SeekBar监听器
        seekBarFillLightIntensity.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    currentFillLightIntensity = progress / 100f
                    updateFillLightIntensityDisplay(currentFillLightIntensity)
                    updateAllBordersFillLightIntensity(currentFillLightIntensity)
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        // 设置补光光晕角度SeekBar监听器
        seekBarFillLightAngle.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    currentFillLightAngle = progress.toFloat()
                    updateFillLightAngleDisplay(currentFillLightAngle)
                    updateAllBordersFillLightAngle(currentFillLightAngle)
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        // 设置外发光强度SeekBar监听器
        seekBarGlowIntensity.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    currentGlowIntensity = progress / 100f
                    updateGlowIntensityDisplay(currentGlowIntensity)
                    updateAllBordersGlowIntensity(currentGlowIntensity)
                }
            }
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })
    }

    /**
     * 更新角度显示
     */
    private fun updateAngleDisplay(angle: Float) {
        tvLightAngle.text = "${angle.toInt()}°"
    }

    /**
     * 更新圆角半径显示
     */
    private fun updateCornerRadiusDisplay(radius: Float) {
        tvCornerRadius.text = "${radius.toInt()}dp"
    }

    /**
     * 更新边框厚度显示
     */
    private fun updateBorderThicknessDisplay(thickness: Float) {
        tvBorderThickness.text = "${thickness.toInt()}dp"
    }

    /**
     * 更新主光晕强度显示
     */
    private fun updateHighlightIntensityDisplay(intensity: Float) {
        tvHighlightIntensity.text = "${(intensity * 100).toInt()}%"
    }

    /**
     * 更新主光晕角度显示
     */
    private fun updateHighlightAngleDisplay(angle: Float) {
        tvHighlightAngle.text = "${angle.toInt()}°"
    }

    /**
     * 更新补光光晕强度显示
     */
    private fun updateFillLightIntensityDisplay(intensity: Float) {
        tvFillLightIntensity.text = "${(intensity * 100).toInt()}%"
    }

    /**
     * 更新补光光晕角度显示
     */
    private fun updateFillLightAngleDisplay(angle: Float) {
        tvFillLightAngle.text = "${angle.toInt()}°"
    }

    /**
     * 更新外发光强度显示
     */
    private fun updateGlowIntensityDisplay(intensity: Float) {
        tvGlowIntensity.text = "${(intensity * 100).toInt()}%"
    }

    /**
     * 更新所有边框的光照角度
     */
    private fun updateAllBordersAngle(angle: Float) {
        metallicBorder1.setLightAngle(angle)
        metallicBorder2.setLightAngle(angle)
        metallicBorder3.setLightAngle(angle)
        metallicBorder4.setLightAngle(angle)
        simpleMetallicBorder.setLightAngle(angle)
        borderFocusedMetallic.setLightAngle(angle)
    }

    /**
     * 更新所有边框的圆角半径
     */
    private fun updateAllBordersCornerRadius(radius: Float) {
        val density = resources.displayMetrics.density
        val radiusPx = radius * density

        metallicBorder1.setCornerRadius(radiusPx)
        metallicBorder2.setCornerRadius(radiusPx)
        metallicBorder3.setCornerRadius(radiusPx)
        metallicBorder4.setCornerRadius(radiusPx)
        simpleMetallicBorder.setCornerRadius(radiusPx)
        borderFocusedMetallic.setCornerRadius(radiusPx)
    }

    /**
     * 更新所有边框的边框厚度
     */
    private fun updateAllBordersBorderThickness(thickness: Float) {
        val density = resources.displayMetrics.density
        val thicknessPx = thickness * density

        metallicBorder1.setBorderThickness(thicknessPx)
        metallicBorder2.setBorderThickness(thicknessPx)
        metallicBorder3.setBorderThickness(thicknessPx)
        metallicBorder4.setBorderThickness(thicknessPx)
        simpleMetallicBorder.setBorderThickness(thicknessPx)
        borderFocusedMetallic.setBorderThickness(thicknessPx)
    }

    /**
     * 更新所有边框的主光晕强度
     */
    private fun updateAllBordersHighlightIntensity(intensity: Float) {
        metallicBorder1.setHighlightIntensity(intensity)
        metallicBorder2.setHighlightIntensity(intensity)
        metallicBorder3.setHighlightIntensity(intensity)
        metallicBorder4.setHighlightIntensity(intensity)
        simpleMetallicBorder.setHighlightIntensity(intensity)
        borderFocusedMetallic.setHighlightIntensity(intensity)
    }

    /**
     * 更新所有边框的主光晕角度
     */
    private fun updateAllBordersHighlightAngle(angle: Float) {
        // 主光晕角度就是光照角度
        updateAllBordersAngle(angle)
    }

    /**
     * 更新所有边框的补光光晕强度
     */
    private fun updateAllBordersFillLightIntensity(intensity: Float) {
        // 补光光晕强度对应阴影强度
        metallicBorder1.setShadowIntensity(intensity)
        metallicBorder2.setShadowIntensity(intensity)
        metallicBorder3.setShadowIntensity(intensity)
        metallicBorder4.setShadowIntensity(intensity)
        simpleMetallicBorder.setShadowIntensity(intensity)
        borderFocusedMetallic.setShadowIntensity(intensity)
    }

    /**
     * 更新所有边框的补光光晕角度
     */
    private fun updateAllBordersFillLightAngle(angle: Float) {
        // 补光光晕角度暂时不实现，因为需要修改View的内部逻辑
        // 这里可以预留接口，后续实现双光源效果
    }

    /**
     * 更新所有边框的外发光强度
     */
    private fun updateAllBordersGlowIntensity(intensity: Float) {
        metallicBorder1.setGlowIntensity(intensity)
        metallicBorder2.setGlowIntensity(intensity)
        metallicBorder3.setGlowIntensity(intensity)
        metallicBorder4.setGlowIntensity(intensity)
        simpleMetallicBorder.setGlowIntensity(intensity)
        borderFocusedMetallic.setGlowIntensity(intensity)
    }

    /**
     * 更新所有边框的外发光颜色
     */
    private fun updateAllBordersGlowColor(color: Int) {
        metallicBorder1.setGlowColor(color)
        metallicBorder2.setGlowColor(color)
        metallicBorder3.setGlowColor(color)
        metallicBorder4.setGlowColor(color)
        simpleMetallicBorder.setGlowColor(color)
        borderFocusedMetallic.setGlowColor(color)
    }

    /**
     * 设置外发光颜色
     */
    private fun setGlowColor(color: Int) {
        currentGlowColor = color
        updateAllBordersGlowColor(color)
    }

    /**
     * 改变边框样式
     */
    private fun changeBorderStyle() {
        // 循环改变边框厚度
        currentBorderThickness = when {
            currentBorderThickness < 3f -> 3f
            currentBorderThickness < 5f -> 5f
            currentBorderThickness < 8f -> 8f
            else -> 2f
        }

        val density = resources.displayMetrics.density
        val thicknessPx = currentBorderThickness * density

        // 应用到所有边框
        metallicBorder1.setBorderThickness(thicknessPx)
        metallicBorder2.setBorderThickness(thicknessPx)
        metallicBorder3.setBorderThickness(thicknessPx)
        metallicBorder4.setBorderThickness(thicknessPx)

        // 同时改变一些颜色效果
        when (currentBorderThickness.toInt()) {
            2 -> {
                metallicBorder1.setMetallicBaseColor(Color.parseColor("#C0C0C0")) // 银色
            }

            3 -> {
                metallicBorder1.setMetallicBaseColor(Color.parseColor("#FFD700")) // 金色
            }

            5 -> {
                metallicBorder1.setMetallicBaseColor(Color.parseColor("#CD853F")) // 铜色
            }

            8 -> {
                metallicBorder1.setMetallicBaseColor(Color.parseColor("#9370DB")) // 紫色
            }
        }
    }

    /**
     * 调整高光强度
     */
    private fun adjustHighlight() {
        currentHighlightIntensity += 0.2f
        if (currentHighlightIntensity > 1.0f) {
            currentHighlightIntensity = 0.3f
        }

        metallicBorder1.setHighlightIntensity(currentHighlightIntensity)
        metallicBorder2.setHighlightIntensity(currentHighlightIntensity)
        metallicBorder3.setHighlightIntensity(currentHighlightIntensity)
        metallicBorder4.setHighlightIntensity(currentHighlightIntensity)
    }

    /**
     * 重置所有效果
     */
    private fun resetEffects() {
        currentAngle = 45f
        currentCornerRadius = 12f
        currentBorderThickness = 6f
        currentHighlightIntensity = 0.9f
        currentHighlightAngle = 45f
        currentFillLightIntensity = 0.3f
        currentFillLightAngle = 225f
        currentGlowIntensity = 0.5f
        currentGlowColor = Color.parseColor("#0080FF")

        val density = resources.displayMetrics.density
        val thicknessPx = currentBorderThickness * density
        val radiusPx = currentCornerRadius * density

        // 重置所有SeekBar
        seekBarLightAngle.progress = currentAngle.toInt()
        seekBarCornerRadius.progress = currentCornerRadius.toInt()
        seekBarBorderThickness.progress = currentBorderThickness.toInt()
        seekBarHighlightIntensity.progress = (currentHighlightIntensity * 100).toInt()
        seekBarHighlightAngle.progress = currentHighlightAngle.toInt()
        seekBarFillLightIntensity.progress = (currentFillLightIntensity * 100).toInt()
        seekBarFillLightAngle.progress = currentFillLightAngle.toInt()
        seekBarGlowIntensity.progress = (currentGlowIntensity * 100).toInt()

        // 更新显示
        updateAngleDisplay(currentAngle)
        updateCornerRadiusDisplay(currentCornerRadius)
        updateBorderThicknessDisplay(currentBorderThickness)
        updateHighlightIntensityDisplay(currentHighlightIntensity)
        updateHighlightAngleDisplay(currentHighlightAngle)
        updateFillLightIntensityDisplay(currentFillLightIntensity)
        updateFillLightAngleDisplay(currentFillLightAngle)
        updateGlowIntensityDisplay(currentGlowIntensity)

        // 重置所有边框
        listOf(metallicBorder1, metallicBorder2, metallicBorder3, metallicBorder4).forEach { border ->
            border.setLightAngle(currentAngle)
            border.setCornerRadius(radiusPx)
            border.setHighlightIntensity(currentHighlightIntensity)
            border.setShadowIntensity(currentFillLightIntensity)
            border.setBorderThickness(thicknessPx)
            border.setGlowIntensity(currentGlowIntensity)
            border.setGlowColor(currentGlowColor)
            border.setDebugMode(false)
        }

        // 重置简化版本
        simpleMetallicBorder.setLightAngle(currentAngle)
        simpleMetallicBorder.setCornerRadius(radiusPx)
        simpleMetallicBorder.setHighlightIntensity(currentHighlightIntensity)
        simpleMetallicBorder.setShadowIntensity(currentFillLightIntensity)
        simpleMetallicBorder.setBorderThickness(thicknessPx)
        simpleMetallicBorder.setGlowIntensity(currentGlowIntensity)
        simpleMetallicBorder.setGlowColor(currentGlowColor)

        // 重置边框专注版本
        borderFocusedMetallic.setLightAngle(currentAngle)
        borderFocusedMetallic.setCornerRadius(radiusPx)
        borderFocusedMetallic.setHighlightIntensity(currentHighlightIntensity)
        borderFocusedMetallic.setShadowIntensity(currentFillLightIntensity)
        borderFocusedMetallic.setBorderThickness(thicknessPx)
        borderFocusedMetallic.setGlowIntensity(currentGlowIntensity)
        borderFocusedMetallic.setGlowColor(currentGlowColor)
        borderFocusedMetallic.setDebugMode(false)

        // 重置颜色
        metallicBorder1.setMetallicBaseColor(Color.parseColor("#C0C0C0"))
        metallicBorder2.setMetallicBaseColor(Color.parseColor("#FFD700"))
        metallicBorder3.setMetallicBaseColor(Color.parseColor("#CD853F"))
        metallicBorder4.setMetallicBaseColor(Color.parseColor("#9370DB"))

        // 重置调试状态
        isDebugMode = false
        btnDebugMode.text = "调试模式"
    }

    /**
     * 切换调试模式
     */
    private fun toggleDebugMode() {
        isDebugMode = !isDebugMode

        listOf(metallicBorder1, metallicBorder2, metallicBorder3, metallicBorder4).forEach { border ->
            border.setDebugMode(isDebugMode)
        }

        btnDebugMode.text = if (isDebugMode) "退出调试" else "调试模式"
    }

    /**
     * 测试高光效果
     */
    private fun testHighlight() {
        // 设置极高的高光强度来测试高光是否可见
        listOf(metallicBorder1, metallicBorder2, metallicBorder3, metallicBorder4).forEach { border ->
            border.setHighlightIntensity(1.0f)
            border.setShadowIntensity(0.8f) // 保持阴影，形成对比
            border.setDebugMode(true) // 开启调试模式，不挖空中心
        }

        // 使用当前SeekBar的角度
        val testAngle = currentAngle
        updateAllBordersAngle(testAngle)

        // 同时测试简化版本
        simpleMetallicBorder.setHighlightIntensity(1.0f)
        simpleMetallicBorder.setShadowIntensity(0.8f)

        // 测试边框专注版本
        borderFocusedMetallic.setHighlightIntensity(1.0f)
        borderFocusedMetallic.setShadowIntensity(0.8f)
        borderFocusedMetallic.setDebugMode(true)
    }

    /**
     * 应用最大效果
     */
    private fun applyMaxEffect() {
        val density = resources.displayMetrics.density

        listOf(metallicBorder1, metallicBorder2, metallicBorder3, metallicBorder4).forEach { border ->
            border.setHighlightIntensity(1.0f)
            border.setShadowIntensity(1.0f)
            border.setBorderThickness(8f * density) // 8dp边框
            border.setDebugMode(false) // 关闭调试模式
        }
    }

    override fun onDestroy() {
        super.onDestroy()
    }
}

/**
 * 扩展函数：创建预设的金属边框样式
 */
object MetallicBorderPresets {

    /**
     * 银色边框预设
     */
    fun applySilverStyle(view: MetallicBorderView) {
        view.apply {
            setMetallicBaseColor(Color.parseColor("#C0C0C0"))
            setHighlightIntensity(0.9f)
            setShadowIntensity(0.7f)
        }
    }

    /**
     * 金色边框预设
     */
    fun applyGoldStyle(view: MetallicBorderView) {
        view.apply {
            setMetallicBaseColor(Color.parseColor("#FFD700"))
            setHighlightIntensity(1.0f)
            setShadowIntensity(0.8f)
        }
    }

    /**
     * 铜色边框预设
     */
    fun applyCopperStyle(view: MetallicBorderView) {
        view.apply {
            setMetallicBaseColor(Color.parseColor("#CD853F"))
            setHighlightIntensity(0.8f)
            setShadowIntensity(0.9f)
        }
    }

    /**
     * 钢铁边框预设
     */
    fun applySteelStyle(view: MetallicBorderView) {
        view.apply {
            setMetallicBaseColor(Color.parseColor("#708090"))
            setHighlightIntensity(0.8f)
            setShadowIntensity(0.7f)
        }
    }

    /**
     * 钛合金边框预设
     */
    fun applyTitaniumStyle(view: MetallicBorderView) {
        view.apply {
            setMetallicBaseColor(Color.parseColor("#A8A8A8"))
            setHighlightIntensity(0.95f)
            setShadowIntensity(0.8f)
        }
    }
}
