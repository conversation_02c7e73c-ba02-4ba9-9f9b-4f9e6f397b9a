package com.socialplay.gpark.ui.view

import android.animation.ValueAnimator
import android.graphics.Color
import android.os.Bundle
import android.widget.Button
import androidx.appcompat.app.AppCompatActivity
import com.socialplay.gpark.R
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener

/**
 * MetallicBorderView 演示Activity
 * 展示如何使用金属边框自定义View
 */
class MetallicBorderDemoActivity : AppCompatActivity() {

    private lateinit var metallicBorder1: MetallicBorderView
    private lateinit var metallicBorder2: MetallicBorderView
    private lateinit var metallicBorder3: MetallicBorderView
    private lateinit var metallicBorder4: MetallicBorderView
    private lateinit var simpleMetallicBorder: SimpleMetallicBorderView
    
    private lateinit var btnRotateLight: Button
    private lateinit var btnChangeBorder: Button
    private lateinit var btnToggleAnimation: Button
    private lateinit var btnIncreaseHighlight: Button
    private lateinit var btnIncreaseShadow: Button
    private lateinit var btnReset: Button
    private lateinit var btnDebugMode: Button
    private lateinit var btnTestHighlight: Button
    private lateinit var btnMaxEffect: Button

    private var currentAngle = 45f
    private var currentBorderThickness = 4f
    private var currentHighlightIntensity = 0.9f
    private var currentShadowIntensity = 0.7f
    private var isAnimating = false
    private var isDebugMode = false
    private var autoRotateAnimator: ValueAnimator? = null
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.sample_metallic_border_view)
        
        initViews()
        setupClickListeners()
        startAutoRotation()
    }
    
    private fun initViews() {
        metallicBorder1 = findViewById(R.id.metallicBorder1)
        metallicBorder2 = findViewById(R.id.metallicBorder2)
        metallicBorder3 = findViewById(R.id.metallicBorder3)
        metallicBorder4 = findViewById(R.id.metallicBorder4)
        simpleMetallicBorder = findViewById(R.id.simpleMetallicBorder)

        btnRotateLight = findViewById(R.id.btnRotateLight)
        btnChangeBorder = findViewById(R.id.btnChangeBorder)
        btnToggleAnimation = findViewById(R.id.btnToggleAnimation)
        btnIncreaseHighlight = findViewById(R.id.btnIncreaseHighlight)
        btnIncreaseShadow = findViewById(R.id.btnIncreaseShadow)
        btnReset = findViewById(R.id.btnReset)
        btnDebugMode = findViewById(R.id.btnDebugMode)
        btnTestHighlight = findViewById(R.id.btnTestHighlight)
        btnMaxEffect = findViewById(R.id.btnMaxEffect)
    }
    
    private fun setupClickListeners() {
        // 旋转光照按钮
        btnRotateLight.setOnAntiViolenceClickListener {
            rotateLight()
        }

        // 改变边框按钮
        btnChangeBorder.setOnAntiViolenceClickListener {
            changeBorderStyle()
        }

        // 切换动画按钮
        btnToggleAnimation.setOnAntiViolenceClickListener {
            toggleAnimation()
        }

        // 增强高光按钮
        btnIncreaseHighlight.setOnAntiViolenceClickListener {
            adjustHighlight()
        }

        // 增强阴影按钮
        btnIncreaseShadow.setOnAntiViolenceClickListener {
            adjustShadow()
        }

        // 重置效果按钮
        btnReset.setOnAntiViolenceClickListener {
            resetEffects()
        }

        // 调试模式按钮
        btnDebugMode.setOnAntiViolenceClickListener {
            toggleDebugMode()
        }

        // 测试高光按钮
        btnTestHighlight.setOnAntiViolenceClickListener {
            testHighlight()
        }

        // 最大效果按钮
        btnMaxEffect.setOnAntiViolenceClickListener {
            applyMaxEffect()
        }
    }
    
    /**
     * 旋转光照角度
     */
    private fun rotateLight() {
        val targetAngle = currentAngle + 45f
        
        val animator = ValueAnimator.ofFloat(currentAngle, targetAngle).apply {
            duration = 1000
            addUpdateListener { animation ->
                val angle = animation.animatedValue as Float
                metallicBorder1.setLightAngle(angle)
                metallicBorder2.setLightAngle(angle + 90f)
                metallicBorder3.setLightAngle(angle + 180f)
                metallicBorder4.setLightAngle(angle + 270f)
            }
        }
        
        animator.start()
        currentAngle = targetAngle % 360f
    }
    
    /**
     * 改变边框样式
     */
    private fun changeBorderStyle() {
        // 循环改变边框厚度
        currentBorderThickness = when {
            currentBorderThickness < 3f -> 3f
            currentBorderThickness < 5f -> 5f
            currentBorderThickness < 8f -> 8f
            else -> 2f
        }

        val density = resources.displayMetrics.density
        val thicknessPx = currentBorderThickness * density

        // 应用到所有边框
        metallicBorder1.setBorderThickness(thicknessPx)
        metallicBorder2.setBorderThickness(thicknessPx)
        metallicBorder3.setBorderThickness(thicknessPx)
        metallicBorder4.setBorderThickness(thicknessPx)

        // 同时改变一些颜色效果
        when (currentBorderThickness.toInt()) {
            2 -> {
                metallicBorder1.setMetallicBaseColor(Color.parseColor("#C0C0C0")) // 银色
            }
            3 -> {
                metallicBorder1.setMetallicBaseColor(Color.parseColor("#FFD700")) // 金色
            }
            5 -> {
                metallicBorder1.setMetallicBaseColor(Color.parseColor("#CD853F")) // 铜色
            }
            8 -> {
                metallicBorder1.setMetallicBaseColor(Color.parseColor("#9370DB")) // 紫色
            }
        }
    }

    /**
     * 切换动画
     */
    private fun toggleAnimation() {
        if (isAnimating) {
            stopAutoRotation()
            btnToggleAnimation.text = "开始动画"
            isAnimating = false
        } else {
            startAutoRotation()
            btnToggleAnimation.text = "停止动画"
            isAnimating = true
        }
    }

    /**
     * 调整高光强度
     */
    private fun adjustHighlight() {
        currentHighlightIntensity += 0.2f
        if (currentHighlightIntensity > 1.0f) {
            currentHighlightIntensity = 0.3f
        }

        metallicBorder1.setHighlightIntensity(currentHighlightIntensity)
        metallicBorder2.setHighlightIntensity(currentHighlightIntensity)
        metallicBorder3.setHighlightIntensity(currentHighlightIntensity)
        metallicBorder4.setHighlightIntensity(currentHighlightIntensity)
    }

    /**
     * 调整阴影强度
     */
    private fun adjustShadow() {
        currentShadowIntensity += 0.2f
        if (currentShadowIntensity > 1.0f) {
            currentShadowIntensity = 0.3f
        }

        metallicBorder1.setShadowIntensity(currentShadowIntensity)
        metallicBorder2.setShadowIntensity(currentShadowIntensity)
        metallicBorder3.setShadowIntensity(currentShadowIntensity)
        metallicBorder4.setShadowIntensity(currentShadowIntensity)
    }

    /**
     * 重置所有效果
     */
    private fun resetEffects() {
        currentHighlightIntensity = 0.9f
        currentShadowIntensity = 0.7f
        currentBorderThickness = 4f

        val density = resources.displayMetrics.density
        val thicknessPx = currentBorderThickness * density

        // 重置所有边框
        listOf(metallicBorder1, metallicBorder2, metallicBorder3, metallicBorder4).forEach { border ->
            border.setHighlightIntensity(currentHighlightIntensity)
            border.setShadowIntensity(currentShadowIntensity)
            border.setBorderThickness(thicknessPx)
        }

        // 重置颜色
        metallicBorder1.setMetallicBaseColor(Color.parseColor("#C0C0C0"))
        metallicBorder2.setMetallicBaseColor(Color.parseColor("#FFD700"))
        metallicBorder3.setMetallicBaseColor(Color.parseColor("#CD853F"))
        metallicBorder4.setMetallicBaseColor(Color.parseColor("#9370DB"))
    }

    /**
     * 切换调试模式
     */
    private fun toggleDebugMode() {
        isDebugMode = !isDebugMode

        listOf(metallicBorder1, metallicBorder2, metallicBorder3, metallicBorder4).forEach { border ->
            border.setDebugMode(isDebugMode)
        }

        btnDebugMode.text = if (isDebugMode) "退出调试" else "调试模式"
    }

    /**
     * 测试高光效果
     */
    private fun testHighlight() {
        // 设置极高的高光强度来测试高光是否可见
        listOf(metallicBorder1, metallicBorder2, metallicBorder3, metallicBorder4).forEach { border ->
            border.setHighlightIntensity(1.0f)
            border.setShadowIntensity(0.8f) // 保持阴影，形成对比
            border.setDebugMode(true) // 开启调试模式，不挖空中心
        }

        // 设置标准的光照角度来测试位置是否正确
        // 45度：光从左上角照射，高光应该在右下角，阴影在左上角
        metallicBorder1.setLightAngle(45f)   // 右下光照
        metallicBorder2.setLightAngle(135f)  // 左下光照
        metallicBorder3.setLightAngle(225f)  // 左上光照
        metallicBorder4.setLightAngle(315f)  // 右上光照

        // 同时测试简化版本
        simpleMetallicBorder.setLightAngle(45f)
        simpleMetallicBorder.setHighlightIntensity(1.0f)
        simpleMetallicBorder.setShadowIntensity(0.8f)
    }

    /**
     * 应用最大效果
     */
    private fun applyMaxEffect() {
        val density = resources.displayMetrics.density

        listOf(metallicBorder1, metallicBorder2, metallicBorder3, metallicBorder4).forEach { border ->
            border.setHighlightIntensity(1.0f)
            border.setShadowIntensity(1.0f)
            border.setBorderThickness(8f * density) // 8dp边框
            border.setDebugMode(false) // 关闭调试模式
        }
    }
    
    /**
     * 启动自动旋转动画
     */
    private fun startAutoRotation() {
        autoRotateAnimator = ValueAnimator.ofFloat(0f, 360f).apply {
            duration = 8000 // 8秒一圈
            repeatCount = ValueAnimator.INFINITE
            addUpdateListener { animation ->
                val angle = animation.animatedValue as Float

                // 让不同的边框以不同的速度和方向旋转
                metallicBorder1.setLightAngle(angle)
                metallicBorder2.setLightAngle(angle + 90f)
                metallicBorder3.setLightAngle(-angle * 0.8f)
                metallicBorder4.setLightAngle(angle * 1.2f)
                simpleMetallicBorder.setLightAngle(angle)
            }
        }

        autoRotateAnimator?.start()
    }

    /**
     * 停止自动旋转动画
     */
    private fun stopAutoRotation() {
        autoRotateAnimator?.cancel()
        autoRotateAnimator = null
    }
    
    override fun onDestroy() {
        super.onDestroy()
        // 清理动画资源
        stopAutoRotation()
    }
}

/**
 * 扩展函数：创建预设的金属边框样式
 */
object MetallicBorderPresets {
    
    /**
     * 银色边框预设
     */
    fun applySilverStyle(view: MetallicBorderView) {
        view.apply {
            setMetallicBaseColor(Color.parseColor("#C0C0C0"))
            setHighlightIntensity(0.9f)
            setShadowIntensity(0.7f)
        }
    }

    /**
     * 金色边框预设
     */
    fun applyGoldStyle(view: MetallicBorderView) {
        view.apply {
            setMetallicBaseColor(Color.parseColor("#FFD700"))
            setHighlightIntensity(1.0f)
            setShadowIntensity(0.8f)
        }
    }

    /**
     * 铜色边框预设
     */
    fun applyCopperStyle(view: MetallicBorderView) {
        view.apply {
            setMetallicBaseColor(Color.parseColor("#CD853F"))
            setHighlightIntensity(0.8f)
            setShadowIntensity(0.9f)
        }
    }

    /**
     * 钢铁边框预设
     */
    fun applySteelStyle(view: MetallicBorderView) {
        view.apply {
            setMetallicBaseColor(Color.parseColor("#708090"))
            setHighlightIntensity(0.8f)
            setShadowIntensity(0.7f)
        }
    }

    /**
     * 钛合金边框预设
     */
    fun applyTitaniumStyle(view: MetallicBorderView) {
        view.apply {
            setMetallicBaseColor(Color.parseColor("#A8A8A8"))
            setHighlightIntensity(0.95f)
            setShadowIntensity(0.8f)
        }
    }
}
