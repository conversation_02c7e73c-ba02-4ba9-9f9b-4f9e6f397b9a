package com.socialplay.gpark.ui.developer

import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.bumptech.glide.Glide
import com.meta.biz.mgs.MgsBiz
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.ReviewGameInfo
import com.socialplay.gpark.data.model.mgs.MgsGameConfigData
import com.socialplay.gpark.databinding.FragmentDeveloperReviewGameBinding
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.mw.bean.MWStartDeveloper
import com.socialplay.gpark.function.mw.launch.TSLaunch
import com.socialplay.gpark.function.mw.launch.bean.TSLaunchParams
import com.socialplay.gpark.function.mw.launch.ui.TSLaunchFailedWrapper
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.ui.developer.adapter.ReviewGameVersionAdapter
import com.socialplay.gpark.ui.developer.viewmodel.DeveloperReviewGameViewModel
import com.socialplay.gpark.util.InputUtil
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import timber.log.Timber

class DeveloperReviewGameFragment : BaseFragment<FragmentDeveloperReviewGameBinding>() {
    private val metaKv: MetaKV by inject()

    private val viewModel by viewModel<DeveloperReviewGameViewModel>()
//    private val metaVerseInteractor: MWLaunchGameInteractor by inject()
    private val tsLaunch: TSLaunch by lazy { TSLaunch() }
    private val adapter by lazy {
        ReviewGameVersionAdapter {
            when {
                it.gameInfo?.code.isNullOrEmpty() -> {
                    toast("code empty")
                }
                it.type != 4 -> {
                    toast(R.string.debug_apk_not_support)
                }
                else -> {
                    ToastUtil.showLong(requireContext(), R.string.game_start_launching)
                    val gameInfo = tsLaunch.createTSGameDetailInfo(it.gameInfo?.code ?: "",
                        it.gameInfo?.packageName ?: "",
                        it.gameInfo?.name ?: "")
                    val resIdBean = ResIdBean().setGameId(it.gameInfo?.code ?: "")
                        .setCategoryID(CategoryId.GAME_REVIEW)
                    val params = TSLaunchParams(gameInfo, resIdBean).apply {
                        developer = MWStartDeveloper(it.viewerId)
                    }
                    params.fromDev = true
                    tsLaunch.launch(requireContext(), params)
                }
            }
        }
    }

    private val args by navArgs<DeveloperReviewGameFragmentArgs>()

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentDeveloperReviewGameBinding? {
        return FragmentDeveloperReviewGameBinding.inflate(inflater, container, false)
    }

    override fun init() {
        viewLifecycleOwner.lifecycleScope.launchWhenCreated {
            viewModel.searchResultFlow.collect {
                when (it) {
                    is DataResult.Success -> {
                        val data = it.data
                        MgsBiz.saveMgsConfig(data.code, data.packageName)
                        metaKv.mgsKV.saveMgsGameConfig(MgsGameConfigData(data.code), data.packageName)
                        binding.infoGroup.visible()
                        updateView(data)
                        binding.loading.gone()
                    }
                    is DataResult.Error -> {
                        toast(it.message)
                        binding.loading.showError()
                    }
                    DataResult.Loading -> {
                        if (binding.etGameId.text.toString().isNotEmpty() || !args.token.isNullOrEmpty()) {
                            binding.loading.visible()
                            binding.loading.showLoading()
                        }
                    }
                }
            }
        }

        binding.titleBar.setOnBackClickedListener {
            findNavController().navigateUp()
        }

        binding.btnSearchGame.setOnAntiViolenceClickListener {
            viewModel.searchKey(binding.etGameId.text.toString())
            InputUtil.hideKeyboard(binding.etGameId)
        }

        binding.recyclerView.adapter = adapter

        viewLifecycleOwner.lifecycleScope.launch {
            delay(500)
            InputUtil.showSoftBoard(binding.etGameId)
        }

        val token = args.token
        if (!token.isNullOrEmpty()) {
            binding.etGameId.visible(false)
            binding.btnSearchGame.visible(false)
            Timber.d("checkcheck_game_review token:$token")

            binding.loading.setRetry {
                viewModel.searchKey(token)
            }

        }
        tsLaunch.onLaunchListener(viewLifecycleOwner) {
            onLaunchGameEnd { params, e ->
                TSLaunchFailedWrapper.show(this@DeveloperReviewGameFragment, params, e)
            }
        }
    }

    private fun updateView(data: ReviewGameInfo) {
        binding.tvGameDetailGameName.text = data.name
        binding.tvPackageName.text = "PackageName: ${data.packageName}"
        Timber.d("checkcheck_game_review gameid:${data.code} pkgName:${data.packageName}")
        Glide.with(this)
            .load(data.icon)
            .placeholder(R.drawable.placeholder_corner_10)
            .into(binding.ivGameDetailGameIcon)

        adapter.setList(data.versionList.also {
            it.forEach { item -> item.gameInfo = data }
        })
    }

    override fun loadFirstData() {
        val token = args.token
        if (!token.isNullOrEmpty()){

            viewModel.searchKey(token)
        }
    }

    override fun getFragmentName(): String {
        return "ReviewGame"
    }
}