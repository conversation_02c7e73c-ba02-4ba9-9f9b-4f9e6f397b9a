package com.socialplay.gpark.ui.view

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.widget.LinearLayout
import androidx.constraintlayout.widget.ConstraintLayout
import kotlin.math.abs


/**
 *  <AUTHOR> taiyuan
 *  time   : 2021/7/4
 *  desc   :
 */
open class WrapNestedScrollableHostLinearLayout(context: Context, attrs: AttributeSet?) : LinearLayout(context, attrs) {
    private var startX = 0f
    private var startY = 0f

    var needIntercept = true

    override fun onInterceptTouchEvent(e: MotionEvent): Bo<PERSON>an {
        handleInterceptTouchEvent(e)
        return super.onInterceptTouchEvent(e)
    }

    private fun handleInterceptTouchEvent(e: MotionEvent) {
        if (!needIntercept) return
        if (e.action == MotionEvent.ACTION_DOWN) {
            startX = e.x
            startY = e.y
            parent.requestDisallowInterceptTouchEvent(true)
        } else if (e.action == MotionEvent.ACTION_MOVE) {
            val disX = abs(e.x - startX)
            val disY = abs(e.y - startY)
            // 水平滑动距离小于 垂直距离界限时 禁止左右滑动切换
            if (disX < disY * 1.5f) {
                parent.requestDisallowInterceptTouchEvent(true)
            } else {
                parent.requestDisallowInterceptTouchEvent(false)
            }
        }
    }
}