package com.socialplay.gpark.ui.view

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ItemDecoration

class SpaceItemDecoration(
    private val leftRight: Int,
    private val topBottom: Int,
    private val showFirst: Boolean = true,
    private val showLast: Boolean = true,
) :
    ItemDecoration() {

    override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
        val layoutManager = (parent.layoutManager as? LinearLayoutManager) ?: return

        val childAdapterPosition = parent.getChildAdapterPosition(view)

        if (layoutManager.orientation == LinearLayoutManager.VERTICAL) {
            if (childAdapterPosition == layoutManager.itemCount - 1) {
                if(showLast){
                    outRect.bottom = topBottom
                }
            }

            if (childAdapterPosition != 0 || showFirst) {
                outRect.top = topBottom
            }

            outRect.left = leftRight
            outRect.right = leftRight
        } else {
            if (childAdapterPosition == layoutManager.itemCount - 1) {
                if(showLast){
                    outRect.right = leftRight
                }
            }
            outRect.top = topBottom

            if (childAdapterPosition != 0 || showFirst) {
                outRect.left = leftRight
            }

            outRect.bottom = topBottom
        }
    }
}