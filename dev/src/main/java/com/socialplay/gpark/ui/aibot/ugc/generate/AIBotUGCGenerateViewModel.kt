package com.socialplay.gpark.ui.aibot.ugc.generate

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.data.model.aibot.AIBotGenerateInfo
import com.socialplay.gpark.data.model.aibot.BotInfo
import com.socialplay.gpark.data.model.aibot.BotInfoCreate
import com.socialplay.gpark.data.model.aibot.BotLabelInfo
import com.socialplay.gpark.util.GsonUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/08/28
 *     desc   :
 *
 */
class AIBotUGCGenerateViewModel(val metaRepository: IMetaRepository, val metaKV: MetaKV) :
    ViewModel() {
    private val _aiBotInfoLiveData = MutableLiveData<BotInfo>()
    val aiBotInfoLiveData = _aiBotInfoLiveData
    private val _tagListLiveData = MutableLiveData<List<BotLabelInfo>>()
    val tagListLiveData = _tagListLiveData
    private val _generateInfo =MutableLiveData<AIBotGenerateInfo?>()
    val generateInfo = _generateInfo
    private val _saveAiBotInfoLiveData= MutableLiveData<DataResult<BotInfo?>?>(null)
    val saveAiBotInfoLiveData = _saveAiBotInfoLiveData

    companion object {
        const val MAX_COUNT = 9
    }
    fun initAIBotInfo(args: AIBotUGCGenerateFragmentArgs) = viewModelScope.launch {
        _aiBotInfoLiveData.value = GsonUtil.gsonSafeParse(args.data)
        getAllTag()
    }

    private fun getAllTag() = viewModelScope.launch {
        val value = metaKV.tTaiKV.aiBotList
        if (value.isNullOrEmpty()) {
            metaRepository.getTTaiConfigById(TTaiKV.ID_KEY_AI_BOT_LABEL).collect() {
                val value = it.data?.value
                convertTagList(value ?: "")
            }
        } else {
            convertTagList(value)
        }
    }

    private fun convertTagList(value: String) {
        var list = GsonUtil.gsonSafeParseCollection<List<BotLabelInfo>>(value) ?: emptyList()
        if (list.size > MAX_COUNT) {
            list = list.subList(0, MAX_COUNT)
        }
        _tagListLiveData.postValue(list)
    }

    /**
     * 保存
     */
    fun saveAIBotInfo(name: String, prologue: String?, dialogueExample: String?, greeting: String?, description: String?) = viewModelScope.launch {
        val data = _aiBotInfoLiveData.value?:return@launch
        val botInfo = BotInfoCreate(name = name,
            icon = data.icon,
            cover = data.cover,
            chatImage = data.chatImage,
            gender = data.gender,
            greeting = if(greeting.isNullOrEmpty())null else greeting,
            description = if(description.isNullOrEmpty())null else description,
            prologue = prologue, extraConfig = null,
            tagIdList = getSelectTagList(),
            dialogueExample = if(dialogueExample.isNullOrEmpty())null else dialogueExample,
        )
        metaRepository.saveAIBotInfo(botInfo).collect {
            if(it.succeeded){
                viewModelScope.launch(Dispatchers.IO) {
                    //成功后加延时为了补全信息
                    delay(2000L)
                    _saveAiBotInfoLiveData.postValue(it)
                }
            } else {
                _saveAiBotInfoLiveData.value = it
                _saveAiBotInfoLiveData.value = null
            }
        }
    }

    fun updateLabelSelect(labelInfo: BotLabelInfo) {
        val tagList = _tagListLiveData.value?.map {
            if (it.tagId == labelInfo.tagId) {
                it.selected = (!(it.selected ?: false))
            }
            it
        }
        _tagListLiveData.value = tagList?: emptyList()
    }

    fun updateLabelSelectList(list: List<BotLabelInfo>, isClean:Boolean) {
        val oldList = _tagListLiveData.value
        if (list.size == MAX_COUNT) {
            _tagListLiveData.value = list
        } else {
            val newList = ArrayList<BotLabelInfo>()
            newList.addAll(list)
            val newTagIdList = newList.map { it.tagId }
            var size = MAX_COUNT - list.size
            while (size > 0) {
                oldList?.forEach {
                    if (!newTagIdList.contains(it.tagId)) {
                        //当前选中列表不存在
                        newList.add(it)
                        size--
                    }
                }
            }
            _tagListLiveData.value = newList

        }
    }

    fun getSelectTagList(): List<Int> {
        return _tagListLiveData.value?.filter { it.selected == true }
            ?.map { it.tagId } ?: emptyList()
    }

    fun generateInfo() = viewModelScope.launch {
        val map = mapOf("gender" to (aiBotInfoLiveData.value?.gender?:0).toString())
        metaRepository.generateInfo(map).collect() {
            _generateInfo.value = it.data
        }
    }

    fun isFirstAICreateTips(): Boolean {
        val isFirst = metaKV.account.isFirstAICreateTips()
        if (isFirst) {
            metaKV.account.setAICreateTips()
        }
        return isFirst
    }

    /**
     * 文本检测
     */
    private fun checkTextMessage()= viewModelScope.launch {

    }



    fun clean() {
        val generateInfo = AIBotGenerateInfo()
        _generateInfo.value = generateInfo
        val list = _tagListLiveData.value?.map {
            it.selected = false
            it
        } ?: emptyList()
        if (list.isNullOrEmpty()) return
        _tagListLiveData.value = list
    }

}