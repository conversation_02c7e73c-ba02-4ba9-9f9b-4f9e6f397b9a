package com.socialplay.gpark.ui.editor.tab.loadingscreen

import android.content.ComponentCallbacks
import androidx.lifecycle.asFlow
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.MVCoreProxyInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.editor.AvatarLoadingStatus
import com.socialplay.gpark.data.model.editor.FullBodyImg
import com.socialplay.gpark.function.cdnview.CacheCdnImageTask
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.data.interactor.EditorGameLoadInteractor
import com.socialplay.gpark.ui.editor.home.EditorHomeViewModel
import com.socialplay.gpark.util.extension.collectIn
import com.socialplay.gpark.util.fromJSON
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.launch
import org.koin.android.ext.android.get
import org.koin.androidx.viewmodel.ext.android.getViewModel
import timber.log.Timber
import kotlin.math.abs

/**
 * Created by bo.li
 * Date: 2022/3/7
 * Desc:
 */

data class AvatarLoadingScreenState(
    val fullBodyImg: FullBodyImg? = null,
    val loadingStatusMessage: String? = null,
    val loadingStatus: AvatarLoadingStatus? = null,
    val artImg: String? = null,
    val tipMessageList: List<String> = emptyList(),
) : MavericksState

class AvatarLoadingScreenViewModel(
    private val editorGameLoadInteractor: EditorGameLoadInteractor,
    private val mvCoreProxyInteractor: MVCoreProxyInteractor,
    private val accountInteractor: AccountInteractor,
    private val repository: IMetaRepository,
    private val metaKV: MetaKV,
    private val editorRoleGameViewModel: EditorHomeViewModel,
    private val initialState: AvatarLoadingScreenState
) : BaseViewModel<AvatarLoadingScreenState>(initialState) {

    companion object : KoinViewModelFactory<AvatarLoadingScreenViewModel, AvatarLoadingScreenState>() {

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: AvatarLoadingScreenState
        ): AvatarLoadingScreenViewModel {
            val editorHomeViewModel = viewModelContext.activity.getViewModel<EditorHomeViewModel>()
            return AvatarLoadingScreenViewModel(get(), get(), get(), get(), get(), editorHomeViewModel, state)
        }
    }


    init {
        editorRoleGameViewModel.loadingStatusFlow.distinctUntilChanged { old, new ->
            return@distinctUntilChanged if (old is AvatarLoadingStatus.Loading && new is AvatarLoadingStatus.Loading) {
                abs(old.progress - new.progress) < 0.001
            } else {
                false
            }
        }.collectIn(viewModelScope) {
            Timber.d("AvatarLoadingScreenViewModel: UpdateAvatarLoadStatus:${it}")
            setState { this.copy(loadingStatus = it) }
            if (it is AvatarLoadingStatus.Loading) {
                Timber.d("AvatarLoadingScreenViewModel: loadingStatus:${it.message}")
                setState { this.copy(loadingStatusMessage = it.message) }
            }
        }

        accountInteractor.accountLiveData.asFlow().collectIn(viewModelScope){

            val onlineFullBodyImgUrl = it?.bgMaskImage

            val fullBodyImg = if (onlineFullBodyImgUrl.isNullOrEmpty()) {
                FullBodyImg(true, CacheCdnImageTask.CDN_DEFAULT_FULL_BODY_IMG)
            } else {
                FullBodyImg(false, onlineFullBodyImgUrl)
            }

            setState { this.copy(fullBodyImg = fullBodyImg, artImg = fullBodyImg.url) }
        }
    }

    fun refreshData() {
        viewModelScope.launch(Dispatchers.IO) {
            val tipMessageList = kotlin.runCatching {
                metaKV.tTaiKV.roleGameLoadTips.fromJSON<List<String>>()
            }.getOrNull()

            Timber.d("RefreshData: getConfig from remote messageList:${tipMessageList}")

            val shuffledList = (tipMessageList ?: emptyList()).shuffled()

            setState {
                this.copy(tipMessageList = shuffledList)
            }
        }
    }
}