package com.socialplay.gpark.ui.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.RectF
import android.util.AttributeSet
import com.socialplay.gpark.util.extension.dp
import com.youth.banner.config.IndicatorConfig
import com.youth.banner.indicator.BaseIndicator

/**
 * Created by bo.li
 * Date: 2022/4/11
 * Desc:
 */
class GameDetailIndicator : BaseIndicator {
    var rectF: RectF? = null

    constructor(context: Context?): this(context, null)

    constructor(context: Context?, attrs: AttributeSet?): this(context, attrs, 0)

    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr)  {
        rectF = RectF()
    }

    override fun getIndicatorConfig(): IndicatorConfig {
        return super.getIndicatorConfig().apply {
            indicatorSpace = 5.dp
            normalWidth = 4.dp
            selectedWidth = 20.dp
            height = 4.dp
            radius = 12.dp
            selectedColor = Color.WHITE
            normalColor = Color.parseColor("#3DFFFFFF")
            margins = IndicatorConfig.Margins(0, 0, 0, 11.dp)
        }
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        val count = config.indicatorSize
        if (count <= 1) {
            return
        }
        //间距*（总数-1）+默认宽度*（总数-1）+选中宽度
        val space = config.indicatorSpace * (count - 1)
        val normal = config.normalWidth * (count - 1)
        setMeasuredDimension(space + normal + config.selectedWidth, config.height)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        val count = config.indicatorSize
        if (count <= 1) {
            return
        }
        var left = 0f
        for (i in 0 until count) {
            mPaint.color = if (config.currentPosition == i) Color.parseColor("#FFFFFF") else config.normalColor
            val indicatorWidth = if (config.currentPosition == i) config.selectedWidth else config.normalWidth
            rectF?.let {
                it.set(left, 0f, left + indicatorWidth, config.height.toFloat())
                left += (indicatorWidth + config.indicatorSpace).toFloat()
                canvas.drawRoundRect(it, config.radius.toFloat(), config.radius.toFloat(), mPaint)
            }
        }
    }
}