package com.socialplay.gpark.ui.base.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.NonNull
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewbinding.ViewBinding
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

/**
 * create by: bin on 2021/5/11
 */
abstract class BasicQuickAdapter<T, VB : ViewBinding>(data: MutableList<T>? = null) :
    BaseQuickAdapter<T, BaseVBViewHolder<VB>>(0, data) {

    private var footerShowListener: (() -> Unit)? = null

    private var headerShowListener: (() -> Unit)? = null

    private var itemShowListener: ((T, Int) -> Unit)? = null

    /**
     * position为data的position，不是加上header的position
     */
    fun setOnItemShowListener(listener: ((item: T, position: Int) -> Unit)?) {
        this.itemShowListener = listener
    }

    fun setOnHeaderShowListener(listener: (() -> Unit)?) {
        this.headerShowListener = listener
    }

    fun setOnFooterShowListener(listener: (() -> Unit)?) {
        this.footerShowListener = listener
    }

    override fun onCreateDefViewHolder(parent: ViewGroup, viewType: Int): BaseVBViewHolder<VB> {
        val binding = viewBinding(parent,viewType)
        val vh = BaseVBViewHolder<VB>(binding.root)
        vh.binding = binding
        return vh
    }

    override fun createBaseViewHolder(view: View): BaseVBViewHolder<VB> {
        return BaseVBViewHolder(view)
    }

    abstract fun viewBinding(parent: ViewGroup, viewType: Int): VB

    fun updateData(@NonNull newDataSize: Int) {
        notifyItemRangeInserted(this.data.size - newDataSize + headerLayoutCount, newDataSize)
        compatibilityDataSizeChanged(newDataSize)
    }

    override fun onViewAttachedToWindow(holder: BaseVBViewHolder<VB>) {
        super.onViewAttachedToWindow(holder)
        val position = holder.layoutPosition - headerLayoutCount
        if (position < 0) {
            headerShowListener?.invoke()
        } else if(position >= data.size){
            footerShowListener?.invoke()
        } else {
            getItemOrNull(position)?.let {
                itemShowListener?.invoke(it, position)
            }
        }
    }

    fun BaseVBViewHolder<*>.invokeClick(block: (pos: Int) -> Unit) {
        var pos = adapterPosition
        if (pos == RecyclerView.NO_POSITION) {
            return
        }
        pos -= headerLayoutCount
        block(pos)
    }

    fun invokeItemShow() {
        kotlin.runCatching {
            val size = data.size
            if (size == 0) return
            val llm = recyclerView.layoutManager as? LinearLayoutManager ?: return
            val firstPosRaw = llm.findFirstVisibleItemPosition()
            if (firstPosRaw == -1) return
            val firstPos = (firstPosRaw - headerLayoutCount).coerceAtLeast(0)
            val lastPosRaw = llm.findLastVisibleItemPosition()
            if (lastPosRaw == -1) return
            val lastPos = (lastPosRaw - headerLayoutCount).coerceAtMost(size - 1)
            if (firstPos < 0 || lastPos < 0 || firstPos > lastPos) return
            for (position in firstPos..lastPos) {
                getItemOrNull(position)?.let {
                    itemShowListener?.invoke(it, position)
                }
            }
        }
    }
}

fun <VB : ViewBinding> ViewGroup.createViewBinding(
    creator: (inflater: LayoutInflater, root: ViewGroup, attachToRoot: Boolean) -> VB,
): VB = creator(LayoutInflater.from(this.context), this, false)

open class BaseVBViewHolder<VB : ViewBinding>(val view: View) : BaseViewHolder(view) {
    lateinit var binding: VB
    var tag: Any? = null
}