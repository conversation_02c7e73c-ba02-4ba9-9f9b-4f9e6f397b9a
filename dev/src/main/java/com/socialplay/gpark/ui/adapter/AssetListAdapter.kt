package com.socialplay.gpark.ui.adapter

import android.graphics.Color
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.bumptech.glide.Glide
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.asset.AssetItem
import com.socialplay.gpark.data.model.asset.AssetType
import com.socialplay.gpark.databinding.ItemAssetListBinding
import com.socialplay.gpark.databinding.ItemLoadMoreFooterBinding
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.util.UnitUtil

/**
 * 资源列表适配器，支持加载更多功能
 */
class AssetListAdapter : ListAdapter<AssetListItem, RecyclerView.ViewHolder>(AssetDiffCallback()) {

    private val backgroundColors = listOf(
        Color.parseColor("#CAA1FD"), Color.parseColor("#A1A9FD"),
        Color.parseColor("#FCA2C6"), Color.parseColor("#FCACA2"),
    )

    private var onLoadMoreClickListener: (() -> Unit)? = null

    companion object {
        private const val VIEW_TYPE_ASSET = 0
        private const val VIEW_TYPE_LOAD_MORE = 1
    }

    override fun getItemViewType(position: Int): Int {
        return when (getItem(position)) {
            is AssetListItem.Asset -> VIEW_TYPE_ASSET
            is AssetListItem.LoadMore -> VIEW_TYPE_LOAD_MORE
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_ASSET -> {
                val binding = ItemAssetListBinding.inflate(LayoutInflater.from(parent.context), parent, false)
                AssetViewHolder(binding)
            }
            VIEW_TYPE_LOAD_MORE -> {
                val binding = ItemLoadMoreFooterBinding.inflate(LayoutInflater.from(parent.context), parent, false)
                LoadMoreViewHolder(binding)
            }
            else -> throw IllegalArgumentException("Unknown view type: $viewType")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is AssetViewHolder -> holder.bind((getItem(position) as AssetListItem.Asset).asset)
            is LoadMoreViewHolder -> holder.bind(getItem(position) as AssetListItem.LoadMore)
        }
    }

    /**
     * 设置加载更多点击监听器
     */
    fun setOnLoadMoreClickListener(listener: (() -> Unit)?) {
        onLoadMoreClickListener = listener
    }

    /**
     * 更新数据，支持加载更多状态
     */
    fun updateData(assets: List<AssetItem>, loadMoreState: LoadMoreState?, isLoading: Boolean = false) {
        val items = mutableListOf<AssetListItem>()
        
        // 添加资源项
        items.addAll(assets.map { AssetListItem.Asset(it) })
        
        // 添加加载更多项
        if (loadMoreState != null) {
            items.add(AssetListItem.LoadMore(loadMoreState, isLoading))
        }
        
        submitList(items)
    }

    inner class AssetViewHolder(private val binding: ItemAssetListBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(item: AssetItem) {
            binding.tvAssetName.text = item.name

            Glide.with(binding.root.context)
                .load(item.imageUrl)
                .into(binding.ivAssetImage)

            binding.likeView.setLikeText(UnitUtil.formatKMCount(item.likeCount))
//            val likeIcon = if (item.isLiked) R.drawable.icon_like_selected else R.drawable.icon_post_like_unselected
//            binding.likeView.setLikeIcon(likeIcon)
            binding.tagContainer.setTags(item.tags)

            if (item.type == AssetType.CLOTHES) {
                // 服装：设置随机背景，隐藏价格
                binding.ivAssetImage.setBackgroundColor(backgroundColors.random())
                binding.priceContainer.isVisible = false
            } else {
                // 模组：无背景，显示价格
                binding.ivAssetImage.setBackgroundColor(Color.TRANSPARENT)
                binding.priceContainer.isVisible = true
                binding.tvPrice.text = item.price.toString()
            }
        }
    }

    inner class LoadMoreViewHolder(private val binding: ItemLoadMoreFooterBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(loadMoreItem: AssetListItem.LoadMore) {
            val loadMoreState = loadMoreItem.loadMoreState
            val isLoading = loadMoreItem.isLoading
            
            when {
                isLoading -> {
                    binding.progressBar.isVisible = true
                    binding.tvLoadMoreText.text = "加载中..."
                    binding.root.setOnClickListener(null)
                }
                loadMoreState.isEnd -> {
                    binding.progressBar.isVisible = false
                    binding.tvLoadMoreText.text = "没有更多数据了"
                    binding.root.setOnClickListener(null)
                }
                loadMoreState.needRefresh -> {
                    binding.progressBar.isVisible = false
                    binding.tvLoadMoreText.text = "加载失败，点击重试"
                    binding.root.setOnClickListener { onLoadMoreClickListener?.invoke() }
                }
                else -> {
                    // 默认状态，显示加载更多
                    binding.progressBar.isVisible = false
                    binding.tvLoadMoreText.text = "点击加载更多"
                    binding.root.setOnClickListener { onLoadMoreClickListener?.invoke() }
                }
            }
        }
    }

    class AssetDiffCallback : DiffUtil.ItemCallback<AssetListItem>() {
        override fun areItemsTheSame(oldItem: AssetListItem, newItem: AssetListItem): Boolean {
            return when {
                oldItem is AssetListItem.Asset && newItem is AssetListItem.Asset -> 
                    oldItem.asset.id == newItem.asset.id
                oldItem is AssetListItem.LoadMore && newItem is AssetListItem.LoadMore -> 
                    true // LoadMore项总是相同的
                else -> false
            }
        }

        override fun areContentsTheSame(oldItem: AssetListItem, newItem: AssetListItem): Boolean {
            return when {
                oldItem is AssetListItem.Asset && newItem is AssetListItem.Asset -> 
                    oldItem.asset == newItem.asset
                oldItem is AssetListItem.LoadMore && newItem is AssetListItem.LoadMore -> 
                    oldItem.loadMoreState == newItem.loadMoreState && oldItem.isLoading == newItem.isLoading
                else -> false
            }
        }
    }
}

/**
 * 资源列表项密封类
 */
sealed class AssetListItem {
    data class Asset(val asset: com.socialplay.gpark.data.model.asset.AssetItem) : AssetListItem()
    data class LoadMore(val loadMoreState: LoadMoreState, val isLoading: Boolean = false) : AssetListItem()
} 