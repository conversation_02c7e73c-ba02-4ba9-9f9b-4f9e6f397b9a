package com.socialplay.gpark.ui.post.feed.tag

import android.os.Bundle
import android.view.View
import androidx.lifecycle.lifecycleScope
import com.airbnb.epoxy.EpoxyController
import com.airbnb.mvrx.args
import com.airbnb.mvrx.asMavericksArgs
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.parentFragmentViewModel
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.post.CommunityFeedInfo
import com.socialplay.gpark.data.model.post.PostPublishStatus
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.data.model.post.request.PostTagFeedRequest
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.views.loadMoreFooter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.post.feed.CommunityFeedFragment
import com.socialplay.gpark.ui.post.feed.ICommunityTopicListener
import com.socialplay.gpark.ui.post.feed.base.BaseCommunityFeedViewModel
import com.socialplay.gpark.ui.post.feed.base.ICommunityFeedModelState
import com.socialplay.gpark.ui.post.feed.communityFeed
import com.socialplay.gpark.ui.post.feed.topicBlock
import com.socialplay.gpark.ui.post.tab.CommunityFeedTabModelState
import com.socialplay.gpark.ui.post.tab.CommunityFeedTabViewModel
import com.socialplay.gpark.ui.view.LoadingView
import com.socialplay.gpark.ui.view.video.FeedVideoHelper
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.ifEmptyNull
import com.socialplay.gpark.util.extension.toast
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 * Created by bo.li
 * Date: 2023/9/15
 * Desc:
 */
class RecommendCommunityFeedFragment : CommunityFeedFragment(), ICommunityTopicListener {

    private val viewModel: RecommendCommunityFeedViewModel by fragmentViewModel()
    private val args by args<TagCommunityFeedFragmentArgs>()
    private val parentViewModel: CommunityFeedTabViewModel by parentFragmentViewModel()
    private val scrollTargetPosition = 2

    private val feedOrder by lazy { if (PandoraToggle.communityNewOrder) PostTagFeedRequest.ORDER_TYPE_CUSTOM else PostTagFeedRequest.ORDER_TYPE_NEWEST }

    override val feedViewModel: BaseCommunityFeedViewModel<ICommunityFeedModelState>
        get() = viewModel as BaseCommunityFeedViewModel<ICommunityFeedModelState>

    companion object {
        fun newInstance(args: TagCommunityFeedFragmentArgs): RecommendCommunityFeedFragment {
            return RecommendCommunityFeedFragment().apply {
                arguments = args.asMavericksArgs()
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        parentViewModel.onEach(
            CommunityFeedTabModelState::currentUid,
            uniqueOnly()
        ) { uid ->
            refreshFeed()
        }
        viewModel.onAsync(
            RecommendCommunityFeedModelState::scrollToTop,
            onSuccess = {
                if (it) {
                    recyclerView.scrollToPosition(0)
                    viewModel.removeScrollToTop()
                }
            }
        )
        parentViewModel.onEach(
            CommunityFeedTabModelState::publishingState,
            uniqueOnly()
        ) { publishingState ->
            if (publishingState == null) {
                viewModel.removePublishingItem(null)
                return@onEach
            }
            when {
                publishingState.status == PostPublishStatus.STATUS_SUCCEEDED -> {
                    publishingState.post?.toPublishFeed(
                        GlobalContext.get().get<AccountInteractor>().accountLiveData.value,
                        publishingState.ts,
                    )?.let { it1 ->
                        viewModel.addPublishingItem(it1, false)
                    }
                }

                publishingState.status == PostPublishStatus.STATUS_FAILED -> {
                    viewModel.removePublishingItem(publishingState.ts)
                }

                !publishingState.publishOver() -> {
                    publishingState.post?.toPublishFeed(
                        GlobalContext.get().get<AccountInteractor>().accountLiveData.value,
                        publishingState.ts,
                    )?.let { it1 ->
                        viewModel.addPublishingItem(it1, true)
                    }
                }
            }
        }
        parentViewModel.onEach(CommunityFeedTabModelState::scrollFeed, uniqueOnly()) {
            Timber.d("checkcheck_refresh, ${it}")
            if (isResumed) {
                scrollToTop()
            }
        }
    }

    private fun scrollToTop() {
        viewLifecycleOwner.lifecycleScope.launch {
            if (layoutManager.findFirstVisibleItemPosition() > scrollTargetPosition) {
                recyclerView.scrollToPosition(scrollTargetPosition)
                delay(50)
            }
            recyclerView.smoothScrollToPosition(0)
        }
    }

    override fun getFeedController(): EpoxyController = simpleController(
        viewModel,
        RecommendCommunityFeedModelState::list,
        RecommendCommunityFeedModelState::loadMore,
        RecommendCommunityFeedModelState::hotTopics,
    ) { list, loadMore, topic ->
        // 话题
        val hasTopic = !topic.isNullOrEmpty()
        topic?.ifEmptyNull()?.let {
            topicBlock(getString(R.string.hot_topics), it, this@RecommendCommunityFeedFragment)
        }
        // feed
        val screenWidth = ScreenUtil.getScreenWidth(requireContext())
        val contentWidth = screenWidth - 32.dp
        list.forEachIndexed { index, communityFeedInfo ->
            communityFeed(
                contentWidth,
                communityFeedInfo,
                index,
                showUserStatus,
                showPin,
                if (hasTopic) 16.dp else 24.dp,
                this@RecommendCommunityFeedFragment,
                this@RecommendCommunityFeedFragment
            )
        }
        // 加载更多
        if (list.isNotEmpty()) {
            loadMoreFooter(loadMore, showEnd = false) {
                loadMoreFeed()
            }
        }
    }

    override fun initLoadingView(loading: LoadingView) {
    }

    override fun getEmptyTip(): String {
        return getString(R.string.footer_load_end)
    }

    override val showUserStatus: Boolean
        get() = true

    override val likeLocation: String = EventParamConstants.LOCATION_LIKE_FEED

    override val showPin: Boolean = true

    override val showTagList: Boolean = false

    override fun getFeedTag(): String = if (args.blockId == null) args.type else args.blockId.toString()

    override fun refreshPage() {
        viewModel.refreshAll(feedOrder, args.blockId, args.type)
    }

    override fun refreshParent() {

    }

    override fun onResume() {
        super.onResume()
        Analytics.track(EventConstants.EVENT_COMMUNITY_FEED_SHOW) {
            put("tag", args.blockName)
        }
    }

    override fun onNewDuration(duration: Long) {
        super.onNewDuration(duration)
        Analytics.track(EventConstants.EVENT_COMMUNITY_FEED_SHOW) {
            put("tag", args.blockName)
            put("playtime", duration)
        }
    }

    override fun goPost(item: CommunityFeedInfo) {
        Analytics.track(EventConstants.EVENT_COMMUNITY_POST_CLICK) {
            put(EventParamConstants.KEY_POSTID, item.postId)
            put(EventParamConstants.KEY_TAG, getFeedTag())
            put(
                EventParamConstants.COMMUNITY_POST_TYPE,
                if (item.top) EventParamConstants.ANALYTIC_COMMUNITY_TYPE_PIN else EventParamConstants.ANALYTIC_COMMUNITY_TYPE_NORMAL
            )
            item.tagList?.map { it.tagId }?.let { put(EventParamConstants.KEY_TAG_LIST, it) }
        }
        val videoProgress = FeedVideoHelper.getVideoProgressByResId(item.localUniqueId)
        MetaRouter.Post.goPostDetail(this, item.postId, "feed", videoProgress = videoProgress)
    }

    override fun goProfile(uuid: String) {
        MetaRouter.Profile.other(this, uuid, "communityFeed")
    }

    override fun goTopicDetail(tag: PostTag) {
        topDetail(tag, null)
    }

    override fun trackTopicRankShow(topic:PostTag, rank: Int) {
        Analytics.track(EventConstants.COMMUNITY_TAG_RECOMMEND_SHOW) {
            put(EventParamConstants.KEY_TAG_ID, topic.tagId)
            put(EventParamConstants.KEY_HOT, if (topic.hot) EventParamConstants.V_HOT else EventParamConstants.V_NOT_HOT)
            put(EventParamConstants.KEY_RK, "$rank")
        }
    }

    override fun goTopic(tag: PostTag, postId: String) {
        if (tag.tagId <= 0) {
            toast(R.string.tag_reviewing_toast)
            return
        }
        topDetail(tag, postId)
    }

    private fun topDetail(tag: PostTag, postId: String?) {
        MetaRouter.Post.topicDetail(this, tag, EventParamConstants.SOURCE_TOPIC_FEED, postId)
    }

    override fun loadMoreFeed() {
        viewModel.loadMoreTagFeed(feedOrder, args.blockId, args.type)
    }

    override val useVideoFunc: Boolean = false
}