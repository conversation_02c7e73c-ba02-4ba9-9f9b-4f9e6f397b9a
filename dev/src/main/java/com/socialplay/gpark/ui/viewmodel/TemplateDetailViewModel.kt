package com.socialplay.gpark.ui.viewmodel

import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.MavericksViewModel
import com.socialplay.gpark.data.model.template.OutstandingWork
import com.socialplay.gpark.data.model.template.TemplateDetail

/**
 * 模板详情页面状态
 */
data class TemplateDetailState(
    val templateDetail: TemplateDetail? = null,
    val isLoading: Boolean = false,
    val error: String? = null
) : MavericksState

/**
 * 模板详情页面ViewModel
 */
class TemplateDetailViewModel(
    initialState: TemplateDetailState
) : MavericksViewModel<TemplateDetailState>(initialState) {

    // 加载模板详情
    fun loadTemplateDetail(templateId: String) {
        setState { copy(isLoading = true, error = null) }

        // TODO: 实际项目中这里应该调用Repository或UseCase
        // 这里用模拟数据演示
        loadMockData()
    }

    private fun loadMockData() {
        // 模拟数据，实际项目中应该从API获取
        val mockTemplateDetail = createMockTemplateDetail()
        setState {
            copy(
                templateDetail = mockTemplateDetail,
                isLoading = false,
                error = null
            )
        }
    }

    private fun createMockTemplateDetail(): TemplateDetail {
        return TemplateDetail(
            id = "1",
            title = "Enchilada Casserole Template",
            coverUrl = "",
            description = "This is a wonderful template for creating amazing content. You can build your own game with this template easily.",
            tags = listOf("Playful", "RPG", "Adventure"),
            likeCount = 22230,
            playCount = 156000,
            starLevel = 3,
            outstandingWorks = listOf(
                OutstandingWork(
                    id = "1",
                    title = "Amazing Adventure Game",
                    coverImageUrl = "",
                    authorName = "Creator1",
                    authorAvatarUrl = "",
                    likeCount = 1200,
                    playCount = 5600
                ),
                OutstandingWork(
                    id = "2",
                    title = "Epic RPG Journey",
                    coverImageUrl = "",
                    authorName = "Creator2",
                    authorAvatarUrl = "",
                    likeCount = 2300,
                    playCount = 8900
                )
            )
        )
    }
} 