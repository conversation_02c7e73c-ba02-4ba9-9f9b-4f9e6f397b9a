package com.socialplay.gpark.ui.pay

import android.os.Bundle
import android.view.View
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.pay.PaymentMethod
import com.socialplay.gpark.data.model.pay.PaymentResult
import com.socialplay.gpark.databinding.DialogPaymentBottomSheetBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.pay.IPayInteractor
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.base.BaseBottomSheetDialogFragment
import com.socialplay.gpark.ui.pay.adapter.PaymentMethodAdapter
import com.socialplay.gpark.util.UnitUtilWrapper
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.property.viewBinding
import org.koin.core.context.GlobalContext

/**
 * 支付弹框
 * 底部弹出的支付确认界面，支持多种支付方式选择
 */
class PaymentBottomSheetDialog : BaseBottomSheetDialogFragment() {

    companion object {
        private const val KEY_PRODUCT_NAME = "product_name"
        private const val KEY_PAYMENT_AMOUNT = "payment_amount"
        private const val KEY_ACTUAL_PRICE = "actual_price"
        private const val KEY_PRODUCT_ID = "product_id"
        private const val KEY_GAME_ID = "game_id"
        private const val KEY_SOURCE = "source"

        /**
         * 显示支付弹框
         */
        fun show(
            fragmentManager: FragmentManager,
            productName: String,
            paymentAmount: Long,
            actualPrice: String,
            productId: String,
            gameId: String? = null,
            source: String? = null,
            callback: ((PaymentResult) -> Unit)? = null
        ): PaymentBottomSheetDialog {
            val dialog = PaymentBottomSheetDialog().apply {
                this.productName = productName
                this.paymentAmount = paymentAmount
                this.actualPrice = actualPrice
                this.productId = productId
                this.gameId = gameId
                this.source = source
                this.paymentCallback = callback
            }
            dialog.show(fragmentManager, "PaymentBottomSheetDialog")
            return dialog
        }
    }

    override val binding by viewBinding(DialogPaymentBottomSheetBinding::inflate)

    // 支付相关数据
    private var productName: String = ""
    private var paymentAmount: Long = 0L
    private var actualPrice: String = ""
    private var productId: String = ""
    private var gameId: String? = null
    private var source: String? = null
    private var paymentCallback: ((PaymentResult) -> Unit)? = null

    // 支付方式适配器
    private val paymentMethodAdapter = PaymentMethodAdapter()
    
    // 当前选中的支付方式
    private var selectedPaymentMethod: PaymentMethod? = null
    
    // 是否同意支付协议
    private var isAgreementChecked = true
    
    // 是否正在支付中
    private var isPaymentProcessing = false

    // 支付交互器
    private val payInteractor: IPayInteractor = GlobalContext.get().get()

    override fun getPageName() = PageNameConstants.DIALOG_PAYMENT

    override fun needCountTime() = true

    override var heightPercent: Float = 0.8f

    override fun init() {
        initViews()
        initData()
        initListeners()
    }

    override fun loadFirstData() {
        // 初始化支付方式数据
        loadPaymentMethods()
    }

    private fun initViews() {
        // 设置支付方式列表
        binding.rvPaymentMethods.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = paymentMethodAdapter
        }
    }

    private fun initData() {
        // 设置商品信息
        binding.tvProductName.text = productName
        binding.tvPaymentAmount.text = UnitUtilWrapper.formatCoinCont(paymentAmount)
        binding.tvActualPrice.text = actualPrice
    }

    private fun initListeners() {
        // 关闭按钮
        binding.ivClose.setOnAntiViolenceClickListener {
            dismissWithResult(PaymentResult.Cancelled())
        }

        // 支付协议复选框
        binding.cbAgreement.setOnCheckedChangeListener { _, isChecked ->
            isAgreementChecked = isChecked
            updateConfirmButtonState()
        }

        // 支付协议链接
        binding.tvAgreementLink.setOnAntiViolenceClickListener {
            // 跳转到支付协议页面
            MetaRouter.Web.navigate(
                this,
                title = getString(R.string.payment_agreement_title),
                url = getString(R.string.payment_agreement_url),
                showTitle = true
            )
        }

        // 确认支付按钮
        binding.btnConfirmPayment.setOnAntiViolenceClickListener {
            if (isAgreementChecked && selectedPaymentMethod != null && !isPaymentProcessing) {
                startPayment()
            }
        }

        // 支付方式选择
        paymentMethodAdapter.setOnItemClickListener { _, position ->
            val paymentMethod = paymentMethodAdapter.getItem(position)
            selectedPaymentMethod = paymentMethod
            paymentMethodAdapter.setSelectedPosition(position)
            updateConfirmButtonState()
        }
    }

    private fun loadPaymentMethods() {
        // 创建支付方式列表
        val paymentMethods = listOf(
            PaymentMethod(
                id = "coin",
                name = getString(R.string.payment_method_coin),
                iconRes = R.drawable.icon_coin,
                isAvailable = true
            ),
            PaymentMethod(
                id = "google_pay",
                name = getString(R.string.payment_method_google_pay),
                iconRes = R.drawable.icon_google_pay,
                isAvailable = true
            ),
            PaymentMethod(
                id = "apple_pay",
                name = getString(R.string.payment_method_apple_pay),
                iconRes = R.drawable.icon_apple_pay,
                isAvailable = false
            )
        )

        paymentMethodAdapter.setData(paymentMethods)
        
        // 默认选中第一个可用的支付方式
        val firstAvailable = paymentMethods.firstOrNull { it.isAvailable }
        if (firstAvailable != null) {
            selectedPaymentMethod = firstAvailable
            paymentMethodAdapter.setSelectedPosition(0)
        }
        
        updateConfirmButtonState()
    }

    private fun updateConfirmButtonState() {
        val isEnabled = isAgreementChecked && selectedPaymentMethod != null && !isPaymentProcessing
        binding.btnConfirmPayment.isEnabled = isEnabled
        
        if (isEnabled) {
            binding.btnConfirmPayment.background = getDrawable(R.drawable.bg_button_primary)
        } else {
            binding.btnConfirmPayment.background = getDrawable(R.drawable.bg_button_disabled)
        }
    }

    private fun startPayment() {
        if (selectedPaymentMethod == null) return

        isPaymentProcessing = true
        binding.loadingView.visible(true)
        updateConfirmButtonState()

        // 埋点：支付开始
        Analytics.track(
            EventConstants.EVENT_PAYMENT_START,
            mapOf(
                "product_id" to productId,
                "payment_amount" to paymentAmount.toString(),
                "payment_method" to (selectedPaymentMethod?.id ?: ""),
                "game_id" to (gameId ?: ""),
                "source" to (source ?: "")
            )
        )

        // 根据支付方式执行不同的支付逻辑
        when (selectedPaymentMethod?.id) {
            "coin" -> {
                // 平台币支付
                payWithCoins()
            }
            "google_pay" -> {
                // Google Pay支付
                payWithGooglePay()
            }
            else -> {
                // 其他支付方式
                handlePaymentError("Unsupported payment method")
            }
        }
    }

    private fun payWithCoins() {
        // 这里调用平台币支付接口
        // 实际项目中需要根据具体的支付接口实现
        payInteractor.coinsPay(
            createPayInfo()
        ) { result ->
            handlePaymentResult(result)
        }
    }

    private fun payWithGooglePay() {
        // 这里调用Google Pay支付接口
        // 实际项目中需要根据具体的支付接口实现
        payInteractor.startPay(
            requireActivity(),
            createCommonPayParams(),
            null,
            getPageName()
        ) { payResult ->
            handlePaymentResult(payResult)
        }
    }

    private fun createPayInfo(): com.socialplay.gpark.ui.gamepay.PayInfo {
        return com.socialplay.gpark.ui.gamepay.PayInfo(
            gameId = gameId ?: "",
            payAmount = paymentAmount,
            source = source,
            rawData = mutableMapOf(
                "productId" to productId,
                "productName" to productName
            )
        )
    }

    private fun createCommonPayParams(): com.socialplay.gpark.data.model.pay.CommonPayParams {
        return com.socialplay.gpark.data.model.pay.CommonPayParams(
            ourProductId = productId,
            parentProductId = productId,
            source = source,
            pageType = "pop",
            gameId = gameId,
            scene = com.socialplay.gpark.data.model.pay.IAPConstants.IAP_SCENE_PG_COIN,
            productId = productId,
            price = paymentAmount,
            sceneCode = com.socialplay.gpark.data.model.pay.IAPConstants.IAP_SCENE_CODE_PG_COIN,
            currencyCode = "USD"
        )
    }

    private fun handlePaymentResult(result: com.socialplay.gpark.data.base.DataResult<*>) {
        isPaymentProcessing = false
        binding.loadingView.visible(false)
        updateConfirmButtonState()

        if (result.succeeded) {
            // 支付成功
            Analytics.track(
                EventConstants.EVENT_PAYMENT_SUCCESS,
                mapOf(
                    "product_id" to productId,
                    "payment_amount" to paymentAmount.toString(),
                    "payment_method" to (selectedPaymentMethod?.id ?: "")
                )
            )
            dismissWithResult(PaymentResult.Success())
        } else {
            // 支付失败
            handlePaymentError(result.message ?: "Payment failed")
        }
    }

    private fun handlePaymentError(errorMessage: String) {
        isPaymentProcessing = false
        binding.loadingView.visible(false)
        updateConfirmButtonState()

        Analytics.track(
            EventConstants.EVENT_PAYMENT_FAILED,
            mapOf(
                "product_id" to productId,
                "payment_amount" to paymentAmount.toString(),
                "payment_method" to (selectedPaymentMethod?.id ?: ""),
                "error_message" to errorMessage
            )
        )

        // 显示错误提示
        com.socialplay.gpark.util.ToastUtil.showShort(errorMessage)
    }

    private fun dismissWithResult(result: PaymentResult) {
        paymentCallback?.invoke(result)
        dismissAllowingStateLoss()
    }

    override fun onBackPressed(): Boolean {
        dismissWithResult(PaymentResult.Cancelled())
        return true
    }
} 