package com.socialplay.gpark.ui.editor.detail.comment

import android.text.TextPaint
import android.text.style.ClickableSpan
import android.view.View
import android.view.ViewPropertyAnimator
import androidx.core.view.isVisible
import com.airbnb.epoxy.ModelCollector
import com.airbnb.epoxy.VisibilityState
import com.meta.box.biz.friend.model.LabelInfo
import com.socialplay.gpark.EnvConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.community.PostMedia
import com.socialplay.gpark.data.model.post.PostComment
import com.socialplay.gpark.data.model.post.PostReply
import com.socialplay.gpark.databinding.ItemUgcCommentBinding
import com.socialplay.gpark.databinding.ItemUgcCommentExpandBinding
import com.socialplay.gpark.databinding.ItemUgcCommentLoadingBinding
import com.socialplay.gpark.databinding.ItemUgcDesignFeedBinding
import com.socialplay.gpark.databinding.ItemUgcReplyBinding
import com.socialplay.gpark.ui.core.IBaseEpoxyItemListener
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.view.ExpandableTextView
import com.socialplay.gpark.ui.view.InterceptClickEventLinkMovementMethod
import com.socialplay.gpark.util.ArticleUtil
import com.socialplay.gpark.util.DateUtil.formatCommentDate
import com.socialplay.gpark.util.SpannableHelper
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.onAnimatorEnd
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.unsetOnClick
import com.socialplay.gpark.util.extension.visible

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/12/07
 *     desc   :
 * </pre>
 */
interface IUgcCommentListener : IBaseEpoxyItemListener {
    fun isMe(uid: String?): Boolean
    fun isCreator(uid: String?): Boolean
    fun iAmCreator(): Boolean
    fun goUserPage(uid: String?)
    fun operateComment(view: View, comment: PostComment, commentPosition: Int, showRedDot: Boolean)
    fun likeComment(comment: PostComment, commentPosition: Int)
    fun reply2Comment(comment: PostComment, commentPosition: Int)
    fun operateReply(
        view: View,
        reply: PostReply,
        replyPosition: Int,
        commentPosition: Int,
        isAuthorReply: Boolean
    )

    fun likeReply(
        reply: PostReply,
        replyPosition: Int,
        commentPosition: Int,
        isAuthorReply: Boolean
    )

    fun reply2Reply(reply: PostReply, commentPosition: Int)
    fun loadMoreReply(comment: PostComment, commentPosition: Int)
    fun collapseReply(comment: PostComment, commentPosition: Int)
    fun goCommentListPage()
    fun previewImage(mediaList: List<PostMedia>?, imagePosition: Int)
    fun showComment(comment: PostComment, commentPosition: Int)
    fun clickLabel(data: Pair<Int, LabelInfo?>)
}

fun ModelCollector.ugcCommentItem(
    uniqueTag: Int,
    item: PostComment,
    position: Int,
    contentWidth: Int,
    enableImage: Boolean,
    firstPin: Boolean,
    listener: IUgcCommentListener
) {
    add(
        UgcCommentItem(
            item,
            position,
            contentWidth,
            enableImage,
            firstPin,
            listener
        ).id("GameComment-${uniqueTag}-${item.commentId}")
    )
}

data class UgcCommentItem(
    val item: PostComment,
    val position: Int,
    val contentWidth: Int,
    val enableImage: Boolean,
    val firstPin: Boolean,
    val listener: IUgcCommentListener
) : ViewBindingItemModel<ItemUgcCommentBinding>(
    R.layout.item_ugc_comment,
    ItemUgcCommentBinding::bind
) {

    private var animator: ViewPropertyAnimator? = null

    override fun ItemUgcCommentBinding.onBind() {
        listener.getGlideOrNull()?.run {
            load(item.avatar).placeholder(R.drawable.icon_default_avatar)
                .circleCrop()
                .into(ivAvatar)
        }
        tvNickname.text = item.nickname
        val isMe = listener.isMe(item.uid)
        labelGroup.show(
            item.tagIds,
            item.userLabelInfo ?: item.user?.labelInfo,
            isMe = isMe,
            isCreator = listener.isCreator(item.uid),
            isUnderReview = item.underReview,
            glide = listener.getGlideOrNull()
        )
        vRedDotMoreBtn.visible(firstPin && position == 0 && listener.iAmCreator() && item.top != true)

        if (item.content.isNullOrEmpty()) {
            tvContent.gone()
            tvContent.unsetOnClick()
            tvContent.setExpandListener(null)
        } else {
            tvContent.movementMethod = InterceptClickEventLinkMovementMethod(tvContent)
            tvContent.visible()
            tvContent.setShrinkEnabled(true)
            tvContent.updateForRecyclerView(
                item.content,
                contentWidth,
                item.expandState
            )
            tvContent.setOnAntiViolenceClickListener { listener.reply2Comment(item, position) }
            tvContent.setExpandListener(object : ExpandableTextView.OnExpandListener {
                override fun onExpand(view: ExpandableTextView) {
                    item.expandState = ExpandableTextView.STATE_EXPAND
                }

                override fun onShrink(view: ExpandableTextView) {
                    item.expandState = ExpandableTextView.STATE_SHRINK
                }
            })
        }
        tvPinLabel.visible(item.top == true)
        // 作者点赞过和作者回复过, 不同时显示, 两个都有时, 优先显示作者赞过
        tvAuthorLikeLabel.visible(item.authorLike ?: false)
        tvAuthorRepliedLabel.visible(!(item.authorLike ?: false) && (item.authorReplied ?: false))

        tvTime.text = item.commentTime.formatCommentDate(root.context)
        tvLikeCount.text = UnitUtil.formatKMCount(item.likeCount)
        tvLikeCount.isSelected = item.isLike
        ivAvatar.setOnAntiViolenceClickListener { listener.goUserPage(item.uid) }
        tvNickname.setOnAntiViolenceClickListener { listener.goUserPage(item.uid) }
        ivMoreBtn.setOnAntiViolenceClickListener {
            listener.operateComment(
                it,
                item,
                position,
                vRedDotMoreBtn.isVisible
            )
        }

        tvLikeCount.setOnAntiViolenceClickListener {
            val newComment = item.switchLike()
            tvLikeCount.text = UnitUtil.formatKMCount(newComment.likeCount)
            tvLikeCount.isSelected = newComment.isLike
            listener.likeComment(item, position)
        }
        if (enableImage) {
            ArticleUtil.setImages(
                listener.getGlideOrNull(),
                cvImageContainer,
                ivImage1,
                ivImage2,
                ivImage3,
                item.mediaList
            ) {
                listener.previewImage(item.mediaList, it)
            }
        } else {
            cvImageContainer.gone()
        }
        vInvokeArea.setOnAntiViolenceClickListener { listener.reply2Comment(item, position) }

        if (item.highlight) {
            item.highlight = false
            vBgHighlight.alpha = 1.0f
            animator?.cancel()
            animator = vBgHighlight.animate()
                .alpha(0.0f)
                .setDuration(3_000)
                .setListener(onAnimatorEnd {
                    animator = null
                })
        }
        labelGroup.setListener(listener::clickLabel)
    }

    override fun ItemUgcCommentBinding.onUnbind() {
        animator?.cancel()
        animator = null
        tvContent.movementMethod = null
        ivAvatar.unsetOnClick()
        tvNickname.unsetOnClick()
        ivMoreBtn.unsetOnClick()
        tvContent.unsetOnClick()
        tvContent.setExpandListener(null)
        tvLikeCount.unsetOnClick()
        ivImage1.unsetOnClick()
        ivImage2.unsetOnClick()
        ivImage3.unsetOnClick()
        vInvokeArea.unsetOnClick()
        labelGroup.setListener(null)
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        if (visibilityState == VisibilityState.VISIBLE) {
            listener.showComment(item, position)
        }
    }
}

fun ModelCollector.ugcReplyItem(
    uniqueTag: Int,
    item: PostReply,
    replyPosition: Int,
    commentPosition: Int,
    isAuthorReply: Boolean,
    atColor: Int,
    contentWidth: Int,
    enableImage: Boolean,
    listener: IUgcCommentListener
) {
    add(
        UgcReplyItem(
            item,
            replyPosition,
            commentPosition,
            isAuthorReply,
            atColor,
            contentWidth,
            enableImage,
            listener
        ).id("GameReply-${uniqueTag}-${item.replyId}")
    )
}

data class UgcReplyItem(
    val item: PostReply,
    val replyPosition: Int,
    val commentPosition: Int,
    val isAuthorReply: Boolean,
    val atColor: Int,
    val contentWidth: Int,
    val enableImage: Boolean,
    val listener: IUgcCommentListener
) : ViewBindingItemModel<ItemUgcReplyBinding>(
    R.layout.item_ugc_reply,
    ItemUgcReplyBinding::bind
) {

    private var animator: ViewPropertyAnimator? = null

    override fun ItemUgcReplyBinding.onBind() {
        listener.getGlideOrNull()?.run {
            load(item.avatar).placeholder(R.drawable.icon_default_avatar)
                .circleCrop()
                .into(ivAvatar)
        }
        tvNickname.text = item.nickname
        val isMe = listener.isMe(item.uid)
        ulv.show(
            item.tagIds,
            item.labelInfo ?: item.user?.labelInfo,
            isMe = isMe,
            isCreator = listener.isCreator(item.uid),
            glide = listener.getGlideOrNull()
        )
        val content = if (item.replyUid.isNullOrBlank()) {
            item.content
        } else {
            SpannableHelper.Builder()
                .text("@${item.replyName}:")
                .click(object : ClickableSpan() {
                    override fun onClick(widget: View) {
                        listener.goUserPage(item.replyUid)
                    }

                    override fun updateDrawState(ds: TextPaint) {
                        ds.isUnderlineText = false
                        ds.color = atColor
                    }
                })
                .text(" " + item.content.orEmpty())
                .build()
        }
        if (content.isNullOrEmpty()) {
            tvContent.gone()
            tvContent.unsetOnClick()
            tvContent.setExpandListener(null)
        } else {
            tvContent.movementMethod = InterceptClickEventLinkMovementMethod(tvContent)
            tvContent.visible()
            tvContent.setShrinkEnabled(true)
            tvContent.updateForRecyclerView(
                content,
                contentWidth,
                item.expandState
            )
            tvContent.setOnAntiViolenceClickListener {
                listener.reply2Reply(item, replyPosition)
            }
            tvContent.setExpandListener(object : ExpandableTextView.OnExpandListener {
                override fun onExpand(view: ExpandableTextView) {
                    item.expandState = ExpandableTextView.STATE_EXPAND
                }

                override fun onShrink(view: ExpandableTextView) {
                    item.expandState = ExpandableTextView.STATE_SHRINK
                }
            })
        }

        tvTime.text = item.replyTime.formatCommentDate(root.context)
        tvLikeCount.text = UnitUtil.formatKMCount(item.likeCount)
        tvLikeCount.isSelected = item.isLike

        ivAvatar.setOnAntiViolenceClickListener { listener.goUserPage(item.uid) }
        tvNickname.setOnAntiViolenceClickListener { listener.goUserPage(item.uid) }
        ivMoreBtn.setOnAntiViolenceClickListener {
            listener.operateReply(it, item, replyPosition, commentPosition, isAuthorReply)
        }
        tvLikeCount.setOnAntiViolenceClickListener {
            val newReply = item.switchLike()
            tvLikeCount.text = UnitUtil.formatKMCount(newReply.likeCount)
            tvLikeCount.isSelected = newReply.isLike
            listener.likeReply(item, replyPosition, commentPosition, isAuthorReply)
        }
        if (enableImage) {
            ArticleUtil.setImages(
                listener.getGlideOrNull(),
                cvImageContainer,
                ivImage1,
                ivImage2,
                ivImage3,
                item.mediaList
            ) {
                listener.previewImage(item.mediaList, it)
            }
        } else {
            cvImageContainer.gone()
        }
        vInvokeArea.setOnAntiViolenceClickListener { listener.reply2Reply(item, replyPosition) }

        if (item.highlight) {
            item.highlight = false
            vBgHighlight.alpha = 1.0f
            animator?.cancel()
            animator = vBgHighlight.animate()
                .alpha(0.0f)
                .setDuration(3_000)
                .setListener(onAnimatorEnd {
                    animator = null
                })
        }
        ulv.setListener(listener::clickLabel)
    }

    override fun ItemUgcReplyBinding.onUnbind() {
        animator?.cancel()
        animator = null
        tvContent.movementMethod = null
        ivAvatar.unsetOnClick()
        tvNickname.unsetOnClick()
        ivMoreBtn.unsetOnClick()
        tvContent.unsetOnClick()
        tvContent.setExpandListener(null)
        tvLikeCount.unsetOnClick()
        ivImage1.unsetOnClick()
        ivImage2.unsetOnClick()
        ivImage3.unsetOnClick()
        vInvokeArea.unsetOnClick()
        ulv.setListener(null)
    }
}

fun ModelCollector.ugcCommentExpandItem(
    uniqueTag: Int,
    comment: PostComment,
    commentPosition: Int,
    showReplyItem: Boolean,
    listener: IUgcCommentListener
) {
    add(
        UgcCommentExpandItem(
            comment,
            commentPosition,
            showReplyItem,
            listener
        ).id("GameCommentExpand-${uniqueTag}-$showReplyItem-${comment.commentId}")
    )
}

data class UgcCommentExpandItem(
    val comment: PostComment,
    val commentPosition: Int,
    val showReplyItem: Boolean,
    val listener: IUgcCommentListener
) : ViewBindingItemModel<ItemUgcCommentExpandBinding>(
    R.layout.item_ugc_comment_expand,
    ItemUgcCommentExpandBinding::bind
) {

    override fun ItemUgcCommentExpandBinding.onBind() {
        val showExpand = comment.canShowExpand
        val context = root.context
        spaceStart.visible(showReplyItem)
        startLine.visible(!showReplyItem)
        tvExpandBtn.visible(showExpand)
        if (showExpand) {
            val replyTotal = comment.replyTotalCount
            val expandTxt =
                if (!comment.collapse && comment.isMoreStatus) {
                    if (EnvConfig.isParty()) {
                        context.getString(R.string.expand_space_cap).trim()
                    } else {
                        context.getString(R.string.expand_space_cap)
                    } + context.getString(R.string.more_cap)
                } else if (replyTotal == 1L) {
                    context.getString(R.string.expand_space_cap) + context.getString(
                        R.string.x_reply,
                        "1"
                    )
                } else {
                    context.getString(R.string.expand_space_cap) + context.getString(
                        R.string.x_replies,
                        replyTotal.toString()
                    )
                }
            tvExpandBtn.text = expandTxt
        }
        tvExpandBtn.setOnAntiViolenceClickListener {
            listener.loadMoreReply(
                comment,
                commentPosition
            )
        }

        val showCollapse = comment.canShowCollapse
        tvCollapseBtn.visible(showCollapse)
        tvCollapseBtn.setOnAntiViolenceClickListener {
            listener.collapseReply(
                comment,
                commentPosition
            )
        }

        spaceExpandBtn.visible(showExpand && showCollapse)
    }

    override fun ItemUgcCommentExpandBinding.onUnbind() {
        tvExpandBtn.unsetOnClick()
        tvCollapseBtn.unsetOnClick()
    }
}

fun ModelCollector.ugcCommentLoading(
    uniqueTag: Int,
    comment: PostComment
) {
    add(UgcCommentLoading().id("GameCommentLoading-${uniqueTag}-${comment.commentId}"))
}

class UgcCommentLoading : ViewBindingItemModel<ItemUgcCommentLoadingBinding>(
    R.layout.item_ugc_comment_loading,
    ItemUgcCommentLoadingBinding::bind
) {

    override fun ItemUgcCommentLoadingBinding.onBind() {
        lavProgress.playAnimation()
    }

    override fun ItemUgcCommentLoadingBinding.onUnbind() {
        lavProgress.cancelAnimation()
    }
}