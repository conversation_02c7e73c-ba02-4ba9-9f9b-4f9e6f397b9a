package com.socialplay.gpark.ui.view

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View
import com.socialplay.gpark.R
import kotlin.math.*

/**
 * 金属边框自定义View，支持光照效果
 *
 * 特性：
 * - 可调节光照角度
 * - 可调节边框厚度
 * - 透明中心区域
 * - 增强的高光和阴影效果
 * - 支持圆角矩形
 */
class MetallicBorderView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // 绘制相关
    private val borderPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val glowPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val clearPaint = Paint(Paint.ANTI_ALIAS_FLAG)

    // 路径和矩形
    private val borderPath = Path()
    private val innerPath = Path()
    private val glowPath = Path()
    private val borderRect = RectF()
    private val innerRect = RectF()
    private val glowRect = RectF()

    // 属性
    private var borderThickness: Float = 0f
    private var lightAngle: Float = 45f
    private var cornerRadius: Float = 0f
    private var highlightColor: Int = Color.WHITE
    private var shadowColor: Int = Color.BLACK
    private var metallicBaseColor: Int = Color.parseColor("#C0C0C0")
    private var highlightIntensity: Float = 0.9f
    private var shadowIntensity: Float = 0.6f
    private var debugMode: Boolean = false // 调试模式，不挖空中心

    init {
        initAttributes(context, attrs)
        initPaints()
    }

    private fun initAttributes(context: Context, attrs: AttributeSet?) {
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.MetallicBorderView)

        borderThickness = typedArray.getDimension(
            R.styleable.MetallicBorderView_borderThickness,
            context.resources.displayMetrics.density * 2 // 默认2dp
        )

        lightAngle = typedArray.getFloat(R.styleable.MetallicBorderView_lightAngle, 45f)

        cornerRadius = typedArray.getDimension(R.styleable.MetallicBorderView_cornerRadius, 0f)

        highlightColor = typedArray.getColor(
            R.styleable.MetallicBorderView_highlightColor,
            Color.WHITE
        )

        shadowColor = typedArray.getColor(
            R.styleable.MetallicBorderView_shadowColor,
            Color.BLACK
        )

        metallicBaseColor = typedArray.getColor(
            R.styleable.MetallicBorderView_metallicBaseColor,
            Color.parseColor("#C0C0C0")
        )

        highlightIntensity = typedArray.getFloat(
            R.styleable.MetallicBorderView_highlightIntensity,
            0.9f
        ).coerceIn(0f, 1f)

        shadowIntensity = typedArray.getFloat(
            R.styleable.MetallicBorderView_shadowIntensity,
            0.6f
        ).coerceIn(0f, 1f)

        typedArray.recycle()
    }

    private fun initPaints() {
        // 基础边框画笔
        borderPaint.apply {
            style = Paint.Style.FILL
            color = metallicBaseColor
        }

        // 高光画笔
        highlightPaint.apply {
            style = Paint.Style.FILL
        }

        // 阴影画笔
        shadowPaint.apply {
            style = Paint.Style.FILL
        }

        // 清除画笔（用于挖空中心）
        clearPaint.apply {
            xfermode = PorterDuffXfermode(PorterDuff.Mode.CLEAR)
        }
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        updatePaths()
    }

    private fun updatePaths() {
        val width = width.toFloat()
        val height = height.toFloat()

        if (width <= 0 || height <= 0) return

        // 外边框矩形
        borderRect.set(0f, 0f, width, height)

        // 内部透明区域矩形
        innerRect.set(
            borderThickness,
            borderThickness,
            width - borderThickness,
            height - borderThickness
        )

        // 创建外边框路径
        borderPath.reset()
        borderPath.addRoundRect(borderRect, cornerRadius, cornerRadius, Path.Direction.CW)

        // 创建内部路径
        innerPath.reset()
        val innerCornerRadius = maxOf(0f, cornerRadius - borderThickness)
        innerPath.addRoundRect(innerRect, innerCornerRadius, innerCornerRadius, Path.Direction.CW)

        // 更新光照效果路径
        updateLightingPaths()
    }

    private fun updateLightingPaths() {
        val width = width.toFloat()
        val height = height.toFloat()

        if (width <= 0 || height <= 0) return

        // 使用与BorderFocusedMetallicView相同的线性渐变方法
        updateBorderShader()
    }

    private fun updateBorderShader() {
        val width = width.toFloat()
        val height = height.toFloat()

        if (width <= 0 || height <= 0) return

        // 计算光照方向
        val angleRad = Math.toRadians(lightAngle.toDouble())
        val lightDx = cos(angleRad).toFloat()
        val lightDy = sin(angleRad).toFloat()

        // 创建跨越整个边框的线性渐变
        val centerX = width / 2
        val centerY = height / 2
        val diagonal = sqrt(width * width + height * height)

        // 渐变从阴影面到高光面
        val startX = centerX + lightDx * diagonal / 3
        val startY = centerY + lightDy * diagonal / 3
        val endX = centerX - lightDx * diagonal / 3
        val endY = centerY - lightDy * diagonal / 3

        // 创建明显的光晕效果
        val shadowColor = Color.argb((255 * shadowIntensity).toInt(), 60, 60, 60)
        val highlightColor = Color.argb((255 * highlightIntensity).toInt(), 255, 255, 255)

        val gradient = LinearGradient(
            startX, startY, endX, endY,
            intArrayOf(
                shadowColor,        // 阴影面：暗
                metallicBaseColor,  // 中间：基础色
                highlightColor,     // 高光面：亮
                metallicBaseColor,  // 中间：基础色
                shadowColor         // 阴影面：暗
            ),
            floatArrayOf(0f, 0.2f, 0.5f, 0.8f, 1f),
            Shader.TileMode.CLAMP
        )

        borderPaint.shader = gradient
        borderPaint.style = Paint.Style.FILL
    }



    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        if (width <= 0 || height <= 0 || borderThickness <= 0) return

        // 绘制带光晕的边框
        canvas.drawPath(borderPath, borderPaint)

        // 挖空中心区域（调试模式下跳过）
        if (!debugMode) {
            canvas.drawPath(innerPath, clearPaint)
        }
    }

    // 公共方法用于动态调整属性

    /**
     * 设置光照角度
     * @param angle 角度（0-360度）
     */
    fun setLightAngle(angle: Float) {
        lightAngle = angle % 360f
        updateBorderShader()
        invalidate()
    }

    /**
     * 设置边框厚度
     * @param thickness 厚度（像素）
     */
    fun setBorderThickness(thickness: Float) {
        borderThickness = thickness
        updatePaths()
        invalidate()
    }

    /**
     * 设置圆角半径
     * @param radius 半径（像素）
     */
    fun setCornerRadius(radius: Float) {
        cornerRadius = radius
        updatePaths()
        invalidate()
    }

    /**
     * 设置高光强度
     * @param intensity 强度（0.0-1.0）
     */
    fun setHighlightIntensity(intensity: Float) {
        highlightIntensity = intensity.coerceIn(0f, 1f)
        updateBorderShader()
        invalidate()
    }

    /**
     * 设置阴影强度
     * @param intensity 强度（0.0-1.0）
     */
    fun setShadowIntensity(intensity: Float) {
        shadowIntensity = intensity.coerceIn(0f, 1f)
        updateBorderShader()
        invalidate()
    }

    /**
     * 设置金属基础颜色
     * @param color 颜色值
     */
    fun setMetallicBaseColor(color: Int) {
        metallicBaseColor = color
        updateBorderShader()
        invalidate()
    }

    /**
     * 动画旋转光照角度
     * @param targetAngle 目标角度
     * @param duration 动画时长（毫秒）
     */
    fun animateLightAngle(targetAngle: Float, duration: Long = 1000) {
        val startAngle = lightAngle
        val endAngle = targetAngle % 360f

        val animator = android.animation.ValueAnimator.ofFloat(startAngle, endAngle).apply {
            this.duration = duration
            addUpdateListener { animation ->
                setLightAngle(animation.animatedValue as Float)
            }
        }
        animator.start()
    }

    /**
     * 开始连续旋转动画
     * @param duration 一圈的时长（毫秒）
     */
    fun startContinuousRotation(duration: Long = 5000) {
        val animator = android.animation.ValueAnimator.ofFloat(0f, 360f).apply {
            this.duration = duration
            repeatCount = android.animation.ValueAnimator.INFINITE
            addUpdateListener { animation ->
                setLightAngle(animation.animatedValue as Float)
            }
        }
        animator.start()
        tag = animator // 保存动画引用以便停止
    }

    /**
     * 停止连续旋转动画
     */
    fun stopContinuousRotation() {
        (tag as? android.animation.ValueAnimator)?.cancel()
        tag = null
    }

    /**
     * 获取当前光照角度
     */
    fun getLightAngle(): Float = lightAngle

    /**
     * 获取当前边框厚度
     */
    fun getBorderThickness(): Float = borderThickness

    /**
     * 获取当前圆角半径
     */
    fun getCornerRadius(): Float = cornerRadius

    /**
     * 设置调试模式（不挖空中心，便于观察高光效果）
     */
    fun setDebugMode(debug: Boolean) {
        debugMode = debug
        invalidate()
    }
}
