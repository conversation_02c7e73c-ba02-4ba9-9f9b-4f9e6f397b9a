package com.socialplay.gpark.ui.view

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View
import com.socialplay.gpark.R
import kotlin.math.*

/**
 * 金属边框自定义View，支持光照效果
 *
 * 特性：
 * - 可调节光照角度
 * - 可调节边框厚度
 * - 透明中心区域
 * - 增强的高光和阴影效果
 * - 支持圆角矩形
 */
class MetallicBorderView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // 绘制相关
    private val borderPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val highlightPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val shadowPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val clearPaint = Paint(Paint.ANTI_ALIAS_FLAG)

    // 路径和矩形
    private val borderPath = Path()
    private val innerPath = Path()
    private val highlightPath = Path()
    private val shadowPath = Path()
    private val borderRect = RectF()
    private val innerRect = RectF()

    // 属性
    private var borderThickness: Float = 0f
    private var lightAngle: Float = 45f
    private var cornerRadius: Float = 0f
    private var highlightColor: Int = Color.WHITE
    private var shadowColor: Int = Color.BLACK
    private var metallicBaseColor: Int = Color.parseColor("#C0C0C0")
    private var highlightIntensity: Float = 0.9f
    private var shadowIntensity: Float = 0.6f

    init {
        setWillNotDraw(false)

        val a = context.obtainStyledAttributes(attrs, R.styleable.MetallicBorderView, defStyleAttr, 0)
        borderThickness = a.getDimension(R.styleable.MetallicBorderView_borderThickness, 4f)
        cornerRadius = a.getDimension(R.styleable.MetallicBorderView_cornerRadius, 0f)
        lightAngle = a.getFloat(R.styleable.MetallicBorderView_lightAngle, 45f)
        highlightColor = a.getColor(R.styleable.MetallicBorderView_highlightColor, Color.WHITE)
        shadowColor = a.getColor(R.styleable.MetallicBorderView_shadowColor, Color.BLACK)
        metallicBaseColor = a.getColor(R.styleable.MetallicBorderView_metallicBaseColor, Color.LTGRAY)
        highlightIntensity = a.getFloat(R.styleable.MetallicBorderView_highlightIntensity, 0.8f).coerceIn(0f, 1f)
        shadowIntensity = a.getFloat(R.styleable.MetallicBorderView_shadowIntensity, 0.8f).coerceIn(0f, 1f)
        a.recycle()

        borderPaint.style = Paint.Style.STROKE
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        if (w > 0 && h > 0) {
            setupBorder(w.toFloat(), h.toFloat())
        }
    }

    private fun setupBorder(width: Float, height: Float) {
        borderPaint.strokeWidth = borderThickness
        val halfThickness = borderThickness / 2
        borderRect.set(halfThickness, halfThickness, width - halfThickness, height - halfThickness)

        borderPath.reset()
        borderPath.addRoundRect(borderRect, cornerRadius, cornerRadius, Path.Direction.CW)

        updateShader(width, height)
    }

    private fun updateShader(width: Float, height: Float) {
        if (width == 0f || height == 0f) return

        val finalHighlightColor = ColorUtils.blendARGB(metallicBaseColor, highlightColor, highlightIntensity)
        val finalShadowColor = ColorUtils.blendARGB(metallicBaseColor, shadowColor, shadowIntensity)

        val angleRad = Math.toRadians(lightAngle.toDouble())
        val length = sqrt(width * width + height * height)
        val centerX = width / 2
        val centerY = height / 2

        val startX = (centerX - cos(angleRad) * length / 2).toFloat()
        val startY = (centerY - sin(angleRad) * length / 2).toFloat()
        val endX = (centerX + cos(angleRad) * length / 2).toFloat()
        val endY = (centerY + sin(angleRad) * length / 2).toFloat()

        val colors = intArrayOf(finalShadowColor, metallicBaseColor, finalHighlightColor, metallicBaseColor, finalShadowColor)
        val positions = floatArrayOf(0.0f, 0.4f, 0.5f, 0.6f, 1.0f)

        borderPaint.shader = LinearGradient(
            startX, startY, endX, endY,
            colors, positions,
            Shader.TileMode.CLAMP
        )
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        canvas.drawPath(borderPath, borderPaint)
    }

    // --- Public API for dynamic modification ---

    fun setLightAngle(angle: Float) {
        this.lightAngle = angle
        updateShader(width.toFloat(), height.toFloat())
        invalidate()
    }

    fun setBorderThickness(thickness: Float) {
        this.borderThickness = thickness
        setupBorder(width.toFloat(), height.toFloat())
        invalidate()
    }

    fun setCornerRadius(radius: Float) {
        this.cornerRadius = radius
        setupBorder(width.toFloat(), height.toFloat())
        invalidate()
    }

    /**
     * 设置高光强度
     * @param intensity 强度（0.0-1.0）
     */
    fun setHighlightIntensity(intensity: Float) {
        highlightIntensity = intensity.coerceIn(0f, 1f)
        updateShader(width.toFloat(), height.toFloat())
        invalidate()
    }

    /**
     * 设置阴影强度
     * @param intensity 强度（0.0-1.0）
     */
    fun setShadowIntensity(intensity: Float) {
        shadowIntensity = intensity.coerceIn(0f, 1f)
        updateShader(width.toFloat(), height.toFloat())
        invalidate()
    }

    /**
     * 设置金属基础颜色
     * @param color 颜色值
     */
    fun setMetallicBaseColor(color: Int) {
        metallicBaseColor = color
        updateShader(width.toFloat(), height.toFloat())
        invalidate()
    }

    /**
     * 动画旋转光照角度
     * @param targetAngle 目标角度
     * @param duration 动画时长（毫秒）
     */
    fun animateLightAngle(targetAngle: Float, duration: Long = 1000) {
        val startAngle = lightAngle
        val endAngle = targetAngle % 360f

        val animator = android.animation.ValueAnimator.ofFloat(startAngle, endAngle).apply {
            this.duration = duration
            addUpdateListener { animation ->
                setLightAngle(animation.animatedValue as Float)
            }
        }
        animator.start()
    }

    /**
     * 开始连续旋转动画
     * @param duration 一圈的时长（毫秒）
     */
    fun startContinuousRotation(duration: Long = 5000) {
        val animator = android.animation.ValueAnimator.ofFloat(0f, 360f).apply {
            this.duration = duration
            repeatCount = android.animation.ValueAnimator.INFINITE
            addUpdateListener { animation ->
                setLightAngle(animation.animatedValue as Float)
            }
        }
        animator.start()
        tag = animator // 保存动画引用以便停止
    }

    /**
     * 停止连续旋转动画
     */
    fun stopContinuousRotation() {
        (tag as? android.animation.ValueAnimator)?.cancel()
        tag = null
    }

    /**
     * 获取当前光照角度
     */
    fun getLightAngle(): Float = lightAngle

    /**
     * 获取当前边框厚度
     */
    fun getBorderThickness(): Float = borderThickness

    /**
     * 获取当前圆角半径
     */
    fun getCornerRadius(): Float = cornerRadius
}
