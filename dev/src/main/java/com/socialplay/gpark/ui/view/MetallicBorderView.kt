package com.socialplay.gpark.ui.view

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View
import androidx.core.content.ContextCompat
import com.socialplay.gpark.R
import kotlin.math.*

/**
 * 金属边框自定义View，支持光照效果
 * 
 * 特性：
 * - 可调节光照角度
 * - 可调节边框厚度
 * - 透明中心区域
 * - 高光和阴影效果
 * - 支持圆角矩形
 */
class MetallicBorderView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // 绘制相关
    private val borderPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val highlightPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val shadowPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val clearPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    
    // 路径和矩形
    private val borderPath = Path()
    private val innerPath = Path()
    private val borderRect = RectF()
    private val innerRect = RectF()
    
    // 属性
    private var borderThickness: Float = 0f
    private var lightAngle: Float = 45f // 默认45度
    private var cornerRadius: Float = 0f
    private var highlightColor: Int = Color.WHITE
    private var shadowColor: Int = Color.BLACK
    private var metallicBaseColor: Int = Color.parseColor("#C0C0C0") // 银色
    private var highlightIntensity: Float = 0.8f
    private var shadowIntensity: Float = 0.3f
    
    init {
        initAttributes(context, attrs)
        initPaints()
    }
    
    private fun initAttributes(context: Context, attrs: AttributeSet?) {
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.MetallicBorderView)
        
        borderThickness = typedArray.getDimension(
            R.styleable.MetallicBorderView_borderThickness,
            context.resources.displayMetrics.density // 默认1dp
        )
        
        lightAngle = typedArray.getFloat(R.styleable.MetallicBorderView_lightAngle, 45f)
        
        cornerRadius = typedArray.getDimension(R.styleable.MetallicBorderView_cornerRadius, 0f)
        
        highlightColor = typedArray.getColor(
            R.styleable.MetallicBorderView_highlightColor,
            Color.WHITE
        )
        
        shadowColor = typedArray.getColor(
            R.styleable.MetallicBorderView_shadowColor,
            Color.BLACK
        )
        
        metallicBaseColor = typedArray.getColor(
            R.styleable.MetallicBorderView_metallicBaseColor,
            Color.parseColor("#C0C0C0")
        )
        
        highlightIntensity = typedArray.getFloat(
            R.styleable.MetallicBorderView_highlightIntensity,
            0.8f
        ).coerceIn(0f, 1f)
        
        shadowIntensity = typedArray.getFloat(
            R.styleable.MetallicBorderView_shadowIntensity,
            0.3f
        ).coerceIn(0f, 1f)
        
        typedArray.recycle()
    }
    
    private fun initPaints() {
        // 基础边框画笔
        borderPaint.apply {
            style = Paint.Style.FILL
            color = metallicBaseColor
        }
        
        // 高光画笔
        highlightPaint.apply {
            style = Paint.Style.FILL
            color = Color.argb(
                (255 * highlightIntensity).toInt(),
                Color.red(highlightColor),
                Color.green(highlightColor),
                Color.blue(highlightColor)
            )
        }
        
        // 阴影画笔
        shadowPaint.apply {
            style = Paint.Style.FILL
            color = Color.argb(
                (255 * shadowIntensity).toInt(),
                Color.red(shadowColor),
                Color.green(shadowColor),
                Color.blue(shadowColor)
            )
        }
        
        // 清除画笔（用于挖空中心）
        clearPaint.apply {
            xfermode = PorterDuffXfermode(PorterDuff.Mode.CLEAR)
        }
    }
    
    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        updatePaths()
    }
    
    private fun updatePaths() {
        val width = width.toFloat()
        val height = height.toFloat()
        
        if (width <= 0 || height <= 0) return
        
        // 外边框矩形
        borderRect.set(0f, 0f, width, height)
        
        // 内部透明区域矩形
        innerRect.set(
            borderThickness,
            borderThickness,
            width - borderThickness,
            height - borderThickness
        )
        
        // 创建外边框路径
        borderPath.reset()
        borderPath.addRoundRect(borderRect, cornerRadius, cornerRadius, Path.Direction.CW)
        
        // 创建内部路径
        innerPath.reset()
        val innerCornerRadius = maxOf(0f, cornerRadius - borderThickness)
        innerPath.addRoundRect(innerRect, innerCornerRadius, innerCornerRadius, Path.Direction.CW)
    }
    
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        
        if (width <= 0 || height <= 0 || borderThickness <= 0) return
        
        // 使用离屏缓冲区来实现挖空效果
        val layerId = canvas.saveLayer(0f, 0f, width.toFloat(), height.toFloat(), null)
        
        // 1. 绘制基础金属边框
        canvas.drawPath(borderPath, borderPaint)
        
        // 2. 绘制光照效果
        drawLightingEffects(canvas)
        
        // 3. 挖空中心区域
        canvas.drawPath(innerPath, clearPaint)
        
        canvas.restoreToCount(layerId)
    }
    
    private fun drawLightingEffects(canvas: Canvas) {
        val width = width.toFloat()
        val height = height.toFloat()
        
        // 计算光照方向向量
        val angleRad = Math.toRadians(lightAngle.toDouble())
        val lightDx = cos(angleRad).toFloat()
        val lightDy = sin(angleRad).toFloat()
        
        // 创建高光渐变
        val highlightGradient = createLightGradient(
            width, height, lightDx, lightDy, highlightColor, true
        )
        highlightPaint.shader = highlightGradient
        
        // 创建阴影渐变（相反方向）
        val shadowGradient = createLightGradient(
            width, height, -lightDx, -lightDy, shadowColor, false
        )
        shadowPaint.shader = shadowGradient
        
        // 绘制高光效果（只在边框区域）
        canvas.drawPath(borderPath, highlightPaint)
        
        // 绘制阴影效果（只在边框区域）
        canvas.drawPath(borderPath, shadowPaint)
    }
    
    private fun createLightGradient(
        width: Float,
        height: Float,
        dx: Float,
        dy: Float,
        color: Int,
        isHighlight: Boolean
    ): LinearGradient {
        // 计算渐变的起点和终点
        val centerX = width / 2
        val centerY = height / 2
        val maxDistance = sqrt(width * width + height * height) / 2
        
        val startX = centerX - dx * maxDistance * 0.5f
        val startY = centerY - dy * maxDistance * 0.5f
        val endX = centerX + dx * maxDistance * 0.5f
        val endY = centerY + dy * maxDistance * 0.5f
        
        val intensity = if (isHighlight) highlightIntensity else shadowIntensity
        val transparentColor = Color.argb(0, Color.red(color), Color.green(color), Color.blue(color))
        val opaqueColor = Color.argb(
            (255 * intensity).toInt(),
            Color.red(color),
            Color.green(color),
            Color.blue(color)
        )
        
        return LinearGradient(
            startX, startY, endX, endY,
            intArrayOf(opaqueColor, transparentColor),
            floatArrayOf(0f, 1f),
            Shader.TileMode.CLAMP
        )
    }
    
    // 公共方法用于动态调整属性
    
    /**
     * 设置光照角度
     * @param angle 角度（0-360度）
     */
    fun setLightAngle(angle: Float) {
        lightAngle = angle % 360f
        invalidate()
    }
    
    /**
     * 设置边框厚度
     * @param thickness 厚度（像素）
     */
    fun setBorderThickness(thickness: Float) {
        borderThickness = thickness
        updatePaths()
        invalidate()
    }
    
    /**
     * 设置圆角半径
     * @param radius 半径（像素）
     */
    fun setCornerRadius(radius: Float) {
        cornerRadius = radius
        updatePaths()
        invalidate()
    }
    
    /**
     * 设置高光强度
     * @param intensity 强度（0.0-1.0）
     */
    fun setHighlightIntensity(intensity: Float) {
        highlightIntensity = intensity.coerceIn(0f, 1f)
        initPaints()
        invalidate()
    }
    
    /**
     * 设置阴影强度
     * @param intensity 强度（0.0-1.0）
     */
    fun setShadowIntensity(intensity: Float) {
        shadowIntensity = intensity.coerceIn(0f, 1f)
        initPaints()
        invalidate()
    }
    
    /**
     * 设置金属基础颜色
     * @param color 颜色值
     */
    fun setMetallicBaseColor(color: Int) {
        metallicBaseColor = color
        borderPaint.color = color
        invalidate()
    }
}
