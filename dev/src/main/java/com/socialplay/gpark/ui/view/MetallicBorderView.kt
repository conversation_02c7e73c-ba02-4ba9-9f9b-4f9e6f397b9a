package com.socialplay.gpark.ui.view

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View
import com.socialplay.gpark.R
import kotlin.math.*

/**
 * 金属边框自定义View，支持光照效果
 *
 * 特性：
 * - 可调节光照角度
 * - 可调节边框厚度
 * - 透明中心区域
 * - 增强的高光和阴影效果
 * - 支持圆角矩形
 */
class MetallicBorderView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // 绘制相关
    private val borderPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val highlightPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val shadowPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val clearPaint = Paint(Paint.ANTI_ALIAS_FLAG)

    // 路径和矩形
    private val borderPath = Path()
    private val innerPath = Path()
    private val highlightPath = Path()
    private val shadowPath = Path()
    private val borderRect = RectF()
    private val innerRect = RectF()

    // 属性
    private var borderThickness: Float = 0f
    private var lightAngle: Float = 45f
    private var cornerRadius: Float = 0f
    private var highlightColor: Int = Color.WHITE
    private var shadowColor: Int = Color.BLACK
    private var metallicBaseColor: Int = Color.parseColor("#C0C0C0")
    private var highlightIntensity: Float = 0.9f
    private var shadowIntensity: Float = 0.6f
    private var debugMode: Boolean = false // 调试模式，不挖空中心

    init {
        initAttributes(context, attrs)
        initPaints()
    }

    private fun initAttributes(context: Context, attrs: AttributeSet?) {
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.MetallicBorderView)

        borderThickness = typedArray.getDimension(
            R.styleable.MetallicBorderView_borderThickness,
            context.resources.displayMetrics.density * 2 // 默认2dp
        )

        lightAngle = typedArray.getFloat(R.styleable.MetallicBorderView_lightAngle, 45f)

        cornerRadius = typedArray.getDimension(R.styleable.MetallicBorderView_cornerRadius, 0f)

        highlightColor = typedArray.getColor(
            R.styleable.MetallicBorderView_highlightColor,
            Color.WHITE
        )

        shadowColor = typedArray.getColor(
            R.styleable.MetallicBorderView_shadowColor,
            Color.BLACK
        )

        metallicBaseColor = typedArray.getColor(
            R.styleable.MetallicBorderView_metallicBaseColor,
            Color.parseColor("#C0C0C0")
        )

        highlightIntensity = typedArray.getFloat(
            R.styleable.MetallicBorderView_highlightIntensity,
            0.9f
        ).coerceIn(0f, 1f)

        shadowIntensity = typedArray.getFloat(
            R.styleable.MetallicBorderView_shadowIntensity,
            0.6f
        ).coerceIn(0f, 1f)

        typedArray.recycle()
    }

    private fun initPaints() {
        // 基础边框画笔
        borderPaint.apply {
            style = Paint.Style.FILL
            color = metallicBaseColor
        }

        // 高光画笔
        highlightPaint.apply {
            style = Paint.Style.FILL
        }

        // 阴影画笔
        shadowPaint.apply {
            style = Paint.Style.FILL
        }

        // 清除画笔（用于挖空中心）
        clearPaint.apply {
            xfermode = PorterDuffXfermode(PorterDuff.Mode.CLEAR)
        }
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        updatePaths()
    }

    private fun updatePaths() {
        val width = width.toFloat()
        val height = height.toFloat()

        if (width <= 0 || height <= 0) return

        // 外边框矩形
        borderRect.set(0f, 0f, width, height)

        // 内部透明区域矩形
        innerRect.set(
            borderThickness,
            borderThickness,
            width - borderThickness,
            height - borderThickness
        )

        // 创建外边框路径
        borderPath.reset()
        borderPath.addRoundRect(borderRect, cornerRadius, cornerRadius, Path.Direction.CW)

        // 创建内部路径
        innerPath.reset()
        val innerCornerRadius = maxOf(0f, cornerRadius - borderThickness)
        innerPath.addRoundRect(innerRect, innerCornerRadius, innerCornerRadius, Path.Direction.CW)

        // 更新光照效果路径
        updateLightingPaths()
    }

    private fun updateLightingPaths() {
        val width = width.toFloat()
        val height = height.toFloat()

        if (width <= 0 || height <= 0) return

        // 计算光照方向向量
        // 注意：Android坐标系Y轴向下，所以需要调整
        // 0度=右，90度=下，180度=左，270度=上
        // 45度应该是右下方向，对应左上角光源
        val angleRad = Math.toRadians(lightAngle.toDouble())
        val lightDx = cos(angleRad).toFloat()
        val lightDy = sin(angleRad).toFloat()

        // 高光应该在光源的相反方向（受光面）
        // 如果光从左上角来（225度），高光应该在右下角（45度）
        val highlightDx = -lightDx  // 高光在光源相反方向
        val highlightDy = -lightDy

        // 阴影在光照方向（背光面）
        val shadowDx = lightDx
        val shadowDy = lightDy

        createHighlightPath(width, height, highlightDx, highlightDy)
        createShadowPath(width, height, shadowDx, shadowDy)
    }

    private fun createHighlightPath(width: Float, height: Float, lightDx: Float, lightDy: Float) {
        highlightPath.reset()

        // 计算高光区域的中心点（沿光照方向偏移更多）
        val centerX = width / 2 + lightDx * width * 0.2f
        val centerY = height / 2 + lightDy * height * 0.2f

        // 创建非常大的高光椭圆区域，确保覆盖整个边框
        val highlightWidth = width * 1.2f // 高光覆盖120%的宽度
        val highlightHeight = height * 1.2f // 高光覆盖120%的高度

        val highlightRect = RectF(
            centerX - highlightWidth / 2,
            centerY - highlightHeight / 2,
            centerX + highlightWidth / 2,
            centerY + highlightHeight / 2
        )

        highlightPath.addOval(highlightRect, Path.Direction.CW)

        // 创建更明显的高光渐变，使用纯白色
        val maxAlpha = (255 * highlightIntensity).toInt()
        val highlightGradient = RadialGradient(
            centerX, centerY,
            min(highlightWidth, highlightHeight) / 3, // 缩小渐变半径，让高光更集中
            intArrayOf(
                Color.argb(maxAlpha, 255, 255, 255),                    // 中心：纯白色
                Color.argb((maxAlpha * 0.8f).toInt(), 255, 255, 255),   // 80%
                Color.argb((maxAlpha * 0.5f).toInt(), 255, 255, 255),   // 50%
                Color.argb((maxAlpha * 0.2f).toInt(), 255, 255, 255),   // 20%
                Color.TRANSPARENT                                        // 边缘：透明
            ),
            floatArrayOf(0f, 0.2f, 0.5f, 0.8f, 1f),
            Shader.TileMode.CLAMP
        )

        highlightPaint.shader = highlightGradient
    }

    private fun createShadowPath(width: Float, height: Float, shadowDx: Float, shadowDy: Float) {
        shadowPath.reset()

        // 计算阴影区域的中心点（与高光相反方向，偏移更多）
        val centerX = width / 2 + shadowDx * width * 0.25f
        val centerY = height / 2 + shadowDy * height * 0.25f

        // 创建更大的阴影椭圆区域
        val shadowWidth = width * 1.0f // 阴影覆盖100%的宽度
        val shadowHeight = height * 1.0f // 阴影覆盖100%的高度

        val shadowRect = RectF(
            centerX - shadowWidth / 2,
            centerY - shadowHeight / 2,
            centerX + shadowWidth / 2,
            centerY + shadowHeight / 2
        )

        shadowPath.addOval(shadowRect, Path.Direction.CW)

        // 创建更明显的阴影渐变
        val maxAlpha = (255 * shadowIntensity).toInt()
        val shadowGradient = RadialGradient(
            centerX, centerY,
            min(shadowWidth, shadowHeight) / 3, // 缩小渐变半径，让阴影更集中
            intArrayOf(
                Color.argb(maxAlpha, 0, 0, 0),                        // 中心：纯黑色
                Color.argb((maxAlpha * 0.7f).toInt(), 0, 0, 0),       // 70%
                Color.argb((maxAlpha * 0.4f).toInt(), 0, 0, 0),       // 40%
                Color.argb((maxAlpha * 0.1f).toInt(), 0, 0, 0),       // 10%
                Color.TRANSPARENT                                      // 边缘：透明
            ),
            floatArrayOf(0f, 0.3f, 0.6f, 0.9f, 1f),
            Shader.TileMode.CLAMP
        )

        shadowPaint.shader = shadowGradient
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        if (width <= 0 || height <= 0 || borderThickness <= 0) return

        // 使用离屏缓冲区来实现复杂的绘制效果
        val layerId = canvas.saveLayer(0f, 0f, width.toFloat(), height.toFloat(), null)

        // 1. 绘制基础金属边框
        canvas.drawPath(borderPath, borderPaint)

        // 2. 绘制阴影效果（使用正片叠底模式）
        shadowPaint.xfermode = PorterDuffXfermode(PorterDuff.Mode.MULTIPLY)
        canvas.save()
        canvas.clipPath(borderPath)
        canvas.drawPath(shadowPath, shadowPaint)
        canvas.restore()
        shadowPaint.xfermode = null

        // 3. 绘制高光效果（使用屏幕模式增强亮度）
        highlightPaint.xfermode = PorterDuffXfermode(PorterDuff.Mode.SCREEN)
        canvas.save()
        canvas.clipPath(borderPath)
        canvas.drawPath(highlightPath, highlightPaint)
        canvas.restore()
        highlightPaint.xfermode = null

        // 4. 挖空中心区域（调试模式下跳过）
        if (!debugMode) {
            canvas.drawPath(innerPath, clearPaint)
        }

        canvas.restoreToCount(layerId)
    }

    // 公共方法用于动态调整属性

    /**
     * 设置光照角度
     * @param angle 角度（0-360度）
     */
    fun setLightAngle(angle: Float) {
        lightAngle = angle % 360f
        updateLightingPaths()
        invalidate()
    }

    /**
     * 设置边框厚度
     * @param thickness 厚度（像素）
     */
    fun setBorderThickness(thickness: Float) {
        borderThickness = thickness
        updatePaths()
        invalidate()
    }

    /**
     * 设置圆角半径
     * @param radius 半径（像素）
     */
    fun setCornerRadius(radius: Float) {
        cornerRadius = radius
        updatePaths()
        invalidate()
    }

    /**
     * 设置高光强度
     * @param intensity 强度（0.0-1.0）
     */
    fun setHighlightIntensity(intensity: Float) {
        highlightIntensity = intensity.coerceIn(0f, 1f)
        updateLightingPaths()
        invalidate()
    }

    /**
     * 设置阴影强度
     * @param intensity 强度（0.0-1.0）
     */
    fun setShadowIntensity(intensity: Float) {
        shadowIntensity = intensity.coerceIn(0f, 1f)
        updateLightingPaths()
        invalidate()
    }

    /**
     * 设置金属基础颜色
     * @param color 颜色值
     */
    fun setMetallicBaseColor(color: Int) {
        metallicBaseColor = color
        borderPaint.color = color
        invalidate()
    }

    /**
     * 动画旋转光照角度
     * @param targetAngle 目标角度
     * @param duration 动画时长（毫秒）
     */
    fun animateLightAngle(targetAngle: Float, duration: Long = 1000) {
        val startAngle = lightAngle
        val endAngle = targetAngle % 360f

        val animator = android.animation.ValueAnimator.ofFloat(startAngle, endAngle).apply {
            this.duration = duration
            addUpdateListener { animation ->
                setLightAngle(animation.animatedValue as Float)
            }
        }
        animator.start()
    }

    /**
     * 开始连续旋转动画
     * @param duration 一圈的时长（毫秒）
     */
    fun startContinuousRotation(duration: Long = 5000) {
        val animator = android.animation.ValueAnimator.ofFloat(0f, 360f).apply {
            this.duration = duration
            repeatCount = android.animation.ValueAnimator.INFINITE
            addUpdateListener { animation ->
                setLightAngle(animation.animatedValue as Float)
            }
        }
        animator.start()
        tag = animator // 保存动画引用以便停止
    }

    /**
     * 停止连续旋转动画
     */
    fun stopContinuousRotation() {
        (tag as? android.animation.ValueAnimator)?.cancel()
        tag = null
    }

    /**
     * 获取当前光照角度
     */
    fun getLightAngle(): Float = lightAngle

    /**
     * 获取当前边框厚度
     */
    fun getBorderThickness(): Float = borderThickness

    /**
     * 获取当前圆角半径
     */
    fun getCornerRadius(): Float = cornerRadius

    /**
     * 设置调试模式（不挖空中心，便于观察高光效果）
     */
    fun setDebugMode(debug: Boolean) {
        debugMode = debug
        invalidate()
    }
}
