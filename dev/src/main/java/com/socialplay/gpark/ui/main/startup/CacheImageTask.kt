package com.socialplay.gpark.ui.main.startup

import com.socialplay.gpark.function.cdnview.CacheCdnImageTask
import kotlinx.coroutines.suspendCancellableCoroutine
import org.koin.core.context.GlobalContext
import kotlin.coroutines.resume

class CacheImageTask : HomeStartupTask("CacheImageTask") {
    override suspend fun run() {
        val e = suspendCancellableCoroutine<Throwable?> { cont ->
            CacheCdnImageTask.startCacheImage(GlobalContext.get().get()) {
                if (cont.isActive){
                    cont.resume(null)
                }
            }
        }
        if (e != null) {
            throw e
        }
    }
}