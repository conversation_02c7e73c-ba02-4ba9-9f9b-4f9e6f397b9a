package com.socialplay.gpark.ui.gamereview.dialog

import android.os.Bundle
import android.text.TextWatcher
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.gamereview.AddAppraiseReplyRequest
import com.socialplay.gpark.databinding.DialogReplyCommentInputBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.ui.account.AccPwdV7Dialog
import com.socialplay.gpark.ui.account.AccPwdV7DialogArgs
import com.socialplay.gpark.ui.base.BaseDialogFragment
import com.socialplay.gpark.ui.gamereview.GameReviewViewModel
import com.socialplay.gpark.util.DeviceUtil
import com.socialplay.gpark.util.InputUtil
import com.socialplay.gpark.util.KeyboardHeightUtilV2
import com.socialplay.gpark.util.extension.setHintWithArgs
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setPaddingEx
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.androidx.viewmodel.ext.android.viewModel
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 * Created by liujiang
 * Date: 2023/12/4 18:24
 * Desc:
 * Version:
 */
class ReplyCommentDialog : BaseDialogFragment() {

    override var navColorRes = R.color.white

    override val binding by viewBinding(DialogReplyCommentInputBinding::inflate)

    private val viewModel by viewModel<GameReviewViewModel>()
    private val isHarmonyOs = DeviceUtil.isHarmonyOs()
    private var result = false
    private var autoInvokeKeyboard = true
    private var textWatcher: TextWatcher? = null
    private var callback: ((Boolean, String) -> Unit)? = null

    companion object {
        private const val KEY = "ReplyCommentDialog"
        private const val KEY_USER_UID = "user_uid"
        private const val USER_COMMENT_ID = "user_comment_id"
        private const val USER_REPLY_ID = "user_reply_id"
        private const val USER_REPLY_UID = "user_reply_uid"
        private const val USER_REPLY_NICKNAME = "user_reply_nickname"
        private const val SOURCE = "source"
        private const val GAME_ID = "game_id"

        fun show(
            fragment: Fragment,
            uid: String?,
            commentId: String?,
            replyId: String?,
            replyUid: String?,
            replyNickname: String?,
            source: String,
            gameId: String,
            callback: (Boolean, String) -> Unit
        ) {
            AccPwdV7Dialog.show(fragment, AccPwdV7DialogArgs.SOURCE_PUBLISH_REPLY_COMMENT) {
                if (it) {
                    val dialog = ReplyCommentDialog()
                    dialog.arguments = Bundle().apply {
                        putString(KEY_USER_UID, uid)
                        putString(USER_COMMENT_ID, commentId)
                        putString(USER_REPLY_ID, replyId)
                        putString(USER_REPLY_UID, replyUid)
                        putString(USER_REPLY_NICKNAME, replyNickname)
                        putString(SOURCE,source)
                        putString(GAME_ID, gameId)
                    }
                    dialog.callback = callback
                    dialog.show(fragment.childFragmentManager, KEY)
                }
            }
        }
    }


    override fun init() {
        upDataContentCountUI("ReplyCommentDialog")
        binding.etMgsMessage.setHintWithArgs(
            R.string.replying_at,
            arguments?.getString(USER_REPLY_NICKNAME)
        )
        binding.btnSendMessage.isVisible = false
        binding.btnSendMessage.setOnAntiViolenceClickListener {
            val input = binding.etMgsMessage.text.toString().trim()
            if (input.length > getContentMaxCount()) {
                return@setOnAntiViolenceClickListener
            }
            if (input.isEmpty()) {
                toast(getString(R.string.cannot_empty).format("getInputEmpty()"))
                return@setOnAntiViolenceClickListener
            }
            editSubmit(binding.etMgsMessage.text?.toString().orEmpty())
        }
        binding.etMgsMessage.addTextChangedListener{
            binding.btnSendMessage.isVisible = (it?.length ?: 0) > 0
        }

        viewModel.replyCommentLiveData.observe(viewLifecycleOwner) {
            val success = it.first
            val msg = it.second
            Timber.d("replyCommentLiveData $success $msg")
            if (success) {
                if (!(arguments?.getString(USER_REPLY_ID) ?: "").isNullOrEmpty()) {
                    Analytics.track(
                        EventConstants.EVENT_GAME_REVIEW_REPLIES_SUCESS,
                        "gameid" to arguments?.getString(GAME_ID).orEmpty()
                    )
                }
            }
            callback?.invoke(success, msg ?: "")
            dismissAllowingStateLoss()
        }
    }

    private fun enableSend(enable: Boolean) {
        if (enable) {
            binding.btnSendMessage.isEnabled = true
            binding.btnSendMessage.alpha = 1F
        } else {
            binding.btnSendMessage.isEnabled = false
            binding.btnSendMessage.alpha = 0.3F
        }
    }

    override fun loadFirstData() {
    }

    override fun onResume() {
        super.onResume()
        Timber.d("onResume isHarmonyOs $isHarmonyOs")
        KeyboardHeightUtilV2.registerKeyboardHeightListener(requireActivity()) {
            Timber.d("onKeyboardHeightChanged height $it paddingBottom ${binding.root.paddingBottom}")
            if (it > 0 && binding.root.paddingBottom == 0) {
                binding.root.setPaddingEx(
                    bottom = harmonyInputPadding(it)
                )
            }
            if (it == 0) {
                dismissAllowingStateLoss()
            }
            Timber.d("onKeyboardHeightChanged paddingBottom ${binding.root.paddingBottom}")
        }
        viewLifecycleOwner.lifecycleScope.launch {
            delay(150)
            InputUtil.showSoftBoard(binding.etMgsMessage)
        }
        Timber.d("autoInvokeKeyboard $autoInvokeKeyboard")
        if (autoInvokeKeyboard) {
            autoInvokeKeyboard = false
            binding.etMgsMessage.requestFocusFromTouch()
        }
    }

    private fun harmonyInputPadding(keyboardHeight: Int): Int {
        Timber.d("harmonyInputPadding keyboardHeight$keyboardHeight")
        return if (keyboardHeight > 200) {
            keyboardHeight
        } else {
            0
        }
    }

    override fun onPause() {
        KeyboardHeightUtilV2.unregisterKeyboardHeightListener(requireActivity())
        binding.root.setPaddingEx(bottom = 0)
        InputUtil.hideKeyboard(binding.etMgsMessage)
        super.onPause()
    }

    override fun onDestroyView() {
        if (textWatcher != null) {
            binding.etMgsMessage.removeTextChangedListener(textWatcher)
            textWatcher = null
        }
        super.onDestroyView()
    }

    private fun getContentMaxCount(): Int {
        return 300
    }

    private fun upDataContentCountUI(content: String?) {
        val compliantLength = (content?.length ?: 0) <= getContentMaxCount()
        val isEmptyName = content.isNullOrEmpty()
        val isFull = !compliantLength
        enableSend(!isEmptyName && !isFull)
    }

    private fun editSubmit(content: String) {
        Timber.d("editSubmit $content")
        val result = AddAppraiseReplyRequest(
            content,
            uid = arguments?.getString(KEY_USER_UID) ?: "",
            commentId = arguments?.getString(USER_COMMENT_ID) ?: "",
            replyUid = arguments?.getString(USER_REPLY_UID) ?: "",
            replyNickname = arguments?.getString(USER_REPLY_NICKNAME) ?: "",
            replyContentId = arguments?.getString(USER_REPLY_ID) ?: "",
        )
        viewModel.replyComment(result)
    }

}