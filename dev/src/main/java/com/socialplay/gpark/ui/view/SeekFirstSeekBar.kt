package com.socialplay.gpark.ui.view

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.appcompat.widget.AppCompatSeekBar
import timber.log.Timber


class SeekFirstSeekBar @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) : AppCompatSeekBar(context, attrs, defStyleAttr){

    override fun dispatchTouchEvent(event: MotionEvent): Boolean {
        Timber.i("SeekFirstSeekBar dispatchTouchEvent")
        parent.requestDisallowInterceptTouchEvent(true)
        return super.dispatchTouchEvent(event)
    }

    override fun canScrollHorizontally(direction: Int): <PERSON><PERSON><PERSON> {
        return true
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        Timber.i("SeekFirstSeekBar onTouchEvent")
        return super.onTouchEvent(event)
    }


    override fun refreshDrawableState() {
        Timber.i("SeekFirstSeekBar refreshDrawableState")
        super.refreshDrawableState()
    }

    override fun drawableStateChanged() {
        Timber.i("SeekFirstSeekBar drawableStateChanged")
        super.drawableStateChanged()
    }

    override fun jumpDrawablesToCurrentState() {
        Timber.i("SeekFirstSeekBar jumpDrawablesToCurrentState")
        super.jumpDrawablesToCurrentState()
    }
}