package com.socialplay.gpark.ui.im.setting

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.meta.box.biz.friend.FriendBiz
import com.meta.lib.api.resolve.data.model.data
import com.meta.lib.api.resolve.data.model.succeeded
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.errMsg
import com.socialplay.gpark.data.interactor.FriendInteractor
import com.socialplay.gpark.util.extension.LifecycleCallback
import kotlinx.coroutines.launch


/**
 *  <AUTHOR> taiyuan
 *  time   : 2021/6/28
 *  desc   :
 */
class RemarkViewModel(private val iMetaRepository: IMetaRepository, private val friendInteractor: FriendInteractor) : ViewModel() {
    val remarkCallback: LifecycleCallback<(RemarkState) -> Unit> = LifecycleCallback()
    fun addFriendRemark(uuid: String, remark: String) = viewModelScope.launch {
        dispatchState(RemarkState.Start)
        val result = FriendBiz.setFriendRemark(uuid, remark)

        val state = if (result.succeeded && result.data == true) {
            RemarkState.Success.remark = remark
            RemarkState.Success
        } else {
            RemarkState.Failed.msg = result.errMsg()
            RemarkState.Failed
        }

        dispatchState(state)
    }

    private fun dispatchState(state: RemarkState) {
        remarkCallback.dispatchOnMainThread {
            invoke(state)
        }
    }

    enum class RemarkState(var remark: String = "", var msg: String? = "") {
        Start, Failed, Success
    }
}