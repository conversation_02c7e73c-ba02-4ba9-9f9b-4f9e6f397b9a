package com.socialplay.gpark.ui.profile.link

import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.account.ProfileLinkInfo
import com.socialplay.gpark.databinding.ItemEditLinkBinding
import com.socialplay.gpark.databinding.ItemLinkLogoBinding
import com.socialplay.gpark.ui.core.GlideGetter
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaModelCollector
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setWidth
import com.socialplay.gpark.util.extension.unsetOnClick

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/08/16
 *     desc   :
 *
 */
fun MetaModelCollector.addLinkItem(
    id: String,
    item: ProfileLinkInfo,
    itemWidth: Int,
    isEditMode: Boolean,
    getGlide: GlideGetter,
    onClick: (ProfileLinkInfo) -> Unit
) {
    add(LinkItem(item, itemWidth, isEditMode, getGlide, onClick).apply {
        id(id)
    })
}

data class LinkItem(
    val item: ProfileLinkInfo,
    val itemWidth: Int,
    val isEditMode: Boolean,
    val getGlide: GlideGetter,
    val onClick: (ProfileLinkInfo) -> Unit
) :
    ViewBindingItemModel<ItemEditLinkBinding>(
        R.layout.item_edit_link,
        ItemEditLinkBinding::bind
    ) {
    override fun ItemEditLinkBinding.onBind() {
        getGlide()?.run {
            load(item.icon).into(imgLogo)
        }
        clContent.setWidth(itemWidth)
        tvTitle.text = item.title
        tvContent.text = item.url
        imgDelete.setOnAntiViolenceClickListener { onClick.invoke(item) }
        if (isEditMode){
            horScroll.smoothScrollTo(1000, 0)
        } else {
            horScroll.smoothScrollTo(-1000, 0)
        }
    }

    override fun ItemEditLinkBinding.onUnbind() {
        imgDelete.unsetOnClick()
    }

}

fun MetaModelCollector.addLinkLogoItem(
    id: Int,
    item: ProfileLinkInfo,
    selectItem: ProfileLinkInfo?,
    itemWidth: Int,
    getGlide: GlideGetter,
    onClick: (ProfileLinkInfo) -> Unit
) {
    add(LinkLogoItem(item, item == selectItem, itemWidth, getGlide, onClick).apply {
        id(id)
    })
}

data class LinkLogoItem(
    val item: ProfileLinkInfo,
    val selected: Boolean,
    val itemWidth: Int,
    val getGlide: GlideGetter,
    val onClick: (ProfileLinkInfo) -> Unit
) :
    ViewBindingItemModel<ItemLinkLogoBinding>(
        R.layout.item_link_logo,
        ItemLinkLogoBinding::bind
    ) {
    override fun ItemLinkLogoBinding.onBind() {
        getGlide()?.run {
            load(item.icon).into(imgLogo)
        }
        if (selected) {
            root.setBackgroundResource(R.drawable.shape_f5f5f5_corner_10_stroke_1)
        } else {
            root.setBackgroundResource(R.drawable.shape_f5f5f5_corner_10)
        }
        root.setOnAntiViolenceClickListener {
            onClick.invoke(item)
        }
    }

    override fun ItemLinkLogoBinding.onUnbind() {
        root.unsetOnClick()
    }

}