package com.socialplay.gpark.ui.editor.create

import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.DiffUtil
import com.bumptech.glide.RequestManager
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.chad.library.adapter.base.module.LoadMoreModule
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.editor.EditorCreationShowInfo
import com.socialplay.gpark.databinding.AdapterEditorCreateV2MineBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.ui.base.adapter.BaseDifferAdapter
import com.socialplay.gpark.ui.base.adapter.BaseVBViewHolder
import com.socialplay.gpark.ui.base.adapter.createViewBinding
import com.socialplay.gpark.ui.core.GlideGetter
import com.socialplay.gpark.util.DateUtil.formatYmdDate
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setTextWithArgs
import com.socialplay.gpark.util.extension.visible

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/04/20
 *     desc   :
 * </pre>
 */
class EditorCreateV2MineAdapter(private val glide: GlideGetter) : BaseDifferAdapter<EditorCreationShowInfo, AdapterEditorCreateV2MineBinding>(
    DIFF_ITEM_CALLBACK
), LoadMoreModule {

    companion object {
        const val PAYLOAD_EDIT = 1
        const val PAYLOAD_STATUS = 2
        const val PAYLOAD_NAME = 3

        private val DIFF_ITEM_CALLBACK = object : DiffUtil.ItemCallback<EditorCreationShowInfo>() {

            override fun areItemsTheSame(
                oldItem: EditorCreationShowInfo, newItem: EditorCreationShowInfo
            ): Boolean {
                return if (oldItem.draftInfo != null) {
                    oldItem.draftInfo?.jsonConfig?.fileId == newItem.draftInfo?.jsonConfig?.fileId
                } else if (oldItem.ugcInfo != null) {
                    oldItem.ugcInfo?.id == newItem.ugcInfo?.id
                } else if (oldItem.cloudProject != null) {
                    oldItem.cloudProject?.id == newItem.cloudProject?.id
                } else {
                    true
                }
            }

            override fun areContentsTheSame(
                oldItem: EditorCreationShowInfo, newItem: EditorCreationShowInfo
            ): Boolean {
                return false
            }

            override fun getChangePayload(
                oldItem: EditorCreationShowInfo, newItem: EditorCreationShowInfo
            ): Any {
                val payload = ArrayList<Any>()
                if (oldItem.hasAvailableUgcGame() != newItem.hasAvailableUgcGame() || oldItem.hasLocalGame() != newItem.hasLocalGame()) {
                    payload.add(PAYLOAD_EDIT)
                }
                if (oldItem.draftInfo?.auditStatusDesc != newItem.draftInfo?.auditStatusDesc) {
                    payload.add(PAYLOAD_STATUS)
                }
                if (oldItem.getGameName() != newItem.getGameName() || oldItem.getShowTime() != newItem.getShowTime() || oldItem.getGameBanner() != newItem.getGameBanner()) {
                    payload.add(PAYLOAD_NAME)
                }
                return payload
            }
        }
    }

    override fun convert(
        holder: BaseVBViewHolder<AdapterEditorCreateV2MineBinding>,
        item: EditorCreationShowInfo,
        payloads: List<Any>
    ) {
        val payload = payloads[0]
        if (payload is List<*> && payload.isNotEmpty()) {
            for (p in payload) {
                when (p) {
                    PAYLOAD_EDIT   -> {
                        updatePlayStatus(holder, item)
                    }
                    PAYLOAD_STATUS -> {
                        updateAuditStatus(holder, item)
                        updateCover(holder, item)
                    }
                    PAYLOAD_NAME   -> {
                        updateDesc(holder, item)
                        updateCover(holder, item)
                    }
                }
            }
        }
    }

    private fun updatePlayStatus(
        holder: BaseVBViewHolder<AdapterEditorCreateV2MineBinding>, item: EditorCreationShowInfo
    ) {
        holder.binding.tvEdit.visible(item.hasLocalGame() || item.isOnlyCloud())
        holder.binding.tvEdit.setText(if (item.isOnlyCloud()) R.string.select_backup_all_caps else R.string.edit)
        holder.binding.tvEdit.setHint(if (item.isOnlyCloud()) R.string.edit else R.string.select_backup_all_caps)
        if (item.isOnlyCloud()) {
            Analytics.track(EventConstants.UGC_BACKUP_EXPOSURE)
        }
    }

    private fun updateAuditStatus(
        holder: BaseVBViewHolder<AdapterEditorCreateV2MineBinding>, item: EditorCreationShowInfo
    ) {
        if (item.isOnline) {
            holder.binding.tvReviewStatus.gone()
            if (item.ugcInfo == null) {
                holder.binding.tvPlayCount.gone()
            } else {
                holder.binding.tvPlayCount.visible()
                holder.binding.tvPlayCount.text =
                    UnitUtil.formatPlayerCount(item.ugcInfo?.pvCount ?: 0L)
            }
        } else {
            holder.binding.tvReviewStatus.visible()
            holder.binding.tvReviewStatus.text = item.getAuditStatus(context)
            holder.binding.tvPlayCount.gone()
        }
    }

    private fun updateDesc(
        holder: BaseVBViewHolder<AdapterEditorCreateV2MineBinding>, item: EditorCreationShowInfo
    ) {
        holder.binding.apply {
            tvTitle.text = item.getGameName()

            tvTime.setTextWithArgs(R.string.update_time, item.getShowTime().formatYmdDate())
            tvTime.visible(item.getShowTime() > 0)

            val banner = item.getGameBanner()
            glide()?.run {
                if (banner?.startsWith("http") == true) {
                    load(banner).centerCrop()
                        .into(sivGameCover)
                } else {
                    load(banner).diskCacheStrategy(DiskCacheStrategy.NONE)
                        .skipMemoryCache(true)
                        .centerCrop()
                        .into(sivGameCover)
                }
            }

            ivCloud.visible(item.isClouded())
        }
    }

    private fun updateCover(
        holder: BaseVBViewHolder<AdapterEditorCreateV2MineBinding>, item: EditorCreationShowInfo
    ) {
        holder.binding.apply {
            vGradient.visible(ivCloud.isVisible || tvPlayCount.isVisible)
        }
    }

    override fun convert(
        holder: BaseVBViewHolder<AdapterEditorCreateV2MineBinding>, item: EditorCreationShowInfo
    ) {
        updatePlayStatus(holder, item)
        updateAuditStatus(holder, item)
        updateDesc(holder, item)
        updateCover(holder, item)
    }

    override fun viewBinding(parent: ViewGroup, viewType: Int): AdapterEditorCreateV2MineBinding {
        return parent.createViewBinding(AdapterEditorCreateV2MineBinding::inflate)
    }

}