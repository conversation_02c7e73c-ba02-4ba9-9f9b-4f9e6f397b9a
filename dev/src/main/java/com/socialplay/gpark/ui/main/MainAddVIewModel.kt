package com.socialplay.gpark.ui.main

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.meta.biz.ugc.model.EditorTemplate
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.interactor.TTaiInteractor
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.data.model.startup.AddTemplate
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.util.GsonUtil
import kotlinx.coroutines.flow.asFlow
import kotlinx.coroutines.flow.flatMapConcat
import org.koin.android.ext.android.get

data class MainAddState(
    val template: Async<EditorTemplate> = Uninitialized
) : MavericksState

class MainAddViewModel(
    initState: MainAddState,
    private val tTaiInteractor: TTaiInteractor,
    private val metaRepository: IMetaRepository
) : BaseViewModel<MainAddState>(initState) {

    fun getTemplateByType(type: String) = withState { s ->
        if (s.template is Loading) return@withState
        tTaiInteractor.getConfigStringTopdownV2(TTaiKV.ID_ADD_TEMPLATE).asFlow().flatMapConcat {
            GsonUtil.gsonSafeParseCollection<List<AddTemplate>>(it)
                ?.firstOrNull { it.type == type }?.templateId?.let {
                metaRepository.getGameTemplateV2(it, 8).asFlow()
            } ?: throw IllegalArgumentException("Illegal template id")
        }.execute(retainValue = MainAddState::template) {
            copy(template = it)
        }
    }

    companion object : KoinViewModelFactory<MainAddViewModel, MainAddState>() {
        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext, state: MainAddState
        ): MainAddViewModel {
            return MainAddViewModel(state, get(), get())
        }
    }
}