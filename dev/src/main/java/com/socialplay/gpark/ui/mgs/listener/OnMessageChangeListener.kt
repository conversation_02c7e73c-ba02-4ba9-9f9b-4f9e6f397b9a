package com.socialplay.gpark.ui.mgs.listener

import com.meta.biz.mgs.data.model.MGSMessage
import com.meta.biz.mgs.data.model.Member
import com.meta.biz.mgs.data.model.MgsPlayerInfo
import com.meta.biz.mgs.data.model.MgsRoomInfo
import com.meta.biz.mgs.data.model.request.MgsShareScreenshot

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2021/08/13
 *     desc   :
 *
 */
interface OnMessageChangeListener {
    fun joinRoom()
    fun leaveRoom()
    fun addTextMessage(data: MGSMessage)
    fun refreshUserList(userList: MutableList<Member>?)
    fun clearChatRoomMessage()
    fun quitGame()
    fun updateRoomStatus(mgsRoomInfo: MgsRoomInfo?)
    fun showUserCardDialog(data: MgsPlayerInfo?, isMe: Boolean)
    fun onAddFriendResult(uuid: String, isSuccess: Boolean, reason: String?)
    fun addRoomUser(user: Member)
    fun removeRoomUser(user: Member, position: Int)
    fun updateRoomUser(user: Member)
    fun showBannedDialog(reason: String)
    fun showBlockedDialog(reason: String)
    fun updateMyVoiceState(member: Member)
    fun updateOtherVoiceState(member: Member)
    fun updateAudioVisible(ballAudioVisible: Boolean, inputAudioVisible: Boolean, isOpenMic: Boolean)
    fun showScreenshot(shareScreenshot: MgsShareScreenshot, jumpUrl: String)
    fun changeAllMuteStatus(mute:Boolean)
    fun shrinkMessageLayer(open: Boolean)
    fun showCheckAgeLimitDialog(age: Int)
    fun expandFloatViewFromCP(data: String)
    fun closeFloatViewFromCP(data: String)
    fun updateSelfAudioState(open: Boolean)
    fun setFloatBallVisible(visible: Boolean)
    fun removeAllFloatView()
    fun addFloatView()
    fun startRecord()
}