package com.socialplay.gpark.ui.locale

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.airbnb.epoxy.EpoxyController
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.mvrx.fragmentViewModel
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.locale.LanguageOption
import com.socialplay.gpark.databinding.FragmentLanguageSettingBinding
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseRecyclerViewFragment
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.util.extension.navigateUp

/**
 * Created by bo.li
 * Date: 2024/4/19
 * Desc: 设置app语言
 */
class LanguageSettingFragment :
    BaseRecyclerViewFragment<FragmentLanguageSettingBinding>(R.layout.fragment_language_setting) {
    override val recyclerView: EpoxyRecyclerView
        get() = binding.rvLang

    private val viewModel: LanguageSettingViewModel by fragmentViewModel()

    companion object {
        const val TAG = "check_lang"
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentLanguageSettingBinding? {
        return FragmentLanguageSettingBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.titleLang.setOnBackClickedListener {
            navigateUp()
        }
        viewModel.onAsync(
            LanguageSettingItemModelState::restart,
            deliveryMode = uniqueOnly(),
            onLoading = {
                binding.loading.showLoading()
            },
            onSuccess = {
                binding.loading.hide()
                it ?: return@onAsync
                MetaRouter.Settings.changeLanguage(requireContext())
            },
            onFail = { _, _ ->
                binding.loading.hide()
            })
    }

    override fun epoxyController(): EpoxyController = simpleController(
        viewModel,
        LanguageSettingItemModelState::selectedOption,
        LanguageSettingItemModelState::optionList
    ) { currentLang, list ->
        list.forEach {
            languageOption(it, currentLang == it, ::onChooseLanguage)
        }
    }

    private fun onChooseLanguage(languageOption: LanguageOption) {
        viewModel.changeAppLanguage(requireContext(), languageOption)
    }

    override fun getPageName(): String = PageNameConstants.FRAGMENT_LANGUAGE_SETTING
}