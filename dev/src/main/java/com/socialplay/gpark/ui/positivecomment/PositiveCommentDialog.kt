package com.socialplay.gpark.ui.positivecomment

import android.view.Gravity
import androidx.fragment.app.FragmentManager
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.feedback.SubmitNewFeedbackRequest
import com.socialplay.gpark.databinding.DialogPositiveCommentBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.mw.lifecycle.AvatarGameTime
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.base.BaseDialogFragment
import com.socialplay.gpark.util.MarketUtil
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.property.viewBinding
import org.koin.core.context.GlobalContext


/**
 * create by: bin on 2022/3/22
 */
class PositiveCommentDialog : BaseDialogFragment() {
    private val metaKV: MetaKV by lazy { GlobalContext.get().get() }
    override val binding by viewBinding(DialogPositiveCommentBinding::inflate)

    override fun show(manager: FragmentManager, tag: String?) {
        super.show(manager, tag)
    }
    override fun init() {
        binding.tvPositiveComment.setOnAntiViolenceClickListener {
            Analytics.track(EventConstants.EVENT_ACCLAIM_UPS_CLICK) {
                put("source", "2")
            }
            metaKV.analytic.setPositiveCommentFlag(metaKV.account.uuid,true)
            AvatarGameTime.submitPositiveComment()
            MarketUtil.goto(this)
            dismissAllowingStateLoss()
        }
        binding.tvFeedback.setOnAntiViolenceClickListener {
            Analytics.track(EventConstants.EVENT_ACCLAIM_UPS_CLICK) {
                put("source", "1")
            }
            metaKV.analytic.setFeedBackTime(metaKV.account.uuid,System.currentTimeMillis())
            MetaRouter.Feedback.feedback(
                this,
                null,
                SubmitNewFeedbackRequest.SOURCE_POSITIVE_COMMENT_NUMBER,
                null,
                false,
                needBackGame = false,
                fromGameId = null
            )
            dismissAllowingStateLoss()
        }
        binding.imgClose.setOnClickListener {
            Analytics.track(EventConstants.EVENT_ACCLAIM_UPS_CLICK) {
                put("source", "3")
            }
            dismissAllowingStateLoss()
        }
    }

    override fun isBackPressedDismiss(): Boolean {
        return false
    }

    override fun gravity(): Int {
        return Gravity.BOTTOM
    }

    override fun isCancelable(): Boolean {
        return false
    }

    override fun isClickOutsideDismiss(): Boolean {
        return false
    }

    override fun loadFirstData() {
    }
}