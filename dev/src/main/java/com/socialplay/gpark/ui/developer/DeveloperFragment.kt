package com.socialplay.gpark.ui.developer

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import android.widget.Toast
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.airbnb.mvrx.asMavericksArgs
import com.meta.pandora.function.event.preview.PandoraEventPreview
import com.socialplay.gpark.R
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.databinding.FragmentDeveloperBinding
import com.socialplay.gpark.function.ad.AdProxy
import com.socialplay.gpark.function.overseabridge.bridge.IAdSdkBridge
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.function.startup.StartupProcessType
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.ui.developer.adapter.ActionAdapter
import com.socialplay.gpark.ui.developer.viewmodel.DeveloperViewModel
import com.socialplay.gpark.ui.view.MetallicBorderDemoActivity
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import org.koin.core.context.GlobalContext

/**
 * xingxiu.hou
 * 2021/6/8
 */
class DeveloperFragment : BaseFragment<FragmentDeveloperBinding>() {

    private val adapter = ActionAdapter()
    private val viewModel: DeveloperViewModel by viewModel()
    private val metaKV by inject<MetaKV>()

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentDeveloperBinding? {
        return FragmentDeveloperBinding.inflate(inflater, container, false)
    }

    override fun init() {
        viewModel.developerToggleLivedata.observe(viewLifecycleOwner) {
            if (it) {
                binding.etDevLock.visibility = View.GONE
                binding.rvActionList.visibility = View.VISIBLE
                hideKeyboard(binding.etDevLock)
            } else {
                /*解锁控制*/
                lifecycleScope.launch(Dispatchers.Main) {
                    delay(500)
                    showKeyboard(binding.etDevLock)
                }
                binding.etDevLock.addTextChangedListener(afterTextChanged = { text ->
                    val input = text?.trimStart()?.trimEnd().toString()
                    viewModel.checkDeveloperOpen(input)
                })
            }
        }

        binding.rvActionList.layoutManager = LinearLayoutManager(requireContext())
        binding.rvActionList.adapter = adapter
        viewModel.developerListLivedata.observe(viewLifecycleOwner) {
            adapter.setList(it)
        }
        adapter.setOnItemClickListener { _, position ->
            val action = adapter.data[position]
            val gamePkg = requireActivity().packageName
            if (DemoWrapper.handleAction(action.name, action.navId, viewLifecycleOwner)) {
                // 已经被其他flavor处理了，这里就不继续往下了
                return@setOnItemClickListener
            }
            when (action.navId) {
                R.id.devDemoListFragment -> {
                    MetaRouter.Control.navigate(this, action.navId, navData = DemoListFragmentArgs("test").asMavericksArgs())
                }

                R.id.devReviewGame -> {
                    MetaRouter.Developer.reviewGame(this)
                }

                R.id.devShowEvent -> {
                    showEvent()
                }

                -1 -> {
                    GlobalContext.get().get<IAdSdkBridge>().showMediationDebugger(requireActivity())
                }

                DeveloperViewModel.SHOW_BANNER -> {
                    /*if (binding.adBannerContainer.childCount < 1) {
                        val ad = AdBannerView(BuildConfig.APPLOVIN_BANNER_AD_UNIT_ID, requireActivity())
                        // Stretch to the width of the screen for banners to be fully functional
                        val width = ViewGroup.LayoutParams.MATCH_PARENT

                        // Banner height on phones and tablets is 50 and 90, respectively
                        val heightPx = resources.getDimensionPixelSize(R.dimen.dp_50)
                        ad.layoutParams = FrameLayout.LayoutParams(width, heightPx)
                        ad.placement = "dev-btn"
                        ad.setRefreshSeconds(60)
                        ad.setGameId("testgameid001")
                        ad.setListener(object : MaxAdViewAdListenerAdapter() {
                            override fun onAdLoaded(ad: MaxAd) {
                                super.onAdLoaded(ad)
                                toast("banner 加载完成!")
                            }

                            override fun onAdLoadFailed(adUnitId: String, error: MaxError) {
                                super.onAdLoadFailed(adUnitId, error)
                                toast("banner 加载失败！")
                            }

                            override fun onAdDisplayFailed(ad: MaxAd, error: MaxError) {
                                super.onAdDisplayFailed(ad, error)
                                toast("banner 展示失败！")
                            }
                        })
                        ad.loadAd()
                        ad.show(binding.adBannerContainer)
                    }*/
                }

                DeveloperViewModel.SHOW_REWARDED -> {
                    requireActivity().apply {
                        AdProxy.startShowRewardedAdResult(
                            this,
                            StartupProcessType.H.desc,
                            "",
                            "",
                            gameId = gamePkg,
                            gamePackage = gamePkg,
                            hashMapOf()
                        )
                    }
                }

                DeveloperViewModel.METALLIC_BORDER_DEMO -> {
                    val intent = Intent(requireContext(), MetallicBorderDemoActivity::class.java)
                    startActivity(intent)
                }

                0 -> {
                    toast("${action.name}->jump failed:${action.navId}")
                }

                else -> {
                    MetaRouter.Control.navigate(this, action.navId)
                }
            }
        }
    }

    private fun showEvent() {
        PandoraEventPreview.requestPermission(requireActivity()) {
            if (it) {
                metaKV.developer.isShowEvent = true
                PandoraEventPreview.open(requireContext())
            } else {
                Toast.makeText(requireContext(), getString(R.string.debug_need_float_permission), Toast.LENGTH_SHORT).show()
            }
        }
    }

    override fun loadFirstData() {
        viewModel.getToggleStatus()
        viewModel.requestListData()
    }

    override fun getFragmentName(): String {
        return "DeveloperFragment"
    }

    private fun hideKeyboard(view: View) {
        val manager: InputMethodManager = view.context
            .getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        manager.hideSoftInputFromWindow(view.windowToken, 0)
    }

    override fun onDestroyView() {
        binding.rvActionList.adapter = null
        //        if (binding.adBannerContainer.childCount > 0){
        //            (binding.adBannerContainer[0] as? AdBannerView)?.destroy()
        //            binding.adBannerContainer.removeAllViews()
        //        }
        super.onDestroyView()
    }

    private fun showKeyboard(view: View) {
        view.requestFocus()
        val manager: InputMethodManager = view.context
            .getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        manager.showSoftInput(view, InputMethodManager.SHOW_IMPLICIT)
    }
}