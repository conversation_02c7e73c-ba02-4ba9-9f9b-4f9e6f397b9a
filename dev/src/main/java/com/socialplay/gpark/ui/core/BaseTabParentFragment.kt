package com.socialplay.gpark.ui.core

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.view.isInvisible
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.CommonTabItem
import com.socialplay.gpark.databinding.FragmentBaseTabParentBinding
import com.socialplay.gpark.databinding.TabIndicatorBaseTabParentFragmentBinding
import com.socialplay.gpark.databinding.TabIndicatorSp16Binding
import com.socialplay.gpark.ui.view.viewpager.CommonTabStateAdapter
import com.socialplay.gpark.ui.view.viewpager.adapterAllowStateLoss
import com.socialplay.gpark.util.extension.addOnTabSelectedListener
import com.socialplay.gpark.util.extension.attach
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.navigateUp

abstract class BaseTabParentFragment :
    BaseFragment<FragmentBaseTabParentBinding>(R.layout.fragment_base_tab_parent) {

    private val tabCallback = object : TabLayout.OnTabSelectedListener {
        override fun onTabSelected(tab: TabLayout.Tab) {
            setTabSelectUI(tab, true)
        }

        override fun onTabUnselected(tab: TabLayout.Tab) {
            setTabSelectUI(tab, false)
        }

        override fun onTabReselected(tab: TabLayout.Tab) {}
    }

    abstract val tabItems: List<CommonTabItem>

    private fun setTabSelectUI(tab: TabLayout.Tab, select: Boolean) {
        tab.customView?.findViewById<TextView>(R.id.tv_selected)?.isInvisible = !select
        tab.customView?.findViewById<TextView>(R.id.tv_normal)?.isInvisible = select
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentBaseTabParentBinding? {
        return FragmentBaseTabParentBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.title.setOnBackAntiViolenceClickedListener { navigateUp() }
        initTab(tabItems)
    }

    private fun initTab(tabs: List<CommonTabItem>) {
        if (tabs.size <= 1) {
            // 当只有一个tab时, 隐藏TabIndicator
            binding.tabLayoutGroups.setSelectedTabIndicatorColor(getColorByRes(R.color.transparent))
        }
        binding.tabLayoutGroups.addOnTabSelectedListener(viewLifecycleOwner, tabCallback)
        val titles = tabs.map {
            getString(it.title)
        }
        binding.viewPagerGroups.adapterAllowStateLoss = CommonTabStateAdapter(
            tabs.map { it.fragmentInvoke },
            childFragmentManager,
            viewLifecycleOwner.lifecycle
        )
        TabLayoutMediator(
            binding.tabLayoutGroups,
            binding.viewPagerGroups
        ) { tab: TabLayout.Tab, position: Int ->
            val tabBinding = TabIndicatorBaseTabParentFragmentBinding.inflate(layoutInflater)
            tabBinding.tvNormal.text = titles[position]
            tabBinding.tvSelected.text = titles[position]

            tab.customView = tabBinding.root
        }.attach(viewLifecycleOwner)
    }

    override fun invalidate() {
    }
}