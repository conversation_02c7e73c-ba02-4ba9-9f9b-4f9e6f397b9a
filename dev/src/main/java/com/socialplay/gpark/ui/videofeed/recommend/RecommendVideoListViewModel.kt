package com.socialplay.gpark.ui.videofeed.recommend

import android.app.Application
import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.model.videofeed.ChoiceHomeVideoItem
import com.socialplay.gpark.data.model.videofeed.RecommendVideoListArgs
import com.socialplay.gpark.data.model.videofeed.VideoFeedApiResult
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.util.ToastData
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import org.koin.android.ext.android.get


data class RecommendVideoListViewModelState(
    val args: RecommendVideoListArgs? = null,
    val isInitialDataAppended: Boolean = false,
    val refresh: Async<List<ChoiceHomeVideoItem>> = Uninitialized,
    val items: List<ChoiceHomeVideoItem> = emptyList(),
    val loadMore: Async<VideoFeedApiResult?> = Uninitialized,
    val toastMsg: ToastData = ToastData.EMPTY,
    val nextPage: Int = 1,
) : MavericksState {

    constructor(args: RecommendVideoListArgs) : this(args, isInitialDataAppended = false)
}


class RecommendVideoListViewModel(
    private val app: Application,
    private val repository: IMetaRepository,
    private val initialState: RecommendVideoListViewModelState,
) : BaseViewModel<RecommendVideoListViewModelState>(initialState) {

    companion object :
        KoinViewModelFactory<RecommendVideoListViewModel, RecommendVideoListViewModelState>() {

        private const val PAGE_SIZE = 10
        private const val PAGE_BEGIN_INDEX = 1

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: RecommendVideoListViewModelState
        ): RecommendVideoListViewModel {
            return RecommendVideoListViewModel(get(), get(), state)
        }
    }


    init {
        refresh()
    }

    fun refresh() {
        withState {
            val shouldUseInitialArgsData = !it.isInitialDataAppended && it.args?.apiResult != null
            val result = if (shouldUseInitialArgsData) {
                initialDataFlow(it.args?.apiResult!!)
            } else {
                repository.getRecommendVideoList(
                    PAGE_BEGIN_INDEX,
                    PAGE_SIZE,
                    it.args?.resId?.getCategoryID() ?: 0
                )
            }

            result.map {
                val reqId = it.reqId ?: ""
                it.items?.map { ChoiceHomeVideoItem(it, reqId) } ?: emptyList()
            }.execute(retainValue = RecommendVideoListViewModelState::refresh) {
                copy(
                    refresh = it,
                    items = it.invoke() ?: emptyList(),
                    isInitialDataAppended = shouldUseInitialArgsData || this.isInitialDataAppended,
                    nextPage = PAGE_BEGIN_INDEX + 1
                )
            }
        }
    }

    fun loadMore() {
        withState {
            repository.getRecommendVideoList(
                it.nextPage,
                PAGE_SIZE,
                it.args?.resId?.getCategoryID() ?: 0
            ).execute {
                if (it is Success) {
                    val apiResult = it.invoke()

                    val newDataList = (apiResult.items ?: emptyList())
                        .map { ChoiceHomeVideoItem(it, apiResult.reqId ?: "") }

                    copy(
                        items = (items + newDataList).distinctBy { it.postDetail.postId },
                        loadMore = it,
                        nextPage = this.nextPage + 1
                    )
                } else {
                    copy(loadMore = it)
                }
            }
        }
    }

    private fun initialDataFlow(apiResult: VideoFeedApiResult): Flow<VideoFeedApiResult> = flow {
        emit(apiResult)
    }

}