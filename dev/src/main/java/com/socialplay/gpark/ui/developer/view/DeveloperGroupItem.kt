package com.socialplay.gpark.ui.developer.view

import com.airbnb.epoxy.EpoxyController
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.AdapterDeveloperGroupItemBinding
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.IStickHeader
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener

fun EpoxyController.developerGroup(
    title: String,
    subTitle: String = "",
    id: Long? = null,
    idString: String? = null,
    onClick: (() -> Unit)? = null
) {
    add(DeveloperGroupItem(
        title, subTitle, onClick
    ).apply {
        if (id != null) {
            id(id)
        }else if (!idString.isNullOrEmpty()){
            id(idString)
        }else{
            id("DeveloperGroupItem-$title-$subTitle")
        }
    })
}

data class DeveloperGroupItem(
    val title: String,
    val subTitle: String,
    val onClick: (() -> Unit)?
) : ViewBindingItemModel<AdapterDeveloperGroupItemBinding>(
    R.layout.adapter_developer_group_item,
    AdapterDeveloperGroupItemBinding::bind
), IStickHeader {
    override fun AdapterDeveloperGroupItemBinding.onBind() {
        tvGroupName.text = title
        tvValue.text = subTitle
        onClick?.let { root.setOnAntiViolenceClickListener { it() } }
    }

    override fun AdapterDeveloperGroupItemBinding.onUnbind() {
        root.setOnClickListener(null)
    }
}