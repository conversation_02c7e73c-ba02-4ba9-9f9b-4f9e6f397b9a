package com.socialplay.gpark.ui.mgs.dialog

import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import android.view.Gravity
import android.view.LayoutInflater
import android.view.WindowManager
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.meta.biz.mgs.data.model.Member
import com.meta.biz.mgs.data.model.MgsPlayerInfo
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.MgsInteractor
import com.socialplay.gpark.data.model.GameDetailInfo
import com.socialplay.gpark.databinding.DialogMgsPlayerInfoLandBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.mgs.MgsDialogManager
import com.socialplay.gpark.ui.dialog.GameCreateDialogHelper
import com.socialplay.gpark.ui.mgs.adapter.PlayerBuildingAdapter
import com.socialplay.gpark.ui.mgs.adapter.PlayerDressAdapter
import com.socialplay.gpark.ui.mgs.listener.OnMgsPlayerInfoDialogListener
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.tagIds
import com.socialplay.gpark.util.extension.visible
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

/**
 * Created by bo.li
 * Date: 2022/2/18
 * Desc: MGS用户资料卡片
 */
class MgsPlayerInfoDialog(
    private val metaApp: Context,
    private val activity: Activity,
    private val gameInfo: GameDetailInfo?,
    private val playerInfo: MgsPlayerInfo, // 目标用户信息
    private val listener: OnMgsPlayerInfoDialogListener
) : Dialog(activity, android.R.style.Theme_Dialog),
    KoinComponent {

    private lateinit var binding: DialogMgsPlayerInfoLandBinding
    private val accountInteractor: AccountInteractor by inject()
    private val mgsInteractor: MgsInteractor by inject()
    private val isMe: Boolean by lazy { accountInteractor.accountLiveData.value?.uuid == playerInfo.uuid }
    private val analyticInfo by lazy {
        mapOf(
            "gameid" to gameInfo?.id.toString(),
            "gamepkg" to (gameInfo?.packageName ?: "")
        )
    }

    private val playerBuildingAdapter by lazy { PlayerBuildingAdapter() }
    private val playerDressAdapter by lazy { PlayerDressAdapter() }

    private var userLabelDescriptionDialog: Dialog? = null

    init {
        initView()
    }

    private fun initDialogParams() {
        setCancelable(true)
        setCanceledOnTouchOutside(true)
        binding = DialogMgsPlayerInfoLandBinding.inflate(LayoutInflater.from(metaApp))
        GameCreateDialogHelper.customInflated(
            activity,
            metaApp,
            this,
            binding.root,
            0.75F,
            gravity = Gravity.CENTER,
            width = WindowManager.LayoutParams.MATCH_PARENT,
            height = WindowManager.LayoutParams.MATCH_PARENT
        )
    }

    override fun dismiss() {
        userLabelDescriptionDialog?.dismiss()
        Analytics.track(EventConstants.EVENT_CLICK_CLOSE_MGS_USER) {
            putAll(analyticInfo)
        }
        super.dismiss()
    }

    private fun initView() {
        if (window == null) {
            dismiss()
            return
        }
        initDialogParams()

        binding.apply {
            ivCloseDialog.setOnAntiViolenceClickListener {
                dismiss()
            }
            viewClickBack.setOnAntiViolenceClickListener {
                dismiss()
            }
            ivPlayerHead.setOnAntiViolenceClickListener { }
            clPlayerCard.setOnAntiViolenceClickListener { }
            Glide.with(metaApp).load(playerInfo.avatar).transform(CircleCrop()).into(ivPlayerHead)
            tvPlayerName.text = playerInfo.nickname

            if (playerInfo.gender > 0) {
                ivPlayerGender.setImageDrawable(metaApp.getDrawable(if (playerInfo.gender == 1) {R.drawable.icon_male} else { R.drawable.icon_female}))
            }

            if (playerInfo.isMember()) {
                tvPlayerName.setCompoundDrawablesWithIntrinsicBounds(null, null, metaApp.resources.getDrawable(R.drawable.icon_vip_plus_mark_small),null)
            }
            binding.labelGroup.show(playerInfo.tagIds, playerInfo.labelInfo)
            binding.labelGroup.setListener {
                userLabelDescriptionDialog?.dismiss()
                userLabelDescriptionDialog = UserLabelDescriptionDialog(metaApp, activity, it)
                userLabelDescriptionDialog?.show()
            }
            tvPlayerInfoMetaNumber.text = metaApp.getString(R.string.number_formatted, playerInfo.metaNumber)
            val isBlock = (playerInfo.canApplyForFriend() && accountInteractor.isRealLoginPermissionButVisitor()) || isMe
            tvAddFriend.isEnabled = isBlock
            binding.tvAddFriend.setTextColor (if (isBlock) context.getColor(R.color.color_17191C) else  context.getColor(R.color.color_999999))

  /*          binding.ivPlayerHead.layoutParams = (binding.ivPlayerHead.layoutParams as ViewGroup.MarginLayoutParams).apply {
                val top = if (!playerInfo.ugcGameList.isNullOrEmpty()) 12.dp else 28.dp
                setMargins(0, top, 0, 0)
            }*/

            //显示玩家的一些记录
            shouldShowPlayerMoment()


            var status = "add"
            tvAddFriend.text = metaApp.getString(
                when {
                    isMe -> {
                        status = "dressup"
                        R.string.edit
                    }
                    !accountInteractor.isRealLoginPermissionButVisitor() -> {
                        status = "visitor"
                        R.string.login_for_add_friend
                    }
                    playerInfo.isFriend() -> {
                        status = "added"
                        R.string.added_cap
                    }
                    playerInfo.friendRelation == MgsPlayerInfo.APPLIED_FOR_FRIENDSHIP -> {
                        status = "applied"
                        R.string.meta_mgs_apply
                    }
                    else -> {
                        R.string.dialog_add_friend
                    }
                }
            )
            updateVoiceBtn(listener.getMemberInfo(playerInfo.uuid?:"")?.isOpenAudio == true)
            val commonGameMap = hashMapOf(
                "location" to gameInfo?.packageName.toString(),
                "status" to status
            )
            tvAddFriend.setOnAntiViolenceClickListener {
                if (isMe) {
                    // 去装扮
                    listener.onClickDressUp()
                    Analytics.track(EventConstants.EVENT_CLICK_MGS_AVATAR_EDIT) {
                        putAll(analyticInfo)
                        putAll(commonGameMap)
                    }
                    dismiss()
                } else if (!accountInteractor.isRealLoginPermissionButVisitor()) {
                    // 没登录toast
                    ToastUtil.gameShowShort(metaApp.getString(R.string.login_for_add_friend))
                    Analytics.track(EventConstants.EVENT_CLICK_GAME_LOGIN) {
                        putAll(analyticInfo)
                        putAll(commonGameMap)
                    }
                } else {
                    // 加好友
                    listener.onClickAddFriend(playerInfo)
                    Analytics.track(EventConstants.EVENT_MGS_CLICK_ADD_FRIEND) {
                        putAll(analyticInfo)
                        put("location", "user_info_card")
                    }
                }
            }
            tvVoiceMute.setOnAntiViolenceClickListener {
                val memberInfo = listener.getMemberInfo(playerInfo.uuid ?:"") ?: return@setOnAntiViolenceClickListener
                listener.onChangeVoiceState(!memberInfo.isOpenAudio, playerInfo.openId ?:"", "3")
            }
            Analytics.track(EventConstants.EVENT_SHOW_USER_CARD) {
                putAll(analyticInfo)
                putAll(commonGameMap)
            }
            llReport.setOnAntiViolenceClickListener {
                MgsDialogManager.showReportDialog(
                    metaApp,
                    activity,
                    playerInfo.uuid,
                    playerInfo.nickname,
                    gameInfo?.id.orEmpty()
                )
            }
            if (isMe) {
                llReport.gone()
            }
        }

    }

    private fun shouldShowPlayerMoment() {
        binding.vBuilding.visible(!playerInfo.ugcGameList.isNullOrEmpty() || !playerInfo.dressList.isNullOrEmpty())
        shouldShowUgcGameList()
        shouldShowPlayerDress()
    }

    private fun shouldShowUgcGameList() {
        //ugc
        if (!playerInfo.ugcGameList.isNullOrEmpty()) {
            binding.rvPlayerBuilding.layoutManager = LinearLayoutManager(context).apply {
                orientation = LinearLayoutManager.HORIZONTAL
            }
            binding.rvPlayerBuilding.adapter = playerBuildingAdapter
            playerBuildingAdapter.setList(playerInfo.ugcGameList)
            playerBuildingAdapter.setOnItemClickListener { _, position ->
                val itemGameId = playerBuildingAdapter.getItem(position).id
                if (itemGameId.isNotEmpty()) {
                    listener.onClickJumpGame(itemGameId)
                    Analytics.track(EventConstants.EVENT_CLICK_MGS_BUILD) {
                        putAll(analyticInfo)
                        put("ugcid", itemGameId)
                    }
                }
                dismiss()
            }

            binding.tvPlayerBuilding.isVisible = true
            binding.rvPlayerBuilding.isVisible = true
        }
    }

    private fun shouldShowPlayerDress() {
        if (!playerInfo.dressList.isNullOrEmpty()) {
            //显示服饰
            binding.rvPlayerDress.layoutManager = LinearLayoutManager(context).apply {
                orientation = LinearLayoutManager.HORIZONTAL
            }
            binding.rvPlayerDress.adapter = playerDressAdapter
            playerDressAdapter.setList(playerInfo.dressList)

            binding.tvPlayerDress.isVisible = true
            binding.rvPlayerDress.isVisible = true
        }
    }

    private var isPlayerOpenAudio = false

    /**
     * @param openAudio 当前状态是否打开语音，是：mute按钮；否：unmute按钮
     */
    fun updateVoiceBtn(openAudio: Boolean) {
        val canShowAudio = mgsInteractor.canAudio() && !isMe
        binding.tvVoiceMute.isVisible = canShowAudio
        if (canShowAudio) {
            binding.tvVoiceMute.background = ContextCompat.getDrawable(metaApp, if (openAudio) R.drawable.bg_button_negative else R.drawable.bg_button_normal_40)
            binding.tvVoiceMute.text = if (openAudio) context.getString(R.string.voice_mute) else context.getString(R.string.voice_unmute)
            binding.tvVoiceMute.setTextColor (if (openAudio) context.getColor(R.color.color_999999) else context.getColor(R.color.color_17191C))
            isPlayerOpenAudio = openAudio
        }
    }

    fun onReceivedVoiceChange(member: Member) {
        if (member.uuid == playerInfo.uuid) {
            if (isPlayerOpenAudio != member.isOpenAudio) {
                updateVoiceBtn(member.isOpenAudio)
            }
        }
    }

    fun onAppliedFriend() {
        binding.tvAddFriend.isEnabled = false
        binding.tvAddFriend.text = metaApp.getString(R.string.meta_mgs_apply)
        binding.tvAddFriend.setTextColor (context.getColor(R.color.color_999999))
    }

    override fun setOnDismissListener(listener: DialogInterface.OnDismissListener?) {
        this.listener.onDismissDialog()
        super.setOnDismissListener(listener)
    }
}