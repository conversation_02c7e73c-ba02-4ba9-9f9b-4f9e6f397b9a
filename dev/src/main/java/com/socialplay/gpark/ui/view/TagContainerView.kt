package com.socialplay.gpark.ui.view

import android.content.Context
import android.content.res.ColorStateList
import android.graphics.drawable.GradientDrawable
import android.util.AttributeSet
import android.view.Gravity
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.socialplay.gpark.R

/**
 * 可复用的标签容器View
 * 支持设置最大展示标签数、标签内容、样式定制等
 */
class TagContainerView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    // 默认配置
    private var maxTagCount = 2
    private var tagTextColor = ContextCompat.getColor(context, R.color.color_8792B2)
    private var tagBackgroundColor = ContextCompat.getColor(context, R.color.transparent)
    private var tagStrokeColor = ContextCompat.getColor(context, R.color.color_C0C8E0)
    private var tagStrokeWidth = 1
    private var tagCornerRadius = context.resources.getDimensionPixelSize(R.dimen.dp_4).toFloat()
    private var tagPaddingHorizontal = context.resources.getDimensionPixelSize(R.dimen.dp_4)
    private var tagPaddingVertical = context.resources.getDimensionPixelSize(R.dimen.dp_2)
    private var tagMarginEnd = context.resources.getDimensionPixelSize(R.dimen.dp_4)
    private var tagTextSize = 12f

    private var tags: List<String> = emptyList()

    init {
        orientation = HORIZONTAL
        gravity = Gravity.CENTER_VERTICAL

        // 解析自定义属性
        attrs?.let {
            parseAttributes(it)
        }
    }

    private fun parseAttributes(attrs: AttributeSet) {
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.TagContainerView)
        try {
            maxTagCount = typedArray.getInt(R.styleable.TagContainerView_maxTagCount, 2)
            tagTextColor = typedArray.getColor(R.styleable.TagContainerView_tagTextColor, tagTextColor)
            tagBackgroundColor = typedArray.getColor(R.styleable.TagContainerView_tagBackgroundColor, tagBackgroundColor)
            tagStrokeColor = typedArray.getColor(R.styleable.TagContainerView_tagStrokeColor, tagStrokeColor)
            tagStrokeWidth = typedArray.getDimensionPixelSize(R.styleable.TagContainerView_tagStrokeWidth, tagStrokeWidth)
            tagCornerRadius = typedArray.getDimension(R.styleable.TagContainerView_tagCornerRadius, tagCornerRadius)
            tagPaddingHorizontal = typedArray.getDimensionPixelSize(R.styleable.TagContainerView_tagPaddingHorizontal, tagPaddingHorizontal)
            tagPaddingVertical = typedArray.getDimensionPixelSize(R.styleable.TagContainerView_tagPaddingVertical, tagPaddingVertical)
            tagMarginEnd = typedArray.getDimensionPixelSize(R.styleable.TagContainerView_tagMarginEnd, tagMarginEnd)
            tagTextSize = typedArray.getDimension(R.styleable.TagContainerView_tagTextSize, tagTextSize)
        } finally {
            typedArray.recycle()
        }
    }

    /**
     * 设置标签列表
     */
    fun setTags(tagList: List<String>) {
        this.tags = tagList
        updateTagViews()
    }

    /**
     * 设置最大标签显示数量
     */
    fun setMaxTagCount(count: Int) {
        this.maxTagCount = count
        updateTagViews()
    }

    /**
     * 设置标签文字颜色
     */
    fun setTagTextColor(color: Int) {
        this.tagTextColor = color
        updateTagViews()
    }

    /**
     * 设置标签背景颜色
     */
    fun setTagBackgroundColor(color: Int) {
        this.tagBackgroundColor = color
        updateTagViews()
    }

    /**
     * 设置标签边框颜色
     */
    fun setTagStrokeColor(color: Int) {
        this.tagStrokeColor = color
        updateTagViews()
    }

    /**
     * 设置标签圆角半径
     */
    fun setTagCornerRadius(radius: Float) {
        this.tagCornerRadius = radius
        updateTagViews()
    }

    /**
     * 更新标签View
     */
    private fun updateTagViews() {
        removeAllViews()

        val tagsToShow = tags.take(maxTagCount)
        tagsToShow.forEachIndexed { index, tag ->
            val tagView = createTagView(tag)

            val layoutParams = LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )

            // 最后一个标签不设置右边距
            if (index < tagsToShow.size - 1) {
                layoutParams.marginEnd = tagMarginEnd
            }

            addView(tagView, layoutParams)
        }
    }

    /**
     * 创建单个标签View
     */
    private fun createTagView(text: String): TextView {
        return MetaTextView(context).apply {
            this.text = text
            textSize = <EMAIL>
            setTextColor(tagTextColor)
            setPadding(tagPaddingHorizontal, tagPaddingVertical, tagPaddingHorizontal, tagPaddingVertical)

            // 创建背景drawable
            background = createTagBackground()

            // MetaTextView已经处理了字体设置，不需要额外设置
        }
    }

    /**
     * 创建标签背景drawable
     */
    private fun createTagBackground(): GradientDrawable {
        return GradientDrawable().apply {
            shape = GradientDrawable.RECTANGLE
            cornerRadius = tagCornerRadius
            setColor(tagBackgroundColor)
            setStroke(tagStrokeWidth.toInt(), tagStrokeColor)
        }
    }
} 