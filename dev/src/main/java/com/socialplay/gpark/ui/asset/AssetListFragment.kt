package com.socialplay.gpark.ui.asset

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.choice.ChoiceCardInfo
import com.socialplay.gpark.data.model.choice.ChoiceCardType
import com.socialplay.gpark.data.model.choice.ChoiceGameInfo
import com.socialplay.gpark.databinding.FragmentAssetListBinding
import com.socialplay.gpark.ui.adapter.AssetListAdapter
import com.socialplay.gpark.ui.adapter.GridSpacingItemDecoration
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.ui.viewmodel.AssetListViewModel
import com.socialplay.gpark.ui.viewmodel.AssetListViewModelState
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.dp
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

class AssetListFragment : Fragment(R.layout.fragment_asset_list) {

    private var _binding: FragmentAssetListBinding? = null
    private val binding get() = _binding!!

    private val viewModel: AssetListViewModel by viewModels()
    private lateinit var assetAdapter: AssetListAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentAssetListBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupViews()
        observeViewModel()

        // 获取从首页传递的数据
        val cardName = arguments?.getString("cardName")
        val gameListJson = arguments?.getString("gameListJson")
        
        if (!cardName.isNullOrEmpty() && !gameListJson.isNullOrEmpty()) {
            // 设置标题
            binding.titleBar.setTitle(cardName)
            
            // 解析JSON数据并设置到ViewModel
            try {
                val gson = com.google.gson.Gson()
                val gameList = gson.fromJson(gameListJson, Array<ChoiceGameInfo>::class.java).toList()
                val assetCard = ChoiceCardInfo(
                    cardId = System.currentTimeMillis(),
                    cardName = cardName,
                    cardType = ChoiceCardType.ASSETS
                ).apply {
                    this.gameList = gameList.toMutableList()
                }
                
                val initialData = convertChoiceCardToAssetItems(assetCard)
                viewModel.setInitialData(initialData)
            } catch (e: Exception) {
                // 如果解析失败，使用默认数据
                binding.titleBar.setTitle("精选资源")
                val initialData = generateInitialData()
                viewModel.setInitialData(initialData)
            }
        } else {
            // 设置默认标题
            binding.titleBar.setTitle("精选资源")
            
            // 模拟从首页传递的初始数据
            val initialData = generateInitialData()
            viewModel.setInitialData(initialData)
        }
    }

    private fun setupViews() {
        assetAdapter = AssetListAdapter()

        // 设置加载更多监听器
        assetAdapter.setOnLoadMoreClickListener {
            viewModel.loadMore()
        }

        binding.recyclerViewAssets.apply {
            layoutManager = GridLayoutManager(context, 2).apply {
                // 设置SpanSizeLookup以支持加载更多footer占满一行
                spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                    override fun getSpanSize(position: Int): Int {
                        return if (position < assetAdapter.currentList.size) {
                            val item = assetAdapter.currentList[position]
                            if (item is com.socialplay.gpark.ui.adapter.AssetListItem.LoadMore) {
                                2 // LoadMore项占满一行
                            } else {
                                1 // 普通资源项占一列
                            }
                        } else {
                            1
                        }
                    }
                }
            }
            adapter = assetAdapter
            addItemDecoration(GridSpacingItemDecoration(2, 16.dp, true))

            addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)
                    val layoutManager = recyclerView.layoutManager as GridLayoutManager
                    val totalItemCount = layoutManager.itemCount
                    val lastVisibleItemPosition = layoutManager.findLastVisibleItemPosition()

                    // 当滑动到倒数第4个item时触发加载更多
                    if (totalItemCount <= lastVisibleItemPosition + 4) {
                        viewModel.loadMore()
                    }
                }
            })
        }

        binding.swipeRefreshLayout.setOnRefreshListener {
            viewModel.refresh()
        }

        binding.titleBar.setOnBackClickedListener {
            activity?.onBackPressed()
        }
    }

    private fun observeViewModel() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.state.collectLatest { state ->
                updateUI(state)
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.isRefreshing.collectLatest { isRefreshing ->
                binding.swipeRefreshLayout.isRefreshing = isRefreshing
            }
        }
    }

    private fun updateUI(state: AssetListViewModelState) {
        // 处理刷新状态
        when (state.refresh) {
            is Loading -> {
                if (state.combinedList.isEmpty()) {
                    binding.loadingView.showLoading()
                    binding.recyclerViewAssets.isVisible = false
                }
            }

            is Success -> {
                binding.loadingView.hide()
                binding.recyclerViewAssets.isVisible = true
                updateAdapterData(state)
            }

            is com.airbnb.mvrx.Fail -> {
                binding.loadingView.hide()
                if (state.combinedList.isEmpty()) {
                    binding.loadingView.showError()
                    binding.loadingView.setRetry {
                        viewModel.refresh()
                    }
                    ToastUtil.showShort(requireContext(), "加载失败，请重试")
                } else {
                    // 如果已有数据，只显示错误提示
                    ToastUtil.showShort(requireContext(), "刷新失败，请重试")
                }
            }

            else -> {
                // Uninitialized状态，不处理
            }
        }

        // 处理加载更多状态
        when (state.loadMore) {
            is Loading -> {
                // 显示加载更多状态
                updateAdapterData(state)
            }

            is Success -> {
                val loadMoreState = state.loadMore()
                updateAdapterData(state)

                // 如果到达末尾，可以显示"没有更多数据"的提示
                if (loadMoreState.isEnd && state.combinedList.isNotEmpty()) {
                    // LoadMore footer会显示"没有更多数据了"
                }
            }

            is com.airbnb.mvrx.Fail -> {
                updateAdapterData(state)
                ToastUtil.showShort(requireContext(), "加载更多失败，请重试")
            }

            else -> {
                // Uninitialized状态，不处理
            }
        }

        // 处理空数据状态
        if (state.combinedList.isEmpty() && state.refresh is Success) {
            binding.loadingView.showEmpty()
            binding.recyclerViewAssets.isVisible = false
        }
    }

    private fun updateAdapterData(state: AssetListViewModelState) {
        val combinedList = state.combinedList

        // 确定是否显示加载更多footer
        val shouldShowLoadMore = when {
            state.loadMore is Loading -> true
            state.loadMore is Success -> {
                val loadMoreState = state.loadMore()
                !loadMoreState.isEnd && combinedList.isNotEmpty()
            }

            state.loadMore is com.airbnb.mvrx.Fail -> true
            else -> false
        }

        // 更新adapter数据
        if (shouldShowLoadMore) {
            val loadMoreState = when (state.loadMore) {
                is Loading -> LoadMoreState(isEnd = false, needRefresh = false)
                is Success -> state.loadMore()
                is com.airbnb.mvrx.Fail -> LoadMoreState(isEnd = false, needRefresh = true)
                else -> LoadMoreState()
            }
            val isLoading = state.loadMore is Loading
            assetAdapter.updateData(combinedList, loadMoreState, isLoading)
        } else {
            assetAdapter.updateData(combinedList, null)
        }
    }

    /**
     * 将ChoiceCardInfo转换为AssetItem列表
     */
    private fun convertChoiceCardToAssetItems(assetCard: ChoiceCardInfo): List<com.socialplay.gpark.data.model.asset.AssetItem> {
        return assetCard.gameList?.map { gameInfo ->
            com.socialplay.gpark.data.model.asset.AssetItem(
                id = gameInfo.code ?: "",
                name = gameInfo.displayName ?: "",
                imageUrl = gameInfo.iconUrl ?: "",
                type = if (gameInfo.price != null) {
                    com.socialplay.gpark.data.model.asset.AssetType.MODULE
                } else {
                    com.socialplay.gpark.data.model.asset.AssetType.CLOTHES
                },
                tags = gameInfo.tagList ?: emptyList(),
                likeCount = (gameInfo.likeCount ?: 0).toLong(),
                isLiked = false,
                price = gameInfo.price,
                creatorAvatar = gameInfo.avatar ?: "",
                creatorName = gameInfo.nickname ?: ""
            )
        } ?: emptyList()
    }

    /**
     * 生成模拟的初始数据（从首页传递过来的数据）
     */
    private fun generateInitialData(): List<com.socialplay.gpark.data.model.asset.AssetItem> {
        return listOf(
            com.socialplay.gpark.data.model.asset.AssetItem(
                id = "initial_1",
                name = "精选服装1",
                imageUrl = "https://picsum.photos/200/200?random=1001",
                type = com.socialplay.gpark.data.model.asset.AssetType.CLOTHES,
                tags = listOf("精选", "热门"),
                likeCount = 1500,
                isLiked = true,
                price = null,
                creatorAvatar = "https://picsum.photos/50/50?random=1001",
                creatorName = "精选创作者1"
            ),
            com.socialplay.gpark.data.model.asset.AssetItem(
                id = "initial_2",
                name = "精选模组1",
                imageUrl = "https://picsum.photos/200/200?random=1002",
                type = com.socialplay.gpark.data.model.asset.AssetType.MODULE,
                tags = listOf("精选", "推荐"),
                likeCount = 2000,
                isLiked = false,
                price = 300,
                creatorAvatar = "https://picsum.photos/50/50?random=1002",
                creatorName = "精选创作者2"
            ),
            com.socialplay.gpark.data.model.asset.AssetItem(
                id = "initial_3",
                name = "精选服装2",
                imageUrl = "https://picsum.photos/200/200?random=1003",
                type = com.socialplay.gpark.data.model.asset.AssetType.CLOTHES,
                tags = listOf("精选", "新品"),
                likeCount = 800,
                isLiked = false,
                price = null,
                creatorAvatar = "https://picsum.photos/50/50?random=1003",
                creatorName = "精选创作者3"
            )
        )
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
} 