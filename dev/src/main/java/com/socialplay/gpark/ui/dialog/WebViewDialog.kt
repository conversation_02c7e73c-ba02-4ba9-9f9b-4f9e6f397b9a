package com.socialplay.gpark.ui.dialog

import android.view.WindowManager
import android.webkit.WebView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.navigation.fragment.navArgs
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.DialogWebviewBinding
import com.socialplay.gpark.ui.base.BaseDialogFragment
import com.socialplay.gpark.ui.web.jsinterfaces.JsBridgeApi
import com.socialplay.gpark.ui.web.jsinterfaces.JsBridgeHelper
import com.socialplay.gpark.ui.web.jsinterfaces.contract.FragmentJsBridgeContract
import com.socialplay.gpark.ui.web.webclients.DefaultWebChromeClient
import com.socialplay.gpark.ui.web.webclients.DefaultWebSettings
import com.socialplay.gpark.ui.web.webclients.DefaultWebViewClient
import com.socialplay.gpark.util.WebUtil
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.property.viewBinding
import timber.log.Timber

/**
 * create by: bin on 2022/6/15
 */
class WebViewDialog : BaseDialogFragment() {
    override val binding by viewBinding(DialogWebviewBinding::inflate)
    private val args by navArgs<WebViewDialogArgs>()

    override fun init() {
        val webView = kotlin.runCatching { WebView(requireContext()) }.getOrElse {
            toast(it.cause.toString())
            dismissAllowingStateLoss()
            return
        }
        webView.setBackgroundColor(resources.getColor(R.color.transparent))
        binding.root.addView(webView, ConstraintLayout.LayoutParams(ConstraintLayout.LayoutParams.MATCH_PARENT,ConstraintLayout.LayoutParams.MATCH_PARENT))
        setupWebView(webView)
    }

    private fun setupWebView(webView: WebView) {
        //添加js接口支持
        webView.addJavascriptInterface(JsBridgeApi(JsBridgeHelper(FragmentJsBridgeContract(this), webView)), JsBridgeHelper.JS_BRIDGE_ALIAS)

        //webview默认设置项
        DefaultWebSettings.setWebSettings(webView)
        WebView.setWebContentsDebuggingEnabled(BuildConfig.LOG_DEBUG)
        webView.webChromeClient = DefaultWebChromeClient(this)
        webView.webViewClient = DefaultWebViewClient(
            this,
            callback = { isSuccess, errorCode ->
                Timber.i("isSuccess: $isSuccess $errorCode")
            },
            onRenderProcessGone = {
                runCatching {
                    toast(R.string.failed_to_load)
                    dismissAllowingStateLoss()
                }
            }
        )
        webView.loadUrl(args.url)
    }
    fun getExtra(): String? {
        return args.extra
    }

    override fun onResume() {
        super.onResume()
        tryGetWebView()?.apply {
            WebUtil.onResume(this)
        }
    }

    override fun onPause() {
        super.onPause()
        tryGetWebView()?.apply {
            WebUtil.onPause(this)
        }
    }

    override fun onDestroyView() {
        kotlin.runCatching {
            tryGetWebView()?.apply {
                WebUtil.clearWebView(this)
            }
        }
        super.onDestroyView()
    }

    private fun tryGetWebView():WebView? {
        return kotlin.runCatching { binding.root.getChildAt(0) as? WebView }.getOrNull()
    }

    override fun windowHeight() = WindowManager.LayoutParams.MATCH_PARENT

    override fun isFullScreen(): Boolean {
        return true
    }

    override fun isHideNavigation(): Boolean {
        return true
    }

    override fun loadFirstData() {
    }
}