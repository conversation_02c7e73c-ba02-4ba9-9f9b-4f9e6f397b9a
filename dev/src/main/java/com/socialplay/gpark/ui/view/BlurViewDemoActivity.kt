package com.socialplay.gpark.ui.view

import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import com.socialplay.gpark.databinding.ActivityBlurViewDemoBinding
import eightbitlab.com.blurview.RenderEffectBlur
import eightbitlab.com.blurview.RenderScriptBlur

class BlurViewDemoActivity : AppCompatActivity() {

    private lateinit var binding: ActivityBlurViewDemoBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityBlurViewDemoBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setupBlurView()
    }

    private fun setupBlurView() {
        val radius = 20f // 模糊半径

        // 1. 找到根视图，即你想要模糊其内容的视图
        val rootView: View = binding.root

        // 2. 获取窗口背景
        val windowBackground: Drawable = window.decorView.background

        // 3. 设置BlurView
        binding.blurView.setupWith(rootView)
            .setFrameClearDrawable(windowBackground) // 可选, 但建议设置
            .setBlurRadius(radius)
    }
} 