package com.socialplay.gpark.ui.recommend.choice

import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.choice.ChoiceCardInfo
import com.socialplay.gpark.data.model.choice.ChoiceGameInfo
import com.socialplay.gpark.databinding.AdapterChoiceCardItemBuyBinding
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaModelCollector
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.getString

fun MetaModelCollector.buyCardList(
    card: ChoiceCardInfo,
    cardPosition: Int,
    spanSize: Int,
    listener: IChoiceListener
) {
    // 标题和更多
    add(
        ChoiceTitleMoreItem(card, spanSize, listener)
            .id("BuyCardTitle-$cardPosition")
            .spanSizeOverride { _, _, _ -> spanSize }
    )
    val games = card.gameList ?: return
    games.forEachIndexed { index, game ->
        add(
            BuyCardItem(
                item = game,
                position = index,
                card = card,
                cardPosition = cardPosition,
                spanSize = spanSize,
                listener = listener
            ).id("BuyCardItem-$cardPosition-$index-${game.code}")
        )
    }
}

data class BuyCardItem(
    val item: ChoiceGameInfo,
    val position: Int,
    val card: ChoiceCardInfo,
    val cardPosition: Int,
    val spanSize: Int,
    val listener: IChoiceListener
) : ViewBindingItemModel<AdapterChoiceCardItemBuyBinding>(
    R.layout.adapter_choice_card_item_buy,
    AdapterChoiceCardItemBuyBinding::bind
) {
    override fun AdapterChoiceCardItemBuyBinding.onBind() {
        // 顶部图片
        listener.getGlideOrNull()?.run {
            load(item.iconUrl).placeholder(R.drawable.placeholder_corner_12)
                .transform(CenterCrop())
                .into(ivGameIcon)
        }
        // 点赞
        likeView.setLikeText(UnitUtil.formatKMCount(item.likeCount ?: 0))
        // 名称
        tvGameTitle.text = item.displayName
        // 标签
        val tags = item.tagList ?: emptyList()
        val tagViews = listOf(tvTag1, tvTag2)
        tagViews.forEachIndexed { i, tv ->
            if (tags.getOrNull(i).isNullOrEmpty()) {
                tv.visibility = android.view.View.GONE
            } else {
                tv.visibility = android.view.View.VISIBLE
                tv.text = tags[i]
            }
        }
        // 价格
        tvPrice.text = "${item.price ?: 0L}"
        // 金币icon
        ivCoin.setImageResource(R.drawable.icon_item_buy_coins_g)
        // 购买按钮
        btnBuy.setOnClickListener { listener.onMoreClick(card) }
        // 整卡点击
        root.setOnClickListener { listener.onItemClick(cardPosition, card, position, item, false) }
    }
} 