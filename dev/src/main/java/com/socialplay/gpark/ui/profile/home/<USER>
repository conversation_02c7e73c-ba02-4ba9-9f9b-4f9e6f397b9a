package com.socialplay.gpark.ui.profile.home

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.ItemHomeDrawerBinding
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.util.extension.compoundDrawables
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setPaddingEx
import com.socialplay.gpark.util.extension.unsetOnClick

interface IHomeDrawerListener {
    fun clickHomeDrawerEntrance(item: HomeDrawerEntrance)
}

sealed class HomeDrawerEntrance(
    @DrawableRes
    val iconResId: Int,
    @StringRes
    val titleResId: Int,
    val showRedDot: Boolean = false
) {
    class EditProfile :
        HomeDrawerEntrance(
            R.drawable.ic_feat_24_1a1a1a_edit_profile,
            R.string.profile_side_bar_edit
        )

    class AddFriend :
        HomeDrawerEntrance(
            R.drawable.ic_feat_24_1a1a1a_add_friends,
            R.string.profile_side_bar_add_friends
        )

    class AccountSetting :
        HomeDrawerEntrance(
            R.drawable.ic_feat_24_1a1a1a_account_security,
            R.string.profile_side_bar_account_setting
        )

    class MyBalance :
        HomeDrawerEntrance(R.drawable.ic_feat_24_1a1a1a_my_wallet, R.string.profile_side_bar_my_walleat)

    class Membership :
        HomeDrawerEntrance(R.drawable.ic_feat_24_1a1a1a_premium, R.string.profile_side_bar_premium)

    class PrivacySetting(showRedDot: Boolean) : HomeDrawerEntrance(
        R.drawable.ic_feat_24_1a1a1a_privacy_settings,
        R.string.privacy_settings,
        showRedDot = showRedDot
    )

    class AboutUs : HomeDrawerEntrance(R.drawable.ic_feat_24_1a1a1a_about_us, R.string.about_us)
}

data class HomeDrawerItem(
    val item: HomeDrawerEntrance,
    val position: Int,
    val itemCount: Int,
    val listener: IHomeDrawerListener
) : ViewBindingItemModel<ItemHomeDrawerBinding>(
    R.layout.item_home_drawer,
    ItemHomeDrawerBinding::bind
) {

    override fun ItemHomeDrawerBinding.onBind() {
        tvTitle.setText(item.titleResId)
        tvTitle.compoundDrawables(
            left = item.iconResId,
            right = R.drawable.ic_arrow_right_18_b3b3b3
        )
        if (position == 0) {
            if (itemCount == 1) {
                tvTitle.setBackgroundResource(R.drawable.bg_white_round_16_ripple_black_8)
                tvTitle.setPaddingEx(top = dp(16), bottom = dp(16))
            } else {
                tvTitle.setBackgroundResource(R.drawable.bg_white_top_round_16_ripple_black_8)
                tvTitle.setPaddingEx(top = dp(16), bottom = dp(20))
            }
        } else if (position == itemCount - 1) {
            tvTitle.setBackgroundResource(R.drawable.bg_white_bottom_round_16_ripple_black_8)
            tvTitle.setPaddingEx(top = dp(20), bottom = dp(16))
        } else {
            tvTitle.setBackgroundResource(R.drawable.bg_white_ripple_black_8)
            tvTitle.setPaddingEx(top = dp(20), bottom = dp(20))
        }
        tvTitle.setOnAntiViolenceClickListener {
            listener.clickHomeDrawerEntrance(item)
        }
    }

    override fun ItemHomeDrawerBinding.onUnbind() {
        tvTitle.unsetOnClick()
    }
}

interface IProfileEntranceListener {
    fun clickProfileEntrance(item: ProfileEntrance)
}

sealed class ProfileEntrance(
    @DrawableRes
    val iconResId: Int,
    @StringRes
    val titleResId: Int,
    val order: Int,
) {
    class Premium: ProfileEntrance(
        R.drawable.ic_profile_entrance_premium,
        R.string.profile_entrance_premium,
        1
    )
    class Wallet: ProfileEntrance(
        R.drawable.ic_profile_entrance_wallet,
        R.string.profile_entrance_wallet,
        2
    )
    class MyGroup: ProfileEntrance(
        R.drawable.ic_profile_entrance_my_group,
        R.string.profile_entrance_my_group,
        3
    )
    class Build: ProfileEntrance(
        R.drawable.ic_profile_entrance_build,
        R.string.profile_entrance_build,
        4
    )
    class TryOn: ProfileEntrance(
        R.drawable.ic_profile_entrance_tryon,
        R.string.profile_entrance_try_on,
        5
    )
}