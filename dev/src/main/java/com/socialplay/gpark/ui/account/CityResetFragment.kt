package com.socialplay.gpark.ui.account

import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.navArgs
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.BanBlockInteractor
import com.socialplay.gpark.databinding.FragmentAccountNicknameResetBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.util.DeviceUtil
import com.socialplay.gpark.util.InputUtil
import com.socialplay.gpark.util.KeyboardHeightUtilV2
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.navigateUp
import com.socialplay.gpark.util.extension.setMargin
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTextColorByRes
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

/**
 * created by liyanfeng on 2022/7/29 2:11 下午
 * @describe:
 */
class CityResetFragment : BaseFragment<FragmentAccountNicknameResetBinding>() {

    companion object {
        const val NAME_COUNT = 60
    }

    private val viewModel by viewModel<NickNameResetViewModel>()
    private val accountInteractor: AccountInteractor by inject()
    private val banBlockInteractor: BanBlockInteractor by inject()
    private val args by navArgs<CityResetFragmentArgs>()

    private val dp40 = 40.dp

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentAccountNicknameResetBinding? {
        return FragmentAccountNicknameResetBinding.inflate(inflater, container, false)
    }

    override fun onResume() {
        super.onResume()
        if (DeviceUtil.isHarmonyOs()) {
            KeyboardHeightUtilV2.registerKeyboardHeightListener(requireActivity()) {
                binding.tvSure.setMargin(bottom = dp40 + it)
            }
        }
    }

    override fun onPause() {
        super.onPause()
        if (DeviceUtil.isHarmonyOs()) {
            KeyboardHeightUtilV2.unregisterKeyboardHeightListener(requireActivity())
        }
        InputUtil.hideKeyboard(binding.etName)
    }

    override fun init() {
        binding.apply {
            tvNameLimitTips.setText(R.string.rename_more_than_60_characters)
            tbl.setTitle(getString(R.string.text_city))
            etName.hint = getString(R.string.text_city)
            etName.setText(args.oldCityName ?: "")
            updateNameUI(etName.text.toString())

            tbl.setOnBackClickedListener {
                navigateUp()
            }

            ivClearIcon.setOnClickListener {
                etName.setText("")
                viewModel.toggleClearIconVisibility()
            }

            etName.addTextChangedListener {
                viewModel.postNameValueChanged(it?.toString())
            }

            tvSure.setOnAntiViolenceClickListener {
                val input = etName.text.toString().trim()
                if (input.isEmpty()) {
                    toast(getString(R.string.cannot_empty).format(getString(R.string.text_city)))
                    return@setOnAntiViolenceClickListener
                } else if (input.length > NAME_COUNT) {
                    toast(R.string.rename_more_than_60_characters)
                    return@setOnAntiViolenceClickListener
                }
                if (input == accountInteractor.accountLiveData.value?.city) {
                    navigateUp()
                    return@setOnAntiViolenceClickListener
                }
                viewModel.updateUserCity(etName.text.toString())
            }
            etName.requestFocus()
            InputUtil.showSoftBoard(etName)
        }

        viewModel.nameLiveData.observe(viewLifecycleOwner) {
            updateNameUI(it)
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.nicknameChangeResultFlow.collect {
                it?.let {
                    if (it.first.data == true) {
                        Analytics.track(EventConstants.EVENT_PROFILE_EDIT_CITY)
                        ToastUtil.showShort(
                            requireContext(),
                            R.string.profile_infomation_save_success
                        )
                        navigateUp()
                    } else {
                        ToastUtil.showShort(requireContext(), it.second)
                    }
                }
            }
        }

        viewModel.accountBanLiveData.observe(viewLifecycleOwner) {
            banBlockInteractor.showBanDialog(BanBlockInteractor.REASON_EDIT_PROFILE, this)
        }
    }

    private fun updateNameUI(it: String?) {
        val trimmed = it.orEmpty().trim()
        val isEmptyName = trimmed.isEmpty()
        binding.ivClearIcon.visible(!isEmptyName, true)
        binding.tvSure.isEnabled = !isEmptyName
        binding.tvSure.setTextColor(resources.getColor(if (!isEmptyName) R.color.black else R.color.black_60))
        val isFull = trimmed.length > NAME_COUNT
        binding.tvNameLimitTips.setTextColorByRes(
            if (isFull) {
                R.color.color_FF5F42
            } else {
                R.color.textColorSecondary
            }
        )
    }

    override fun loadFirstData() {

    }

    override fun getFragmentName(): String = PageNameConstants.FRAGMENT_CITY_RESET

    override fun cancelFitsSystemWindows4HarmonyOs() = true to true
}