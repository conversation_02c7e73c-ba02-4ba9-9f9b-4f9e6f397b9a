package com.socialplay.gpark.ui.recommend.choice

import android.view.View
import com.airbnb.epoxy.Carousel
import com.airbnb.epoxy.VisibilityState
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.choice.ChoiceCardInfo
import com.socialplay.gpark.data.model.choice.ChoiceGameInfo
import com.socialplay.gpark.databinding.AdapterChoiceUgcCreateFullBinding
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaModelCollector
import com.socialplay.gpark.ui.core.views.carouselNoSnapWrapBuilder
import com.socialplay.gpark.ui.room.RoomCardUtil
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setMargin
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setWidth

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/11/01
 *     desc   :
 * </pre>
 */
fun MetaModelCollector.choiceUgcCreateCard(
    card: ChoiceCardInfo,
    cardPosition: Int,
    spanSize: Int,
    listener: IChoiceListener
) {
    val games = card.gameList
    if (games.isNullOrEmpty()) return
    add(
        ChoiceTitleMoreItem(card, spanSize, listener).id("ChoiceUgcCreateCardTitle-$cardPosition")
            .spanSizeOverride { _, _, _ -> spanSize }
    )
    carouselNoSnapWrapBuilder {
        id("ChoiceUgcCreateCardList-$cardPosition")
        padding(Carousel.Padding.dp(16, 16, 6, 8, 0))
        hasFixedSize(true)
        initialPrefetchItemCount(3)
        onVisibilityStateChanged { _, _, visibilityState ->
            if (visibilityState == VisibilityState.VISIBLE) {
                listener.onCardShow(cardPosition, card)
            }
        }
        spanSizeOverride { _, _, _ ->
            spanSize
        }
        val itemWidth = RoomCardUtil.getRoomCardWidth()
        games.forEachIndexed { position, game ->
            add(
                ChoiceUgcCreateCardItem(
                    game,
                    position,
                    card,
                    cardPosition,
                    itemWidth,
                    spanSize,
                    listener
                ).id("ChoiceUgcCreateCardGame-$cardPosition-$position-${game.code}")
            )
        }
    }
}

data class ChoiceUgcCreateCardItem(
    val item: ChoiceGameInfo,
    val position: Int,
    val card: ChoiceCardInfo,
    val cardPosition: Int,
    val itemWidth: Int,
    val spanSize: Int,
    val listener: IChoiceListener
) : ViewBindingItemModel<AdapterChoiceUgcCreateFullBinding>(
    R.layout.adapter_choice_ugc_create_full,
    AdapterChoiceUgcCreateFullBinding::bind
) {

    override fun AdapterChoiceUgcCreateFullBinding.onBind() {
        root.setWidth(itemWidth)
        root.setMargin(right = dp(10))
        listener.getGlideOrNull()?.run {
            load(item.avatar).circleCrop()
                .into(ivAuthorAvatar)

            load(item.imageUrl).into(ivIcon)
        }
        tvAuthorName.text = item.nickname
        tvName.text = item.displayName
        tvHeat.setLikeText(UnitUtil.formatPlayerCount(item.playingCount?.toLong() ?: 0))
        root.setOnAntiViolenceClickListener {
            listener.onItemClick(cardPosition, card, position, item, false)
        }
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        if (visibilityState == VisibilityState.VISIBLE) {
            listener.onItemShow(cardPosition, card, position, item, false)
        }
    }
}