package com.socialplay.gpark.ui.reportBlock

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.luck.picture.lib.config.PictureMimeType
import com.luck.picture.lib.entity.LocalMedia
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.UploadFileInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.kv.TTaiKV.Companion.ID_REPORT_USER_REASON
import com.socialplay.gpark.data.model.reportBlock.ReportAttachment
import com.socialplay.gpark.data.model.reportBlock.ReportReasonItem
import com.socialplay.gpark.data.model.reportBlock.ReportType
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.ui.reportBlock.user.AppReportUserFragment
import com.socialplay.gpark.ui.reportBlock.user.AppReportUserFragment.Companion.IMAGE_COUNT
import com.socialplay.gpark.ui.reportBlock.user.AppReportUserFragment.Companion.VIDEO_COUNT
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.extension.likelyPath
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.File

/**
 * created by liyanfeng on 2022/9/27 9:56 上午
 * @describe:
 */
class ReportViewModel(
    private val metaRepository: IMetaRepository,
    private val metaKV: MetaKV,
    private val uploadFileInteractor: UploadFileInteractor
) : ViewModel() {

    private val _reasonItemsLiveData = MutableLiveData<List<ReportReasonItem>?>()
    val reasonItemsLiveData: LiveData<List<ReportReasonItem>?> get() = _reasonItemsLiveData

    private val _selectedItemLiveData = MutableLiveData<ReportReasonItem?>()
    val selectedItemLiveData: LiveData<ReportReasonItem?> get() = _selectedItemLiveData

    private val _reportResultLiveData = MutableLiveData<DataResult<Boolean>>()
    val reportResultLiveData: LiveData<DataResult<Boolean>> get() = _reportResultLiveData

    private val _mediaAttachmentLiveData = MutableLiveData<List<ReportAttachment>?>(emptyList())
    val mediaAttachmentLiveData: LiveData<List<ReportAttachment>?> get() = _mediaAttachmentLiveData

    companion object {
        const val SOURCE_GAME = "game"
        const val SOURCE_PROFILE = "profile"
        const val SOURCE_CHAT = "chat"

        fun getAnalyticSourceByApiSource(apiSource: String): String {
            return when (apiSource) {
                SOURCE_PROFILE -> EventParamConstants.SOURCE_REPORT_PROFILE
                SOURCE_CHAT -> EventParamConstants.SOURCE_REPORT_CHAT
                SOURCE_GAME -> EventParamConstants.SOURCE_REPORT_GAME
                else -> apiSource
            }
        }
    }

    fun getReportReviewReasonList() {
        val cacheResult =
            GsonUtil.gsonSafeParseCollection<List<ReportReasonItem>>(metaKV.tTaiKV.reportReviewReason)
        _reasonItemsLiveData.value = cacheResult
    }

    fun getReportLibraryReasonList() {
        val cacheResult =
            GsonUtil.gsonSafeParseCollection<List<ReportReasonItem>>(metaKV.tTaiKV.reportLibraryReason)
        _reasonItemsLiveData.value = cacheResult
    }

    fun getReportUserReasonList() {
        val cacheResult = GsonUtil.gsonSafeParseCollection<List<ReportReasonItem>>(metaKV.tTaiKV.reportUserReason)
        _reasonItemsLiveData.value = cacheResult
        viewModelScope.launch {
            runCatching {
                val rawNetData = metaRepository.getTTaiConfigByIdV2(ID_REPORT_USER_REASON).invoke()
                val data = GsonUtil.gsonSafeParseCollection<List<ReportReasonItem>>(rawNetData.value)
                if (!data.isNullOrEmpty()) {
                    _reasonItemsLiveData.postValue(data)
                }
            }
        }
    }

    fun selectItem(item: ReportReasonItem) {
        reasonItemsLiveData.value?.forEach { it.isChecked = it == item }
        _reasonItemsLiveData.value = _reasonItemsLiveData.value
        _selectedItemLiveData.value = item
    }

    fun doReport(
        type: ReportType,
        reportTargetId: String,
        reportReasonId: String?,
        reportedUid: String?,
        reportReason: String?,
        reportSource: String?,
        reportAdditional: String?
    ) = viewModelScope.launch {
        if (type == ReportType.ReportUser) {
            doUserReport(
                type,
                reportTargetId,
                reportReasonId,
                reportedUid,
                reportReason,
                reportSource,
                reportAdditional
            )
        } else {
            doReport(
                type,
                reportTargetId,
                reportReasonId,
                reportedUid,
                reportReason,
                null,
                reportSource,
                reportAdditional
            )
        }
    }

    private suspend fun doUserReport(
        type: ReportType,
        reportTargetId: String,
        reportReasonId: String?,
        reportedUid: String?,
        reportReason: String?,
        reportSource: String?,
        reportAdditional: String?
    ) {
        val needUploadFileList = kotlin.runCatching {
            _mediaAttachmentLiveData.value?.filter { it.url.isNullOrEmpty() }?.map {
                File(it.localMedia?.likelyPath ?: "")
            }?.filter { file ->
                file.exists()
            }
        }.getOrElse {
            Timber.e(AppReportUserFragment.TAG, "needUploadFileList error $it")
            null
        }
        if (needUploadFileList.isNullOrEmpty()) {
            doReport(
                type,
                reportTargetId,
                reportReasonId,
                reportedUid,
                reportReason,
                null,
                reportSource,
                reportAdditional
            )
        } else {
            uploadFileInteractor.uploadList(
                UploadFileInteractor.BIZ_CODE_REPORT,
                needUploadFileList
            ).map { resultList ->
                val list = ArrayList(_mediaAttachmentLiveData.value ?: emptyList())
                resultList.mapNotNull { it.data }.forEach { result ->
                    list.find { it.localMedia?.likelyPath == result.filePath }?.apply {
                        if (url.isNullOrEmpty()) url = result.url
                    }
                }
                _mediaAttachmentLiveData.postValue(list)
                val allSuccess = !resultList.any { !it.succeeded }
                Triple(
                    list.mapNotNull { it.url },
                    allSuccess,
                    resultList.find { !it.succeeded }?.message
                )
            }.collect { uploadPair ->
                val uploadSuccess = uploadPair.second
                val onlineUrlMap = uploadPair.first
                val errorMsg = uploadPair.third
                if (uploadSuccess) {
                    doReport(
                        type,
                        reportTargetId,
                        reportReasonId,
                        reportedUid,
                        reportReason,
                        onlineUrlMap,
                        reportSource,
                        reportAdditional,
                    )
                } else {
                    _reportResultLiveData.postValue(DataResult.Error(-1, errorMsg.orEmpty(), null))
                }
            }
        }
    }

    private suspend fun doReport(
        type: ReportType,
        reportTargetId: String,
        reportReasonId: String?,
        reportedUid: String?,
        reportReason: String?,
        attachmentUrlList: List<String>?,
        reportSource: String?,
        reportAdditional: String?,
    ) {
        metaRepository.reportAdd(
            type.key,
            reportTargetId,
            reportReasonId,
            reportedUid,
            reportReason,
            attachmentUrlList,
            reportSource,
            reportAdditional
        ).collect {
            _reportResultLiveData.postValue(it)
        }
    }

    fun getReportRoomReasonList() {
        val cacheResult =
            GsonUtil.gsonSafeParseCollection<List<ReportReasonItem>>(metaKV.tTaiKV.reportReviewReason)
        _reasonItemsLiveData.value = cacheResult
    }

    fun getReportGroupChatReasonList() {
        val cacheResult =
            GsonUtil.gsonSafeParseCollection<List<ReportReasonItem>>(metaKV.tTaiKV.reportGroupChatReason)
        _reasonItemsLiveData.value = cacheResult
    }

    fun addAttachment(item: List<LocalMedia>) {
        if (item.isEmpty()) return
        val list = ArrayList(_mediaAttachmentLiveData.value ?: emptyList())
        list.addAll(item.map { ReportAttachment(false, it, null) })
        _mediaAttachmentLiveData.value = list
    }

    fun removeAttachment(item: ReportAttachment) {
        if (item.localMedia == null) return
        val list = ArrayList(_mediaAttachmentLiveData.value ?: emptyList())
        list.remove(item)
        _mediaAttachmentLiveData.value = list
    }

    fun canAddMediaAttachment(mediaAttachments: List<ReportAttachment>?): Triple<Boolean, Boolean, Boolean> {
        val videoSize =
            mediaAttachments?.filter { PictureMimeType.isHasVideo(it.localMedia?.mimeType) }?.size
                ?: 0
        val imageSize =
            mediaAttachments?.filter { PictureMimeType.isHasImage(it.localMedia?.mimeType) }?.size
                ?: 0
        val canChooseAll = videoSize == 0 && imageSize == 0
        val canChooseVideo = imageSize <= 0 && videoSize < VIDEO_COUNT
        val canChooseImage = videoSize <= 0 && imageSize < IMAGE_COUNT
        return Triple(canChooseAll, canChooseVideo, canChooseImage)
    }

}