package com.socialplay.gpark.ui.view

import android.app.Activity
import android.os.Build
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.PopupWindow
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.StatusBarUtil

/**
 * popupWindow
 */
class PopupWindowCompat(contentView: View?, width: Int, height: Int) : PopupWindow(contentView, width, height) {

    override fun showAsDropDown(anchor: View, xoff: Int, yoff: Int, gravity: Int) {
        if(anchor.windowToken == null){
            return
        }
        // 7.0 以下或者高度为WRAP_CONTENT, 默认显示
        if (Build.VERSION.SDK_INT < 24 || height == ViewGroup.LayoutParams.WRAP_CONTENT) {
            showCompatSuper(anchor, xoff, yoff, gravity)
        } else {
            if (contentView.context is Activity) {
                // 获取屏幕真实高度, 减掉虚拟按键的高度
                val screenHeight: Int = ScreenUtil.getScreenHeight(contentView.context) - StatusBarUtil.getStatusBarHeight(contentView.context)
                val location = IntArray(2)
                // 获取控件在屏幕的位置
                anchor.getLocationOnScreen(location)
                // 算出popwindow最大高度
                val maxHeight: Int = screenHeight - location[1] - anchor.height
                // popupwindow  有具体的高度值，但是小于anchor下边缘与屏幕底部的距离， 正常显示
                if (height > 0 && height < maxHeight) {
                    showCompatSuper(anchor, xoff, yoff, gravity)
                } else {
                    // match_parent 或者 popwinddow的具体高度值大于anchor下边缘与屏幕底部的距离， 都设置为最大可用高度
                    height = maxHeight
                    showCompatSuper(anchor, xoff, yoff, gravity)
                }
            }
        }
    }

    fun showAsDropDownByLocation(view: View, x: Int, y: Int, autoHeight: Boolean = true) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            val location = IntArray(2)
            view.getLocationInWindow(location)
            val screenSize = ScreenUtil.getScreenSize(view.context)
            val screenHeight = screenSize?.getOrElse(1) { ScreenUtil.getScreenHeight(contentView.context) } ?: 0
            if (autoHeight && (height == WindowManager.LayoutParams.MATCH_PARENT || ScreenUtil.getScreenHeight(contentView.context) <= height)) {
                height = screenHeight - location[1] - view.height
            }
            showAtLocation(view, Gravity.NO_GRAVITY, location[0] + x, location[1] + view.height + y)
            update()
        } else {
            showAsDropDown(view, x, y)
            update()
        }
    }

    // 解决andorid4.3及以下异常 java.lang.NoSuchMethodError: android.widget.PopupWindow.showAsDropDown
    private fun showCompatSuper(anchor: View, xoff: Int, yoff: Int, gravity: Int) {
        kotlin.runCatching {
            super.showAsDropDown(anchor, xoff, yoff, gravity)
        }
    }

}