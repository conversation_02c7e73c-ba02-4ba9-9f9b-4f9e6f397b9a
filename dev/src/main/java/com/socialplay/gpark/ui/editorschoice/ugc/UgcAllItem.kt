package com.socialplay.gpark.ui.editorschoice.ugc

import androidx.lifecycle.LifecycleCoroutineScope
import com.airbnb.epoxy.EpoxyController
import com.bumptech.glide.Glide
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.choice.ChoiceGameInfo
import com.socialplay.gpark.databinding.AdapterChoiceUgcCreateFullBinding
import com.socialplay.gpark.ui.core.GlideGetter
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener

fun EpoxyController.ugcGameItem(
    choiceGameInfo: ChoiceGameInfo,
    lifecycleCoroutineScope: LifecycleCoroutineScope,
    getGlide: GlideGetter,
    onClick: (() -> Unit)? = null
) {
    add(
        UgcGameItem(
            choiceGameInfo,lifecycleCoroutineScope, getGlide, onClick
        ).apply {
            id(choiceGameInfo.code)
        })
}

data class UgcGameItem(
    val choiceGameInfo: ChoiceGameInfo,
    val lifecycleCoroutineScope: LifecycleCoroutineScope,
    val getGlide: GlideGetter,
    val onClick: (() -> Unit)?
) : ViewBindingItemModel<AdapterChoiceUgcCreateFullBinding>(
    R.layout.adapter_choice_ugc_create_full,
    AdapterChoiceUgcCreateFullBinding::bind
) {
    override fun AdapterChoiceUgcCreateFullBinding.onBind() {
        tvName.text = choiceGameInfo.displayName
        tvAuthorName.text = choiceGameInfo.nickname
        tvHeat.setLikeText(UnitUtil.formatPlayerCount(choiceGameInfo.playingCount?.toLong() ?: 0))
        getGlide()?.run {
            load(choiceGameInfo.iconUrl).into(ivIcon)
        }
        onClick?.let { root.setOnAntiViolenceClickListener { it() } }
        Glide.with(root).load(choiceGameInfo.avatar).circleCrop().into(ivAuthorAvatar)
    }

    override fun AdapterChoiceUgcCreateFullBinding.onUnbind() {
        root.setOnClickListener(null)
    }
}