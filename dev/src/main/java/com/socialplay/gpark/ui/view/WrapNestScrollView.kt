package com.socialplay.gpark.ui.view

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.core.widget.NestedScrollView
import androidx.recyclerview.widget.RecyclerView
import kotlin.math.absoluteValue
import kotlin.math.sign

class WrapNestScrollView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : NestedScrollView(context, attrs) {

    private var touchSlop = 0
    private var initialX = 0f
    private var initialY = 0f

    var disallowParentInterceptTouchEvent = false

    override fun dispatchTouchEvent(ev: MotionEvent): Boolean {

        when(ev.action) {
            MotionEvent.ACTION_DOWN -> {
                initialX = ev.x
                initialY = ev.y
                parent.requestDisallowInterceptTouchEvent(true)
            }
            MotionEvent.ACTION_MOVE -> {

                val endX = ev.x
                val endY = ev.y

                val dx = endX - initialX
                val dy = endY - initialY

                if (dx.absoluteValue > dy.absoluteValue * .5f) {
                    val canScrollHorizontally = if(disallowParentInterceptTouchEvent) disallowParentInterceptTouchEvent else canScrollHorizontally(-dx.sign.toInt())
                    parent.requestDisallowInterceptTouchEvent(canScrollHorizontally)
                } else {
                    parent.requestDisallowInterceptTouchEvent(canScrollVertically(-dy.sign.toInt()))
                }

            }
            MotionEvent.ACTION_CANCEL,MotionEvent.ACTION_UP -> {
                parent.requestDisallowInterceptTouchEvent(false)
            }
        }

        return super.dispatchTouchEvent(ev)
    }

    fun setOnFinishActionListener(listener: () -> Unit) {
    }

}