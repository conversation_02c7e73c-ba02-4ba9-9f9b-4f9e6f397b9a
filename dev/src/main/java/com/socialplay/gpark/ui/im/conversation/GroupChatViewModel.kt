package com.socialplay.gpark.ui.im.conversation

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.meta.box.biz.friend.model.FriendInfo
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.exception
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.groupchat.GroupChatAddMembers
import com.socialplay.gpark.data.model.groupchat.GroupChatDetailInfo
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import kotlinx.coroutines.launch
import org.koin.android.ext.android.get

data class GroupChatState(
    val createGroupChatResult: Async<Pair<Int, DataResult<GroupChatDetailInfo>>> = Uninitialized,
    val inviteMembersResult: Async<Pair<MutableMap<String, FriendInfo>, DataResult<GroupChatAddMembers>>> = Uninitialized,
    val groupChatDetailResult: Async<GroupChatDetailInfo> = Uninitialized,
    val imIdToGroupIds: Map<String, Long> = emptyMap(),
) : MavericksState

class GroupChatViewModel(
    initialState: GroupChatState,
    val metaRepository: com.socialplay.gpark.data.IMetaRepository,
    val metaKV: MetaKV
) : BaseViewModel<GroupChatState>(initialState) {

    fun isGroupOwner(memberId: String): Boolean? {
        val groupDetail = oldState.groupChatDetailResult.invoke()
        return groupDetail?.isGroupOwner(memberId)
    }

    fun isGroupManager(memberId: String): Boolean? {
        val groupDetail = oldState.groupChatDetailResult.invoke()
        return groupDetail?.isGroupManager(memberId)
    }

    fun showCreateGroupChatRedDot(): Boolean {
        return metaKV.account.showCreateGroupRedDot
    }

    fun clickCreateGroupChatMenu() {
        metaKV.account.showCreateGroupRedDot = false
    }

    fun getGroupIdByImId(imId: String): Long? {
        return oldState.imIdToGroupIds[imId]
    }

    fun fetchGroupIdByImIds(imIds: List<String>) {
        viewModelScope.launch {
            val result = metaRepository.getGroupIdByImIds(imIds)
            val oldData = oldState.imIdToGroupIds
            if (result.isNotEmpty()) {
                if (oldData.isEmpty()) {
                    setState {
                        copy(imIdToGroupIds = result)
                    }
                } else {
                    setState {
                        copy(imIdToGroupIds = oldData.toMutableMap().apply {
                            putAll(result)
                        })
                    }
                }
            }
        }
    }

    fun createGroupChat(members: List<String>) {
        if (oldState.createGroupChatResult is Loading) {
            return
        }
        setState {
            copy(createGroupChatResult = Loading())
        }
        viewModelScope.launch {
            val result = metaRepository.createGroupChat(members)
            setState {
                copy(createGroupChatResult = Success(members.size to result))
            }
        }
    }

    fun inviteMembers(chatGroupId: Long, members: MutableMap<String, FriendInfo>) {
        if (oldState.inviteMembersResult is Loading) {
            return
        }
        setState {
            copy(inviteMembersResult = Loading())
        }
        viewModelScope.launch {
            val result = metaRepository.inviteGroupChatMembers(
                chatGroupId,
                members.keys.toList()
            )
            setState {
                copy(inviteMembersResult = Success(members to result))
            }
        }
    }

    fun getCurrentGroupChatDetailInfo(chatGroupId: String): GroupChatDetailInfo? {
        return oldState.groupChatDetailResult.invoke()
    }

    fun getGroupChatDetailInfo(chatGroupId: String) {
        if (oldState.groupChatDetailResult is Loading) {
            return
        }
        setState {
            copy(groupChatDetailResult = Loading())
        }
        val groupIdLong = runCatching {
            chatGroupId.toLong()
        }.getOrNull() ?: return
        viewModelScope.launch {
            metaRepository.getGroupChatDetailInfo(groupIdLong).collect { result ->
                if (result.succeeded && result.data != null) {
                    setState {
                        copy(groupChatDetailResult = Success(result.data!!))
                    }
                } else {
                    setState {
                        copy(
                            groupChatDetailResult = Fail(
                                result.exception ?: Exception("unknown exception")
                            )
                        )
                    }
                }
            }
        }
    }

    companion object : KoinViewModelFactory<GroupChatViewModel, GroupChatState>() {
        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext, state: GroupChatState
        ): GroupChatViewModel {
            return GroupChatViewModel(state, get(), get())
        }
    }
}