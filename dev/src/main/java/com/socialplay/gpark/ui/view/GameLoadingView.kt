package com.socialplay.gpark.ui.view

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View
import androidx.annotation.ColorInt
import com.socialplay.gpark.R


/**
 *  <AUTHOR> taiyuan
 *  time   : 2021/5/19
 *  desc   :
 */
class GameLoadingView(context: Context, attrs: AttributeSet) : View(context, attrs) {

    private val paint: Paint
    /**
     * @param strokeWidth 设置圆环的宽度
     */
    var strokeWidth: Float = 0.toFloat()
    /**
     * @param radius 设置圆的的半径
     */
    var radius: Float = 0.toFloat()
    /**
     * @param roundRadius 设置圆角的半径
     */
    var roundRadius: Float = 0.toFloat()
    /**
     * @param progress 设置进度
     */
    var progress: Int = 0
        set(progress) {
            if (progress in 0..mMaxProgress) {
                field = progress
                postInvalidate()
            }
        }
    private var mMaxProgress: Int = 0
    private var mBackgroundColor: Int = 0

    init {
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.GameLoadingView)
        radius = typedArray.getDimension(R.styleable.GameLoadingView_radius, RADIUS_DEFAULT)
        strokeWidth =
            typedArray.getDimension(R.styleable.GameLoadingView_strokeWidth, STROKE_WIDTH_DEFAULT)
        mMaxProgress =
            typedArray.getInteger(R.styleable.GameLoadingView_maxProgress, MAX_PROGRESS_DEFAULT)
        roundRadius = typedArray.getDimension(
            R.styleable.GameLoadingView_loadRoundRadius,
            ROUND_RADIUS_DEFAULT.toFloat()
        )
        mBackgroundColor = typedArray.getColor(
            R.styleable.GameLoadingView_backgroundColor,
            resources.getColor(R.color.black_50)
        )
        typedArray.recycle()
        setLayerType(LAYER_TYPE_SOFTWARE, null)//关闭硬件加速
        paint = Paint(Paint.ANTI_ALIAS_FLAG)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        // 绘制背景
        paint.color = mBackgroundColor
        paint.style = Paint.Style.FILL
        val round = RectF(0f, 0f, width.toFloat(), height.toFloat())
        canvas.drawRoundRect(round, roundRadius, roundRadius, paint)
        // 绘制圆环
        paint.color = Color.RED
        paint.strokeWidth = strokeWidth
        // 采用 clear 的方式
        paint.xfermode = PorterDuffXfermode(PorterDuff.Mode.CLEAR)
        paint.style = Paint.Style.STROKE
        canvas.drawCircle((width / 2).toFloat(), (height / 2).toFloat(), radius, paint)
        // 绘制内圆
        paint.style = Paint.Style.FILL
        val sweepAngle = (360 * progress / mMaxProgress).toFloat()
        val rectF =
            RectF(width / 2 - radius, height / 2 - radius, width / 2 + radius, height / 2 + radius)
        canvas.drawArc(rectF, -90f, sweepAngle, true, paint)
        // 记得设置为 null 不然会没有效果
        paint.xfermode = null
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        val width = measuredWidth
        val height = measuredHeight
        setMeasuredDimension(
            if (width > height) height else width,
            if (width > height) height else width
        )
    }

    fun setMaxProgress(maxProgress: Int) {
        mMaxProgress = maxProgress
    }

    /**
     * @param backgroundColor 设置背景颜色
     */
    override fun setBackgroundColor(@ColorInt backgroundColor: Int) {
        mBackgroundColor = backgroundColor
    }

    companion object {

        private val TAG = "CustomView"
        /**
         * 默认的环的宽度
         */
        private val STROKE_WIDTH_DEFAULT = 5f
        /**
         * 默认的半径
         */
        private val RADIUS_DEFAULT = 50f
        /**
         * 默认最大的进度
         */
        private val MAX_PROGRESS_DEFAULT = 100
        /**
         * 圆角的半径默认值
         */
        private val ROUND_RADIUS_DEFAULT = 12
    }
}

