package com.socialplay.gpark.ui.im.activities

import android.view.LayoutInflater
import android.view.ViewGroup
import com.bumptech.glide.Glide
import com.socialplay.gpark.data.model.SysHeaderInfo
import com.socialplay.gpark.databinding.AdapterGameBottomTabBinding
import com.socialplay.gpark.ui.base.adapter.BaseAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder
import com.socialplay.gpark.util.extension.visible

class GameBottomTabAdapter(list : ArrayList<SysHeaderInfo.TabItemEntity>): BaseAdapter<SysHeaderInfo.TabItemEntity, AdapterGameBottomTabBinding>(list) {

    override fun createViewBinding(
        layoutInflater: LayoutInflater,
        parent: ViewGroup,
        viewType: Int
    ): AdapterGameBottomTabBinding {
        return AdapterGameBottomTabBinding.inflate(LayoutInflater.from(parent.context))
    }

    override fun convert(
        holder: BindingViewHolder<AdapterGameBottomTabBinding>,
        item: SysHeaderInfo.TabItemEntity,
        position: Int
    ) {
        holder.binding.apply {
            tv.text = item.name
            Glide.with(iv).load(item.icon).centerCrop().into(iv)
            vLine.visible(holder.position < itemCount - 1)
        }
    }
}