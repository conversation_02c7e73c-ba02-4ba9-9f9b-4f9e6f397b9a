package com.socialplay.gpark.ui.splash

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.airbnb.mvrx.fragmentViewModel
import com.socialplay.gpark.EnvConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.FragmentSplashBinding
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.login.LoginSpecialWrapper
import com.socialplay.gpark.util.extension.visible
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * Created by shuai.wang
 * Date: 2025/2/18
 * Desc:
 */
class SplashFragment : BaseFragment<FragmentSplashBinding>(R.layout.fragment_splash) {

    override var navColorRes = R.color.color_040412

    private val vm: SplashViewModel by fragmentViewModel()

    private var job: Job? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (!EnvConfig.isParty() || !vm.oldState.firstTime) {
            navMain()
        }
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentSplashBinding? {
        return FragmentSplashBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Timber.d("check_report splash onViewCreated")
        initView()
        initData()
    }

    override fun onStart() {
        super.onStart()
        if (EnvConfig.isParty()) {
            if (job == null) {
                job = lifecycleScope.launch {
                    delay(500)
                    navMain()
                    job = null
                }
            }
        } else {
            navMain()
        }
    }

    private fun initView() {
        if (EnvConfig.isParty()) {
            binding.tvTitle.visible(true)
            binding.tvDesc.visible(true)
        }
        if (EnvConfig.isParty()) {
            binding.ivAgeRestriction.visible(true)
            binding.ivAgeRestriction.setImageResource(LoginSpecialWrapper.getAgeRestrictionIconRes())
        } else {
            binding.ivAgeRestriction.visible(false)
        }
    }

    private fun initData() {
    }

    private fun navMain() {
        kotlin.runCatching {
            if (isAdded && !isDetached) {
                val ctl = findNavController()
                if (ctl.currentBackStackEntry?.destination?.id == R.id.splash && ctl.previousBackStackEntry?.destination?.id != R.id.main) {
                    MetaRouter.Main.navigate(this@SplashFragment)
                    vm.record()
                }
            }
        }.getOrElse {
            it.printStackTrace()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
    }

    override fun invalidate() {

    }

    override fun getPageName(): String = PageNameConstants.FRAGMENT_SPLASH
}