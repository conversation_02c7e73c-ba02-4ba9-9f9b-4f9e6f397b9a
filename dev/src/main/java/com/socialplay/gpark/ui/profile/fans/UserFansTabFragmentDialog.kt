package com.socialplay.gpark.ui.profile.fans

import android.os.Parcelable
import android.widget.TextView
import androidx.core.view.isInvisible
import com.airbnb.mvrx.args
import com.airbnb.mvrx.asMavericksArgs
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.withState
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.CommonTabItem
import com.socialplay.gpark.data.model.profile.RelationCountResult
import com.socialplay.gpark.databinding.FragmentUserFansTabBinding
import com.socialplay.gpark.databinding.TabIndicatorProfileBinding
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.core.BaseBottomSheetDialogFragment
import com.socialplay.gpark.ui.view.viewpager.CommonTabStateAdapter
import com.socialplay.gpark.ui.view.viewpager.adapterAllowStateLoss
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.parcelize.Parcelize
import timber.log.Timber

/**
 * Created by bo.li
 * Date: 2023/9/20
 * Desc:
 */
@Parcelize
data class UserFansTabFragmentDialogArgs(
    val isMe: Boolean,
    val uuid: String,
    val followCount: Long,
    val fansCount: Long,
    val friendCount: Long? = 0,
    val type: Int
) : Parcelable

class UserFansTabFragmentDialog : BaseBottomSheetDialogFragment() {

    override val binding by viewBinding(FragmentUserFansTabBinding::inflate)

    private var tabLayoutMediator: TabLayoutMediator? = null
    private val viewModel: UserFansTabViewModel by fragmentViewModel()
    private val args: UserFansTabFragmentDialogArgs by args()

    private val tabCallback = object : TabLayout.OnTabSelectedListener {
        override fun onTabSelected(tab: TabLayout.Tab) {
            setTabSelectUI(tab, true)
        }

        override fun onTabUnselected(tab: TabLayout.Tab) {
            setTabSelectUI(tab, false)
        }

        override fun onTabReselected(tab: TabLayout.Tab) {}
    }

    companion object {
        const val TYPE_LOCATION_FRIEND = 0
        const val TYPE_LOCATION_FANS = 1
        const val TYPE_LOCATION_FOLLOW= 2
        fun newInstance(
            isMe: Boolean,
            uuid: String,
            followCount: Long,
            fansCount: Long,
            friendCount:Long?=0,
            type:Int
        ): UserFansTabFragmentDialog {
            return UserFansTabFragmentDialog().apply {
                arguments = UserFansTabFragmentDialogArgs(
                    isMe,
                    uuid,
                    followCount,
                    fansCount,
                    friendCount,
                    type
                ).asMavericksArgs()
            }
        }
    }

    private fun setTabSelectUI(tab: TabLayout.Tab, select: Boolean) {
        tab.customView?.findViewById<TextView>(R.id.tv_selected)?.isInvisible = !select
        tab.customView?.findViewById<TextView>(R.id.tv_normal)?.isInvisible = select
    }

    override fun init() {
        skipCollapsed()
        withState(viewModel) {
            Timber.d("checkcheck_vp, withState")
            initTab(it.tabItems, it.count)
            binding.vpUserFans.setCurrentItem(getPost(it.type), false)
            viewModel.cancelJump2Fans()
        }
        viewModel.onEach(
            UserFansTabModelState::tabItems,
            UserFansTabModelState::count,
        ) { tabItems, count ->
            val titles = getTitle(tabItems, count)
            titles.forEachIndexed { index, title ->
                binding.tabUserFans.getTabAt(index)?.apply {
                    customView?.findViewById<TextView>(R.id.tv_normal)?.text = title
                    customView?.findViewById<TextView>(R.id.tv_selected)?.text = title
                }
            }
        }
    }

    /**
     * 当前选中位置
     */
    private fun getPost(type: Int): Int {
        return if (PandoraToggle.isIMEntrance) {
            type
        } else {
            type - 1
        }
    }

    private fun initTab(tabs: List<CommonTabItem>, count: RelationCountResult) {
        binding.tabUserFans.addOnTabSelectedListener(tabCallback)
        val titles = getTitle(tabs, count)
        binding.vpUserFans.adapterAllowStateLoss = CommonTabStateAdapter(
            tabs.map { it.fragmentInvoke },
            childFragmentManager,
            viewLifecycleOwner.lifecycle
        )
        tabLayoutMediator = TabLayoutMediator(
            binding.tabUserFans,
            binding.vpUserFans
        ) { tab: TabLayout.Tab, position: Int ->
            val tabBinding = TabIndicatorProfileBinding.inflate(layoutInflater)
            tabBinding.tvNormal.text = titles[position]
            tabBinding.tvSelected.text = titles[position]

            tab.customView = tabBinding.root
        }
        tabLayoutMediator?.attach()
    }

    private fun getTitle(tabItems: List<CommonTabItem>, count: RelationCountResult): List<String> {
        val titles = tabItems.map {
            when (it.type) {
                UserFansItemFragment.TYPE_FANS, UserFansItemFragment.TYPE_FOLLOWING -> {
                    getString(
                        it.title,
                        UnitUtil.formatKMCount(if (it.type == UserFansItemFragment.TYPE_FANS) count.reverseCount else count.forwardCount)
                    )
                }

                UserFansItemFragment.TYPE_FRIEND                                    -> {
                    UnitUtil.formatKMCount(count.friendCount) + " " + getString(it.title)
                }

                else                                                                -> {
                    ""
                }
            }
        }
        return  titles
    }


    override fun invalidate() {

    }

    override fun onDestroyView() {
        binding.tabUserFans.clearOnTabSelectedListeners()
        binding.vpUserFans.adapterAllowStateLoss = null
        tabLayoutMediator?.detach()
        tabLayoutMediator = null
        super.onDestroyView()
    }

    override fun getStyle() = R.style.BottomDialogStyleOrigin_NoFullScreen

    override fun needCountTime(): Boolean = true

    override var heightPercent: Float = 1F
    override var heightPercentOffset = 110.dp

    override fun getPageName(): String = PageNameConstants.FRAGMENT_USER_FANS_TAB

}