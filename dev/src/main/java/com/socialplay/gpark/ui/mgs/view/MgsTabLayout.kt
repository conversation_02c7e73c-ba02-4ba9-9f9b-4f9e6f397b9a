package com.socialplay.gpark.ui.mgs.view

import android.content.Context
import android.graphics.Typeface
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.res.ResourcesCompat
import androidx.core.view.isVisible
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.mgs.MgsTabEnum
import com.socialplay.gpark.databinding.ViewMgsExpandHorizontalTabBinding
import com.socialplay.gpark.databinding.ViewMgsExpandVerticalTabBinding
import com.socialplay.gpark.ui.mgs.listener.OnMgsTabSelectListener
import com.socialplay.gpark.util.extension.visible

class MgsTabLayout : ConstraintLayout {

    private lateinit var expandHorBinding: ViewMgsExpandHorizontalTabBinding
    private lateinit var expandVerBinding: ViewMgsExpandVerticalTabBinding
    private var listener: OnMgsTabSelectListener? = null

    private var tabType: Int = TYPE_EXPAND_HORIZONTAL

    companion object {
        const val TYPE_EXPAND_HORIZONTAL = 1
        const val TYPE_EXPAND_VERTICAL = 2
    }

    constructor(context: Context) : super(context) {
        init(context, null)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init(context, attrs)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        init(context, attrs)
    }


    private fun init(context: Context, attrs: AttributeSet?) {
        if (attrs == null) {
            return
        }
        kotlin.runCatching {
            val mTypedArray = context.obtainStyledAttributes(attrs, R.styleable.MgsTabLayout)
            tabType = mTypedArray.getInt(R.styleable.MgsTabLayout_tabType, TYPE_EXPAND_HORIZONTAL)
            mTypedArray.recycle()
        }
        if (tabType == TYPE_EXPAND_HORIZONTAL) {
            expandHorBinding = ViewMgsExpandHorizontalTabBinding.inflate(LayoutInflater.from(context), this)
        } else if (tabType == TYPE_EXPAND_VERTICAL) {
            expandVerBinding = ViewMgsExpandVerticalTabBinding.inflate(LayoutInflater.from(context), this)
        }
    }

    fun addTabSelectListener(onTabSelectListener: OnMgsTabSelectListener) {
        listener = onTabSelectListener
        when (tabType) {
            TYPE_EXPAND_HORIZONTAL -> {
                expandHorBinding.mgsExpandTabRoom.setOnClickListener {
                    onSelectExpandTab(MgsTabEnum.ROOM_PLAYER_TAB)
                }
                expandHorBinding.mgsExpandTabFriend.setOnClickListener {
                    onSelectExpandTab(MgsTabEnum.MY_FRIEND_TAB)
                }
            }
            TYPE_EXPAND_VERTICAL   -> {
                expandVerBinding.mgsExpandTabRoom.setOnClickListener {
                    onSelectExpandTab(MgsTabEnum.ROOM_PLAYER_TAB)
                }
                expandVerBinding.mgsExpandTabFriend.setOnClickListener {
                    onSelectExpandTab(MgsTabEnum.MY_FRIEND_TAB)
                }
            }
        }
    }

    private fun onSelectExpandTab(type: MgsTabEnum) {
        listener?.onSelect(type)
        changeSelectTab(getTvRoom(), getIndicatorRoom(), getIvRoom(), type == MgsTabEnum.ROOM_PLAYER_TAB)
        changeSelectTab(getTvFriend(), getIndicatorFriend(), getIvFriend(), type == MgsTabEnum.MY_FRIEND_TAB)
    }

    private fun changeSelectTab(tv: TextView, indicator: View, iv: ImageView, select: Boolean) {
        tv.isSelected = select
        tv.typeface = ResourcesCompat.getFont(context, if (select) R.font.poppins_semi_bold_600 else R.font.poppins_regular_400)
        iv.isSelected = select
        if(select){
            indicator.visibility = (View.VISIBLE)
        }else{
            indicator.visibility = (View.INVISIBLE)
        }

    }


    private fun getIvRoom(): ImageView {
        return if (tabType == TYPE_EXPAND_HORIZONTAL) expandHorBinding.ivMgsExpandTabRoom else expandVerBinding.ivMgsExpandTabRoom
    }

    private fun getTvRoom(): TextView {
        return if (tabType == TYPE_EXPAND_HORIZONTAL) expandHorBinding.tvMgsExpandTabRoom else expandVerBinding.tvMgsExpandTabRoom
    }

    private fun getIndicatorRoom(): View {
        return if (tabType == TYPE_EXPAND_HORIZONTAL) expandHorBinding.mgsExpandTabRoomIndicator else expandVerBinding.mgsExpandTabRoomIndicator
    }

    private fun getIvFriend(): ImageView {
        return if (tabType == TYPE_EXPAND_HORIZONTAL) expandHorBinding.ivMgsExpandTabFriend else expandVerBinding.ivMgsExpandTabFriend
    }

    private fun getTvFriend(): TextView {
        return if (tabType == TYPE_EXPAND_HORIZONTAL) expandHorBinding.tvMgsExpandTabFriend else expandVerBinding.tvMgsExpandTabFriend
    }

    private fun getIndicatorFriend(): View {
        return if (tabType == TYPE_EXPAND_HORIZONTAL) expandHorBinding.mgsExpandTabFriendIndicator else expandVerBinding.mgsExpandTabFriendIndicator
    }

    fun selectTab(tab: MgsTabEnum) {
        onSelectExpandTab(tab)
    }

}