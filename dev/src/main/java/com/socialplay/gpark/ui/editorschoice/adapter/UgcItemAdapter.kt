package com.socialplay.gpark.ui.editorschoice.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.updateLayoutParams
import androidx.lifecycle.Lifecycle
import androidx.recyclerview.widget.DiffUtil
import com.bumptech.glide.RequestManager
import com.chad.library.adapter.base.module.LoadMoreModule
import com.socialplay.gpark.data.model.choice.ChoiceGameInfo
import com.socialplay.gpark.databinding.AdapterChoiceUgcCreateFullBinding
import com.socialplay.gpark.ui.base.adapter.BaseDifferAdapter
import com.socialplay.gpark.ui.base.adapter.BaseVBViewHolder
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.dp

class UgcItemAdapter(
    private val glide: RequestManager,
    gameList: MutableList<ChoiceGameInfo>? = null,
) : BaseDifferAdapter<ChoiceGameInfo, AdapterChoiceUgcCreateFullBinding>(DIFF_CALLBACK, gameList), LoadMoreModule {

    companion object {
        private val DIFF_CALLBACK = object : DiffUtil.ItemCallback<ChoiceGameInfo>() {
            override fun areItemsTheSame(oldItem: ChoiceGameInfo, newItem: ChoiceGameInfo): Boolean {
                return oldItem.code == newItem.code
            }

            override fun areContentsTheSame(oldItem: ChoiceGameInfo, newItem: ChoiceGameInfo): Boolean {
                return oldItem == newItem
            }
        }
    }

    override fun viewBinding(parent: ViewGroup, viewType: Int): AdapterChoiceUgcCreateFullBinding {
        return AdapterChoiceUgcCreateFullBinding.inflate(LayoutInflater.from(parent.context), parent, false).apply {
            root.updateLayoutParams {
                width = if (ScreenUtil.isPad(context)) {
                    164.dp
                } else {
                    ((ScreenUtil.getScreenWidth(context) - 12.dp - 10.dp - 10.dp) / 2.1).toInt()
                }
            }
        }
    }

    override fun convert(holder: BaseVBViewHolder<AdapterChoiceUgcCreateFullBinding>, item: ChoiceGameInfo) {
        holder.binding.apply {
            glide.load(item.avatar).circleCrop().into(ivAuthorAvatar)
            tvAuthorName.text = item.nickname
            tvName.text = item.displayName
            tvHeat.setLikeText(UnitUtil.formatPlayerCount(item.playingCount?.toLong() ?: 0))
            glide.load(item.iconUrl).into(ivIcon)
        }
    }
}