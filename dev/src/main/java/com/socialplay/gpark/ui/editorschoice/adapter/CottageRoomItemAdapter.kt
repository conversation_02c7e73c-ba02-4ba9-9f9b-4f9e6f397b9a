package com.socialplay.gpark.ui.editorschoice.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.room.House
import com.socialplay.gpark.databinding.ItemHomeRoomBinding
import com.socialplay.gpark.ui.base.adapter.BaseAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setSize
import com.socialplay.gpark.util.extension.setWidth

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2023/10/09
 *     desc   :
 *
 */
class CottageRoomItemAdapter (private val glide: RequestManager, private val matchParent: Boolean = false, private val fitItemWidth:Int = 164.dp, list:ArrayList<House> = arrayListOf()) :
    BaseAdapter<House,ItemHomeRoomBinding>(list){
    override fun createViewBinding(
        layoutInflater: LayoutInflater,
        parent: ViewGroup,
        viewType: Int
    ): ItemHomeRoomBinding {

        return ItemHomeRoomBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            .apply {
                if (matchParent) {
                    root.setSize(
                        RecyclerView.LayoutParams.MATCH_PARENT,
                        RecyclerView.LayoutParams.WRAP_CONTENT
                    )
                    clContent.setWidth(ConstraintLayout.LayoutParams.MATCH_PARENT)
                } else {
                    clContent.setWidth(fitItemWidth)
                }
            }
    }

    override fun convert(
        holder: BindingViewHolder<ItemHomeRoomBinding>,
        item: House,
        position: Int
    ) {
        glide.load(item.image).error(R.drawable.placeholder).into(holder.binding.imgRoomCover)
        holder.binding.tvUserName.text = item.ownerNickname
        glide.load(item.ownerAvatar).error(R.drawable.icon_default_avatar).into(holder.binding.imgUserAvatar)
        holder.binding.tvRoomName.text = item.description
        holder.binding.tvRoomNumber.text = (item.number + "/" + item.limitNumber)
    }

}