package com.socialplay.gpark.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.socialplay.gpark.data.model.asset.AssetItem
import com.socialplay.gpark.data.model.asset.AssetType
import com.socialplay.gpark.ui.core.views.LoadMoreState
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch

/**
 * 资源列表ViewModel状态
 */
data class AssetListViewModelState(
    val initialList: List<AssetItem> = emptyList(), // 首页传递的初始数据
    val pagedList: List<AssetItem> = emptyList(),   // 分页接口获取的数据
    val refresh: Async<List<AssetItem>> = Uninitialized,
    val loadMore: Async<LoadMoreState> = Uninitialized,
    val offset: String? = null, // 分页偏移量
    val isEnd: Boolean = false,
    val isRefreshing: Boolean = false
) : MavericksState {
    
    // 合并后的完整列表
    val combinedList: List<AssetItem> = initialList + pagedList
}

/**
 * 资源列表ViewModel
 */
class AssetListViewModel : ViewModel() {

    private val _state = MutableStateFlow(AssetListViewModelState())
    val state: StateFlow<AssetListViewModelState> = _state

    private val _isRefreshing = MutableStateFlow(false)
    val isRefreshing: StateFlow<Boolean> = _isRefreshing

    companion object {
        private const val PAGE_SIZE = 20
    }

    init {
        // 初始化时加载分页数据
        loadMoreData(isRefresh = true)
    }

    /**
     * 设置初始数据（从首页传递过来的数据）
     */
    fun setInitialData(initialList: List<AssetItem>) {
        _state.value = _state.value.copy(initialList = initialList)
    }

    /**
     * 刷新数据
     */
    fun refresh() {
        loadMoreData(isRefresh = true)
    }

    /**
     * 加载更多数据
     */
    fun loadMore() {
        val currentState = _state.value
        if (currentState.loadMore is Loading || currentState.isEnd) return
        loadMoreData(isRefresh = false)
    }

    /**
     * 加载分页数据
     */
    private fun loadMoreData(isRefresh: Boolean) {
        viewModelScope.launch {
            val currentState = _state.value
            
            if (isRefresh) {
                _isRefreshing.value = true
                _state.value = currentState.copy(
                    pagedList = emptyList(),
                    offset = null,
                    isEnd = false,
                    refresh = Loading(),
                    loadMore = Uninitialized
                )
            } else {
                _state.value = currentState.copy(loadMore = Loading())
            }

            // 模拟网络延迟
            delay(1000)

            try {
                // 模拟分页数据
                val newAssets = generateFakeData(
                    offset = if (isRefresh) null else currentState.offset,
                    pageSize = PAGE_SIZE
                )

                val updatedState = if (isRefresh) {
                    // 刷新时替换分页数据
                    currentState.copy(
                        pagedList = newAssets.list,
                        offset = newAssets.nextOffset,
                        isEnd = newAssets.isEnd,
                        refresh = Success(newAssets.list),
                        loadMore = Success(LoadMoreState(isEnd = newAssets.isEnd)),
                        isRefreshing = false
                    )
                } else {
                    // 加载更多时追加数据
                    val combinedPagedList = currentState.pagedList + newAssets.list
                    currentState.copy(
                        pagedList = combinedPagedList,
                        offset = newAssets.nextOffset,
                        isEnd = newAssets.isEnd,
                        loadMore = Success(LoadMoreState(isEnd = newAssets.isEnd))
                    )
                }

                _state.value = updatedState
                _isRefreshing.value = false

            } catch (e: Exception) {
                val errorState = if (isRefresh) {
                    currentState.copy(
                        refresh = Fail(e),
                        isRefreshing = false
                    )
                } else {
                    currentState.copy(
                        loadMore = Fail(e)
                    )
                }
                _state.value = errorState
                _isRefreshing.value = false
            }
        }
    }

    /**
     * 生成模拟分页数据
     */
    private fun generateFakeData(offset: String?, pageSize: Int): PagedDataResult {
        // 模拟数据总数
        val totalCount = 100
        val currentOffset = offset?.toIntOrNull() ?: 0
        
        // 如果超出范围，返回空列表
        if (currentOffset >= totalCount) {
            return PagedDataResult(emptyList(), null, true)
        }

        val assetList = mutableListOf<AssetItem>()
        val endIndex = minOf(currentOffset + pageSize, totalCount)
        
        for (i in currentOffset until endIndex) {
            val isClothes = i % 3 != 0 // 2/3 的概率是服装
            val type = if (isClothes) AssetType.CLOTHES else AssetType.MODULE
            
            assetList.add(
                AssetItem(
                    id = "asset_$i",
                    name = if (isClothes) "服装${i + 1}" else "模组${i + 1}",
                    imageUrl = "https://picsum.photos/200/200?random=$i",
                    type = type,
                    tags = listOf("热门", "推荐"),
                    likeCount = (100..1000).random().toLong(),
                    isLiked = false,
                    price = if (type == AssetType.MODULE) (100..500).random().toLong() else null,
                    creatorAvatar = "https://picsum.photos/50/50?random=${i + 100}",
                    creatorName = "创作者${i + 1}"
                )
            )
        }

        val nextOffset = if (endIndex >= totalCount) null else endIndex.toString()
        val isEnd = endIndex >= totalCount

        return PagedDataResult(assetList, nextOffset, isEnd)
    }

    /**
     * 分页数据结果
     */
    private data class PagedDataResult(
        val list: List<AssetItem>,
        val nextOffset: String?,
        val isEnd: Boolean
    )
} 