package com.socialplay.gpark.ui.aibot.tag

import android.content.ComponentCallbacks
import androidx.lifecycle.MutableLiveData
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.data.model.aibot.BotLabelInfo
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.util.GsonUtil
import kotlinx.coroutines.launch
import org.koin.android.ext.android.get

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/07/01
 *     desc   :
 *
 */
data class AiBotTagState(
    val tagIdList: List<Int> = emptyList(),
    val isBlack: Boolean = false,
    val maxCount: Int = 1,
) : MavericksState {
    constructor(args: AiBotTagDialogArgs) : this(
        tagIdList = args.tagIdList ?: emptyList(),
        isBlack = args.isBlack,
        maxCount = args.maxCount
    )
}


class AiBotTagViewModel(
    initialState: AiBotTagState,
    val metaRepository: IMetaRepository,
    val metaKV: MetaKV
) : BaseViewModel<AiBotTagState>(initialState) {
    companion object : KoinViewModelFactory<AiBotTagViewModel, AiBotTagState>() {
        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: AiBotTagState,
        ): AiBotTagViewModel {
            return AiBotTagViewModel(state, get(), get())
        }
    }

    private val _tagList = MutableLiveData<List<BotLabelInfo>>()
    val tagList = _tagList

    fun getAllTag() = viewModelScope.launch {
        val value = metaKV.tTaiKV.aiBotList
        if (!value.isNullOrEmpty()) {
            convertTagList(value)
            return@launch
        }
        metaRepository.getTTaiConfigById(TTaiKV.ID_KEY_AI_BOT_LABEL).collect() {
            convertTagList(value)
        }
    }

    private fun convertTagList(value: String) = withState { oldState ->
        val list = (GsonUtil.gsonSafeParseCollection<List<BotLabelInfo>>(value) ?: emptyList()).filterNotNull()
        if (!list.isNullOrEmpty()) {
            val selectIdList = oldState.tagIdList
            if (selectIdList.isNullOrEmpty()) {
                _tagList.postValue(list)
                return@withState
            }
            val newList = list.map {
                it.selected = selectIdList.contains(it.tagId)
                it
            }
            _tagList.postValue(newList)
        }
    }


    fun updateLabelSelect(tagId: Int) = withState { oldState ->
        val newTagIdList = oldState.tagIdList.toMutableList()
        val result = if (newTagIdList.contains(tagId)) {
            // 已经存在的，需要移除
            newTagIdList.remove(tagId)
            listOf(*newTagIdList.toTypedArray())
        } else {
            // 不存在的
            if (oldState.tagIdList.size < oldState.maxCount) {
                // 选中数量小于最大选中数量，新增
                newTagIdList.add(tagId)
                listOf(*newTagIdList.toTypedArray())
            } else {
                oldState.tagIdList
            }
        }
        setState { copy(tagIdList = result) }
    }

    fun getSelectList(selectIdList: List<Int>): ArrayList<BotLabelInfo> {
        val tagList = _tagList.value?.filterNotNull()
        val selectList = ArrayList<BotLabelInfo>()
        selectIdList.forEach { id ->
            val list = tagList?.filter { (it != null && it.tagId == id) }
            if (list != null) {
                selectList.addAll(list)
            }
        }
        return selectList
    }

}