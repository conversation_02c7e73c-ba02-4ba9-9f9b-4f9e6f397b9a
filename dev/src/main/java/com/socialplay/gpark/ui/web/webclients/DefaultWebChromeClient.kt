package com.socialplay.gpark.ui.web.webclients

import android.net.Uri
import android.webkit.ValueCallback
import android.webkit.WebChromeClient
import android.webkit.WebView
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.Fragment
import timber.log.Timber

/**
 *
 * <AUTHOR>
 * @date 2021/05/19
 */
class DefaultWebChromeClient(fragment: Fragment) : WebChromeClient() {

     private var uploadMessageAboveL: ValueCallback<Array<Uri>>? = null

    //获取图片
    val getContent = fragment.registerForActivityResult(ActivityResultContracts.GetContent()) {uri: Uri? ->
        Timber.i("registerForActivityResult --- ${uri.toString()}")
        uri?.let {
            uploadMessageAboveL?.onReceiveValue(arrayOf(it))
            uploadMessageAboveL = null
        }
    }

    override fun onReceivedTitle(view: WebView?, title: String?) {
        super.onReceivedTitle(view, title)
    }

    override fun onProgressChanged(view: WebView?, newProgress: Int) {
        super.onProgressChanged(view, newProgress)
    }

    override fun onShowFileChooser(
        webView: WebView?,
        filePathCallback: ValueCallback<Array<Uri>>?,
        fileChooserParams: FileChooserParams?
    ): Boolean {
        uploadMessageAboveL = filePathCallback
        getContent.launch("image/*")
        return true
    }
}