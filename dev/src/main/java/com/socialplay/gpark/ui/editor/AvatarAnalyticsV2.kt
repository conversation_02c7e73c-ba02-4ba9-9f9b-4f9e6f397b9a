package com.socialplay.gpark.ui.editor

import android.os.SystemClock
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import timber.log.Timber
import kotlin.to

/**
 *
 * 新加的角色加载时长统计埋点
 * https://meta.feishu.cn/wiki/PabhwzE61iiGz3kwaAZcgE3Knqe
 */
object AvatarAnalyticsV2 {

    private var startTime = 0L
    private var source: String = ""
    private var isPreload: Boolean = false
    private var isLoadedOnStart: Boolean = false

    private var startTimes: Int = 0
    private var pausedTasks: String? = null
    private var isPausedTasks: Boolean = false

    private var isEndSent: Boolean = false
    private var isFinishSent:Boolean = false
    private var isDownloadEngineNeeded:Boolean = false

    private fun resetIfFlowComplete() {
        if (startTime != 0L && isEndSent && isFinishSent) {
            startTime = 0
            source = ""
            isPreload = false
            isPausedTasks = false
            pausedTasks = ""
            isEndSent = false
            isFinishSent = false
            isDownloadEngineNeeded = false
        }
    }

    /**
     * @param isPreload 本次触发加载是否是否是预加载
     * @param source 触发加载的来源
     * @param isLoaded 游戏是否已经加载好了
     * @param isFinish
     */
    fun start(
        isPreload: Boolean, source: String,
        isLoaded: Boolean,
        isPausedTasks: Boolean,
        pausedTasks: String?,
        isDownloadEngineNeeded: Boolean,
    ) {
        this.startTime = SystemClock.elapsedRealtime()
        this.source = source
        this.isLoadedOnStart = isLoaded
        this.isPreload = isPreload

        this.pausedTasks = pausedTasks
        this.isPausedTasks = isPausedTasks
        this.isEndSent = false
        this.isDownloadEngineNeeded = isDownloadEngineNeeded

        Analytics.track(
            EventConstants.EVENT_TOUCH_AVATAR_TAB,
            mapOf(
                "preload" to (if (isPreload) "1" else "2"),
                "source" to source,
                "cache_hit" to (if (isLoaded) "1" else "2"),
                "loading_count" to (++startTimes),
                "ispausehometask" to if (isPausedTasks) "1" else "2",
                "pausetasklist" to (pausedTasks ?: ""),
                "engine_download" to (if (isDownloadEngineNeeded) "1" else "2"),
            )
        )
    }

    /**
     *
     * @param reason 结束原因
     * @param errorCode 错误码
     * @param subErrorCode 子错误码
     * @param isAvatarViewVisible 角色View是否看见
     */
    fun end(
        reason: EndReason,
        errorCode: Int, subErrorCode: String,
        isAvatarViewVisible: Boolean,
    ) {
        if (startTime == 0L || isEndSent) {
            return
        }

        isEndSent = true

        val loadingTime = if (isLoadedOnStart) 0 else SystemClock.elapsedRealtime() - startTime
        Timber.i("end : $loadingTime  reason: $reason")


        val reasonStr = when (reason) {
            EndReason.Success -> "1"
            EndReason.Fail -> "2"
            EndReason.Leave -> "3"
        }


        Analytics.track(
            EventConstants.EVENT_TOUCH_AVATAR_LOADING_END, mapOf(
                "loading_time" to loadingTime,
                "errorcode" to errorCode,
                "sub_errorcode" to subErrorCode,
                "is_foreground" to (if (isAvatarViewVisible) "1" else "0"),
                "preload" to (if (isPreload) "1" else "2"),
                "source" to source,
                "result" to reasonStr,
                "loading_count" to (startTimes),
                "ispausehometask" to if (isPausedTasks) "1" else "2",
                "engine_download" to (if (isDownloadEngineNeeded) "1" else "2"),
            )
        )

        isEndSent = true
    }

    fun finish(
        reason: FinishReason,
        errorCode: Int,
        subErrorCode: String,
        isAvatarViewVisible: Boolean,
        retryCount: Int
    ) {
        if (startTime == 0L || isFinishSent) {
            return
        }

        isFinishSent = true

        val loadingTime =  SystemClock.elapsedRealtime() - startTime
        Timber.i("finish : $loadingTime  reason: $reason")

        val reasonStr = when (reason) {
            FinishReason.Success -> "1"
            FinishReason.Fail -> "2"
            FinishReason.Leave -> "3"
        }

        Analytics.track(
            EventConstants.EVENT_TOUCH_AVATAR_LOADING_FINISH, mapOf(
                "loading_time" to loadingTime,
                "errorcode" to errorCode,
                "sub_errorcode" to subErrorCode,
                "is_foreground" to (if (isAvatarViewVisible) "1" else "0"),
                "preload" to (if (isPreload) "1" else "2"),
                "source" to source,
                "result" to reasonStr,
                "loading_count" to (startTimes),
                "count" to (retryCount),
                "ispausehometask" to if (isPausedTasks) "1" else "2",
                "engine_download" to (if (isDownloadEngineNeeded) "1" else "2"),
            )
        )

        resetIfFlowComplete()
    }

    enum class FinishReason{

        /**
         * 角色游戏加载完成
         */
        Success,

        /**
         * 角色游戏加载失败
         */
        Fail,

        /**
         * 离开角色加载页面
         */
        Leave
    }

    enum class EndReason{
        /**
         * 角色游戏加载完成
         */
        Success,

        /**
         * 角色游戏加载失败
         */
        Fail,

        /**
         * 离开角色加载页面
         */
        Leave
    }

}