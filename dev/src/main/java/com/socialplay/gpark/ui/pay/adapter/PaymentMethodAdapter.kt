package com.socialplay.gpark.ui.pay.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.socialplay.gpark.data.model.pay.PaymentMethod
import com.socialplay.gpark.databinding.ItemPaymentMethodBinding
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener

/**
 * 支付方式列表适配器
 */
class PaymentMethodAdapter : RecyclerView.Adapter<PaymentMethodAdapter.PaymentMethodViewHolder>() {

    private var paymentMethods: List<PaymentMethod> = emptyList()
    private var selectedPosition = 0
    private var onItemClickListener: ((ViewGroup, Int) -> Unit)? = null

    fun setData(paymentMethods: List<PaymentMethod>) {
        this.paymentMethods = paymentMethods
        notifyDataSetChanged()
    }

    fun setSelectedPosition(position: Int) {
        val oldPosition = selectedPosition
        selectedPosition = position
        notifyItemChanged(oldPosition)
        notifyItemChanged(selectedPosition)
    }

    fun setOnItemClickListener(listener: ((ViewGroup, Int) -> Unit)?) {
        this.onItemClickListener = listener
    }

    fun getItem(position: Int): PaymentMethod? {
        return if (position in 0 until paymentMethods.size) {
            paymentMethods[position]
        } else null
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PaymentMethodViewHolder {
        val binding = ItemPaymentMethodBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return PaymentMethodViewHolder(binding)
    }

    override fun onBindViewHolder(holder: PaymentMethodViewHolder, position: Int) {
        holder.bind(paymentMethods[position], position == selectedPosition)
    }

    override fun getItemCount(): Int = paymentMethods.size

    inner class PaymentMethodViewHolder(
        private val binding: ItemPaymentMethodBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        init {
            binding.root.setOnAntiViolenceClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    onItemClickListener?.invoke(binding.root, position)
                }
            }
        }

        fun bind(paymentMethod: PaymentMethod, isSelected: Boolean) {
            // 设置支付方式图标
            binding.ivPaymentIcon.setImageResource(paymentMethod.iconRes)
            
            // 设置支付方式名称
            binding.tvPaymentName.text = paymentMethod.name
            
            // 设置选中状态
            binding.ivSelected.visibility = if (isSelected) {
                android.view.View.VISIBLE
            } else {
                android.view.View.GONE
            }
            
            // 设置是否可用
            binding.root.isEnabled = paymentMethod.isAvailable
            binding.tvPaymentName.alpha = if (paymentMethod.isAvailable) 1.0f else 0.5f
            binding.ivPaymentIcon.alpha = if (paymentMethod.isAvailable) 1.0f else 0.5f
        }
    }
} 