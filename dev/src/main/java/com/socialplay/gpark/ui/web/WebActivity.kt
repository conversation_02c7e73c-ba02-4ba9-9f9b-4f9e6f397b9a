package com.socialplay.gpark.ui.web

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Parcelable
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import androidx.activity.OnBackPressedCallback
import androidx.annotation.Keep
import androidx.core.os.bundleOf
import androidx.core.view.LayoutInflaterCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.commitNow
import androidx.navigation.NavBackStackEntry
import androidx.navigation.NavController
import androidx.navigation.NavHostController
import androidx.navigation.Navigation
import androidx.navigation.Navigator
import androidx.navigation.findNavController
import androidx.navigation.fragment.FragmentNavigator
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.fragment.findNavController
import com.socialplay.gpark.contract.web.extra
import com.socialplay.gpark.contract.web.gameId
import com.meta.lib.mwbiz.MWBizBridge
import com.meta.web.WebRouter
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.ActivityWebBinding
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.apm.page.view.PageMonitorLayoutInflaterFactory2
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.base.BaseActivity
import com.socialplay.gpark.ui.view.MetaTextView
import com.socialplay.gpark.util.StatusBarUtil
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.parcelize.Parcelize

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2021/07/22
 *     desc   :
 *
 */
class WebActivity : BaseActivity() {
    companion object{
        internal const val KEY_NAV_TARGET = "nav_target"
    }

    override val binding by viewBinding(ActivityWebBinding::inflate)

    private var navTarget: NavTarget? = null


    override fun onCreate(savedInstanceState: Bundle?) {
        if (layoutInflater.factory == null && layoutInflater.factory2 == null) {
            LayoutInflaterCompat.setFactory2(layoutInflater, object : LayoutInflater.Factory2 {
                override fun onCreateView(parent: View?, name: String, context: Context, attrs: AttributeSet): View? {
                    val mName = if (name == "TextView" || name == "androidx.appcompat.widget.AppCompatTextView") {
                        MetaTextView::class.java.name
                    } else {
                        name
                    }
                    return PageMonitorLayoutInflaterFactory2.onCreateView(name, context, attrs) ?: delegate.createView(parent, mName, context, attrs)
                }

                override fun onCreateView(name: String, context: Context, attrs: AttributeSet): View? {
                    return null
                }
            })
        }
        super.onCreate(savedInstanceState)
        StatusBarUtil.setTransparent(this)

        val args = intent.extras
        if(args == null){
            finish()
            return
        }

        navTarget = NavTarget(
            isBabel = PandoraToggle.isOpenBabelWeb,
            args = args
        )

        initNavHostFragment()
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        val args = intent?.extras
        if(args == null){
            finish()
            return
        }

        val navTarget = NavTarget(isBabel = PandoraToggle.isOpenBabelWeb, args = args).also {
            this.navTarget = it
        }

        findNavController(R.id.nav_host_fragment).let {
            it.navigatorProvider.getNavigator(AutoFinishFragmentNavigator::class.java).navTarget = navTarget
            navTarget.navigateToWeb(it)
        }
    }

    private fun initNavHostFragment() {
        val existHostFragment = supportFragmentManager.findFragmentById(R.id.nav_host_fragment) as? AutoFinishNavHostFragment
        val hostFragment = existHostFragment ?: AutoFinishNavHostFragment()

        hostFragment.arguments = bundleOf(KEY_NAV_TARGET to navTarget,)

        supportFragmentManager.commitNow(true) {
            if (existHostFragment == hostFragment) { //Activity恢复实例的情况
                show(hostFragment)
            } else {
                add(R.id.nav_host_fragment, hostFragment)
            }
            setPrimaryNavigationFragment(hostFragment)
        }
        Navigation.setViewNavController(binding.navHostFragment, hostFragment.navController)

        val callback = object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                val curDestinationId = hostFragment.navController.currentBackStackEntry?.destination?.id
                val preDestinationId = hostFragment.navController.previousBackStackEntry?.destination?.id
                when {
                    curDestinationId == navTarget?.destId && preDestinationId == R.id.fragment_nav_stub -> {
                        finish()
                    }

                    curDestinationId == R.id.fragment_nav_stub && preDestinationId == null -> {
                        finish()
                    }

                    else -> {
                        hostFragment.navController.popBackStack()
                    }
                }
            }
        }
        onBackPressedDispatcher.addCallback(this, callback)
    }

    override fun navigateUpTo(upIntent: Intent?): Boolean {
        return findNavController(R.id.nav_host_fragment).navigateUp()
    }

}


@Navigator.Name("fragment")
private class AutoFinishFragmentNavigator(
    private val context: Context,
    private val fragmentManager: FragmentManager,
    private val containerId: Int,
) : FragmentNavigator(context, fragmentManager, containerId) {

    internal var navTarget: NavTarget? = null

    override fun popBackStack(popUpTo: NavBackStackEntry, savedState: Boolean) {
        if (popUpTo.destination.id == navTarget?.destId) {
            trackGoBackGame()
            (context as? Activity)?.finish()
            return
        }
        super.popBackStack(popUpTo, savedState)
    }

    private fun trackGoBackGame(){
        navTarget?.runCatching { WebActivityArgs.fromBundle(args) }?.getOrNull()?.gameId?.let {
            if (it.isNotEmpty()) {
                MWBizBridge.resumeGame(it)
            }
        }
    }
}

class AutoFinishNavHostFragment : NavHostFragment() {

    override fun onCreate(savedInstanceState: Bundle?) {
        val graphInflater = navController.navInflater
        val navGraph = graphInflater.inflate(R.navigation.root)
        navGraph.setStartDestination(R.id.fragment_nav_stub)
        navController.setGraph(navGraph, arguments)
        super.onCreate(savedInstanceState)
    }

    override fun onCreateNavHostController(navHostController: NavHostController) {
        super.onCreateNavHostController(navHostController)
        val navTarget = arguments?.getParcelable<NavTarget>(WebActivity.KEY_NAV_TARGET) ?: return
        val fragmentNavigator = AutoFinishFragmentNavigator(requireContext(), childFragmentManager, id)
        fragmentNavigator.navTarget = navTarget

        navHostController.navigatorProvider.addNavigator(fragmentNavigator)
    }
}


class WebStubStartFragment : Fragment() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val navTarget = arguments?.getParcelable<NavTarget>(WebActivity.KEY_NAV_TARGET) ?: return
        navTarget.navigateToWeb(findNavController())
    }
}


@Parcelize
@Keep
data class NavTarget(
    val isBabel: Boolean,
    val args: Bundle
):Parcelable{
    val destId:Int get() {
        return if (isBabel) R.id.fragment_web_standalone else R.id.web
    }

    fun navigateToWeb(navController: NavController) {
        if (this.isBabel) {
            val webActivityArgs = WebActivityArgs.fromBundle(this.args)
            WebRouter.showStandaloneFragment(
                navController,
                webActivityArgs.url,
                ResIdBean()
            ) {
                this.title = webActivityArgs.title
                this.showTitle = webActivityArgs.showTitle
                this.showStatusBar = webActivityArgs.showStatusBar
                this.statusBarColor = webActivityArgs.statusBarColor
                this.from = webActivityArgs.from
                this.gameId = webActivityArgs.gameId
                this.extra = webActivityArgs.extra
            }

        } else {
            navController.navigate(R.id.web, this.args)
        }
    }
}
