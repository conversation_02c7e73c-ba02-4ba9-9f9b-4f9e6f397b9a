package com.socialplay.gpark.ui.view.voice;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.view.View;

public class Circle extends View {
    private int   radius;
    private Paint paint;
    private final int   maxRadius = 84;

    private final int defaultWidth  = 600; // px
    private final int defaultHeight = 600; // px

    private int w; // 实际View的宽度
    private int h; // 实际View的高度
    private final int color = 0xFF2BFC96;

    public Circle(Context context) {
        this(context, null);
    }

    public Circle(Context context, AttributeSet attrs) {
        super(context, attrs);
        initPaint();
    }

    private void initPaint() {
        paint = new Paint();
        paint.setStyle(Paint.Style.FILL);
        paint.setAntiAlias(true);
        radius = maxRadius / 2;
        paint.setColor(color);
    }

    public void setRadius(int radius) {
        this.radius = radius;
        invalidate();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        int widthSize = MeasureSpec.getSize(widthMeasureSpec);
        int widthMode = MeasureSpec.getMode(widthMeasureSpec);

        int heightSize = MeasureSpec.getSize(heightMeasureSpec);
        int heightMode = MeasureSpec.getMode(heightMeasureSpec);

        w = (widthMode == MeasureSpec.AT_MOST ? Math.min(defaultWidth, widthSize) : widthSize);
        h = (heightMode == MeasureSpec.AT_MOST ? Math.min(defaultHeight, heightSize) : heightSize);
        setMeasuredDimension(w, h);

    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        int pLeft = getPaddingLeft();
        int pRight = getPaddingRight();
        int pTop = getPaddingTop();
        int pBottom = getPaddingBottom();
        canvas.drawCircle((w - pLeft - pRight) / 2 + pLeft, (h - pTop - pBottom) / 2 + pTop, radius, paint);

    }


    public void setColor(int color) {
        paint.setAlpha(color);
    }
}
