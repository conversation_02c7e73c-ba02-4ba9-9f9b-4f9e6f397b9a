package com.socialplay.gpark.ui.view.refresh

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.animation.Animation
import android.widget.FrameLayout
import com.socialplay.gpark.R

class VideoFeedRefreshHeaderView @JvmOverloads constructor(
    context: Context,
    attr: AttributeSet? = null,
    defStyle: Int = 0,
    defStyleRes: Int = 0
) : FrameLayout(context, attr, defStyle, defStyleRes) {

    private var mListener: Animation.AnimationListener? = null

    init {
        LayoutInflater.from(context).inflate(R.layout.layout_video_feed_refresh_header, this, true)
    }


    fun setAnimationListener(listener: Animation.AnimationListener?) {
        mListener = listener
    }

    override fun onAnimationStart() {
        super.onAnimationStart()
        mListener?.onAnimationStart(animation)
    }

    override fun onAnimationEnd() {
        super.onAnimationEnd()
        mListener?.onAnimationEnd(animation)
    }

}