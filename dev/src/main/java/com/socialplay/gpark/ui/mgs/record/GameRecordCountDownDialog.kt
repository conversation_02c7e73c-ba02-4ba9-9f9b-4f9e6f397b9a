package com.socialplay.gpark.ui.mgs.record

import android.animation.Animator
import android.animation.ValueAnimator
import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.os.Message
import android.view.Gravity
import android.view.LayoutInflater
import android.view.WindowManager
import android.widget.TextView
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.DialogGameCountDownBinding
import com.socialplay.gpark.databinding.DialogMgsPlayerInfoLandBinding
import com.socialplay.gpark.databinding.DialogQuitGameNormalBinding
import com.socialplay.gpark.ui.dialog.GameCreateDialogHelper
import com.socialplay.gpark.ui.mgs.listener.OnMgsQuitGameDialogListener
import com.socialplay.gpark.util.ScreenUtil

/**
 * Created by bo.li
 * Date: 2022/2/18
 * Desc: MGS退出游戏
 */
class GameRecordCountDownDialog(
    private val metaApp: Context,
    private val activity: Activity,
    count: Int, private val finishAction: () -> Unit
) : Dialog(activity, android.R.style.Theme_Dialog) {

    private var mCurrentCount = count + 1
    private lateinit var binding: DialogGameCountDownBinding

    init {
        initView()
    }

    private val mHandler = object : Handler() {
        override fun handleMessage(msg: Message) {
            mCurrentCount--
            if (mCurrentCount > 0) {
                binding.tvCount.text = mCurrentCount.toString()
                showAnimation()
            } else {
                finishAction.invoke()
                dismiss()
            }

        }
    }

    private fun showAnimation() {
        val valueAnimation = ValueAnimator.ofFloat(0.2f, 1.2f, 1f).setDuration(DURATION_ANIMATION)
        valueAnimation.addUpdateListener {
            binding.tvCount.scaleX = it.animatedValue as Float
            binding.tvCount.scaleY = it.animatedValue as Float
        }
        valueAnimation.addListener(object : Animator.AnimatorListener {
            override fun onAnimationStart(animation: Animator) {
            }

            override fun onAnimationEnd(animation: Animator) {
                mHandler.sendEmptyMessageDelayed(1, DURATION_DELAYED)
            }

            override fun onAnimationCancel(animation: Animator) {
            }

            override fun onAnimationRepeat(animation: Animator) {
            }

        })
        valueAnimation.start()
    }

    private fun initDialogParams() {
        setCancelable(false)
        setCanceledOnTouchOutside(false)
        binding = DialogGameCountDownBinding.inflate(LayoutInflater.from(metaApp))
        val gravity: Int = Gravity.CENTER
        GameCreateDialogHelper.customInflated(
            activity,
            metaApp,
            this,
            binding.root,
            dimAmount = 0.1f,
            gravity = gravity,
            width = WindowManager.LayoutParams.MATCH_PARENT,
            height = WindowManager.LayoutParams.MATCH_PARENT
        )
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mHandler.sendEmptyMessageDelayed(1, 200)
    }

    override fun dismiss() {
        super.dismiss()
        mHandler.removeCallbacksAndMessages(null)
    }

    private fun initView() {
        if (window == null) {
            dismiss()
            return
        }
        initDialogParams()
    }

    companion object {
        private const val DURATION_ANIMATION = 500L
        private const val DURATION_DELAYED = 500L
    }
}