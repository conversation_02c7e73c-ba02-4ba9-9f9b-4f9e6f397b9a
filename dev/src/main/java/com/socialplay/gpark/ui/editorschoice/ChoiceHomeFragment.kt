package com.socialplay.gpark.ui.editorschoice

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.core.view.updatePadding
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.meta.lib.mwbiz.MWBiz
import com.meta.lib.mwbiz.MWLifeCallback
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.EditorInteractor
import com.socialplay.gpark.data.interactor.TokenChangedCallback
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.LoadType
import com.socialplay.gpark.data.model.choice.ChoiceCardInfo
import com.socialplay.gpark.data.model.choice.ChoiceCardType
import com.socialplay.gpark.data.model.choice.ChoiceContentType
import com.socialplay.gpark.data.model.choice.ChoiceGameInfo
import com.socialplay.gpark.data.model.choice.IChoiceItem
import com.socialplay.gpark.data.model.feedback.SubmitNewFeedbackRequest
import com.socialplay.gpark.data.model.room.ChatRoomInfo
import com.socialplay.gpark.data.model.room.House
import com.socialplay.gpark.data.model.videofeed.ChoiceHomeVideoItem
import com.socialplay.gpark.databinding.FragmentChoiceHomeBinding
import com.socialplay.gpark.databinding.HeaderHomeFeedbackBinding
import com.socialplay.gpark.databinding.ViewChoiceContinueGameBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.analytics.resid.ResIdUtils
import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.editor.EditorUGCLaunchParams
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.cottage.detail.CottageRoomDetailDialog
import com.socialplay.gpark.ui.editor.BaseEditorFragment
import com.socialplay.gpark.ui.editorschoice.adapter.CottageRoomItemAdapter
import com.socialplay.gpark.ui.editorschoice.adapter.RoomItemAdapter
import com.socialplay.gpark.ui.editorschoice.adapter.SmallItemAdapter
import com.socialplay.gpark.ui.editorschoice.header.ChoiceHomeAdapterHeaderIndex
import com.socialplay.gpark.ui.editorschoice.header.friends.ChoiceHomeHeaderFriends
import com.socialplay.gpark.ui.home.HomeViewModel
import com.socialplay.gpark.ui.home.adapter.GameItemPlayedAdapter
import com.socialplay.gpark.ui.main.MainViewModel
import com.socialplay.gpark.ui.room.HomeRoomAnalytics
import com.socialplay.gpark.ui.room.RoomDetailDialog
import com.socialplay.gpark.ui.suggestion.GameSuggestionViewModel
import com.socialplay.gpark.ui.view.EmptyLoadMoreView
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.UniJumpUtil
import com.socialplay.gpark.util.extension.collectWithLifecycleOwner
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.navigateUp
import com.socialplay.gpark.util.extension.setOnAntiViolenceChildItemClickListener
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setOnAntiViolenceItemClickListener
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.sharedViewModel
import org.koin.androidx.viewmodel.ext.android.viewModel
import timber.log.Timber

/**
 * created by liyanfeng on 2022/8/5 10:38 上午
 * @describe:
 */
class ChoiceHomeFragment : BaseEditorFragment<FragmentChoiceHomeBinding>() {

    companion object {
        const val TAG = "Choice-Fragment"
    }

    private val headContinueGameBinding by lazy { getHeadContinueView() }
    private val headFeedbackBinding by lazy { getHeadFeedbackView() }
    private val editorInteractor: EditorInteractor by inject()
    private fun getHeadContinueView(): ViewChoiceContinueGameBinding {
        return ViewChoiceContinueGameBinding.inflate(LayoutInflater.from(context))
    }

    private fun getHeadFeedbackView(): HeaderHomeFeedbackBinding {
        return HeaderHomeFeedbackBinding.inflate(LayoutInflater.from(context))
    }


    override fun getFragmentName(): String = PageNameConstants.FRAGMENT_NAME_HOME_CHOICE

    private val viewModel by viewModel<HomeViewModel>()
    private val suggestionViewModel by sharedViewModel<GameSuggestionViewModel>()
    private val mainViewModel: MainViewModel by sharedViewModel()
    private val playedAdapter = GameItemPlayedAdapter(::glide)
    private val choiceHomeAdapter: ChoiceHomeCardAdapter by lazy { getChoiceAdapterByGroup() }

    private val accountInteractor: AccountInteractor by inject()
    private val metaKV: MetaKV by inject()

    private var isFirstResume = true

    private val homeHeaderFriends by lazy { ChoiceHomeHeaderFriends(this) }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentChoiceHomeBinding? {
        return FragmentChoiceHomeBinding.inflate(inflater, container, false)
    }

    override fun init() {
        super.init()
        initView()
        initData()
    }

    override fun loadFirstData() {
        binding.lv.showLoading()
        refreshAllData()
    }

    private fun refreshAllData() {
        if (!PandoraToggle.openUgcHomeEntrance) {
            ChoiceCardType.removeSupportCardType(ChoiceCardType.HOME)
        }
        viewModel.refreshData()
        refreshMyGames()
    }

    private fun initView() {
        if (PandoraToggle.isHomeRecommend) {
            binding.root.updatePadding(bottom = 0)
        }
        binding.tbl.visible(PandoraToggle.isHomeRecommend)
        binding.tbl.setOnBackClickedListener {
            navigateUp()
        }
        binding.clTitleBar.visible(!PandoraToggle.isHomeRecommend)
        binding.vLine.isInvisible = PandoraToggle.openHomeFeedbackHead
        initLinearRecyclerView()
        initRecentPlayedView()
        initRefreshView()
        //首页好友列表
        homeHeaderFriends.initialize()
        binding.ivSearch.setOnAntiViolenceClickListener {
            MetaRouter.Search.navigate(this)
        }
        binding.ivSearch.visible(PandoraToggle.isSearchOpen)
        binding.ivMsg.setOnAntiViolenceClickListener {
            MetaRouter.IM.goChatTabFragment(
                this,
                source = EventParamConstants.SRC_MESSAGE_LIST_ENTRANCE_MAPS
            )
        }
    }

    private fun initData() {
        viewModel.roomListLiveData.observe(viewLifecycleOwner) {
            updateRoomList(it)
        }
        viewModel.cottageRoomList.observe(viewLifecycleOwner) {
            it?.let { it1 -> updateCottageRoomList(it1) }
        }
        initCardData()
        suggestionViewModel.insertPartyPageCallback.observe(viewLifecycleOwner) {
            viewModel.insertTrending()
        }
        observeRefreshRoomList()
        refreshMyGames()
        mainViewModel.msgUnReadCountFlow.collectWithLifecycleOwner(viewLifecycleOwner) {
            binding.vMsgRedDot.visible(it > 0)
        }
    }

    private fun initCardData() {
        viewModel.cardListLiveData.observe(viewLifecycleOwner) { event ->
            viewLifecycleOwner.lifecycleScope.launch {
                binding.refresh.isRefreshing = false

                when (event.loadStatus.status) {
                    LoadType.Refresh -> {
                        binding.lv.gone()
                        choiceHomeAdapter.setList(event.originalDataList)
                        choiceHomeAdapter.resetLoadMore()
                        choiceHomeAdapter.loadMoreModule.loadMoreComplete()
                    }

                    LoadType.LoadMore -> {
                        binding.lv.gone()
                        choiceHomeAdapter.submitData(event.originalDataList, false)
                        choiceHomeAdapter.loadMoreModule.loadMoreComplete()
                    }

                    LoadType.Fail -> {
                        if (choiceHomeAdapter.data.isEmpty()) {
                            binding.lv.visible()
                            //失败情况下显示
                            binding.lv.showError()
                        } else {
                            binding.lv.gone()
                            choiceHomeAdapter.loadMoreModule.loadMoreFail()
                        }
                    }

                    LoadType.End -> {
                        if (event.originalDataList.isNullOrEmpty()) {
                            binding.lv.visible()
                            binding.lv.showEmpty(getString(R.string.no_data))
                        } else {
                            binding.lv.gone()
                            choiceHomeAdapter.submitData(event.originalDataList, false)
                            choiceHomeAdapter.loadMoreModule.loadMoreEnd()
                        }
                    }

                    LoadType.Update -> {
                        binding.lv.gone()
                        choiceHomeAdapter.submitData(event.originalDataList, false)
                    }

                    else -> {}
                }
            }
        }
    }

    private fun updateCottageRoomList(listRoom: List<House>) {
        val cardIndex = viewModel.getCottageRoomIndex()
        if (cardIndex >= 0) {
            val holder = binding.rv.findViewHolderForAdapterPosition(cardIndex + choiceHomeAdapter.headerLayoutCount)
            val adapter = holder?.itemView?.findViewById<RecyclerView>(R.id.rv_choice_item_list)?.adapter as? CottageRoomItemAdapter
            adapter?.setList(listRoom)
        }
    }

    private val accountCallback = object : TokenChangedCallback {
        override fun invoke(old: String?, new: String?) {
            if (old.isNullOrEmpty() && !new.isNullOrEmpty()) {
                accountInteractor.removeTokenChangedCallback(this)
                checkRefreshRoomList()
            }
        }
    }

    private fun checkRefreshRoomList() {
        if (accountInteractor.checkAccountInit() && MWBiz.isAvailable()) {
            viewModel.checkRefreshRoomList()
        }
    }

    private fun observeRefreshRoomList() {
        accountInteractor.addTokenChangedCallback(accountCallback)
        MWLifeCallback.available.observe(viewLifecycleOwner) {
            checkRefreshRoomList()
        }
    }

    private fun updateRoomList(listRoom: List<ChatRoomInfo>) {
        val cardIndex = viewModel.getRoomCardIndex()
        if (cardIndex >= 0) {
            val holder = binding.rv.findViewHolderForAdapterPosition(cardIndex + choiceHomeAdapter.headerLayoutCount)
            val adapter = holder?.itemView?.findViewById<RecyclerView>(R.id.rv_choice_item_list)?.adapter as? RoomItemAdapter
            adapter?.submitData(viewLifecycleOwner.lifecycle, listRoom.toMutableList())
        }
    }

    private fun requirePlayedGameResIdBean(gameId: String): ResIdBean {
        val cacheBean = metaKV.analytic.getLaunchResIdBeanWithId(gameId)
        return ResIdBean().setCategoryID(CategoryId.RECOMMEND_MY_GAMES).apply {
            cacheBean?.also {
                setTsType(it.getTsType())
            }
        }
    }

    private fun initRecentPlayedView() {
        headContinueGameBinding.rvRecentlyPlayed.layoutManager = LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
        playedAdapter.setOnItemClickListener { _, position ->
            playedAdapter.peek(position)?.let {
                val house = editorInteractor.userMainHouse.value?.data
                if (house?.gameId.equals(it.id)) {
                    // 小屋
                    editorInteractor.launchCottageGame(this, accountInteractor.curUuid, "8", CategoryId.COTTAGE_ROOM_LIST, "dsHomePlayedGame")
                } else {
                    if (it.isUgcGame) {
                        MetaRouter.MobileEditor.ugcDetail(this, it.id, null, requirePlayedGameResIdBean(it.id).setParam1(position + 1))
                    } else {
                        playGame(it.id, it.packageName ?: "", requirePlayedGameResIdBean(it.id).setParam1(position + 1))
                    }
                }
            }
        }
        headContinueGameBinding.rvRecentlyPlayed.adapter = playedAdapter
        headContinueGameBinding.rvRecentlyPlayed.setHasFixedSize(true)

        viewLifecycleOwner.lifecycleScope.launchWhenCreated {
            playedAdapter.loadStateFlow.collectLatest {
                val showContinue = playedAdapter.itemCount > 0
                headContinueGameBinding.rvRecentlyPlayed.isVisible = showContinue
                headContinueGameBinding.tvPlayedTitle.isVisible = showContinue
                headContinueGameBinding.vContinueSpace.isVisible = showContinue
                headContinueGameBinding.tvPlayedManage.isVisible = showContinue
                Timber.tag(TAG).d("collect playedAdapter.loadStateFlow ${it.refresh} isVisible:${playedAdapter.itemCount > 0} ${playedAdapter.itemCount}")
            }
        }
        headContinueGameBinding.tvPlayedManage.setOnClickListener {
            MetaRouter.Played.manager(this)
        }
    }

    // 请求最近玩过列表数据
    private fun refreshMyGames() {
        viewLifecycleOwner.lifecycleScope.launchWhenCreated {
            viewModel.playedGames.collectLatest {
                playedAdapter.submitData(it)
            }
        }
    }

    private fun initRefreshView() {
        binding.lv.setRetry {
            refreshAllData()
        }

        binding.refresh.setOnRefreshListener {
            refreshAllData()
        }
    }

    private fun playGame(id: String, packageName: String, resIdBean: ResIdBean) {
        MetaRouter.GameDetail.navigate(this@ChoiceHomeFragment, id, resIdBean, packageName, type = "ts")
    }

    private fun jump(subInfo: ChoiceGameInfo, resIdBean: ResIdBean) {
        when (subInfo.type) {
            ChoiceContentType.GAME -> {
                Analytics.track(EventConstants.EVENT_ITEM_CLICK) {
                    put("gameid", subInfo.code.toString())
                    put("game_type", subInfo.gcMode.toString())
                    putAll(ResIdUtils.getAnalyticsMap(resIdBean))
                }
                if (subInfo.gcMode == "UGC") {
                    if (PandoraToggle.enableUgcDetail) {
                        MetaRouter.MobileEditor.ugcDetail(this, subInfo.code.orEmpty(), null, resIdBean)
                    } else {
                        val params = EditorUGCLaunchParams(subInfo.code ?: "", subInfo.packageName, subInfo.displayName ?: "", subInfo.iconUrl ?: "", "")
                        editorGameLaunchHelper?.startUgcGame(this, params, resIdBean.setTsType(ResIdBean.TS_TYPE_UCG))
                    }
                } else {
                    playGame(subInfo.code ?: "", subInfo.packageName ?: "", resIdBean.setTsType(ResIdBean.TS_TYPE_NORMAL))
                }
            }

            ChoiceContentType.LINK -> {
                // 内部、外部的web链接，外部scheme
                subInfo.router?.let { router ->
                    Analytics.track(EventConstants.HOME_BANNER_CLICK) {
                        put("link", router)
                        put("source", "2")
                    }
                    if (subInfo.jumpOperation()) {
                    } else if (subInfo.jumpOutside()) {
                        // 外部scheme/http url
                        kotlin.runCatching {
                            val intent = Intent(Intent.ACTION_VIEW, Uri.parse(router))
                            startActivity(intent)
                        }.getOrElse {
                            jumpToWeb(this, subInfo.title, router)
                        }
                    } else {
                        // 内部http url
                        jumpToWeb(this, subInfo.title, router)
                    }
                }
            }

            ChoiceContentType.OPERATION -> {
                // 内部scheme
                Analytics.track(EventConstants.HOME_BANNER_CLICK) {
                    put("link", subInfo.operatingPosition?.param1.orEmpty())
                    put("source", "2")
                    put(EventParamConstants.KEY_EVENT_ID, subInfo.operatingPosition?.id.orEmpty())
                }
                subInfo.operatingPosition?.let {
                    val handled = UniJumpUtil.jump(
                        this,
                        it,
                        LinkData.SOURCE_CHOICE_OPERATION,
                        CategoryId.HOME_RECOMMEND_CATEGORY_BANNER,
                        mainViewModel,
                        null
                    )
                    if (!handled) {
                        toast(R.string.not_currently_support)
                    }
                } ?: run {
                    toast(R.string.not_currently_support)
                }
            }

            else -> {

            }
        }
    }

    fun fetchTrendingAnchorView(): View? {
        if (!isResumed) return null
        // 计算index时去掉header
        val trendingIndex = choiceHomeAdapter.data.indexOfFirst {
            ChoiceCardType.isTrending(it.cardName, it.cardType)
        } + choiceHomeAdapter.headerLayoutCount
        if (trendingIndex < 0) return null
        val rvTrending =
            choiceHomeAdapter.getViewByPosition(trendingIndex, R.id.rv_choice_item_list)
        if (rvTrending == null || rvTrending !is RecyclerView) return null
        return (rvTrending.adapter as? SmallItemAdapter)?.getViewByPosition(0, R.id.smallCardRoot)
    }

    override fun onPause() {
        super.onPause()
    }

    private fun jumpToWeb(fragment: Fragment, title: String? = null, url: String) {
        if (url.startsWith("http://", ignoreCase = true) or url.startsWith("https", true)) {
            MetaRouter.Web.navigate(fragment, title, url)
        }
    }

    private fun getChoiceAdapterByGroup(): ChoiceHomeCardAdapter {
        val smallCardAdapter = ChoiceHomeCardAdapter(
            Glide.with(this), itemCallback
        )
        if (!PandoraToggle.isHomeRecommend) {
            addFeedbackHeader(smallCardAdapter)
            homeHeaderFriends.addToHeadView(smallCardAdapter)
            addContinueView(smallCardAdapter)
        }
        return smallCardAdapter
    }

    private val itemCallback = object : ChoiceHomeCardAdapter.HomeItemCallback {
        override fun onItemShow(
            cardPos: Int,
            card: ChoiceCardInfo,
            itemPos: Int,
            item: IChoiceItem,
            isBanner: Boolean
        ) {
            when (item) {
                is ChoiceGameInfo -> onItemShow(cardPos + 1, card, itemPos + 1, item, if (isBanner) CategoryId.HOME_RECOMMEND_CATEGORY_BANNER else CategoryId.HOME_RECOMMEND_CATEGORY)

                is ChatRoomInfo -> HomeRoomAnalytics.trackRoomShow(item, CategoryId.HOME_RECOMMEND_CATEGORY)

                is House -> Analytics.track(EventConstants.EVENT_DSHOME_ENTRY_SHOW, mapOf("show_categoryid" to "2"))

                is ChoiceHomeVideoItem -> {// Recommend video
                    RecommendVideoAnalytics.trackVideoItemShow(
                        ResIdBean
                            .newInstance()
                            .setCategoryID(CategoryId.HOME_RECOMMEND_NEWEST_GAME)
                            .setParam1(itemPos + 1)
                            .setReqId(item.reqId),
                        item.postDetail
                    )
                }
            }
        }

        override fun onItemClick(
            cardPos: Int,
            card: ChoiceCardInfo,
            itemPos: Int,
            item: IChoiceItem,
            isBanner: Boolean
        ) {
            when (item) {
                is ChoiceGameInfo -> onItemClick(cardPos + 1, card, itemPos + 1, item, if (isBanner) CategoryId.HOME_RECOMMEND_CATEGORY_BANNER else CategoryId.HOME_RECOMMEND_CATEGORY)

                is ChatRoomInfo -> showRoomDetail(item)

                is House -> {
                    Analytics.track(
                        EventConstants.EVENT_DSHOME_ENTRY_CLICK,
                        mapOf("show_categoryid" to "2", "userid" to (item.ownerUuid ?: ""), "homename" to (item.description ?: ""), "onlinenumber" to (item.number ?: ""), "roomid" to (item.roomId ?: ""))
                    )
                    item.roomId?.let { CottageRoomDetailDialog.show(this@ChoiceHomeFragment, it, ResIdBean().setGameId(item.gameId).setCategoryID(CategoryId.HOME_RECOMMEND_CATEGORY), "2") }
                }

                is ChoiceHomeVideoItem -> {
                    val resId = ResIdBean
                        .newInstance()
                        .setCategoryID(CategoryId.HOME_RECOMMEND_NEWEST_GAME)
                        .setParam1(itemPos + 1)
                        .setReqId(item.reqId)

                    RecommendVideoAnalytics.trackVideoItemClick(resId, item.postDetail)

                    MetaRouter.Video.goRecommendVideoFeed(
                        this@ChoiceHomeFragment,
                        resId,
                        item.postDetail.postId
                    )
                }
            }
        }

        override fun onCardShow(cardPos: Int, card: ChoiceCardInfo) {
            if (ChoiceCardType.isRoomCardType(card.cardType)) {
                HomeRoomAnalytics.trackHomeRoomCardShow(card.roomList?.size ?: 0)
            }
            card.recommend?.let { item ->
                Analytics.track(
                    EventConstants.C_FEED_ITEM_SHOW,
                    "gameid" to item.code.toString(),
                    "packagename" to item.packageName.toString(),
                    "reqid" to item.reqid.toString(),
                    "icon_type" to item.gcMode.toString(),
                    "type" to "1",
                    "show_categoryid" to CategoryId.HOME_RECOMMEND_NEWEST_GAME,
                    "show_param1" to cardPos
                )
            }
        }
    }

    /**
     * 点击展示房间详情页
     */
    private fun showRoomDetail(roomInfo: ChatRoomInfo) {
        HomeRoomAnalytics.trackRoomClick(roomInfo, CategoryId.HOME_RECOMMEND_CATEGORY)
        RoomDetailDialog.show(this, roomInfo, ResIdBean().setGameId(roomInfo.platformGameId).setCategoryID(CategoryId.HOME_RECOMMEND_CATEGORY))
    }

    private fun refreshRoom() {
        viewModel.refreshRoomList()
    }

    override fun onResume() {
        super.onResume()
        if (!isFirstResume) {
            refreshRoom()
        } else {
            isFirstResume = false
        }
    }

    private fun initLinearRecyclerView() {
        choiceHomeAdapter.loadMoreModule.loadMoreView = EmptyLoadMoreView()
        choiceHomeAdapter.loadMoreModule.isEnableLoadMore = true
        choiceHomeAdapter.loadMoreModule.setOnLoadMoreListener {
            if (isBindingAvailable() && !binding.refresh.isRefreshing) {
                viewModel.loadMore()
            }
        }
        choiceHomeAdapter.addChildClickViewIds(R.id.tv_card_more)
        choiceHomeAdapter.setOnAntiViolenceChildItemClickListener { _, view, position ->
            when (view.id) {
                R.id.tv_card_more -> {
                    val cardItem = choiceHomeAdapter.getItemOrNull(position) as? ChoiceCardInfo
                    if (cardItem != null) {
                        if (ChoiceCardType.isRoomCardType(cardItem.cardType)) {
                            HomeRoomAnalytics.trackHomeRoomCardSeeAllCLick()
                            jumpToMoreRoomPage()
                        } else if (ChoiceCardType.isHomeRoomCardType(cardItem.cardType)) {
                            jumpToHomeRoomPage()
                        } else if (ChoiceCardType.isUgcCreate(cardItem.cardType)) {
                            MetaRouter.MobileEditor.ugcAll(this, cardItem.cardId.toString(), cardItem.cardName ?: "")
                        } else if (ChoiceCardType.isHomeVideoCardType(cardItem.cardType)) {

                            val resId = ResIdBean
                                .newInstance()
                                .setCategoryID(CategoryId.HOME_RECOMMEND_NEWEST_GAME)
                                .setReqId(cardItem.videoFeeds?.reqId ?: "")

                            RecommendVideoAnalytics.trackVideoMoreClick(resId)

                            MetaRouter.Video.goRecommendVideoList(this, resId, cardItem.videoFeeds)
                        }
                    }
                }
            }
        }
        binding.rv.layoutManager = object : GridLayoutManager(requireContext(), 3) {
            override fun onLayoutChildren(recycler: RecyclerView.Recycler?, state: RecyclerView.State?) {
                kotlin.runCatching {
                    super.onLayoutChildren(recycler, state)
                }
            }
        }.apply {
            spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                override fun getSpanSize(position: Int): Int {
                    return when (choiceHomeAdapter.getItemViewType(position)) {
                        ChoiceCardType.RECOMMEND -> 1
                        else -> 3
                    }
                }
            }
        }
        binding.rv.adapter = choiceHomeAdapter
        choiceHomeAdapter.setOnAntiViolenceItemClickListener { adapter, view, position ->
            val item = (choiceHomeAdapter.getItem(position) as? ChoiceCardInfo)?.recommend ?: return@setOnAntiViolenceItemClickListener
            Analytics.track(
                EventConstants.C_FEED_ITEM_CLICK,
                "gameid" to item.code.toString(),
                "packagename" to item.packageName.toString(),
                "reqid" to item.reqid.toString(),
                "type" to "1",
                "icon_type" to item.gcMode.toString(),
                "show_categoryid" to CategoryId.HOME_RECOMMEND_NEWEST_GAME,
                "show_param1" to position
            )
            onItemClick(position, null, position, item, CategoryId.HOME_RECOMMEND_NEWEST_GAME)
        }
    }

    private fun jumpToHomeRoomPage() {
        MetaRouter.HomeRoom.goAllHomeRoom(this)
    }

    private fun jumpToMoreRoomPage() {
        MetaRouter.HomeRoom.goAllRoom(this)
    }

    private fun addFeedbackHeader(adapter: ChoiceHomeCardAdapter) {
        if (PandoraToggle.openHomeFeedbackHead) {
            adapter.setHeaderView(
                headFeedbackBinding.root,
                ChoiceHomeAdapterHeaderIndex.HEAD_PRIORITY_FEEDBACK
            )
            headFeedbackBinding.apply {
                headFeedbackBinding.tvEnter.setOnAntiViolenceClickListener {
                    MetaRouter.Feedback.feedback(
                        this@ChoiceHomeFragment,
                        null,
                        SubmitNewFeedbackRequest.SOURCE_HOME_NUMBER,
                        null,
                        false,
                        needBackGame = false,
                        fromGameId = null
                    )
                }
                root.layoutParams = (root.layoutParams as? MarginLayoutParams)?.apply {
                    width = ScreenUtil.getScreenWidth(requireContext())
                    val dp10 = 10.dp
                    val dp11 = 11.dp
                    leftMargin = -dp11
                    rightMargin = -dp11
                    topMargin = -dp10
                }
            }
        }
    }

    private fun addContinueView(adapter: ChoiceHomeCardAdapter) {
        adapter.setHeaderView(
            headContinueGameBinding.root,
            ChoiceHomeAdapterHeaderIndex.HEAD_PRIORITY_PLAYED
        )
    }

    private fun onItemShow(
        cardPos: Int,
        card: ChoiceCardInfo?,
        itemPos: Int,
        item: ChoiceGameInfo,
        categoryId: Int = CategoryId.HOME_RECOMMEND_CATEGORY
    ) {
        Timber.tag(TAG)
            .d("itemShow cardPos:$cardPos card:[${card?.cardId}, ${card?.cardName}, ${card?.gameList?.size}] itemPos:$itemPos item:[${item.code}, ${item.displayName}, ${item.packageName}]")
        when (item.type) {
            ChoiceContentType.LINK -> {
                Analytics.track(EventConstants.HOME_BANNER_SHOW, "link" to item.router.toString(), "source" to "2")
            }

            ChoiceContentType.OPERATION -> {
                Analytics.track(
                    EventConstants.HOME_BANNER_SHOW,
                    "link" to item.operatingPosition?.param1.orEmpty(),
                    "source" to "2",
                    EventParamConstants.KEY_EVENT_ID to item.operatingPosition?.id.orEmpty()
                )
            }

            ChoiceContentType.GAME -> {
                Analytics.track(EventConstants.EVENT_ITEM_SHOW) {
                    put("gameid", item.code ?: "")
                    put("packagename", item.packageName ?: "")
                    put("game_type", item.typeToString())
                    putAll(
                        ResIdUtils.getAnalyticsMap(
                            ResIdBean().setGameId(item.code ?: "").setCategoryID(categoryId)
                                .setParam1(cardPos).setParam2(itemPos).setParamExtra(card?.cardName)
                        )
                    )
                }
            }
        }
    }

    private fun onItemClick(
        cardPos: Int,
        card: ChoiceCardInfo?,
        itemPos: Int,
        item: ChoiceGameInfo,
        categoryId: Int = CategoryId.HOME_RECOMMEND_CATEGORY,
    ) {
        Timber.tag(TAG)
            .e("itemClick cardPos:$cardPos card:[${card?.cardId}, ${card?.cardName}, ${card?.gameList?.size}] itemPos:$itemPos item:[${item.code}, ${item.displayName}, ${item.packageName}]")
        val resIdBean =
            ResIdBean().setGameId(item.code ?: "").setCategoryID(categoryId).setParam1(cardPos)
                .setParam2(itemPos).setParamExtra(card?.cardName).setReqId(item.reqid.toString())
                .setIconType(item.gcMode)
        jump(item, resIdBean)
    }
}
