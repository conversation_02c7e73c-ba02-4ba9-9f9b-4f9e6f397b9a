package com.socialplay.gpark.ui.editor.module.guide

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.meta.biz.ugc.model.EditorConfigJsonEntity
import com.meta.biz.ugc.model.EditorTemplate
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.BaseAccountInteractor
import com.socialplay.gpark.data.interactor.TTaiInteractor
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.data.model.guide.UgcModuleGuidInfo
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import kotlinx.coroutines.flow.map
import org.koin.android.ext.android.get

data class UgcModuleGuideState(
    val guideInfo: Async<UgcModuleGuidInfo> = Uninitialized,
    val template: Async<EditorTemplate> = Uninitialized
) : MavericksState

class UgcModuleGuideViewModel(
    initState: UgcModuleGuideState,
    private val metaRepository: IMetaRepository,
    private val tTaiInteractor: TTaiInteractor,
    val accountInteractor: AccountInteractor,
) : BaseViewModel<UgcModuleGuideState>(initState) {

    init {
        getGuideInfo()
    }

    fun getGuideInfo() = withState { s ->
        if (s.guideInfo is Loading) return@withState
        tTaiInteractor.getTTaiWithTypeV3<UgcModuleGuidInfo>(TTaiKV.ID_MODULE_GUIDE_ID).map {
            check(it != null)
            it
        }.execute {
            copy(guideInfo = it)
        }
    }

    fun skipGuide() {
        accountInteractor.moduleGuideStatus = BaseAccountInteractor.MODULE_GUIDE_STATUS_SKIP_USER
    }

    fun getModuleTemplate() = withState { s ->
        if (s.template is Loading) return@withState
        val templateId = s.guideInfo()?.templateIds?.random() ?: return@withState
        metaRepository.getGameTemplateV2(templateId, 7).map {
            it.type = EditorConfigJsonEntity.TYPE_MODULE
            it
        }.execute {
            copy(template = it)
        }
    }

    companion object :
        KoinViewModelFactory<UgcModuleGuideViewModel, UgcModuleGuideState>() {

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: UgcModuleGuideState
        ): UgcModuleGuideViewModel {
            return UgcModuleGuideViewModel(state, get(), get(), get())
        }
    }
}