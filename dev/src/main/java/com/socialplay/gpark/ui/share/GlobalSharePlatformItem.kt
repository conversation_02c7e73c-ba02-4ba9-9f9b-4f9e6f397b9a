package com.socialplay.gpark.ui.share

import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.view.updateLayoutParams
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.share.ShareFeature
import com.socialplay.gpark.data.model.share.SharePlatform
import com.socialplay.gpark.databinding.ItemSharePlatformBinding
import com.socialplay.gpark.databinding.ItemShareScreenshotPreviewBinding
import com.socialplay.gpark.ui.core.IBaseEpoxyItemListener
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaEpoxyController
import com.socialplay.gpark.util.extension.compoundDrawables
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.screenHeight
import com.socialplay.gpark.util.extension.screenWidth
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTextResFirst
import com.socialplay.gpark.util.extension.statusBarHeight
import com.socialplay.gpark.util.extension.unsetOnClick
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.glide.ScreenshotCrop

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/08/22
 *     desc   :
 * </pre>
 */
interface IGlobalShareListener : IBaseEpoxyItemListener {
    fun clickPlatform(item: SharePlatform)
    fun clickFeature(item: ShareFeature)
}

fun MetaEpoxyController.globalSharePlatformItem(
    item: SharePlatform,
    listener: IGlobalShareListener
) {
    add {
        GlobalSharePlatformItem(
            item,
            listener
        ).id("GlobalShare-${item.platform}-${it}")
    }
}

data class GlobalSharePlatformItem(
    val item: SharePlatform,
    val listener: IGlobalShareListener
) : ViewBindingItemModel<ItemSharePlatformBinding>(
    R.layout.item_share_platform,
    ItemSharePlatformBinding::bind
) {

    override fun ItemSharePlatformBinding.onBind() {
        tvTitle.setText(item.titleRes)
        tvTitle.compoundDrawables(top = item.iconRes)
        root.setOnAntiViolenceClickListener {
            listener.clickPlatform(item)
        }
    }

    override fun ItemSharePlatformBinding.onUnbind() {
        root.unsetOnClick()
    }
}

fun MetaEpoxyController.globalShareScreenshotPreviewItem(
    path: String,
    isPortrait: Boolean,
    listener: IGlobalShareListener
) {
    add {
        GlobalShareScreenshotPreviewItem(path, isPortrait, listener).id("GlobalShareScreenshotPreview")
    }
}

data class GlobalShareScreenshotPreviewItem(
    val path: String,
    val isPortrait: Boolean,
    val listener: IGlobalShareListener
) : ViewBindingItemModel<ItemShareScreenshotPreviewBinding>(
    R.layout.item_share_screenshot_preview,
    ItemShareScreenshotPreviewBinding::bind
) {

    override fun ItemShareScreenshotPreviewBinding.onBind() {
        val statusBarHeight = if (isPortrait) {
            statusBarHeight
        } else {
            0
        }
        val maxWidth =
            ((screenWidth - statusBarHeight) / screenHeight.toFloat() * dp(78)).toInt() + dp(2)
        ivScreenshot.updateLayoutParams<ConstraintLayout.LayoutParams> {
            matchConstraintMaxWidth = maxWidth
        }
        if (statusBarHeight > 0) {
            listener.getGlideOrNull()?.run {
                load(path).transform(ScreenshotCrop(screenWidth, statusBarHeight))
                    .into(ivScreenshot)
            }
        } else {
            listener.getGlideOrNull()?.run {
                load(path)
                .into(ivScreenshot)
            }
        }
    }
}

fun MetaEpoxyController.globalShareFeatureItem(
    item: ShareFeature,
    listener: IGlobalShareListener
) {
    add {
        GlobalShareFeatureItem(
            item,
            listener
        ).id("GlobalShareFeature-${item.featureId}-${it}")
    }
}

data class GlobalShareFeatureItem(
    val item: ShareFeature,
    val listener: IGlobalShareListener
) : ViewBindingItemModel<ItemSharePlatformBinding>(
    R.layout.item_share_platform,
    ItemSharePlatformBinding::bind
) {

    override fun ItemSharePlatformBinding.onBind() {
        tvTitle.setTextResFirst(item.titleRes, item.title)
        tvTitle.compoundDrawables(top = item.iconRes)
        vRedDot.visible(item.showRedDot)
        root.setOnAntiViolenceClickListener {
            listener.clickFeature(item)
        }
    }

    override fun ItemSharePlatformBinding.onUnbind() {
        root.unsetOnClick()
    }
}