<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- Base Shape -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#FF3D3D3D" />
            <corners android:radius="100dp" />
        </shape>
    </item>

    <!-- Highlight -->
    <item
        android:bottom="2dp"
        android:left="2dp"
        android:right="2dp"
        android:top="2dp">
        <shape android:shape="rectangle">
            <gradient
                android:angle="270"
                android:endColor="@android:color/transparent"
                android:startColor="#66FFFFFF" />
            <corners android:radius="100dp" />
        </shape>
    </item>
    
    <!-- Inner Stroke -->
    <item>
        <shape android:shape="rectangle">
            <stroke
                android:width="1dp"
                android:color="#A0A0A0" />
            <corners android:radius="100dp" />
        </shape>
    </item>

</layer-list> 