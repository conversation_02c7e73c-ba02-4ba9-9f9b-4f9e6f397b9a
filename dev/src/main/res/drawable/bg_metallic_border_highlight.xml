<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 外层边框基础 -->
    <item>
        <shape android:shape="oval">
            <solid android:color="#80FFFFFF" />
        </shape>
    </item>

    <!-- 挖空内部形成边框 -->
    <item
        android:bottom="3dp"
        android:left="3dp"
        android:right="3dp"
        android:top="3dp">
        <shape android:shape="oval">
            <solid android:color="@color/transparent" />
        </shape>
    </item>

    <!-- 顶部高光渐变 -->
    <item
        android:bottom="50%"
        android:left="1dp"
        android:right="1dp"
        android:top="1dp">
        <shape android:shape="oval">
            <gradient
                android:angle="270"
                android:endColor="@color/transparent"
                android:startColor="#FFFFFF" />
        </shape>
    </item>

    <!-- 底部阴影渐变 -->
    <item
        android:bottom="1dp"
        android:left="1dp"
        android:right="1dp"
        android:top="50%">
        <shape android:shape="oval">
            <gradient
                android:angle="90"
                android:endColor="@color/transparent"
                android:startColor="#40FFFFFF" />
        </shape>
    </item>

    <!-- 再次挖空内部 -->
    <item
        android:bottom="3dp"
        android:left="3dp"
        android:right="3dp"
        android:top="3dp">
        <shape android:shape="oval">
            <solid android:color="@color/transparent" />
        </shape>
    </item>

</layer-list>
