<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <item android:drawable="@color/white" android:state_focused="false" android:state_pressed="false" android:state_selected="false" />
    <item android:drawable="@color/white" android:state_pressed="false" android:state_selected="true" />
    <!-- Focused states -->
    <item android:drawable="@color/white" android:state_focused="true" android:state_pressed="false" android:state_selected="false" />
    <item android:drawable="@color/white" android:state_focused="true" android:state_pressed="false" android:state_selected="true" />

</selector>