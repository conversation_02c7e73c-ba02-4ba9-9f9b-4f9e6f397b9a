<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 透明内部 -->
    <item>
        <shape android:shape="oval">
            <solid android:color="@color/transparent" />
        </shape>
    </item>

    <!-- 主要金属边框 - 光线从左上角照射 -->
    <item>
        <shape android:shape="oval">
            <stroke
                android:width="4dp">
                <gradient
                    android:angle="315"
                    android:startColor="#FFFFFFFF"
                    android:centerColor="#80FFFFFF"
                    android:endColor="#20FFFFFF" />
            </stroke>
        </shape>
    </item>

    <!-- 反射高光 - 右下角反射 -->
    <item
        android:bottom="1dp"
        android:left="1dp"
        android:right="1dp"
        android:top="1dp">
        <shape android:shape="oval">
            <stroke
                android:width="2dp">
                <gradient
                    android:angle="135"
                    android:startColor="#10FFFFFF"
                    android:centerColor="#60FFFFFF"
                    android:endColor="#A0FFFFFF" />
            </stroke>
        </shape>
    </item>

    <!-- 精细高光线 - 顶部最亮点 -->
    <item
        android:bottom="2dp"
        android:left="2dp"
        android:right="2dp"
        android:top="2dp">
        <shape android:shape="oval">
            <stroke
                android:width="1dp">
                <gradient
                    android:angle="270"
                    android:startColor="#F0FFFFFF"
                    android:centerColor="#60FFFFFF"
                    android:endColor="#30FFFFFF" />
            </stroke>
        </shape>
    </item>

</layer-list>
