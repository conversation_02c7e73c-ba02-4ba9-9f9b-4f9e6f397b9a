<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">


    <item android:id="@android:id/background"
        android:gravity="center_vertical|fill_horizontal">
        <shape android:shape="rectangle">
            <corners android:radius="@dimen/dp_2" />
            <size android:height="@dimen/dp_2" />
            <solid android:color="@color/white_50" />
        </shape>
    </item>
    <item android:id="@android:id/secondaryProgress"
        android:gravity="center_vertical|fill_horizontal">
        <scale android:scaleWidth="100%">
            <selector>
                <item android:state_enabled="false"
                    android:drawable="@color/transparent" />
                <item>
                    <shape android:shape="rectangle">
                        <corners android:radius="@dimen/dp_2" />
                        <size android:height="@dimen/dp_2" />
                        <solid android:color="@color/white_60" />
                    </shape>
                </item>
            </selector>
        </scale>
    </item>
    <item android:id="@android:id/progress"
        android:gravity="center_vertical|fill_horizontal">
        <scale android:scaleWidth="100%">
            <selector>
                <item android:state_enabled="false"
                    android:drawable="@color/transparent" />
                <item>
                    <shape android:shape="rectangle" >
                        <corners android:radius="@dimen/dp_2" />
                        <size android:height="@dimen/dp_2" />
                        <solid android:color="@color/colorPrimary" />
                    </shape>
                </item>
            </selector>
        </scale>
    </item>

</layer-list>