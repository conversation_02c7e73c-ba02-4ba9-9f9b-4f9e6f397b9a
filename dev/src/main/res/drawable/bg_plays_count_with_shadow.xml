<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 图层1: 阴影/外发光层 (底层) -->
    <!-- 这个形状比上层内容要大一点，颜色是半透明的，模拟发光效果 -->
    <item>
        <shape android:shape="rectangle">
            <!-- 您可以根据设计图调整发光的颜色和透明度 -->
            <solid android:color="#26FFFFFF" />
            <!-- 圆角要和内容层保持一致或稍大一点，让光晕更柔和 -->
            <corners android:radius="@dimen/dp_10" />
        </shape>
    </item>

    <!-- 图层2: 内容背景层 (顶层) -->
    <!-- 我们给这一层设置了边距，这样底下的阴影层就能从四周"露"出来 -->
    <item
        android:bottom="2dp"
        android:left="1dp"
        android:right="1dp"
        android:top="1dp">
        <shape android:shape="rectangle">
            <!-- 这是内容区域的背景 -->
            <solid android:color="#1AFFFFFF" />
            <corners android:radius="@dimen/dp_10" />
            <!-- 如果设计稿有描边，可以加上 -->
            <stroke
                android:width="0.5dp"
                android:color="#4DFFFFFF" />
        </shape>
    </item>

</layer-list> 