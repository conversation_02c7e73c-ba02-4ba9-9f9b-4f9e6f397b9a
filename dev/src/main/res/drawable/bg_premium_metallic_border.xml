<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 外层边框 -->
    <item>
        <shape android:shape="oval">
            <solid android:color="#40FFFFFF" />
        </shape>
    </item>

    <!-- 左上角高光 -->
    <item
        android:bottom="50%"
        android:left="0dp"
        android:right="50%"
        android:top="0dp">
        <shape android:shape="oval">
            <gradient
                android:angle="315"
                android:endColor="@color/transparent"
                android:startColor="#FFFFFF" />
        </shape>
    </item>

    <!-- 右下角阴影 -->
    <item
        android:bottom="0dp"
        android:left="50%"
        android:right="0dp"
        android:top="50%">
        <shape android:shape="oval">
            <gradient
                android:angle="135"
                android:endColor="@color/transparent"
                android:startColor="#20FFFFFF" />
        </shape>
    </item>

    <!-- 顶部强高光线 -->
    <item
        android:bottom="70%"
        android:left="20%"
        android:right="20%"
        android:top="0dp">
        <shape android:shape="oval">
            <gradient
                android:angle="270"
                android:endColor="@color/transparent"
                android:startColor="#F0FFFFFF" />
        </shape>
    </item>

    <!-- 挖空内部 -->
    <item
        android:bottom="4dp"
        android:left="4dp"
        android:right="4dp"
        android:top="4dp">
        <shape android:shape="oval">
            <solid android:color="@color/transparent" />
        </shape>
    </item>

</layer-list>
