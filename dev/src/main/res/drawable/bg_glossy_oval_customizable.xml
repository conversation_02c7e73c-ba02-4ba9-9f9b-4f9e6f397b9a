<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 底层背景 -->
    <item>
        <shape android:shape="oval">
            <solid android:color="@color/white_40" />
        </shape>
    </item>

    <!-- 主要渐变层 -->
    <item
        android:bottom="4dp"
        android:left="4dp"
        android:right="4dp"
        android:top="4dp">
        <shape android:shape="oval">
            <gradient
                android:angle="270"
                android:centerColor="@color/white_30"
                android:endColor="@color/white_10"
                android:startColor="@color/white_70"
                android:type="radial"
                android:gradientRadius="60%p" />
        </shape>
    </item>

    <!-- 顶部高光效果 -->
    <item
        android:bottom="50%"
        android:left="25%"
        android:right="25%"
        android:top="15%">
        <shape android:shape="oval">
            <gradient
                android:angle="270"
                android:endColor="@color/transparent"
                android:startColor="@color/white_80" />
        </shape>
    </item>

    <!-- 中心亮点 -->
    <item
        android:bottom="50%"
        android:left="40%"
        android:right="40%"
        android:top="30%">
        <shape android:shape="oval">
            <solid android:color="@color/white_90" />
        </shape>
    </item>

    <!-- 外边框 -->
    <item>
        <shape android:shape="oval">
            <stroke
                android:width="1dp"
                android:color="@color/white_20" />
        </shape>
    </item>

</layer-list>
