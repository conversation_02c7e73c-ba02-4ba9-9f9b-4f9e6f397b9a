<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
  <path
      android:pathData="M11.999,22.498C19,22.498 22.498,17.798 22.498,11.999C22.498,6.201 19,1.5 11.999,1.5C4.998,1.5 1.5,6.201 1.5,11.999C1.5,17.798 4.998,22.498 11.999,22.498Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:centerX="19.125"
          android:centerY="6.75"
          android:gradientRadius="28.128"
          android:type="radial">
        <item android:offset="0" android:color="#FFFFF478"/>
        <item android:offset="0.475" android:color="#FFFFB02E"/>
        <item android:offset="1" android:color="#FFF70A8D"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M11.999,22.498C19,22.498 22.498,17.798 22.498,11.999C22.498,6.201 19,1.5 11.999,1.5C4.998,1.5 1.5,6.201 1.5,11.999C1.5,17.798 4.998,22.498 11.999,22.498Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:centerX="19.125"
          android:centerY="6.75"
          android:gradientRadius="29.212"
          android:type="radial">
        <item android:offset="0" android:color="#FFFFF478"/>
        <item android:offset="0.475" android:color="#FFFFB02E"/>
        <item android:offset="1" android:color="#FFF70A8D"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M11.999,22.498C19,22.498 22.498,17.798 22.498,11.999C22.498,6.201 19,1.5 11.999,1.5C4.998,1.5 1.5,6.201 1.5,11.999C1.5,17.798 4.998,22.498 11.999,22.498Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:centerX="12"
          android:centerY="9.375"
          android:gradientRadius="13.385"
          android:type="radial">
        <item android:offset="0.788" android:color="#00F59639"/>
        <item android:offset="0.973" android:color="#FFFF7DCE"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M11.999,22.498C19,22.498 22.498,17.798 22.498,11.999C22.498,6.201 19,1.5 11.999,1.5C4.998,1.5 1.5,6.201 1.5,11.999C1.5,17.798 4.998,22.498 11.999,22.498Z"
      android:fillAlpha="0.6">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:centerX="13.5"
          android:centerY="10.5"
          android:gradientRadius="30.759"
          android:type="radial">
        <item android:offset="0.315" android:color="#00000000"/>
        <item android:offset="1" android:color="#FF000000"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M11.999,22.498C19,22.498 22.498,17.798 22.498,11.999C22.498,6.201 19,1.5 11.999,1.5C4.998,1.5 1.5,6.201 1.5,11.999C1.5,17.798 4.998,22.498 11.999,22.498Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:centerX="12"
          android:centerY="12.75"
          android:gradientRadius="21.11"
          android:type="radial">
        <item android:offset="0.508" android:color="#007D6133"/>
        <item android:offset="1" android:color="#FF715B32"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M11.999,22.498C19,22.498 22.498,17.798 22.498,11.999C22.498,6.201 19,1.5 11.999,1.5C4.998,1.5 1.5,6.201 1.5,11.999C1.5,17.798 4.998,22.498 11.999,22.498Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:centerX="12.375"
          android:centerY="12.375"
          android:gradientRadius="9.985"
          android:type="radial">
        <item android:offset="0" android:color="#FFFFB849"/>
        <item android:offset="1" android:color="#00FFB847"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M11.999,22.498C19,22.498 22.498,17.798 22.498,11.999C22.498,6.201 19,1.5 11.999,1.5C4.998,1.5 1.5,6.201 1.5,11.999C1.5,17.798 4.998,22.498 11.999,22.498Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:centerX="15.375"
          android:centerY="13.5"
          android:gradientRadius="8.754"
          android:type="radial">
        <item android:offset="0" android:color="#FFFFA64B"/>
        <item android:offset="0.9" android:color="#00FFAE46"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M11.999,22.498C19,22.498 22.498,17.798 22.498,11.999C22.498,6.201 19,1.5 11.999,1.5C4.998,1.5 1.5,6.201 1.5,11.999C1.5,17.798 4.998,22.498 11.999,22.498Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:centerX="13.125"
          android:centerY="11.25"
          android:gradientRadius="44.29"
          android:type="radial">
        <item android:offset="0.185" android:color="#00000000"/>
        <item android:offset="1" android:color="#66000000"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M6.763,10.43C8.699,10.43 10.268,11.999 10.268,13.935C10.268,15.87 8.699,17.44 6.763,17.44C4.827,17.44 3.258,15.87 3.258,13.935C3.258,11.999 4.827,10.43 6.763,10.43Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:centerX="12.994"
          android:centerY="13.156"
          android:gradientRadius="6.829"
          android:type="radial">
        <item android:offset="0" android:color="#FF392108"/>
        <item android:offset="1" android:color="#00C87928"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M14.433,10.359C16.191,10.359 17.617,11.785 17.617,13.543C17.617,15.301 16.191,16.726 14.433,16.726C12.675,16.726 11.25,15.301 11.25,13.543C11.25,11.785 12.675,10.359 14.433,10.359Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:centerX="20.092"
          android:centerY="12.835"
          android:gradientRadius="5.752"
          android:type="radial">
        <item android:offset="0" android:color="#FF392108"/>
        <item android:offset="1" android:color="#00C87928"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M7.814,11.832C9.557,11.832 10.969,13.244 10.969,14.986C10.969,16.728 9.557,18.141 7.814,18.141C6.072,18.141 4.66,16.728 4.66,14.986C4.66,13.244 6.072,11.832 7.814,11.832Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M16.176,11.774C17.934,11.774 19.359,13.199 19.359,14.957C19.359,16.715 17.934,18.141 16.176,18.141C14.418,18.141 12.993,16.715 12.993,14.957C12.993,13.199 14.418,11.774 16.176,11.774Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M8.25,12.75C9.493,12.75 10.5,13.757 10.5,15C10.5,16.243 9.493,17.25 8.25,17.25C7.007,17.25 6,16.243 6,15C6,13.757 7.007,12.75 8.25,12.75Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="12.375"
          android:startY="18"
          android:endX="11.625"
          android:endY="12.75"
          android:type="linear">
        <item android:offset="0" android:color="#FF553B3E"/>
        <item android:offset="1" android:color="#FF3D2432"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M15.75,12.75C16.993,12.75 18,13.757 18,15C18,16.243 16.993,17.25 15.75,17.25C14.507,17.25 13.5,16.243 13.5,15C13.5,13.757 14.507,12.75 15.75,12.75Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="12.375"
          android:startY="18"
          android:endX="11.625"
          android:endY="12.75"
          android:type="linear">
        <item android:offset="0" android:color="#FF553B3E"/>
        <item android:offset="1" android:color="#FF3D2432"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M7.8,10.35C8.131,10.599 8.602,10.531 8.85,10.2C9.082,9.891 10.134,9 12,9C13.866,9 14.918,9.891 15.15,10.2C15.399,10.531 15.869,10.599 16.2,10.35C16.531,10.101 16.598,9.631 16.35,9.3C15.832,8.609 14.334,7.5 12,7.5C9.666,7.5 8.168,8.609 7.65,9.3C7.402,9.631 7.469,10.101 7.8,10.35Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:centerX="12"
          android:centerY="11.25"
          android:gradientRadius="4.125"
          android:type="radial">
        <item android:offset="0.348" android:color="#FF241A1A"/>
        <item android:offset="0.628" android:color="#FF57444A"/>
        <item android:offset="1" android:color="#FF502A56"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
