<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape>
            <solid android:color="@color/color_7FFF5F42" />
            <corners android:radius="@dimen/dp_30" />
        </shape>
    </item>

    <item android:state_enabled="false">
        <shape>
            <solid android:color="@color/color_7FFF5F42" />
            <corners android:radius="@dimen/dp_30" />
        </shape>
    </item>

    <item>
        <shape>
            <solid android:color="@color/homochromy_color_2" />
            <corners android:radius="@dimen/dp_30" />
        </shape>
    </item>
</selector>