<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 边框基础层 -->
    <item>
        <shape android:shape="oval">
            <solid android:color="#60FFFFFF" />
        </shape>
    </item>

    <!-- 顶部高光 -->
    <item
        android:bottom="60%"
        android:left="0dp"
        android:right="0dp"
        android:top="0dp">
        <shape android:shape="oval">
            <gradient
                android:angle="270"
                android:endColor="@color/transparent"
                android:startColor="#FFFFFF" />
        </shape>
    </item>

    <!-- 挖空内部 -->
    <item
        android:bottom="4dp"
        android:left="4dp"
        android:right="4dp"
        android:top="4dp">
        <shape android:shape="oval">
            <solid android:color="@color/transparent" />
        </shape>
    </item>

</layer-list>
