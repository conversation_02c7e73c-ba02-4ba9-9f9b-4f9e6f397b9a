<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 透明内部 -->
    <item>
        <shape android:shape="oval">
            <solid android:color="@color/transparent" />
        </shape>
    </item>

    <!-- 外层边框 - 创建边框区域 -->
    <item>
        <shape android:shape="oval">
            <solid android:color="#E0FFFFFF" />
        </shape>
    </item>

    <!-- 内层 - 挖空中间，形成边框效果 -->
    <item
        android:bottom="4dp"
        android:left="4dp"
        android:right="4dp"
        android:top="4dp">
        <shape android:shape="oval">
            <solid android:color="@color/transparent" />
        </shape>
    </item>

    <!-- 顶部高光渐变 -->
    <item
        android:bottom="2dp"
        android:left="2dp"
        android:right="2dp"
        android:top="2dp">
        <shape android:shape="oval">
            <gradient
                android:angle="270"
                android:endColor="@color/transparent"
                android:startColor="#F0FFFFFF" />
        </shape>
    </item>

    <!-- 再次挖空内部 -->
    <item
        android:bottom="4dp"
        android:left="4dp"
        android:right="4dp"
        android:top="4dp">
        <shape android:shape="oval">
            <solid android:color="@color/transparent" />
        </shape>
    </item>

</layer-list>
