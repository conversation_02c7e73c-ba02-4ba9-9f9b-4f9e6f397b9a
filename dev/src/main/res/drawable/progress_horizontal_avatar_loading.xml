<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!--未加载的进度区域-->
    <item android:id="@android:id/background">
        <layer-list>
            <item>
                <shape>
                    <!--进度条的圆角-->
                    <corners android:radius="@dimen/dp_8" />
                    <!--未加载的进度区域颜色-->
                    <solid android:color="#FFEF30" />
                </shape>
            </item>

            <item
                android:bottom="@dimen/dp_1"
                android:end="@dimen/dp_1"
                android:start="@dimen/dp_1"
                android:top="@dimen/dp_1">
                <shape>
                    <!--进度条的圆角-->
                    <corners android:radius="@dimen/dp_8" />

                    <!--未加载的进度区域颜色-->
                    <solid android:color="#FFF" />

                </shape>
            </item>

        </layer-list>
    </item>


    <!--已经加载完的进度的区域-->
    <item
        android:id="@android:id/progress">
        <layer-list>
            <item>
                <shape>
                    <!--进度条的圆角-->
                    <corners android:radius="@dimen/dp_8" />
                    <!--未加载的进度区域颜色-->
                    <solid android:color="#00FFFFFF" />
                </shape>
            </item>

            <item
                android:bottom="@dimen/dp_2"
                android:end="@dimen/dp_2"
                android:start="@dimen/dp_2"
                android:top="@dimen/dp_2">

                <scale android:scaleWidth="100%">
                    <shape>
                        <!--进度条的圆角-->
                        <corners android:radius="@dimen/dp_8" />

                        <!--已经加载完的进度的颜色-->
                        <gradient
                            android:endColor="#FFC530"
                            android:startColor="#FFEF30" />
                    </shape>
                </scale>
            </item>

        </layer-list>
    </item>
</layer-list>