<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 主体毛玻璃背景 -->
    <item>
        <shape android:shape="oval">
            <solid android:color="#50FFFFFF" />
        </shape>
    </item>

    <!-- 外层光晕 -->
    <item
        android:bottom="2dp"
        android:left="2dp"
        android:right="2dp"
        android:top="2dp">
        <shape android:shape="oval">
            <gradient
                android:angle="270"
                android:centerColor="#30FFFFFF"
                android:endColor="@color/transparent"
                android:startColor="#70FFFFFF"
                android:type="radial"
                android:gradientRadius="80%p" />
        </shape>
    </item>

    <!-- 顶部高光 -->
    <item
        android:bottom="40%"
        android:left="20%"
        android:right="20%"
        android:top="10%">
        <shape android:shape="oval">
            <gradient
                android:angle="270"
                android:endColor="@color/transparent"
                android:startColor="#90FFFFFF" />
        </shape>
    </item>

    <!-- 中心高光点 -->
    <item
        android:bottom="45%"
        android:left="35%"
        android:right="35%"
        android:top="25%">
        <shape android:shape="oval">
            <solid android:color="#A0FFFFFF" />
        </shape>
    </item>

    <!-- 边缘描边 */
    <item>
        <shape android:shape="oval">
            <stroke
                android:width="0.5dp"
                android:color="#40FFFFFF" />
        </shape>
    </item>

</layer-list>
