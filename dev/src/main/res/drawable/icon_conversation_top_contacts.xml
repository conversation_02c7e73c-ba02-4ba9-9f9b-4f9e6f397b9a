<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="32dp"
    android:height="32dp"
    android:viewportWidth="32"
    android:viewportHeight="32">
  <path
      android:pathData="M16,0L16,0A16,16 0,0 1,32 16L32,16A16,16 0,0 1,16 32L16,32A16,16 0,0 1,0 16L0,16A16,16 0,0 1,16 0z"
      android:fillColor="#F0F0F0"/>
  <path
      android:pathData="M21.25,13C22.493,13 23.5,11.993 23.5,10.75C23.5,9.507 22.493,8.5 21.25,8.5C20.007,8.5 19,9.507 19,10.75C19,11.993 20.007,13 21.25,13Z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#1A1A1A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M12.25,16.75H16"
      android:strokeLineJoin="round"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#1A1A1A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M12.25,19.75H19"
      android:strokeLineJoin="round"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#1A1A1A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M15.726,8.5L13.75,8.5C10,8.5 8.5,10 8.5,13.75V18.25C8.5,22 10,23.5 13.75,23.5H18.25C22,23.5 23.5,22 23.5,18.25V16.041"
      android:strokeLineJoin="round"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#1A1A1A"
      android:strokeLineCap="round"/>
</vector>
