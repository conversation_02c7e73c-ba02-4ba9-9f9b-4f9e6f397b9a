<?xml version="1.0" encoding="utf-8"?>
<resources>

    <attr name="title_text_color" format="color" />
    <attr name="title_divider_color" format="color"/>
    <attr name="bgColor" format="color"/>
    <attr name="loadingText" format="string" />

    <declare-styleable name="MetaRefreshLayout">
        <attr name="lottie_rawRes" format="reference" />
        <attr name="gif" format="reference" />
    </declare-styleable>
    <declare-styleable name="SimpleSwipeRefreshLayout">
        <attr name="max_offset_top" format="dimension" />
        <attr name="trigger_offset_top" format="dimension" />
        <attr name="indicator_overlay" format="boolean"/>
    </declare-styleable>

    <!-- 顶栏 -->
    <declare-styleable name="TitleBarLayout">
        <attr name="title_text" format="string" />
        <attr name="title_text_color" />
        <attr name="background_color" format="reference" />
        <attr name="isDividerVisible" format="boolean" />
        <attr name="title_divider_color" />
        <attr name="rightIcon" format="reference" />
        <attr name="isRightIconVisible" format="boolean" />
        <attr name="back_icon" format="reference" />
        <attr name="back_icon_tint" format="color" />
        <attr name="showRoleDress" format="boolean" />
        <attr name="roleDressText" format="string" />
        <attr name="rightText" format="string"/>
        <attr name="showRightText" format="boolean"/>
        <attr name="showBackIcon" format="boolean" />

    </declare-styleable>
    <!-- 下载进度按钮 -->
    <declare-styleable name="DownloadProgressButton">
        <attr name="progress_btn_radius" format="dimension" />
        <attr name="progress_btn_background_color" format="color" />
        <attr name="progress_btn_background_color_start" format="color" />
        <attr name="progress_btn_background_color_end" format="color" />
        <attr name="progress_btn_background_second_color" format="color" />
        <attr name="progress_btn_text_color" format="color" />
        <attr name="progress_btn_text_size" format="integer" />
        <attr name="progress_btn_text_cover_color" format="color" />
        <attr name="progress_btn_border_width" format="dimension" />
        <attr name="progress_btn_text_drawable" format="reference" />
        <attr name="progress_btn_show_bolder" format="boolean" />
        <attr name="progress_btn_current_text" format="string" />
        <attr name="progress_btn_text_drawable_padding_end" format="dimension" />
        <attr name="progress_btn_progress_dot_size" format="integer" />
    </declare-styleable>
    <!-- 下载进度 -->
    <declare-styleable name="GameLoadingView">
        <attr name="maxProgress" format="integer" />
        <attr name="radius" format="dimension" />
        <attr name="strokeWidth" format="dimension" />
        <attr name="loadRoundRadius" format="dimension" />
        <attr name="backgroundColor" format="color" />
    </declare-styleable>

    <!-- TagTextView -->
    <declare-styleable name="TagTextView">
        <attr name="tagColor" format="color" />
        <attr name="tagRadius" format="dimension" />
    </declare-styleable>

    <declare-styleable name="MyRatingBar">
        <!--星星间距-->
        <attr name="starDistance" format="dimension" />
        <!--星星大小-->
        <attr name="starSize" format="dimension" />
        <!--星星个数-->
        <attr name="starCount" format="integer" />
        <!--星星空图-->
        <attr name="starEmpty" format="reference" />
        <!--星星满图-->
        <attr name="starFill" format="reference" />
    </declare-styleable>

    <!-- 游戏评论 评分view -->
    <declare-styleable name="RatingView">
        <attr name="ratingIsIndicator" format="boolean" />
        <attr name="rating" format="float" />
        <attr name="ratingCount" format="integer" />
        <attr name="ratingEmpty" format="reference" />
        <attr name="ratingHalf" format="reference" />
        <attr name="ratingFilled" format="reference" />
        <attr name="ratingSize" format="dimension" />
        <attr name="ratingMargin" format="dimension" />
    </declare-styleable>

    <declare-styleable name="QRCoverView">

        <attr name="cornerLength" format="dimension" />
        <attr name="cornerWidth" format="dimension" />

        <attr name="cornerColor" format="color" />
        <attr name="lineColor" format="color" />
        <attr name="lineWidth" format="dimension" />

        <attr name="laser" format="reference" />
        <attr name="laserLineHeight" format="dimension" />
        <attr name="useLaser" format="boolean" />

        <attr name="maskLayerClipWidth" format="dimension"/>
        <attr name="maskLayerClipHeight" format="dimension"/>
        <attr name="maskLayerColor" format="color"/>
        <attr name="maskLayerCorner" format="dimension"/>

        <attr name="mode" format="enum">
            <enum name="full" value="0"/>
            <enum name="mask" value="1"/>
        </attr>

    </declare-styleable>

    <declare-styleable name="MaxHeightRecyclerView">
        <attr name="maxHeight" format="dimension" />
    </declare-styleable>

    <declare-styleable name="MaxWidthEpoxyRecyclerView">
        <attr name="maxWidth" format="dimension" />
    </declare-styleable>

    <declare-styleable name="SettingLineView">
        <attr name="title_text_color" />
        <attr name="desc_text_color" />
    </declare-styleable>

    <!-- 点击展开文本 -->
    <declare-styleable name="MgsTabLayout">
        <attr name="tabType" format="enum">
            <enum name="expandHorizontal" value="1" />
            <enum name="expandVertical" value="2" />
        </attr>
    </declare-styleable>

    <!-- 点击展开文本 -->
    <declare-styleable name="FolderTextView">
        <attr name="foldText" format="string" />
        <attr name="unFoldText" format="string" />
        <attr name="foldLine" format="integer" />
        <attr name="tailTextColor" format="color" />
        <attr name="canFoldAgain" format="boolean" />
        <attr name="unFold_typeface" format="reference" />
    </declare-styleable>

    <!-- 流式标签布局 -->
    <declare-styleable name="FlowLayout">
        <attr name="horizontalSpacing" format="dimension" />
        <attr name="verticalSpacing" format="dimension" />
        <attr name="itemSize" format="dimension" />
        <attr name="itemColor" format="color" />
        <attr name="backgroundResource" format="reference" />
        <attr name="textPaddingH" format="dimension" />
        <attr name="textPaddingV" format="dimension" />
        <attr name="maxLines" format="integer" />
    </declare-styleable>

    <declare-styleable name="LoadingView">
        <attr name="loadingColor" format="color" />
        <attr name="emptyColor" format="color" />
        <attr name="loadingProgressSize" format="dimension" />
        <attr name="loadingText" />

        <attr name="net_error_tip_text_color" format="color" />
        <attr name="net_error_retry_button_text_color" format="color" />
        <attr name="net_error_retry_button_background" format="reference" />

    </declare-styleable>

    <declare-styleable name="LoadingButton">
        <attr name="buttonText" format="string" />
        <attr name="buttonTextStyle" format="reference" />
        <attr name="loadingText" />
        <attr name="loadingTextStyle" format="reference" />
    </declare-styleable>

    <declare-styleable name="MetaTextView">
        <attr name="uiLineHeight" format="dimension" />
    </declare-styleable>

    <declare-styleable name="FocusableScrollView">
        <attr name="focusAt" format="reference" />
    </declare-styleable>

    <!-- 描边文字 -->
    <declare-styleable name="StrokeTextView">
        <attr name="textStrokeColor" format="color" />
        <attr name="textStrokeWidth" format="dimension" />
    </declare-styleable>

    <!-- 描边文字 -->
    <declare-styleable name="MgsInputView">
        <attr name="hasVoice" format="boolean" />
    </declare-styleable>

    <declare-styleable name="HollowTextView">
        <attr name="text" format="string"/>
        <attr name="textSize" format="dimension"/>
        <attr name="bgColor"/>
        <attr name="cornerRadius" format="dimension"/>
        <attr name="roundTopLeft" format="boolean"/>
        <attr name="roundTopRight" format="boolean"/>
        <attr name="roundBottomLeft" format="boolean"/>
        <attr name="roundBottomRight" format="boolean"/>
    </declare-styleable>

    <declare-styleable name="pickerview">
        <attr name="wheelview_gravity">
            <enum name="center" value="17"/>
            <enum name="left" value="3"/>
            <enum name="right" value="5"/>
        </attr>
        <attr name="wheelview_center_textSize" format="dimension"/>
        <attr name="wheelview_outer_textSize" format="dimension"/>
        <attr name="wheelview_textColorOut" format="color"/>
        <attr name="wheelview_textColorCenter" format="color"/>
        <attr name="wheelview_dividerColor" format="color"/>
        <attr name="wheelview_dividerWidth" format="dimension"/>
        <attr name="wheelview_lineSpacingMultiplier" format="float"/>
    </declare-styleable>

    <declare-styleable name="PreferenceItemView">

        <attr name="style">
            <enum name="custom" value="0"/>
            <enum name="text" value="1"/>
            <enum name="toggle" value="2"/>
            <enum name="icon" value="3"/>
            <enum name="edit" value="4"/>
        </attr>

        <attr name="label" format="string" />

        <attr name="hint" format="string" />
        <attr name="icon_url" format="string" />

        <attr name="text"/>

        <attr name="show_divider" format="boolean" />

        <attr name="navigatiable" format="boolean" />

        <attr name="toggled" format="boolean" />

    </declare-styleable>

    <declare-styleable name="CircleProgressBar">
        <attr name="line_count" format="integer|reference"/>
        <attr name="line_width" format="dimension|reference"/>
        <attr name="progress_start_color" format="color|reference"/>
        <attr name="progress_end_color" format="color|reference"/>
        <attr name="progress_text_color" format="color|reference"/>
        <attr name="progress_text_size" format="dimension|reference"/>
        <attr name="progress_stroke_width" format="dimension|reference"/>
        <attr name="progress_background_color" format="color|reference"/>
        <attr name="progress_start_degree" format="integer|reference"/>
        <attr name="drawBackgroundOutsideProgress" format="boolean|reference"/>

        <attr name="progress_blur_radius" format="dimension|reference"/>
        <attr name="progress_blur_style">
            <enum name="normal" value="0"/>
            <enum name="solid" value="1"/>
            <enum name="outer" value="2"/>
            <enum name="inner" value="3"/>
        </attr>

        <attr name="progress_style">
            <enum name="line" value="0"/>
            <enum name="solid" value="1"/>
            <enum name="solid_line" value="2"/>
        </attr>

        <attr name="progress_shader">
            <enum name="linear" value="0"/>
            <enum name="radial" value="1"/>
            <enum name="sweep" value="2"/>
        </attr>

        <attr name="progress_stroke_cap">
            <enum name="butt" value="0"/>
            <enum name="round" value="1"/>
            <enum name="square" value="2"/>
        </attr>

    </declare-styleable>

    <declare-styleable name="ExpandableTextView">
        <!--收缩时hint的左移距离-->
        <attr name="etv_ToExpandHintOffset" format="dimension" />
        <!--点击hint后面的区域也可以进行展开收起/接到onClick事件-->
        <attr name="etv_ExtendClickScope" format="boolean" />
        <attr name="etv_MaxLinesOnShrink" format="reference|integer" />
        <!--default is ..-->
        <attr name="etv_EllipsisHint" format="reference|string" />
        <!--"to expand" hint string, default is "Expand"-->
        <attr name="etv_ToExpandHint" format="reference|string" />
        <!--"to shrink" hint string, default is "Shrink"-->
        <attr name="etv_ToShrinkHint" format="reference|string" />
        <attr name="etv_ToShrinkHintBold" format="boolean" />
        <attr name="etv_ToExpandHintBold" format="boolean" />
        <!--gap between "toExpandHint" and "trimmed text"-->
        <attr name="etv_GapToExpandHint" format="reference|string" />
        <!--gap between "toShrinkHint" and "trimmed text"-->
        <attr name="etv_GapToShrinkHint" format="reference|string" />
        <!--点击整个item是否展开/收起，与etv_EnableToggleClick互斥 -->
        <attr name="etv_WholeToggle" format="reference|boolean" />
        <!--点击hint是否展开/收起，与etv_WholeToggle互斥-->
        <attr name="etv_EnableToggleClick" format="reference|boolean" />
        <!--if show "toExpandHint"-->
        <attr name="etv_ToExpandHintShow" format="reference|boolean" />
        <!--if show "toShrinkHint"-->
        <attr name="etv_ToShrinkHintShow" format="reference|boolean" />
        <!--text color of "toExpandHint"-->
        <attr name="etv_ToExpandHintColor" format="reference|color" />
        <!--text color of "toShrinkHint"-->
        <attr name="etv_ToShrinkHintColor" format="reference|color" />
        <!--background color if "toExpandHint" pressed-->
        <attr name="etv_ToExpandHintColorBgPressed" format="reference|color" />
        <!--background color if "toShrinkHint" pressed-->
        <attr name="etv_ToShrinkHintColorBgPressed" format="reference|color" />
        <!--init state, default is shrink-->
        <attr name="etv_InitState" format="enum">
            <enum name="shrink" value="0" />
            <enum name="expand" value="1" />
        </attr>
    </declare-styleable>
    <declare-styleable name="REditText">
        <attr name="edit_background_color" format="reference|color" />
        <attr name="edit_foreground_color" format="reference|color" />
        <attr name="edit_text_color" format="reference|color" />
    </declare-styleable>

    <declare-styleable name="ShimmerFrameLayout">
        <attr format="boolean" name="shimmer_clip_to_children"/>
        <attr format="boolean" name="shimmer_colored"/>
        <attr format="color" name="shimmer_base_color"/>
        <attr format="color" name="shimmer_highlight_color"/>
        <attr format="float" name="shimmer_base_alpha"/>
        <attr format="float" name="shimmer_highlight_alpha"/>
        <attr format="boolean" name="shimmer_auto_start"/>
        <attr format="integer" name="shimmer_duration"/>
        <attr format="integer" name="shimmer_repeat_count"/>
        <attr format="integer" name="shimmer_repeat_delay"/>
        <attr format="enum" name="shimmer_repeat_mode">
            <enum name="restart" value="1"/>
            <enum name="reverse" value="2"/>
        </attr>
        <attr format="enum" name="shimmer_direction">
            <enum name="left_to_right" value="0"/>
            <enum name="top_to_bottom" value="1"/>
            <enum name="right_to_left" value="2"/>
            <enum name="bottom_to_top" value="3"/>
        </attr>
        <attr format="float" name="shimmer_dropoff"/>
        <attr format="dimension" name="shimmer_fixed_width"/>
        <attr format="dimension" name="shimmer_fixed_height"/>
        <attr format="float" name="shimmer_intensity"/>
        <attr format="float" name="shimmer_width_ratio"/>
        <attr format="float" name="shimmer_height_ratio"/>
        <attr format="enum" name="shimmer_shape">
            <enum name="linear" value="0"/>
            <enum name="radial" value="1"/>
        </attr>
        <attr format="float" name="shimmer_tilt"/>
    </declare-styleable>

    <declare-styleable name="ShimmerView">
        <attr name="shimmer_width" format="dimension"/>
    </declare-styleable>

    <declare-styleable name="ApmMonitor">
        <attr name="speed_monitor" format="boolean" />
    </declare-styleable>

    <declare-styleable name="ImageCropView">
        <!--遮罩颜色-->
        <attr name="bgColor"/>
        <attr name="maskColor" format="color" />
        <attr name="borderColor" format="color" />
        <attr name="radius" />
        <attr name="img_angle" format="dimension" />
        <attr name="maxScale" format="float" />
        <attr name="doubleClickScale" format="float" />
        <attr name="ratioWidth" format="float" />
        <attr name="ratioHeight" format="float" />
        <attr name="enableZoom" format="boolean" />
        <attr name="enableDoubleClick" format="boolean" />
        <attr name="cropBoxHorPadding" format="dimension" />
    </declare-styleable>
    <declare-styleable name="ucrop_UCropView">


        <attr format="float" name="ucrop_aspect_ratio_x"/>
        <attr format="float" name="ucrop_aspect_ratio_y"/>


        <attr format="boolean" name="ucrop_show_oval_crop_frame"/>
        <attr format="boolean" name="ucrop_circle_dimmed_layer"/>
        <attr format="color" name="ucrop_dimmed_color"/>

        <attr format="dimension" name="ucrop_grid_stroke_size"/>
        <attr format="color" name="ucrop_grid_color"/>
        <attr format="integer" name="ucrop_grid_row_count"/>
        <attr format="integer" name="ucrop_grid_column_count"/>
        <attr format="boolean" name="ucrop_show_grid"/>

        <attr format="dimension" name="ucrop_frame_stroke_size"/>
        <attr format="color" name="ucrop_frame_color"/>
        <attr format="boolean" name="ucrop_show_frame"/>

    </declare-styleable>


    <declare-styleable name="StyledImageView">
        <attr format="enum" name="scaleStyle">
            <enum name="FixedWidth" value="1" />
            <enum name="FixedWidthAlignBottom" value="2" />
            <!--指定缩放因子，并顶部对齐-->
            <enum name="ScaleAlignTop" value="5" />
            <!--指定相对于View大小的缩放因子，并顶部对齐-->
            <enum name="ScaleBaseViewSizeAlignTop" value="4" />
        </attr>

        <attr name="scaleFactor" format="float"/>
    </declare-styleable>

    <declare-styleable name="MetaLikeView">
        <attr name="likeIcon" format="reference" />
        <attr name="likeText" format="string" />
        <attr name="likeBackground" format="reference" />
        <attr name="showIcon" format="boolean" />
    </declare-styleable>

    <!-- TagContainerView 自定义属性 -->
    <declare-styleable name="TagContainerView">
        <!-- 最大标签显示数量 -->
        <attr name="maxTagCount" format="integer" />
        <!-- 标签文字颜色 -->
        <attr name="tagTextColor" format="color" />
        <!-- 标签背景颜色 -->
        <attr name="tagBackgroundColor" format="color" />
        <!-- 标签边框颜色 -->
        <attr name="tagStrokeColor" format="color" />
        <!-- 标签边框宽度 -->
        <attr name="tagStrokeWidth" format="dimension" />
        <!-- 标签圆角半径 -->
        <attr name="tagCornerRadius" format="dimension" />
        <!-- 标签水平内边距 -->
        <attr name="tagPaddingHorizontal" format="dimension" />
        <!-- 标签垂直内边距 -->
        <attr name="tagPaddingVertical" format="dimension" />
        <!-- 标签之间的右边距 -->
        <attr name="tagMarginEnd" format="dimension" />
        <!-- 标签文字大小 -->
        <attr name="tagTextSize" format="dimension" />
    </declare-styleable>

    <declare-styleable name="UserPrivacyAgreementView">
        <attr name="android:textColor" />
    </declare-styleable>

    <declare-styleable name="UserAvatarView">
        <attr name="size_level" format="integer" />
    </declare-styleable>

    <declare-styleable name="MetallicBorderView">
        <!-- 边框厚度 -->
        <attr name="borderThickness" format="dimension" />
        <!-- 光照角度（度数，0-360） -->
        <attr name="lightAngle" format="float" />
        <!-- 圆角半径 -->
        <attr name="cornerRadius" />
        <!-- 高光颜色 -->
        <attr name="highlightColor" format="color" />
        <!-- 阴影颜色 -->
        <attr name="shadowColor" format="color" />
        <!-- 基础金属色 -->
        <attr name="metallicBaseColor" format="color" />
        <!-- 高光强度 (0.0-1.0) -->
        <attr name="highlightIntensity" format="float" />
        <!-- 阴影强度 (0.0-1.0) -->
        <attr name="shadowIntensity" format="float" />
    </declare-styleable>

</resources>