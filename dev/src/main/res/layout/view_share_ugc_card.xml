<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_long_image_container"
    android:layout_width="@dimen/dp_300"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:visibility="invisible"
    app:layout_constraintTop_toTopOf="parent"
    tools:visibility="visible">

    <ImageView
        android:id="@+id/iv_long_image_game_banner_single"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_165"
        android:scaleType="centerCrop"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_long_image_game_banner_double_1"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_165"
        android:scaleType="centerCrop"
        app:layout_constraintEnd_toStartOf="@id/iv_long_image_game_banner_double_2"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="gone" />

    <ImageView
        android:id="@+id/iv_long_image_game_banner_double_2"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_165"
        android:scaleType="centerCrop"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_long_image_game_banner_double_1"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="gone" />

    <ImageView
        android:id="@+id/iv_long_image_game_banner_triple_1"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_165"
        android:scaleType="centerCrop"
        app:layout_constraintEnd_toStartOf="@id/iv_long_image_game_banner_triple_2"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="gone" />

    <ImageView
        android:id="@+id/iv_long_image_game_banner_triple_2"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_165"
        android:scaleType="centerCrop"
        app:layout_constraintEnd_toStartOf="@id/iv_long_image_game_banner_triple_3"
        app:layout_constraintStart_toEndOf="@id/iv_long_image_game_banner_triple_1"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="gone" />

    <ImageView
        android:id="@+id/iv_long_image_game_banner_triple_3"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_165"
        android:scaleType="centerCrop"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_long_image_game_banner_triple_2"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="gone" />

    <View
        android:id="@+id/v_long_image_game_banner_mask"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_166"
        android:background="@drawable/ic_game_share_banner_mask"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_game_share_desc_bg"
        app:layout_constraintBottom_toBottomOf="parent" />

    <com.socialplay.gpark.ui.view.RoundImageView
        android:id="@+id/iv_long_image_game_icon"
        android:layout_width="@dimen/dp_72"
        android:layout_height="@dimen/dp_72"
        android:layout_marginStart="@dimen/dp_12"
        android:layout_marginTop="@dimen/dp_115"
        android:scaleType="centerCrop"
        app:cornerRadii="@dimen/dp_16"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_long_image_game_name"
        style="@style/MetaTextView.S15.PoppinsMedium600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_10"
        android:layout_marginEnd="@dimen/dp_16"
        android:minHeight="@dimen/dp_20"
        android:singleLine="true"
        android:textSize="@dimen/dp_15"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/tv_long_image_game_pv"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_long_image_game_icon"
        app:layout_constraintTop_toTopOf="@id/iv_long_image_game_icon"
        app:layout_constraintVertical_chainStyle="packed"
        app:uiLineHeight="@dimen/dp_20"
        tools:text="@tools:sample/lorem/random" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_long_image_game_pv"
        style="@style/MetaTextView.S12"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_12"
        android:drawableStart="@drawable/ic_game_share_pv"
        android:drawablePadding="@dimen/dp_4"
        android:gravity="center"
        android:includeFontPadding="false"
        android:minHeight="@dimen/dp_18"
        android:paddingHorizontal="@dimen/dp_4"
        android:textColor="@color/color_666666"
        android:textSize="@dimen/dp_12"
        app:layout_constraintBottom_toBottomOf="@id/iv_long_image_game_icon"
        app:layout_constraintStart_toStartOf="@id/tv_long_image_game_name"
        app:layout_constraintTop_toBottomOf="@id/tv_long_image_game_name"
        app:layout_constraintVertical_chainStyle="packed"
        app:uiLineHeight="@dimen/dp_18"
        tools:text="9999 Players" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_long_image_game_like"
        style="@style/MetaTextView.S12"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_4"
        android:drawableStart="@drawable/ic_game_share_like"
        android:drawablePadding="@dimen/dp_4"
        android:gravity="center"
        android:includeFontPadding="false"
        android:minHeight="@dimen/dp_18"
        android:paddingHorizontal="@dimen/dp_4"
        android:textColor="@color/color_666666"
        android:textSize="@dimen/dp_12"
        app:layout_constraintBottom_toBottomOf="@id/tv_long_image_game_pv"
        app:layout_constraintStart_toEndOf="@id/tv_long_image_game_pv"
        app:layout_constraintTop_toTopOf="@id/tv_long_image_game_pv"
        app:uiLineHeight="@dimen/dp_18"
        tools:text="9999" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_long_image_game_desc"
        style="@style/MetaTextView.S12"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_12"
        android:layout_marginTop="@dimen/dp_16"
        android:ellipsize="end"
        android:lines="3"
        android:minHeight="@dimen/dp_20"
        android:textSize="@dimen/dp_12"
        app:layout_constraintTop_toBottomOf="@id/iv_long_image_game_icon"
        app:uiLineHeight="@dimen/dp_20"
        tools:text="@tools:sample/lorem/random" />

    <ImageView
        android:id="@+id/iv_long_image_qr_code"
        android:layout_width="@dimen/dp_100"
        android:layout_height="@dimen/dp_100"
        android:layout_marginStart="@dimen/dp_42"
        android:layout_marginTop="@dimen/dp_26"
        android:background="@drawable/bg_game_share_qr"
        android:padding="@dimen/dp_8"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_long_image_game_desc" />

    <ImageView
        android:id="@+id/iv_long_image_logo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_8"
        android:src="@drawable/ic_game_share_logo"
        app:layout_constraintBottom_toTopOf="@id/tv_long_image_scan_tips"
        app:layout_constraintStart_toEndOf="@id/iv_long_image_qr_code"
        app:layout_constraintTop_toTopOf="@id/iv_long_image_qr_code"
        app:layout_constraintVertical_chainStyle="packed" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_long_image_scan_tips"
        style="@style/MetaTextView.S12.PoppinsMedium500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginTop="@dimen/dp_8"
        android:layout_marginEnd="@dimen/dp_16"
        android:gravity="center_vertical"
        android:text="@string/share_image_qr"
        android:textSize="@dimen/dp_12"
        app:layout_constraintBottom_toBottomOf="@id/iv_long_image_qr_code"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_long_image_qr_code"
        app:layout_constraintTop_toBottomOf="@id/iv_long_image_logo"
        app:uiLineHeight="@dimen/dp_18" />

    <Space
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_40"
        app:layout_constraintTop_toBottomOf="@id/iv_long_image_qr_code" />

</androidx.constraintlayout.widget.ConstraintLayout>