<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="@dimen/dp_6"
    android:paddingBottom="@dimen/dp_12">

    <TextView
        android:id="@+id/tv_room_tag"
        style="@style/MetaTextView.S12.PoppinsMedium500"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_36"
        android:gravity="center"
        android:textColor="@color/textColorPrimary"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>