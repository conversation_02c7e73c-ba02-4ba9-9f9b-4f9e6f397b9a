<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/bg_corner_top_20_white"
    android:clipToPadding="false">

    <!-- 顶部标题栏（需写死，抽取到strings） -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_48">

        <TextView
            android:id="@+id/tv_title"
            style="@style/MetaTextView.S14.PoppinsSemiBold600"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_centerInParent="true"
            android:text="@string/payment_title"/>

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="@dimen/dp_24"
            android:layout_height="@dimen/dp_24"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="@dimen/dp_16"
            android:src="@drawable/ic_close_page" />
    </RelativeLayout>

    <!-- 商品信息区（示例内容直接写死） -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingStart="@dimen/dp_16"
        android:paddingEnd="@dimen/dp_16"
        android:paddingTop="@dimen/dp_12"
        android:paddingBottom="@dimen/dp_12"
        android:gravity="center_vertical">

        <ImageView
            android:id="@+id/iv_product"
            android:layout_width="@dimen/dp_68"
            android:layout_height="@dimen/dp_68"
            android:scaleType="centerCrop"
            android:background="@drawable/bg_f8f8f8_round_12" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:paddingStart="@dimen/dp_12">

            <TextView
                android:id="@+id/tv_product_name"
                style="@style/MetaTextView.S14.PoppinsMedium500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="w1chilada Casserole tuikss" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginTop="@dimen/dp_2">

                <ImageView
                    android:id="@+id/iv_coin"
                    android:layout_width="@dimen/dp_18"
                    android:layout_height="@dimen/dp_18"
                    android:src="@drawable/icon_g_coin_size_20" />

                <TextView
                    android:id="@+id/tv_price"
                    style="@style/MetaTextView.S14.PoppinsSemiBold600"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="999,999"
                    android:layout_marginStart="@dimen/dp_4" />
            </LinearLayout>

            <TextView
                android:id="@+id/tv_sold"
                style="@style/MetaTextView.S10.PoppinsRegular400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Sold 230,103"
                android:layout_marginTop="@dimen/dp_2" />
        </LinearLayout>
    </LinearLayout>

    <!-- 分割线 -->
    <View
        android:layout_width="@dimen/dp_343"
        android:layout_height="@dimen/dp_0_5"
        android:background="@color/color_F0F0F0"
        android:layout_gravity="center_horizontal" />

    <!-- 游戏名与价格（示例内容直接写死） -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingStart="@dimen/dp_16"
        android:paddingEnd="@dimen/dp_16"
        android:paddingTop="@dimen/dp_12"
        android:paddingBottom="@dimen/dp_12"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/tv_game_name"
            style="@style/MetaTextView.S14.PoppinsRegular400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Game name" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <ImageView
                android:id="@+id/iv_game_coin"
                android:layout_width="@dimen/dp_16"
                android:layout_height="@dimen/dp_16"
                android:src="@drawable/icon_g_coin_size_20" />

            <TextView
                android:id="@+id/tv_game_price"
                style="@style/MetaTextView.S12.PoppinsSemiBold600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="999,999"
                android:layout_marginStart="@dimen/dp_4" />
        </LinearLayout>
    </LinearLayout>

    <!-- 余额信息（需写死，抽取到strings） -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingStart="@dimen/dp_16"
        android:paddingEnd="@dimen/dp_16"
        android:paddingTop="0dp"
        android:paddingBottom="@dimen/dp_12"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/tv_balance_label"
            style="@style/MetaTextView.S14.PoppinsRegular400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/payment_balance_label" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <ImageView
                android:id="@+id/iv_balance_coin"
                android:layout_width="@dimen/dp_16"
                android:layout_height="@dimen/dp_16"
                android:src="@drawable/icon_g_coin_size_20" />

            <TextView
                android:id="@+id/tv_balance"
                style="@style/MetaTextView.S12.PoppinsSemiBold600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="999,999"
                android:layout_marginStart="@dimen/dp_4" />
        </LinearLayout>
    </LinearLayout>

    <!-- 分割线 -->
    <View
        android:layout_width="@dimen/dp_343"
        android:layout_height="@dimen/dp_0_5"
        android:background="@color/color_F0F0F0"
        android:layout_gravity="center_horizontal" />

    <!-- 创作者信息（示例内容直接写死） -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingStart="@dimen/dp_16"
        android:paddingEnd="@dimen/dp_16"
        android:paddingTop="@dimen/dp_12"
        android:paddingBottom="@dimen/dp_12"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/tv_creator_label"
            style="@style/MetaTextView.S14.PoppinsRegular400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Creator" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <ImageView
                android:id="@+id/iv_creator_avatar"
                android:layout_width="@dimen/dp_18"
                android:layout_height="@dimen/dp_18"
                android:src="@drawable/icon_default_avatar" />

            <TextView
                android:id="@+id/tv_creator_name"
                style="@style/MetaTextView.S12.PoppinsRegular400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="SEThomson12"
                android:layout_marginStart="@dimen/dp_4" />
        </LinearLayout>
    </LinearLayout>

    <!-- 支付按钮（需写死，抽取到strings） -->
    <Button
        android:id="@+id/btn_pay"
        style="@style/Button.S16.PoppinsSemiBold600"
        android:layout_width="@dimen/dp_279"
        android:layout_height="@dimen/dp_48"
        android:layout_gravity="center_horizontal"
        android:text="@string/payment_btn_pay"
        android:background="@drawable/bg_ffef30_round_100"
        android:layout_marginTop="@dimen/dp_12" />

    <!-- Home Indicator -->
    <View
        android:layout_width="@dimen/dp_134"
        android:layout_height="@dimen/dp_5"
        android:layout_gravity="center_horizontal"
        android:background="@color/black_80"
        android:layout_marginTop="@dimen/dp_12"
        android:layout_marginBottom="@dimen/dp_8"
        android:alpha="0.8" />

</LinearLayout> 