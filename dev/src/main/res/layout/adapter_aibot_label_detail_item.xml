<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:paddingVertical="@dimen/dp_4"
    android:background="@drawable/bg_white_5_round_stroke_1"
    android:paddingHorizontal="@dimen/dp_8">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/icon_tag"
        android:layout_width="@dimen/dp_15"
        android:layout_height="@dimen/dp_15"
        android:layout_marginRight="@dimen/dp_4"
        android:layout_centerVertical="true" />

    <TextView
        android:id="@+id/tvTableName"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:layout_toRightOf="@+id/icon_tag"
        android:fontFamily="@font/poppins_regular_400"
        android:gravity="center"
        android:includeFontPadding="false"
        android:ellipsize="end"
        android:textColor="@color/white_90"
        android:textSize="@dimen/dp_12"
        tools:text="PlayfulPlayful" />


</RelativeLayout>
