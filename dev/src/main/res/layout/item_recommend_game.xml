<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingVertical="@dimen/dp_7">

    <ImageView
        android:id="@+id/iv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="166:136"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.RoundImageView
        android:id="@+id/iv_like_bg"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:bottomLeftRadius="12dp"
        app:bottomRightRadius="12dp"
        app:layout_constraintBottom_toBottomOf="@id/iv"
        app:layout_constraintLeft_toLeftOf="@id/iv"
        app:layout_constraintRight_toRightOf="@id/iv"
        app:layout_constraintTop_toTopOf="@id/iv"
        app:topLeftRadius="0dp"
        app:topRightRadius="0dp" />

    <!--    <View-->
    <!--        android:id="@+id/vBgTop"-->
    <!--        android:layout_width="0dp"-->
    <!--        android:layout_height="@dimen/dp_34"-->
    <!--        android:background="@drawable/bg_recommend_shape"-->
    <!--        android:clickable="false"-->
    <!--        app:layout_constraintBottom_toBottomOf="@id/iv"-->
    <!--        app:layout_constraintLeft_toLeftOf="@id/iv"-->
    <!--        app:layout_constraintRight_toRightOf="@id/iv" />-->

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvNew"
        android:layout_width="43dp"
        android:layout_height="20dp"
        android:background="@drawable/shape_bg_red_new"
        android:gravity="center"
        android:text="@string/tag_new"
        android:textColor="#fff"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.MetaLikeView
        android:id="@+id/mlvScore"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginBottom="@dimen/dp_8"
        app:layout_constraintBottom_toBottomOf="@id/iv"
        app:layout_constraintLeft_toLeftOf="@id/iv"
        app:likeIcon="@drawable/icon_score"
        app:likeText="5.0" />

    <com.socialplay.gpark.ui.view.MetaLikeView
        android:id="@+id/mlvLike"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginBottom="@dimen/dp_8"
        app:layout_constraintBottom_toBottomOf="@id/iv"
        app:layout_constraintLeft_toRightOf="@id/mlvScore"
        app:likeIcon="@drawable/icon_item_like"
        app:likeText="233" />

    <com.socialplay.gpark.ui.view.MetaLikeView
        android:id="@+id/mlvPlayerNum"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginBottom="@dimen/dp_8"
        app:layout_constraintBottom_toBottomOf="@id/iv"
        app:layout_constraintLeft_toRightOf="@id/mlvLike"
        app:likeIcon="@drawable/icon_player_num"
        app:likeText="233"
        app:showIcon="false" />

    <!--    <TextView-->
    <!--        android:id="@+id/tvScore"-->
    <!--        style="@style/MetaTextView.S12.PoppinsRegular400"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:layout_marginStart="8dp"-->
    <!--        android:layout_marginBottom="@dimen/dp_6"-->
    <!--        android:drawableLeft="@drawable/icon_score"-->
    <!--        android:drawablePadding="2dp"-->
    <!--        android:gravity="center_vertical"-->
    <!--        android:textColor="#fff"-->
    <!--        app:layout_constraintBottom_toBottomOf="@id/iv"-->
    <!--        app:layout_constraintLeft_toLeftOf="@id/iv"-->
    <!--        tools:text="5.0" />-->

    <!--    <TextView-->
    <!--        android:id="@+id/tvLike"-->
    <!--        style="@style/MetaTextView.S12.PoppinsRegular400"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:layout_marginStart="8dp"-->
    <!--        android:layout_marginBottom="@dimen/dp_6"-->
    <!--        android:drawableLeft="@drawable/icon_recommend_like"-->
    <!--        android:drawablePadding="2dp"-->
    <!--        android:gravity="center_vertical"-->
    <!--        android:textColor="#fff"-->
    <!--        app:layout_constraintBottom_toBottomOf="@id/iv"-->
    <!--        app:layout_constraintLeft_toRightOf="@id/tvScore"-->
    <!--        tools:text="9999" />-->

    <!--    <TextView-->
    <!--        android:id="@+id/tvPlayerNum"-->
    <!--        style="@style/MetaTextView.S12.PoppinsRegular400"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:layout_marginStart="8dp"-->
    <!--        android:layout_marginBottom="@dimen/dp_6"-->
    <!--        android:drawableLeft="@drawable/icon_player_num"-->
    <!--        android:drawablePadding="2dp"-->
    <!--        android:gravity="center_vertical"-->
    <!--        android:textColor="#fff"-->
    <!--        app:layout_constraintBottom_toBottomOf="@id/iv"-->
    <!--        app:layout_constraintLeft_toRightOf="@id/tvLike"-->
    <!--        tools:text="9999" />-->

    <TextView
        android:id="@+id/tvTitle"
        style="@style/MetaTextView.S13.PoppinsRegular400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_4"
        android:singleLine="true"
        app:layout_constraintLeft_toLeftOf="@id/iv"
        app:layout_constraintRight_toRightOf="@id/iv"
        app:layout_constraintTop_toBottomOf="@id/iv"
        tools:text="Copycat ware SmoothieCopycat ware SmoothieCopycat ware SmoothieCopycat ware SmoothieCopycat ware SmoothieCopycat ware SmoothieCopycat ware SmoothieCopycat ware SmoothieCopycat ware Smoothie" />

    <!-- 标签区 -->
    <com.socialplay.gpark.ui.view.TagContainerView
        android:id="@+id/layout_tags"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_6"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvTitle" />

    <ImageView
        android:id="@+id/ivIcon"
        android:layout_width="18dp"
        android:layout_height="18dp"
        android:layout_marginTop="@dimen/dp_4"
        app:layout_constraintLeft_toLeftOf="@id/iv"
        app:layout_constraintTop_toBottomOf="@id/layout_tags" />

    <TextView
        android:id="@+id/tvName"
        style="@style/MetaTextView.S12.PoppinsRegular400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="4dp"
        android:layout_marginTop="@dimen/dp_4"
        android:singleLine="true"
        android:textColor="#666"
        app:layout_constraintLeft_toRightOf="@id/ivIcon"
        app:layout_constraintRight_toRightOf="@id/iv"
        app:layout_constraintTop_toBottomOf="@id/layout_tags"
        tools:text="Copycat ware SmoothieCopycat ware SmoothieCopycat ware SmoothieCopycat ware SmoothieCopycat ware SmoothieCopycat ware SmoothieCopycat ware SmoothieCopycat ware SmoothieCopycat ware Smoothie" />

</androidx.constraintlayout.widget.ConstraintLayout>