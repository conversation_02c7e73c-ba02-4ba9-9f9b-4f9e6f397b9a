<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/dp_90"
    android:layout_height="@dimen/dp_90"
    android:id="@+id/itemRoot"
    tools:background="@drawable/bg_round_16_9242ff_stroke_2">

    <ImageView
        android:id="@+id/ivGift"
        android:layout_width="@dimen/dp_48"
        android:layout_height="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_6"
        android:scaleType="centerInside"
        android:src="@drawable/ic_single_flower"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvFlowerCount"
        style="@style/MetaTextView.S10.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:gravity="center_vertical"
        android:text="@string/send_flower_dialog_a_flower"
        android:textColor="@color/color_333333"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivGift" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvCoins"
        style="@style/MetaTextView.S10.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawableStart="@drawable/icon_g_coin_size_14"
        android:drawablePadding="@dimen/dp_4"
        android:gravity="center_vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvFlowerCount"
        tools:text="10"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvMinimumDesc"
        style="@style/MetaTextView.S9.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:gravity="center_vertical"
        android:text="@string/send_flower_dialog_minimum_send_flowers_desc"
        android:textColor="@color/color_B3B3B3"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvFlowerCount"
        tools:visibility="gone" />
</androidx.constraintlayout.widget.ConstraintLayout>