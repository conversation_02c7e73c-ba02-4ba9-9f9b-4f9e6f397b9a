<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground"
    android:paddingVertical="@dimen/dp_12"
    android:paddingHorizontal="@dimen/dp_16">

    <!-- 支付方式图标 -->
    <ImageView
        android:id="@+id/ivPaymentIcon"
        android:layout_width="@dimen/dp_32"
        android:layout_height="@dimen/dp_32"
        android:scaleType="centerCrop"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:src="@drawable/icon_coin" />

    <!-- 支付方式名称 -->
    <TextView
        android:id="@+id/tvPaymentName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_12"
        android:layout_marginEnd="@dimen/dp_12"
        android:text="@string/payment_method_coin"
        android:textColor="@color/textColorPrimary"
        android:textSize="@dimen/sp_16"
        app:layout_constraintEnd_toStartOf="@id/ivSelected"
        app:layout_constraintStart_toEndOf="@id/ivPaymentIcon"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <!-- 选中状态图标 -->
    <ImageView
        android:id="@+id/ivSelected"
        android:layout_width="@dimen/dp_20"
        android:layout_height="@dimen/dp_20"
        android:src="@drawable/ic_check_circle"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:tint="@color/colorPrimary"
        tools:visibility="visible" />

    <!-- 分割线 -->
    <View
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:layout_marginStart="@dimen/dp_44"
        android:background="@color/color_F0F0F0"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout> 