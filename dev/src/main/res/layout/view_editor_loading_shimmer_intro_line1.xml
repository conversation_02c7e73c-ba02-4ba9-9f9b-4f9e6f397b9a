<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:id="@+id/v_line1_left_1"
        android:layout_width="@dimen/dp_36"
        android:layout_height="@dimen/dp_18"
        android:background="@drawable/bg_neutral_color_8_round_4"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/v_line1_left_2"
        android:layout_width="@dimen/dp_64"
        android:layout_height="@dimen/dp_18"
        android:layout_marginStart="@dimen/dp_14"
        android:background="@drawable/bg_neutral_color_8_round_4"
        app:layout_constraintStart_toEndOf="@id/v_line1_left_1"
        app:layout_constraintTop_toTopOf="@id/v_line1_left_1" />

</androidx.constraintlayout.widget.ConstraintLayout>