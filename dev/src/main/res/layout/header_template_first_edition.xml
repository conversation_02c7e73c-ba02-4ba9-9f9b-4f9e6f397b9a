<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_F7F7F8"
    android:paddingHorizontal="16dp">

    <ImageView
        android:id="@+id/ivTemplateTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="1dp"
        android:src="@drawable/icon_template_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvTemplateTitle" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvTemplateTitle"
        style="@style/MetaTextView.S18.PoppinsBold700"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:layout_marginTop="16dp"
        android:gravity="center_vertical"
        android:text="@string/template_all_cap"
        app:layout_constraintStart_toEndOf="@id/ivTemplateTitle"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivTemplate1"
        android:layout_width="0dp"
        android:layout_height="195dp"
        android:layout_marginTop="8dp"
        android:background="@drawable/placeholder_corner_20"
        android:scaleType="centerCrop"
        android:src="@drawable/placeholder_corner_20"
        app:layout_constraintDimensionRatio="195:176"
        app:layout_constraintEnd_toStartOf="@id/ivTemplate2"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTemplateTitle"
        app:shapeAppearance="@style/shapeRound20Style" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvTemplate1"
        style="@style/MetaTextView.S12.PoppinsBold700"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="12dp"
        app:layout_constraintStart_toStartOf="@id/ivTemplate1"
        app:layout_constraintTop_toTopOf="@id/ivTemplate1"
        tools:text="HUT" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivTemplate2"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="8dp"
        android:background="@drawable/placeholder_corner_20"
        android:scaleType="centerCrop"
        android:src="@drawable/placeholder_corner_20"
        app:layout_constraintBottom_toTopOf="@id/ivTemplate3"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/ivTemplate1"
        app:layout_constraintTop_toTopOf="@id/ivTemplate1"
        app:shapeAppearance="@style/shapeRound20Style" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvTemplate2"
        style="@style/MetaTextView.S12.PoppinsBold700"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="12dp"
        app:layout_constraintStart_toStartOf="@id/ivTemplate2"
        app:layout_constraintTop_toTopOf="@id/ivTemplate2"
        tools:text="BLANK" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivTemplate3"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="8dp"
        android:layout_marginTop="8dp"
        android:background="@drawable/placeholder_corner_20"
        android:scaleType="centerCrop"
        android:src="@drawable/placeholder_corner_20"
        app:layout_constraintBottom_toBottomOf="@id/ivTemplate1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/ivTemplate1"
        app:layout_constraintTop_toBottomOf="@+id/ivTemplate2"
        app:shapeAppearance="@style/shapeRound20Style" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvTemplate3"
        style="@style/MetaTextView.S12.PoppinsBold700"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="12dp"
        app:layout_constraintStart_toStartOf="@id/ivTemplate3"
        app:layout_constraintTop_toTopOf="@id/ivTemplate3"
        tools:text="PARKOUR" />
</androidx.constraintlayout.widget.ConstraintLayout>