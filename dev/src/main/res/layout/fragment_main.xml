<?xml version="1.0" encoding="utf-8"?>
<com.socialplay.gpark.ui.view.CustomDrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:speed_monitor="true">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipChildren="false">

        <FrameLayout
            android:id="@+id/fragment_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <View
            android:id="@+id/v_tab_bar_divider"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_gravity="bottom"
            android:layout_marginBottom="@dimen/tab_layout_height"
            android:background="@color/color_D9D9D9" />

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tab_layout"
            android:layout_width="match_parent"
            android:layout_height="@dimen/tab_layout_height"
            android:layout_gravity="bottom"
            android:background="@color/white"
            app:tabGravity="fill"
            app:tabIndicator="@null"
            app:tabPadding="0dp"
            app:tabPaddingBottom="0dp"
            app:tabPaddingEnd="0dp"
            app:tabPaddingStart="0dp"
            app:tabPaddingTop="0dp" />

        <com.socialplay.gpark.ui.view.LoadingView
            android:id="@+id/lv"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white"
            android:saveEnabled="false"
            android:visibility="gone" />

    </FrameLayout>

</com.socialplay.gpark.ui.view.CustomDrawerLayout>