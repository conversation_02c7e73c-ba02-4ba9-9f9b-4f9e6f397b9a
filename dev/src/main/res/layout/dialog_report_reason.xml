<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_white_top_round_24"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/dp_28"
        android:paddingBottom="@dimen/dp_42"
        android:paddingTop="@dimen/dp_28"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvReportTitle"
            style="@style/MetaTextView.S16.PoppinsBold700.TitleBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:uiLineHeight="@dimen/dp_22"
            tools:text="Report" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/mRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/dp_12"
            android:orientation="vertical"
            android:overScrollMode="never"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toTopOf="@+id/llBottom"
            app:layout_constraintTop_toBottomOf="@+id/tvReportTitle"
            tools:listitem="@layout/adapter_report_reason_item" />


        <LinearLayout
            android:id="@+id/llBottom"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_36"
            android:layout_marginTop="@dimen/dp_20"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/mRecyclerView">

            <TextView
                android:id="@+id/tvCancel"
                style="@style/Button.S15.PoppinsRegular400.CancelPrimary"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/cancel" />

            <Space
                android:layout_width="@dimen/dp_15"
                android:layout_height="wrap_content" />

            <TextView
                android:id="@+id/tvReport"
                style="@style/Button.S15.PoppinsMedium500"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/report" />
        </LinearLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>
