<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:ignore="MissingDefaultResource">

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:title_text="@string/draft_migrate" />

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/refresh"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/title"
        android:padding="16dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/tvEnv"
                style="@style/MetaTextView.S12.PoppinsBold700"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/black_70"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvTips"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/debug_migration_attention"
                android:textColor="@color/black_70"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvEnv" />

            <View
                android:id="@+id/vLine"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="16dp"
                android:background="@color/color_placeholder"
                app:layout_constraintTop_toBottomOf="@id/tvTips" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvTitle"
                style="@style/MetaTextView.S12.PoppinsBold700"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/debug_draft_num"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/vLine" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvSize"
                style="@style/MetaTextView.S12.PoppinsBold700"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/zero_int"
                app:layout_constraintStart_toEndOf="@+id/tvTitle"
                app:layout_constraintTop_toTopOf="@id/tvTitle" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvBtn"
                style="@style/Button.S12.PoppinsMedium500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="@string/debug_start_migrate"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

            <EditText
                android:id="@+id/et"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginTop="16dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvBtn" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/loading"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clickable="true"
        android:visibility="gone" />
</RelativeLayout>