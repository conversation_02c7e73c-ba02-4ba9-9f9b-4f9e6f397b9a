<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
   >
    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/reviewStatus"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/titleBarReview"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:isDividerVisible="false"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/reviewStatus"
        app:title_text="" />

    <ImageView
        android:id="@+id/ivTitleIcon"
        android:layout_width="@dimen/dp_33"
        android:layout_height="@dimen/dp_33"
        android:layout_centerHorizontal="true"
        android:alpha="0"
        android:src="@drawable/placeholder_corner_10"
        app:layout_constraintBottom_toBottomOf="@+id/titleBarReview"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/titleBarReview" />


    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvRatings"
        style="@style/MetaTextView.S18.PoppinsExtraBold800.LeftTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_16"
        android:text="@string/rating_cap"
        android:textSize="@dimen/sp_18"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/titleBarReview" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvRatingNum"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_5"
        android:textColor="@color/color_999999"
        android:textSize="@dimen/sp_14"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tvRatings"
        app:layout_constraintLeft_toRightOf="@id/tvRatings"
        app:layout_constraintTop_toTopOf="@id/tvRatings" />

    <com.socialplay.gpark.ui.view.HeaderNestedScrollView
        android:id="@+id/nsvGameDetail"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:background="@drawable/bg_game_detail_top_corner_20"
        android:orientation="vertical"
        android:overScrollMode="never"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        app:layout_constraintBottom_toTopOf="@+id/clGameDetailBottom"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvRatings">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <include
                android:id="@+id/includeRating"
                layout="@layout/layout_game_review_rating"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_105"
                android:layout_marginTop="@dimen/dp_10"
                app:layout_constraintTop_toTopOf="parent" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvSortType"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_23"
                android:layout_marginTop="@dimen/dp_15"
                android:layout_marginEnd="@dimen/dp_15"
                android:clickable="true"
                android:text="@string/most_favorable"
                android:textColor="@color/color_4AB4FF"
                android:textSize="@dimen/sp_15"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/includeRating" />

            <ImageView
                android:id="@+id/ivArrow"
                android:layout_width="@dimen/dp_10"
                android:layout_height="@dimen/dp_10"
                android:layout_marginBottom="@dimen/dp_2"
                android:scaleType="fitCenter"
                android:src="@drawable/ic_arrow_downward_4ab4ff"
                app:layout_constraintBottom_toBottomOf="@id/tvSortType"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@id/tvSortType" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvReviews"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:overScrollMode="never"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvSortType" />
        </androidx.constraintlayout.widget.ConstraintLayout>


    </com.socialplay.gpark.ui.view.HeaderNestedScrollView>

    <!--底栏-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clGameDetailBottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:paddingStart="@dimen/dp_8"
        android:paddingTop="@dimen/dp_12"
        android:paddingEnd="@dimen/dp_8"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <TextView
            android:id="@+id/tvLikeInReview"
            style="@style/MetaTextView.S12.PoppinsMedium500"
            android:layout_width="76dp"
            android:layout_height="wrap_content"
            android:drawableTop="@drawable/game_detail_icon_like"
            android:gravity="center"
            android:paddingHorizontal="5dp"
            android:singleLine="true"
            android:textColor="#B3B3B3"
            app:layout_constraintBottom_toBottomOf="@id/dpbDownloadGame"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/dpbDownloadGame" />

        <com.socialplay.gpark.ui.view.DownloadProgressButton
            android:id="@+id/dpbDownloadGame"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_46"
            android:layout_marginHorizontal="@dimen/dp_8"
            android:layout_marginVertical="@dimen/dp_16"
            android:fontFamily="@font/poppins_black_900"
            android:gravity="center"
            android:text="@string/play"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintLeft_toRightOf="@id/tvLikeInReview"
            app:layout_constraintRight_toRightOf="parent"
            app:progress_btn_background_color_end="@color/colorAccentPrimary"
            app:progress_btn_background_color_start="@color/colorAccentPrimary"
            app:progress_btn_background_second_color="@color/color_game_detail_desc_bg"
            app:progress_btn_radius="@dimen/dp_40"
            app:progress_btn_show_bolder="false"
            app:progress_btn_text_color="@color/textColorPrimary"
            app:progress_btn_text_cover_color="@color/textColorPrimary"
            app:progress_btn_text_size="18" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!--    <View-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="0.3dp"-->
    <!--        android:background="@color/color_CDCECF"-->
    <!--        app:layout_constraintTop_toBottomOf="@id/nsvGameDetail"-->
    <!--        />-->

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/loading"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_20"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>