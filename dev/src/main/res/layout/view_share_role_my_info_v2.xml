<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_my_info"
    android:layout_width="@dimen/dp_240"
    android:layout_height="@dimen/dp_320">

    <ImageView
        android:id="@+id/iv_bg_my_info"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_share_role_screenshots_my_info_v2"
        tools:layout_editor_absoluteX="0dp"
        tools:layout_editor_absoluteY="0dp" />

    <ImageView
        android:id="@+id/iv_avatar_my_info"
        android:layout_width="@dimen/dp_42"
        android:layout_height="@dimen/dp_42"
        android:layout_marginTop="@dimen/dp_32"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.MetaTextView
        style="@style/MetaTextView.S12.PoppinsSemiBold600"
        android:id="@+id/tv_username_my_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_8"
        android:gravity="center"
        android:singleLine="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_avatar_my_info"
        tools:text="Username" />

    <ImageView
        android:id="@+id/iv_qr_bg_my_info"
        android:layout_width="@dimen/dp_86"
        android:layout_height="@dimen/dp_86"
        android:layout_marginTop="@dimen/dp_8"
        android:background="@drawable/bg_share_role_screenshots_qr_v2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_username_my_info" />

    <ImageView
        android:id="@+id/iv_qr_code_my_info"
        android:layout_width="@dimen/dp_70"
        android:layout_height="@dimen/dp_70"
        android:scaleType="fitCenter"
        app:layout_constraintBottom_toBottomOf="@id/iv_qr_bg_my_info"
        app:layout_constraintEnd_toEndOf="@id/iv_qr_bg_my_info"
        app:layout_constraintStart_toStartOf="@id/iv_qr_bg_my_info"
        app:layout_constraintTop_toTopOf="@id/iv_qr_bg_my_info" />

    <com.socialplay.gpark.ui.view.MetaTextView
        style="@style/MetaTextView.S14.PoppinsSemiBold600"
        android:id="@+id/tv_233_number_my_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_10"
        android:background="@drawable/bg_share_my_info_number"
        android:gravity="center"
        android:paddingHorizontal="@dimen/dp_16"
        android:paddingVertical="@dimen/dp_6"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_qr_bg_my_info"
        tools:text="ID:785443656" />

    <com.socialplay.gpark.ui.view.MetaTextView
        style="@style/roundCorner12"
        android:id="@+id/tv_tips_my_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_8"
        android:gravity="center"
        android:text="@string/qr_code_tip"
        android:textColor="@color/black_40"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_233_number_my_info" />

</androidx.constraintlayout.widget.ConstraintLayout>