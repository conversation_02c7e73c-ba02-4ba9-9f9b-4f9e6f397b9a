<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/sbphv_placeholder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/tbl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:back_icon="@drawable/icon_back_array_bold_black"
        app:background_color="@color/transparent"
        app:isDividerVisible="false"
        app:layout_constraintTop_toBottomOf="@+id/sbphv_placeholder"
        app:rightText="@string/login_skip" />

    <ImageView
        android:id="@+id/iv_age_restriction"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="@dimen/dp_20"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tbl"
        app:layout_constraintRight_toRightOf="@id/tbl"
        app:layout_constraintTop_toTopOf="@id/tbl"
        tools:visibility="visible" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/rl_content_area"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:paddingBottom="@dimen/dp_16"
        app:layout_constraintBottom_toTopOf="@+id/cl_agreement"
        app:layout_constraintTop_toBottomOf="@id/tbl">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:focusable="true"
            android:focusableInTouchMode="true"
            app:layout_constraintTop_toTopOf="parent">

            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/ivContinueLogin"
                android:layout_width="@dimen/dp_74"
                android:layout_height="@dimen/dp_74"
                android:src="@drawable/placeholder_corner_360"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@id/tvContinueLogin"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:shapeAppearance="@style/circleStyle" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvContinueLogin"
                style="@style/MetaTextView.S16.PoppinsBold700"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_12"
                android:gravity="center"
                android:paddingBottom="@dimen/dp_4"
                android:textColor="@color/neutral_color_1"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@id/iv_login_account_tip"
                app:layout_constraintTop_toBottomOf="@id/ivContinueLogin"
                tools:text="fdhsjkfgabhjlasdfhklsa" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/iv_login_account_tip"
                style="@style/MetaTextView.S28.PoppinsBlack900"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/poppins_black_900"
                android:text="@string/intl_login"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvContinueLogin" />

            <View
                android:id="@+id/v_phone_login_view"
                style="@style/Button.S18.PoppinsBlack900.Height46"
                android:layout_width="0dp"
                android:layout_height="@dimen/dp_60"
                android:layout_marginHorizontal="@dimen/dp_32"
                android:layout_marginTop="@dimen/dp_20"
                app:layout_constraintBottom_toTopOf="@id/tvOr"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/iv_login_account_tip"
                app:layout_constraintVertical_chainStyle="packed" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tv_phone_login"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:ellipsize="end"
                android:gravity="center"
                android:maxLines="1"
                android:singleLine="true"
                android:text="@string/login_by_phone"
                android:textColor="@color/neutral_color_1"
                android:textSize="@dimen/sp_18"
                android:textStyle="bold"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toBottomOf="@id/v_phone_login_view"
                app:layout_constraintEnd_toEndOf="@id/v_phone_login_view"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="@id/v_phone_login_view"
                app:layout_constraintTop_toTopOf="@id/v_phone_login_view" />


            <View
                android:id="@+id/vLineOrL"
                android:layout_width="@dimen/dp_34"
                android:layout_height="@dimen/dp_1"
                android:background="@color/black_50"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/tvOr"
                app:layout_constraintEnd_toStartOf="@id/tvOr"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/tvOr"
                tools:visibility="visible" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvOr"
                style="@style/MetaTextView.S14.PoppinsRegular400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_12"
                android:paddingHorizontal="@dimen/dp_8"
                android:text="@string/intl_or_all_cap"
                android:textColor="@color/black_50"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@id/input_account"
                app:layout_constraintEnd_toStartOf="@id/vLineOrR"
                app:layout_constraintStart_toEndOf="@id/vLineOrL"
                app:layout_constraintTop_toBottomOf="@id/v_phone_login_view"
                app:layout_constraintVertical_chainStyle="packed"
                tools:visibility="visible" />

            <View
                android:id="@+id/vLineOrR"
                android:layout_width="@dimen/dp_34"
                android:layout_height="@dimen/dp_1"
                android:background="@color/black_50"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/tvOr"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tvOr"
                app:layout_constraintTop_toTopOf="@id/tvOr"
                tools:visibility="visible" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/input_account"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_60"
                android:layout_marginLeft="@dimen/dp_32"
                android:layout_marginTop="@dimen/dp_12"
                android:layout_marginRight="@dimen/dp_32"
                android:background="@drawable/bg_f6_corner_12"
                android:hint="@string/intl_enter_account_or_email"
                android:textColorHint="@color/color_B3B3B3"
                app:boxStrokeWidth="0dp"
                app:boxStrokeWidthFocused="0dp"
                app:hintAnimationEnabled="true"
                app:hintTextAppearance="@style/hintAppearence"
                app:hintTextColor="@color/color_B3B3B3"
                app:layout_constraintTop_toBottomOf="@id/tvOr">

                <com.socialplay.gpark.ui.view.AutoFillTextInputEditText
                    android:id="@+id/et_account"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="@dimen/dp_48"
                    android:background="#00FFFFFF"
                    android:fontFamily="@font/poppins_regular_400"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/color_1A1A1A"
                    android:textSize="@dimen/sp_14" />
            </com.google.android.material.textfield.TextInputLayout>

            <View
                android:id="@+id/vAccountBlock"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:clickable="true"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/input_account"
                app:layout_constraintEnd_toEndOf="@id/input_account"
                app:layout_constraintStart_toStartOf="@id/input_account"
                app:layout_constraintTop_toTopOf="@id/input_account" />

            <ImageView
                android:id="@+id/ivClearName"
                android:layout_width="@dimen/dp_40"
                android:layout_height="0dp"
                android:layout_marginEnd="@dimen/dp_8"
                android:paddingHorizontal="@dimen/dp_8"
                android:src="@drawable/ic_clear_input"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/input_account"
                app:layout_constraintEnd_toEndOf="@id/input_account"
                app:layout_constraintTop_toTopOf="@id/input_account"
                tools:visibility="visible" />


            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/input_password"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:layout_marginLeft="@dimen/dp_32"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginRight="@dimen/dp_32"
                android:background="@drawable/bg_f6_corner_12"
                android:hint="@string/intl_enter_password"
                android:textColorHint="@color/color_B3B3B3"
                app:boxStrokeWidth="0dp"
                app:boxStrokeWidthFocused="0dp"
                app:hintAnimationEnabled="true"
                app:hintTextAppearance="@style/hintAppearence"
                app:hintTextColor="@color/color_B3B3B3"

                app:layout_constraintTop_toBottomOf="@id/input_account">

                <com.socialplay.gpark.ui.view.AutoFillTextInputEditText
                    android:id="@+id/et_password"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginRight="@dimen/dp_88"
                    android:background="#00FFFFFF"
                    android:fontFamily="@font/poppins_regular_400"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/color_1A1A1A"
                    android:textSize="@dimen/sp_14"
                    app:boxStrokeColor="@color/color_input_background" />
            </com.google.android.material.textfield.TextInputLayout>

            <ImageView
                android:id="@+id/ivClearPassword"
                android:layout_width="@dimen/dp_40"
                android:layout_height="0dp"
                android:paddingHorizontal="@dimen/dp_8"
                android:src="@drawable/ic_clear_input"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/input_password"
                app:layout_constraintEnd_toStartOf="@id/iv_password_visibility"
                app:layout_constraintTop_toTopOf="@id/input_password"
                tools:visibility="visible" />

            <ImageView
                android:id="@+id/iv_password_visibility"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:paddingLeft="@dimen/dp_8"
                android:paddingEnd="@dimen/dp_16"
                android:src="@drawable/icon_login_visible_password"
                app:layout_constraintBottom_toBottomOf="@+id/input_password"
                app:layout_constraintEnd_toEndOf="@+id/input_password"
                app:layout_constraintTop_toTopOf="@+id/input_password" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvSwitchLoginWay"
                style="@style/MetaTextView.S12"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableStart="@drawable/ic_login_switch_type"
                android:drawablePadding="@dimen/dp_5"
                android:gravity="center_vertical"
                android:paddingVertical="@dimen/dp_12"
                android:text="@string/login_by_gpark_id"
                android:textColor="@color/color_666666"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="@id/input_password"
                app:layout_constraintTop_toBottomOf="@id/input_password" />

            <TextView
                android:id="@+id/tv_login"
                style="@style/Button.S18.PoppinsBlack900.Height46"
                android:layout_width="0dp"
                android:layout_height="@dimen/dp_56"
                android:layout_marginHorizontal="@dimen/dp_40"
                android:layout_marginTop="@dimen/dp_12"
                android:text="@string/intl_log_in"
                android:textColor="@color/neutral_color_1"
                app:layout_constraintEnd_toEndOf="@id/input_password"
                app:layout_constraintStart_toStartOf="@id/input_password"
                app:layout_constraintTop_toBottomOf="@id/tvSwitchLoginWay"
                app:layout_goneMarginTop="@dimen/dp_24" />

            <LinearLayout
                android:id="@+id/ll_forget_password"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_12"
                android:gravity="center"
                android:orientation="horizontal"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_login">

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tv_forgot_password"
                    style="@style/MetaTextView.S14.PoppinsMedium500"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:text="@string/intl_forgot_questionmark"
                    android:textColor="@color/neutral_color_3"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_login" />
            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_agreement"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_32"
        android:layout_marginBottom="@dimen/dp_32"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent">


        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/cb_agree"
            android:layout_width="@dimen/dp_16"
            android:layout_height="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_1"
            android:layout_marginRight="@dimen/dp_2"
            android:background="@drawable/s_agree_check"
            android:button="@null"
            android:src="@drawable/backup_icon_unselected"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"

            />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_agreement"
            style="@style/MetaTextView.S12.PoppinsLight300"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_18"
            android:gravity="center_horizontal"
            android:textColor="#53535E"
            android:textSize="@dimen/sp_12"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:uiLineHeight="@dimen/dp_18"
            tools:ignore="RtlSymmetry"
            tools:text="I have read and agreed to the user agreement and privacy policy" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/v_hot_check"
        android:layout_width="@dimen/dp_80"
        android:layout_height="@dimen/dp_60"
        android:layout_marginLeft="-12dp"
        android:layout_marginTop="-22dp"
        android:clickable="true"
        app:layout_constraintLeft_toLeftOf="@id/cl_agreement"
        app:layout_constraintTop_toTopOf="@id/cl_agreement" />

    <LinearLayout
        android:id="@+id/ll_login_by_other_sdk"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_44"
        android:layout_marginBottom="@dimen/dp_30"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintBottom_toTopOf="@id/cl_agreement"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

    </LinearLayout>

    <TextView
        android:id="@+id/tvThirdLoginHint"
        style="@style/MetaTextView.S11"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_6"
        android:text="@string/third_party_login_method"
        android:textColor="@color/black_40"
        app:layout_constraintBottom_toTopOf="@id/ll_login_by_other_sdk"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <ImageView
        android:layout_width="@dimen/dp_125"
        android:layout_height="@dimen/dp_5"
        android:layout_marginRight="@dimen/dp_8"
        android:src="@drawable/line_login_hint_left"
        app:layout_constraintBottom_toBottomOf="@id/tvThirdLoginHint"
        app:layout_constraintRight_toLeftOf="@id/tvThirdLoginHint"
        app:layout_constraintTop_toTopOf="@id/tvThirdLoginHint" />

    <ImageView
        android:layout_width="@dimen/dp_125"
        android:layout_height="@dimen/dp_5"
        android:layout_marginLeft="@dimen/dp_8"
        android:src="@drawable/line_login_hint_right"
        app:layout_constraintBottom_toBottomOf="@id/tvThirdLoginHint"
        app:layout_constraintLeft_toRightOf="@id/tvThirdLoginHint"
        app:layout_constraintTop_toTopOf="@id/tvThirdLoginHint" />
</androidx.constraintlayout.widget.ConstraintLayout>