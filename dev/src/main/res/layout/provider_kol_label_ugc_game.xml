<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <Space
        android:id="@+id/spaceTop"
        android:layout_width="@dimen/dp_1"
        android:layout_height="0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_icon"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:scaleType="centerCrop"
        android:src="@drawable/placeholder"
        app:layout_constraintDimensionRatio="166:118"
        app:layout_constraintEnd_toStartOf="@id/spaceEnd"
        app:layout_constraintStart_toEndOf="@id/spaceStart"
        app:layout_constraintTop_toBottomOf="@id/spaceTop"
        app:shapeAppearance="@style/round_corner_12dp" />

    <View
        android:id="@+id/v_shadow_top_mask"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_44"
        android:background="@drawable/bg_top_icon_shadow_mask_s12_v2"
        app:layout_constraintEnd_toEndOf="@id/iv_icon"
        app:layout_constraintStart_toStartOf="@id/iv_icon"
        app:layout_constraintTop_toTopOf="@id/iv_icon" />

    <View
        android:id="@+id/v_shadow_bottom_mask"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_34"
        android:background="@drawable/bg_bottom_icon_shadow_mask_s12"
        app:layout_constraintBottom_toBottomOf="@id/iv_icon"
        app:layout_constraintEnd_toEndOf="@id/iv_icon"
        app:layout_constraintStart_toStartOf="@id/iv_icon" />

    <com.socialplay.gpark.ui.view.FlowLayout
        android:id="@+id/flLabel"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_8"
        android:layout_marginTop="@dimen/dp_6"
        app:horizontalSpacing="@dimen/dp_4"
        app:layout_constraintEnd_toStartOf="@id/iv_more_btn"
        app:layout_constraintStart_toStartOf="@id/iv_icon"
        app:layout_constraintTop_toTopOf="@id/iv_icon"
        app:verticalSpacing="@dimen/dp_4" />

    <ImageView
        android:id="@+id/iv_more_btn"
        android:layout_width="@dimen/dp_34"
        android:layout_height="@dimen/dp_34"
        android:padding="@dimen/dp_6"
        android:src="@drawable/ic_item_more"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/iv_icon"
        app:layout_constraintTop_toTopOf="@id/iv_icon"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_heat"
        style="@style/MetaTextView.S12.PoppinsRegular400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_8"
        android:layout_marginBottom="@dimen/dp_8"
        android:drawableStart="@drawable/ic_ugc_fire"
        android:drawablePadding="@dimen/dp_4"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:textColor="@color/white"
        app:layout_constraintBottom_toBottomOf="@id/iv_icon"
        app:layout_constraintEnd_toEndOf="@id/iv_icon"
        app:layout_constraintStart_toStartOf="@id/iv_icon"
        tools:text="9.9k players" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_name"
        style="@style/MetaTextView.S13.PoppinsRegular400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_4"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/neutral_color_1"
        app:layout_constraintEnd_toEndOf="@id/iv_icon"
        app:layout_constraintStart_toStartOf="@id/iv_icon"
        app:layout_constraintTop_toBottomOf="@id/iv_icon"
        tools:text="Leslie Alexander\n范德萨" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_author_avatar"
        android:layout_width="@dimen/dp_18"
        android:layout_height="@dimen/dp_18"
        android:layout_marginTop="@dimen/dp_4"
        app:layout_constraintStart_toStartOf="@id/tv_name"
        app:layout_constraintTop_toBottomOf="@id/tv_name"
        app:shapeAppearance="@style/circleStyle"
        tools:src="@drawable/placeholder" />

    <TextView
        android:id="@+id/tv_author_name"
        style="@style/MetaTextView.S12.PoppinsRegular400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_4"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/color_666666"
        app:layout_constraintBottom_toBottomOf="@id/iv_author_avatar"
        app:layout_constraintEnd_toEndOf="@id/tv_name"
        app:layout_constraintStart_toEndOf="@+id/iv_author_avatar"
        app:layout_constraintTop_toTopOf="@id/iv_author_avatar"
        tools:text="Leslie Alexander Alexander" />

    <Space
        android:id="@+id/spaceBottom"
        android:layout_width="@dimen/dp_1"
        android:layout_height="@dimen/dp_14"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_author_avatar" />

    <Space
        android:id="@+id/spaceStart"
        android:layout_width="0dp"
        android:layout_height="1dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <Space
        android:id="@+id/spaceEnd"
        android:layout_width="0dp"
        android:layout_height="1dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>