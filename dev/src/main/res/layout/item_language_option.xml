<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_72"
    android:paddingStart="@dimen/dp_16"
    android:paddingEnd="@dimen/dp_10">

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvLang"
        style="@style/MetaTextView.S14.PoppinsRegular400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/neutral_color_1"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="English" />

    <ImageView
        android:id="@+id/ivSelect"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_black_check"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tvLang"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvLang"
        tools:visibility="visible" />
</androidx.constraintlayout.widget.ConstraintLayout>