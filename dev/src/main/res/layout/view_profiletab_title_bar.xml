<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/title_bar"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_48">

    <ImageView
        android:id="@+id/v_bg_title_bar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:alpha="0"
        android:background="@color/white" />

    <androidx.appcompat.widget.AppCompatImageButton
        android:id="@+id/ib_back"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_48"
        android:background="?attr/actionBarItemBackground"
        android:paddingHorizontal="@dimen/dp_16"
        android:src="@drawable/icon_arrow_back"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <Space
        android:id="@+id/spcae_right"
        android:layout_width="@dimen/dp_16"
        android:layout_height="match_parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_right"
        android:layout_width="@dimen/dp_34"
        android:layout_height="@dimen/dp_34"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/spcae_right"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/ic_feat_24_1a1a1a_more_dot"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_title_share"
        android:layout_width="@dimen/dp_34"
        android:layout_height="@dimen/dp_34"
        android:src="@drawable/ic_feat_24_1a1a1a_share"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/iv_right"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_title_edit"
        android:layout_width="@dimen/dp_34"
        android:layout_height="@dimen/dp_34"
        android:src="@drawable/ic_profile_edit"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/iv_title_share"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <View
        android:id="@+id/view_title_divider"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/color_account_line"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>