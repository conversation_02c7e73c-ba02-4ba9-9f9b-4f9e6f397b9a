<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingBottom="4.5dp">

    <TextView
        android:id="@+id/tv_normal"
        style="@style/MetaTextView.S14.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="center_horizontal|bottom"
        android:gravity="center_horizontal|bottom"
        android:maxLines="1"
        android:paddingVertical="@dimen/dp_3"
        android:singleLine="true"
        android:textColor="@color/neutral_color_4"
        android:textSize="@dimen/dp_14"
        tools:text="Add Friend" />

    <TextView
        android:id="@+id/tv_selected"
        style="@style/MetaTextView.S14.PoppinsBold700"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="center_horizontal|bottom"
        android:gravity="center_horizontal|bottom"
        android:maxLines="1"
        android:paddingVertical="@dimen/dp_3"
        android:singleLine="true"
        android:textColor="@color/neutral_color_2"
        android:textSize="@dimen/dp_14"
        android:visibility="invisible"
        tools:text="Add Friend" />

</FrameLayout>




