<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/meta_mgs_rl_message_after"
    android:layout_width="wrap_content"
    android:layout_height="@dimen/dp_32"
    android:layout_marginStart="@dimen/dp_10"
    android:background="@drawable/shape_black_70_corner_8"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/iv_recording"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:paddingLeft="@dimen/dp_8"
        android:paddingRight="@dimen/dp_6"
        android:src="@drawable/icon_mgs_recording" />

    <Chronometer
        android:id="@+id/chronometer_free_record"
        android:layout_width="@dimen/dp_36"
        android:layout_height="match_parent"
        android:gravity="center"
        tools:text="11:11"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_10" />

    <ImageView
        android:id="@+id/iv_voice"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:paddingLeft="@dimen/dp_4"
        android:paddingRight="@dimen/dp_8"
        android:src="@drawable/icon_mgs_record_voice_b" />

</LinearLayout>
