<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingBottom="@dimen/dp_22">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_dialog_29">

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvDeleteReviewDesc"
            style="@style/MetaTextView.S16.PoppinsBlack900.CenterTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_28"
            android:paddingHorizontal="@dimen/dp_28"
            android:text="@string/delete_my_review_desc"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvDeleteReviewConfirm"
            style="@style/MetaTextView.S18.PoppinsMedium500.CenterDanger"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_28"
            android:layout_marginTop="@dimen/dp_35"
            android:background="@drawable/bg_common_dialog_cancel"
            android:paddingVertical="@dimen/dp_12"
            android:text="@string/delete_cap"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvDeleteReviewDesc" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvDeleteReviewCancel"
            style="@style/MetaTextView.S18.PoppinsMedium500.ButtonOther"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_28"
            android:layout_marginVertical="@dimen/dp_19"
            android:background="@drawable/bg_common_dialog_cancel"
            android:paddingVertical="@dimen/dp_12"
            android:text="@string/dialog_cancel"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvDeleteReviewConfirm" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
