<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivAvatar"
        style="@style/Avatar.Round"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_12"
        android:scaleType="centerCrop"
        android:src="@drawable/icon_default_avatar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/circleStyle" />

    <Space
        android:layout_width="@dimen/dp_12"
        android:layout_height="@dimen/dp_12"
        app:layout_constraintTop_toBottomOf="@id/ivAvatar"
        app:layout_constraintLeft_toLeftOf="@id/ivAvatar" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvUserName"
        style="@style/MetaTextView.S14.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_12"
        android:layout_marginTop="@dimen/dp_4"
        android:ellipsize="end"
        android:singleLine="true"
        app:layout_constraintEnd_toStartOf="@+id/label_group"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@+id/ivAvatar"
        app:layout_constraintTop_toTopOf="@id/ivAvatar"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_constrainedWidth="true"
        app:layout_goneMarginEnd="@dimen/dp_12"
        app:uiLineHeight="@dimen/dp_20"
        tools:text="Harry Potter" />

    <com.socialplay.gpark.ui.view.UserLabelView
        android:id="@+id/label_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_12"
        app:layout_constraintBottom_toBottomOf="@id/tvUserName"
        app:layout_constraintEnd_toStartOf="@id/tvAgree"
        app:layout_constraintStart_toEndOf="@id/tvUserName"
        app:layout_constraintTop_toTopOf="@id/tvUserName" />


    <com.socialplay.gpark.ui.view.FolderTextView
        android:id="@+id/tvReson"
        style="@style/MetaTextView.S12.PoppinsRegular400.secondary"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_4"
        android:layout_marginEnd="@dimen/dp_12"
        android:gravity="center_vertical"
        android:lineSpacingMultiplier="1.2"
        android:paddingBottom="@dimen/dp_12"
        app:canFoldAgain="false"
        app:foldLine="1"
        app:layout_constraintEnd_toStartOf="@id/tvAgree"
        app:layout_constraintStart_toStartOf="@id/tvUserName"
        app:layout_constraintTop_toBottomOf="@id/tvUserName"
        app:tailTextColor="@color/color_003b70"
        app:uiLineHeight="@dimen/dp_20"
        app:unFoldText="@string/expand"
        tools:text="Potter \nand \nthe \nrie" />

    <View
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:layout_marginLeft="@dimen/dp_5"
        android:background="@color/white_18"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/ivAvatar"
        app:layout_constraintRight_toRightOf="parent" />


    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvApplyState"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_12"
        android:textColor="@color/neutral_color_5"
        android:textSize="@dimen/sp_14"
        app:layout_constraintBottom_toBottomOf="@id/ivAvatar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/ivAvatar" />


    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvAgree"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/Button.S12.PoppinsMedium500"
        android:minHeight="@dimen/dp_28"
        android:minWidth="74dp"
        android:text="@string/friend_agree"
        android:layout_marginEnd="4dp"
        app:layout_constraintBottom_toBottomOf="@id/ivAvatar"
        app:layout_constraintTop_toTopOf="@id/ivAvatar"
        app:layout_constraintEnd_toStartOf="@id/ivDisAgree" />

    <ImageView
        android:id="@+id/ivDisAgree"
        android:src="@drawable/icon_close"
        android:layout_width="@dimen/dp_32"
        android:layout_height="@dimen/dp_32"
        app:tint="@color/color_DCDEE2"
        android:padding="6dp"
        android:layout_marginEnd="@dimen/dp_10"
        app:layout_constraintBottom_toBottomOf="@id/ivAvatar"
        app:layout_constraintTop_toTopOf="@id/ivAvatar"
        app:layout_constraintEnd_toStartOf="@id/tvApplyState" />

</androidx.constraintlayout.widget.ConstraintLayout>