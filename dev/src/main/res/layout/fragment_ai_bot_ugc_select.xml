<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <ImageView
        android:id="@+id/bg_line_ai_bot_ugc_back"
        android:layout_width="354dp"
        android:layout_height="354dp"
        android:scaleType="centerCrop"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/statusBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/img_back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/dp_16"
        android:src="@drawable/ic_back"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/statusBar" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/poppins_semi_bold_600"
        android:text="@string/welcome_to_app"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16"
        app:layout_constraintBottom_toBottomOf="@+id/img_back"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/img_back" />

    <ImageView
        android:id="@+id/img_bg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginLeft="@dimen/dp_99"
        android:layout_marginTop="@dimen/dp_25"
        android:layout_marginRight="@dimen/dp_23"
        android:layout_marginBottom="@dimen/dp_34"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toTopOf="@id/tv_generate"
        app:layout_constraintTop_toTopOf="@+id/cl_icon" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginLeft="@dimen/dp_99"
        android:layout_marginRight="@dimen/dp_23"
        android:background="@drawable/bg_ai_bot_select_top"
        app:layout_constraintDimensionRatio="253:232"
        app:layout_constraintTop_toTopOf="@+id/img_bg" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginLeft="@dimen/dp_99"
        android:layout_marginRight="@dimen/dp_23"
        android:background="@drawable/bg_ai_bot_select_buttom"
        app:layout_constraintBottom_toBottomOf="@+id/img_bg"
        app:layout_constraintDimensionRatio="253:232" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_5"
        app:layout_constraintBottom_toTopOf="@+id/userIconTw0"
        app:layout_constraintRight_toRightOf="@id/img_bg">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/userIconThree"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:scaleType="centerCrop"
            android:src="@drawable/icon_default_avatar"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeAppearance="@style/circleStyle" />

        <View
            android:layout_width="92dp"
            android:layout_height="18dp"
            android:layout_marginRight="@dimen/dp_4"
            android:background="@drawable/bg_ai_bot_hor"
            app:layout_constraintBottom_toBottomOf="@+id/userIconThree"
            app:layout_constraintRight_toLeftOf="@+id/userIconThree"
            app:layout_constraintTop_toTopOf="@+id/userIconThree" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/userIconTw0"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_5"
        app:layout_constraintBottom_toTopOf="@+id/clUserOne"
        app:layout_constraintLeft_toLeftOf="@id/img_bg">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/userIconTwo"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:scaleType="centerCrop"
            android:src="@drawable/icon_default_avatar"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeAppearance="@style/circleStyle" />

        <View
            android:layout_width="92dp"
            android:layout_height="18dp"
            android:layout_marginLeft="@dimen/dp_4"
            android:background="@drawable/bg_ai_bot_hor"
            app:layout_constraintBottom_toBottomOf="@+id/userIconTwo"
            app:layout_constraintLeft_toRightOf="@+id/userIconTwo"
            app:layout_constraintTop_toTopOf="@+id/userIconTwo" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clUserOne"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_34"
        app:layout_constraintBottom_toBottomOf="@+id/img_bg"
        app:layout_constraintLeft_toLeftOf="@id/img_bg">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/userIconOne"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:scaleType="centerCrop"
            android:src="@drawable/icon_default_avatar"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeAppearance="@style/circleStyle" />

        <View
            android:layout_width="129dp"
            android:layout_height="18dp"
            android:layout_marginLeft="@dimen/dp_4"
            android:background="@drawable/bg_ai_bot_hor"
            app:layout_constraintBottom_toBottomOf="@+id/userIconOne"
            app:layout_constraintLeft_toRightOf="@+id/userIconOne"
            app:layout_constraintTop_toTopOf="@+id/userIconOne" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_icon"
        android:layout_width="@dimen/dp_183"
        android:layout_height="@dimen/dp_183"
        android:layout_marginStart="@dimen/dp_15"
        android:layout_marginTop="@dimen/dp_23"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        app:shapeAppearance="@style/circleStyle">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/img_icon"
            android:layout_width="@dimen/dp_183"
            android:layout_height="@dimen/dp_183"
            android:scaleType="centerCrop"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeAppearance="@style/circleStyle" />

        <View
            android:layout_width="@dimen/dp_183"
            android:layout_height="@dimen/dp_183"
            android:background="@drawable/bg_d9d9d9_storke_1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/img_convert"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="@dimen/dp_12"
            android:layout_marginBottom="@dimen/dp_12"
            android:src="@drawable/icon_ai_bot_convert"
            app:layout_constraintBottom_toBottomOf="@+id/img_icon"
            app:layout_constraintRight_toRightOf="@+id/img_icon" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <TextView
        android:id="@+id/tv_generate"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_24"
        android:background="@drawable/shape_white_corner"
        android:fontFamily="@font/poppins_semi_bold_600"
        android:gravity="center"
        android:paddingVertical="13dp"
        android:text="@string/intl_go_next"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_16"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>