<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/sbpLang"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/titleLang"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:isDividerVisible="false"
        app:layout_constraintTop_toBottomOf="@id/sbpLang"
        app:title_text="@string/language_cap" />

    <com.airbnb.epoxy.EpoxyRecyclerView
        android:id="@+id/rvLang"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:paddingBottom="@dimen/dp_24"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titleLang"
        tools:itemCount="10"
        tools:listitem="@layout/item_language_option" />

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/loading"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/titleLang"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="0dp" />

</androidx.constraintlayout.widget.ConstraintLayout>