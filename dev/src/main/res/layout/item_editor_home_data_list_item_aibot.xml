<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:layout_gravity="center">


    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_icon"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:scaleType="fitXY"
        android:src="@drawable/placeholder"
        app:layout_constraintDimensionRatio="165:221"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/round_corner_12dp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="165:92"
        android:background="@drawable/bg_linear_vertivor"
        app:layout_constraintBottom_toBottomOf="@+id/iv_icon"
        >

    </View>
    <HorizontalScrollView
        android:id="@+id/sc_label"
        app:layout_constraintBottom_toBottomOf="@+id/iv_icon"
        android:layout_width="match_parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:scrollbars="none"
        android:layout_height="wrap_content">
        <LinearLayout
            android:id="@+id/rl_label"
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            android:layout_marginBottom="@dimen/dp_8"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/dp_8"
            android:visibility="visible" />
    </HorizontalScrollView>

    <TextView
        android:id="@+id/tv_desc"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="2"
        android:paddingHorizontal="@dimen/dp_8"
        android:paddingBottom="@dimen/dp_8"
        android:textColor="@color/white_80"
        android:textSize="@dimen/sp_10"
        app:layout_constraintBottom_toTopOf="@+id/sc_label"
        app:layout_constraintLeft_toLeftOf="parent"
        android:text="He is a math teacher. Once you were the He is a math teacher. He is a math teacher. Once you were theOnce you were the..." />

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:fontFamily="@font/poppins_semi_bold_600"
        android:lineHeight="@dimen/dp_16"
        android:maxLines="1"
        android:paddingHorizontal="@dimen/dp_8"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_12"
        app:layout_constraintBottom_toTopOf="@+id/tv_desc"
        app:layout_constraintLeft_toLeftOf="parent"
        tools:text="Obama" />


</androidx.constraintlayout.widget.ConstraintLayout>