<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/statusBar"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/layoutTitleBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:back_icon="@drawable/icon_back_array_bold_black"
        app:isDividerVisible="true"
        app:layout_constraintTop_toBottomOf="@id/statusBar"
        app:title_text="@string/edit_group_desc_title"
        app:title_text_color="@color/color_1A1A1A" />

    <View
        android:id="@+id/vInputBg"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_254"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_16"
        android:background="@drawable/shape_f0f0f0_corner_16"
        app:layout_constraintTop_toBottomOf="@id/layoutTitleBar" />

    <com.ly123.tes.mgs.im.view.IMEditText
        android:id="@+id/etInput"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/transparent"
        android:gravity="top"
        android:hint="@string/hint_edit_group_desc"
        android:inputType="textMultiLine"
        android:padding="@dimen/dp_16"
        android:textColor="@color/color_1A1A1A"
        android:textColorHint="@color/color_B3B3B3"
        android:textCursorDrawable="@drawable/bg_cursor"
        android:textSize="@dimen/sp_14"
        app:layout_constrainedHeight="true"
        app:layout_constraintBottom_toBottomOf="@id/vInputBg"
        app:layout_constraintEnd_toEndOf="@id/vInputBg"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="@id/vInputBg"
        app:layout_constraintTop_toTopOf="@id/vInputBg"
        app:layout_constraintVertical_bias="0" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvWordCount"
        style="@style/MetaTextView.S12.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_8"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/color_1A1A1A"
        android:textSize="@dimen/sp_12"
        app:layout_constraintEnd_toEndOf="@id/vInputBg"
        app:layout_constraintTop_toBottomOf="@id/vInputBg"
        tools:text="(0/512)" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvSave"
        style="@style/MetaTextView.S16.PoppinsMedium500"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_48"
        android:layout_marginHorizontal="@dimen/dp_36"
        android:layout_marginTop="@dimen/dp_24"
        android:background="@drawable/bg_ffef30_round_40"
        android:gravity="center"
        android:text="@string/dialog_edit_group_desc_save_btn"
        android:textColor="@color/color_1A1A1A"
        android:textSize="@dimen/sp_16"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/tvWordCount"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>