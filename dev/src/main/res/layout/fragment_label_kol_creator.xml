<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/statusBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:isDividerVisible="false"
        app:title_text="@string/kol_start_creator"
        app:layout_constraintTop_toBottomOf="@id/statusBar" />

    <include
        android:id="@+id/includeLabel"
        layout="@layout/include_label_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/title"
        android:layout_marginTop="@dimen/dp_16" />

    <com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout
        android:id="@+id/refresh"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/includeLabel">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.airbnb.epoxy.EpoxyRecyclerView
                android:id="@+id/rvMoreCreator"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_alignParentBottom="true"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:orientation="vertical"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:spanCount="1" />

            <com.socialplay.gpark.ui.view.LoadingView
                android:id="@+id/loadingMoreCreator"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone" />
        </RelativeLayout>
    </com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout>
</androidx.constraintlayout.widget.ConstraintLayout>