<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_gravity="center"
    android:gravity="bottom"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="177dp"
        android:background="@drawable/update_engine" />
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@color/white">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginBottom="44dp"
            android:background="@color/white"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:paddingHorizontal="16dp">
            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tv_title"
                style="@style/MetaTextView.S16.PoppinsBold700"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="14dp"
                android:layout_marginBottom="@dimen/dp_16"
                android:ellipsize="end"
                android:singleLine="true"
                android:text="@string/update_dialog_title"
                android:textColor="#1a1a1a" />

            <ScrollView
                android:id="@+id/scrollview_update"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:id="@+id/parent_LinearLayout_Id"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                </LinearLayout>

            </ScrollView>

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tv_update"
                style="@style/MetaTextView.S16.PoppinsMedium500"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginHorizontal="4dp"
                android:layout_marginTop="16dp"
                android:background="@drawable/bg_common_dialog_yellow"
                android:gravity="center"
                android:minHeight="@dimen/dp_40"
                android:text="@string/update_now" />

            <TextView
                android:id="@+id/tv_exit"
                style="@style/MetaTextView.S16.PoppinsMedium500"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginHorizontal="4dp"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="20dp"
                android:background="@drawable/bg_common_dialog_cancel"
                android:gravity="center"
                android:text="@string/exit_"
                android:textColor="#333"
                android:textSize="14sp" />

        </LinearLayout>
    </LinearLayout>

</LinearLayout>
