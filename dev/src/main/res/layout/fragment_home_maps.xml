<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:paddingBottom="@dimen/tab_layout_height"
    app:speed_monitor="true">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/holder"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/v_title_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_height"
        app:layout_constraintTop_toBottomOf="@id/holder" />

    <!--    <View-->
    <!--        android:id="@+id/vLine"-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="0.5dp"-->
    <!--        android:background="@color/color_F0F0F0"-->
    <!--        app:layout_constraintBottom_toBottomOf="@id/v_title_bar" />-->

    <ImageView
        android:id="@+id/iv_msg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_24"
        android:src="@drawable/ic_msg_message"
        app:layout_constraintBottom_toBottomOf="@id/v_title_bar"
        app:layout_constraintEnd_toStartOf="@id/ivSearch"
        app:layout_constraintTop_toTopOf="@id/v_title_bar" />

    <ImageView
        android:id="@+id/ivSearch"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_16"
        android:src="@drawable/ic_search"
        app:layout_constraintBottom_toBottomOf="@id/v_title_bar"
        app:layout_constraintEnd_toEndOf="@id/v_title_bar"
        app:layout_constraintTop_toTopOf="@id/v_title_bar" />

    <View
        android:id="@+id/v_msg_red_dot"
        android:layout_width="@dimen/dp_8"
        android:layout_height="@dimen/dp_8"
        android:layout_marginTop="0dp"
        android:layout_marginEnd="-2dp"
        android:background="@drawable/sp_red_dot"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/iv_msg"
        app:layout_constraintTop_toTopOf="@id/iv_msg"
        tools:visibility="visible" />

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabLayout"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_44"
        android:layout_marginTop="@dimen/dp_12"
        android:background="@color/transparent"
        android:clipToPadding="false"
        android:paddingLeft="8dp"
        app:layout_constraintBottom_toBottomOf="@id/v_title_bar"
        app:layout_constraintLeft_toLeftOf="@id/v_title_bar"
        app:layout_constraintRight_toLeftOf="@id/iv_msg"
        app:layout_constraintTop_toTopOf="@id/v_title_bar"
        app:tabGravity="start"
        app:tabIndicator="@drawable/indicator_profile"
        app:tabIndicatorColor="@color/color_FFDE70"
        app:tabIndicatorFullWidth="false"
        app:tabIndicatorGravity="bottom"
        app:tabIndicatorHeight="@dimen/dp_8"
        app:tabMinWidth="@dimen/dp_10"
        app:tabMode="scrollable"
        app:tabPaddingBottom="@dimen/dp_16"
        app:tabPaddingEnd="@dimen/dp_16"
        app:tabPaddingStart="@dimen/dp_8"
        app:tabPaddingTop="0dp"
        app:tabRippleColor="@null" />

    <com.socialplay.gpark.ui.view.viewpager.AllowStateLossFrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v_title_bar">

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewpagerTabs"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </com.socialplay.gpark.ui.view.viewpager.AllowStateLossFrameLayout>

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/loading_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/white"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v_title_bar" />

</androidx.constraintlayout.widget.ConstraintLayout>