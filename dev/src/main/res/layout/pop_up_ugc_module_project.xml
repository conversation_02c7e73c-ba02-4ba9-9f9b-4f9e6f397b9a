<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.cardview.widget.CardView
        android:id="@+id/cv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp_16"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="@dimen/dp_14"
        app:cardElevation="@dimen/dp_4">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/mtv_copy"
                style="@style/MetaTextView.S14.PoppinsMedium500.Popup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableStart="@drawable/ic_module_project_copy"
                android:drawablePadding="@dimen/dp_4"
                android:gravity="center"
                android:paddingHorizontal="@dimen/dp_18"
                android:paddingVertical="@dimen/dp_12"
                android:text="@string/duplicate_cap"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/v_copy_click"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="@id/mtv_copy"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/mtv_copy" />

            <View
                android:id="@+id/v_divider_1"
                android:layout_width="0dp"
                android:layout_height="0.3dp"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:background="@color/color_E6E6E6"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/mtv_copy" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/mtv_select_backup"
                style="@style/MetaTextView.S14.PoppinsMedium500.Popup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableStart="@drawable/ic_module_project_cloud"
                android:drawablePadding="@dimen/dp_4"
                android:gravity="center"
                android:paddingHorizontal="@dimen/dp_18"
                android:paddingVertical="@dimen/dp_12"
                android:text="@string/module_project_cloud_btn"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/v_divider_1" />

            <View
                android:id="@+id/v_select_backup_click"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="@id/mtv_select_backup"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/mtv_select_backup" />

            <View
                android:id="@+id/v_divider_2"
                android:layout_width="0dp"
                android:layout_height="0.3dp"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:background="@color/color_E6E6E6"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/mtv_select_backup" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/mtv_rename"
                style="@style/MetaTextView.S14.PoppinsMedium500.Popup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableStart="@drawable/ic_module_project_rename"
                android:drawablePadding="@dimen/dp_4"
                android:gravity="center"
                android:paddingHorizontal="@dimen/dp_18"
                android:paddingVertical="@dimen/dp_12"
                android:text="@string/rename_local_game"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/v_divider_2" />

            <View
                android:id="@+id/v_rename_click"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="@id/mtv_rename"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/mtv_rename" />

            <View
                android:id="@+id/v_divider_3"
                android:layout_width="0dp"
                android:layout_height="0.3dp"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:background="@color/color_E6E6E6"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/mtv_rename" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/mtv_delete"
                style="@style/MetaTextView.S14.PoppinsMedium500.Popup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableStart="@drawable/ic_module_project_delete"
                android:drawablePadding="@dimen/dp_4"
                android:gravity="center"
                android:paddingHorizontal="@dimen/dp_18"
                android:paddingVertical="@dimen/dp_12"
                android:text="@string/delete_cap"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/v_divider_3" />

            <View
                android:id="@+id/v_delete_click"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="@id/mtv_delete"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/mtv_delete" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.cardview.widget.CardView>

</FrameLayout>