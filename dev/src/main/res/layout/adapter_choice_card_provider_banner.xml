<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
  >

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_card_more"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="@dimen/dp_5"
        android:drawableRight="@drawable/icon_right_arrow"
        android:drawablePadding="@dimen/dp_5"
        android:textColor="@color/textColorSecondary"
        android:textSize="@dimen/sp_14"
        android:visibility="gone"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_card_title"
        style="@style/MetaTextView.S14.PoppinsSemiBold600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_5"
        android:layout_marginTop="@dimen/dp_12"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:singleLine="true"
        tools:text="Shooting"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/tv_card_more"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.WrapEpoxyBanner
        android:id="@+id/banner"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_8"
        android:layout_marginHorizontal="5dp"
        app:layout_constraintTop_toBottomOf="@id/tv_card_title" />

    <com.zhpan.indicator.IndicatorView
        android:id="@+id/indicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_32"
        android:layout_marginBottom="@dimen/dp_8"
        app:layout_constraintBottom_toBottomOf="@id/banner"
        app:layout_constraintEnd_toEndOf="@id/banner"
        app:vpi_orientation="horizontal"
        app:vpi_slide_mode="smooth"
        app:vpi_slider_checked_color="@color/white"
        app:vpi_slider_normal_color="@color/white_50"
        app:vpi_style="round_rect" />

    <View
        android:id="@+id/view_space"
        android:layout_width="match_parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/banner"
        android:layout_height="8dp"/>

</androidx.constraintlayout.widget.ConstraintLayout>