<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/statusBar"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/titleLayout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_height"
        app:layout_constraintTop_toBottomOf="@id/statusBar">

        <ImageView
            android:id="@+id/ivBackBtn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_8"
            android:padding="@dimen/dp_8"
            android:src="@drawable/icon_back_array_bold_black"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvTitle"
            style="@style/MetaTextView.S18.PoppinsSemiBold600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/color_17191C"
            android:textSize="@dimen/sp_18"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="@string/group_members_page_title" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvAdd"
            style="@style/MetaTextView.S14.PoppinsMedium500"
            android:layout_width="@dimen/dp_61"
            android:layout_height="@dimen/dp_31"
            android:layout_marginEnd="@dimen/dp_16"
            android:background="@drawable/bg_ffef30_round_100"
            android:gravity="center"
            android:text="@string/group_members_page_add_button"
            android:textColor="@color/color_1A1A1A"
            android:textSize="@dimen/sp_14"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titleLayout">

        <com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout
            android:id="@+id/refreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginEnd="@dimen/dp_28">

            <com.airbnb.epoxy.EpoxyRecyclerView
                android:id="@+id/rv"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:orientation="vertical"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:spanCount="1"
                tools:listitem="@layout/item_group_chat_member" />
        </com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout>

        <com.socialplay.gpark.ui.view.AlphabetIndexView
            android:id="@+id/indexBar"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="end|center_vertical"
            android:layout_marginEnd="@dimen/dp_8"
            android:visibility="visible" />

        <com.socialplay.gpark.ui.view.LoadingView
            android:id="@+id/loadingView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white"
            android:visibility="gone" />
    </FrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>