<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_gravity="center"
    android:background="@drawable/bg_alphabet_indicator">

    <TextView
        android:id="@+id/tv_overlay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="A"
        android:minHeight="@dimen/dp_36"
        android:minWidth="@dimen/dp_48"
        android:textStyle="bold"
        android:gravity="center"
        android:textColor="#1A1A1A"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>