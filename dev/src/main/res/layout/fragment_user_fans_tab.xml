<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    tools:background="@color/black">

    <include layout="@layout/include_common_bottom_sheet_handle" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white">

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tabUserFans"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_44"
            android:layout_marginHorizontal="@dimen/dp_32"
            android:background="@null"
            android:paddingTop="@dimen/dp_16"
            app:tabBackground="@null"
            app:tabGravity="fill"
            app:tabIndicator="@drawable/indicator_hut_tab"
            app:tabIndicatorFullWidth="false"
            app:tabIndicatorHeight="3dp"
            app:tabMode="fixed"
            app:tabPaddingBottom="0dp"
            app:tabPaddingEnd="0dp"
            app:tabPaddingStart="0dp"
            app:tabPaddingTop="0dp"
            app:tabRippleColor="@color/transparent" />

    </FrameLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/neutral_color_8" />

    <com.socialplay.gpark.ui.view.viewpager.AllowStateLossFrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/vpUserFans"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white" />

    </com.socialplay.gpark.ui.view.viewpager.AllowStateLossFrameLayout>
</LinearLayout>