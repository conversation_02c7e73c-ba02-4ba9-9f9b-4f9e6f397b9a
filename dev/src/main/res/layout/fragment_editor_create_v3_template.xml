<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:speed_monitor="true">

    <com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout
        android:id="@+id/srl"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_5"
            android:overScrollMode="never" />

    </com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout>

    <FrameLayout
        android:id="@+id/layout_build_card_container"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_84"
        android:layout_gravity="bottom"
        android:layout_marginBottom="-84dp"
        android:background="@color/white"
        tools:layout_marginBottom="0dp">

        <androidx.cardview.widget.CardView
            android:id="@+id/layout_build_card"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48"
            android:layout_margin="@dimen/dp_16"
            app:cardBackgroundColor="@color/color_FFEF30"
            app:cardCornerRadius="@dimen/dp_100"
            app:cardElevation="0dp"
            app:layout_constraintBottom_toTopOf="@id/tv_build_list_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tv_build"
                style="@style/MetaTextView.S16.PoppinsSemiBold600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:drawablePadding="@dimen/dp_4"
                android:gravity="center"
                android:text="@string/create_v2_build"
                android:textColor="@color/color_212121"
                app:drawableStartCompat="@drawable/go_build" />
        </androidx.cardview.widget.CardView>
    </FrameLayout>

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/lv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:visibility="gone" />

</FrameLayout>