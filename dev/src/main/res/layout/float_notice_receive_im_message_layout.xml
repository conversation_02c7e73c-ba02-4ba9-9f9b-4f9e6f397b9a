<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/float_notice_root_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:layout_marginHorizontal="@dimen/dp_16"
    android:layout_marginBottom="@dimen/dp_12"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="@dimen/dp_12">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_80">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/ivAvatar"
            android:layout_width="@dimen/dp_48"
            android:layout_height="@dimen/dp_48"
            android:layout_marginStart="@dimen/dp_16"
            android:scaleType="centerCrop"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeAppearance="@style/circleStyle"
            tools:src="@drawable/icon_item_group_chat_avatar" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvTitle"
            style="@style/MetaTextView.S15.PoppinsMedium500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_4"
            android:ellipsize="end"
            android:maxLines="1"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toStartOf="@id/layoutCloseNotification"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toEndOf="@id/ivAvatar"
            app:layout_constraintTop_toTopOf="@id/ivAvatar"
            tools:text="dx's Test Group-----------------------------" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvContent"
            style="@style/MetaTextView.S12.PoppinsRegular400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_2"
            android:ellipsize="end"
            android:maxLines="1"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toStartOf="@id/layoutCloseNotification"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="@id/tvTitle"
            app:layout_constraintTop_toBottomOf="@id/tvTitle"
            tools:text="消息内容-----------------------------------------------------" />

        <FrameLayout
            android:id="@+id/layoutCloseNotification"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp_6"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/layoutMessageReplay"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/ivCloseNotification"
                android:layout_width="@dimen/dp_36"
                android:layout_height="@dimen/dp_36"
                android:background="@drawable/bg_f0f0f0_oval"
                android:padding="@dimen/dp_7"
                android:scaleType="centerCrop"
                android:src="@drawable/icon_im_notification_close" />

        </FrameLayout>

        <FrameLayout
            android:id="@+id/layoutMessageReplay"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_10"
            android:padding="@dimen/dp_6"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/ivMessageReplay"
                android:layout_width="@dimen/dp_36"
                android:layout_height="@dimen/dp_36"
                android:background="@drawable/bg_ffef30_circle"
                android:padding="@dimen/dp_7"
                android:scaleType="centerCrop"
                android:src="@drawable/icon_im_notification_message" />

        </FrameLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>