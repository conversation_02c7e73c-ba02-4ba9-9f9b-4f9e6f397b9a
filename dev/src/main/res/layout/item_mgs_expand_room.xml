<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rlMgsRoomItemRoot"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:background="@color/textColorPrimary">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:paddingHorizontal="@dimen/dp_13"
        android:paddingTop="@dimen/dp_8"
        android:paddingBottom="@dimen/dp_8">

        <ImageView
            android:id="@+id/iv_mgs_room_avatar"
            android:layout_width="@dimen/dp_32"
            android:layout_height="@dimen/dp_32"
            android:src="@drawable/placeholder_corner_360"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tvMgsRoomUserName"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvMgsRoomUserName"
            style="@style/MetaTextView.S12.PoppinsBold700"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="2"
            android:paddingStart="@dimen/dp_5"
            android:paddingEnd="@dimen/dp_8"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_12"
            app:layout_constraintBottom_toBottomOf="@id/iv_mgs_room_avatar"
            app:layout_constraintEnd_toStartOf="@id/ivCertification"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toEndOf="@id/iv_mgs_room_avatar"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constrainedWidth="true"
            tools:text="isdisdisdisdisdisd" />

        <ImageView
            android:id="@+id/ivCertification"
            android:layout_width="@dimen/dp_14"
            android:layout_height="@dimen/dp_14"
            android:layout_marginStart="@dimen/dp_4"
            android:layout_marginEnd="@dimen/dp_15"
            android:src="@drawable/official_certification"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/tvMgsRoomUserName"
            app:layout_constraintEnd_toStartOf="@id/ivVoiceState"
            app:layout_constraintStart_toEndOf="@id/tvMgsRoomUserName"
            app:layout_constraintTop_toTopOf="@id/tvMgsRoomUserName"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/ivVoiceState"
            android:layout_width="@dimen/dp_24"
            android:layout_height="@dimen/dp_24"
            android:src="@drawable/icon_mgs_open_other"
            android:layout_marginEnd="@dimen/dp_8"
            tools:visibility="gone"
            app:layout_goneMarginEnd="0dp"
            app:layout_constraintBottom_toBottomOf="@id/tvMgsRoomAddFriend"
            app:layout_constraintEnd_toStartOf="@id/tvMgsRoomAddFriend"
            app:layout_constraintTop_toTopOf="@id/tvMgsRoomAddFriend" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvMgsRoomAddFriend"
            style="@style/MetaTextView.S12.PoppinsBold700"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_8"
            android:layout_marginEnd="@dimen/dp_4"
            android:background="@drawable/selector_button"
            android:gravity="center"
            android:includeFontPadding="false"
            android:minWidth="@dimen/dp_50"
            android:minHeight="@dimen/dp_24"
            android:text="@string/meta_mgs_apply"
            android:textColor="@color/selector_button_primary_text_color"
            android:textSize="@dimen/sp_10"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</RelativeLayout>