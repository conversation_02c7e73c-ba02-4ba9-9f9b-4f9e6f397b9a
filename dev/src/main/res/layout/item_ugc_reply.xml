<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:id="@+id/v_invoke_area"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/dp_3"
        app:layout_constraintTop_toBottomOf="@id/tv_nickname"
        android:layout_marginBottom="@dimen/dp_8"
        app:layout_constraintBottom_toTopOf="@id/tv_time"
        app:layout_constraintStart_toStartOf="@id/tv_nickname"
        app:layout_constraintEnd_toStartOf="@id/iv_more_btn" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guide_right"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_end="@dimen/dp_16" />

    <View
        android:id="@+id/v_bg_highlight"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:alpha="0"
        android:background="@color/color_f5f5f5"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_avatar"
        android:layout_width="@dimen/dp_28"
        android:layout_height="@dimen/dp_28"
        android:layout_marginStart="@dimen/dp_60"
        android:layout_marginTop="@dimen/dp_16"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_nickname"
        style="@style/MetaTextView.S12.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_8"
        android:ellipsize="end"
        android:maxLines="1"
        android:singleLine="true"
        android:textColor="@color/color_BDBDBD"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toStartOf="@id/ulv"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@id/iv_avatar"
        app:layout_constraintTop_toTopOf="@id/iv_more_btn"
        app:layout_constraintBottom_toBottomOf="@id/iv_more_btn"
        tools:text="SomebodySomebodySomebody" />

    <com.socialplay.gpark.ui.view.UserLabelView
        android:id="@+id/ulv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/iv_more_btn"
        app:layout_constraintEnd_toStartOf="@id/space_more"
        app:layout_constraintStart_toEndOf="@id/tv_nickname"
        app:layout_constraintTop_toTopOf="@id/iv_more_btn"
        tools:visibility="visible" />

    <Space
        android:id="@+id/space_more"
        android:layout_width="@dimen/dp_8"
        android:layout_height="@dimen/dp_8"
        app:layout_constraintEnd_toStartOf="@id/iv_more_btn"
        app:layout_constraintTop_toTopOf="@id/iv_more_btn" />

    <ImageView
        android:id="@+id/iv_more_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_game_detail_common_comment_more"
        app:layout_constraintEnd_toEndOf="@id/guide_right"
        app:layout_constraintTop_toTopOf="@id/iv_avatar" />

    <com.socialplay.gpark.ui.view.ExpandableTextView
        android:id="@+id/tv_content"
        style="@style/MetaTextView.S14"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_8"
        android:lineSpacingMultiplier="1.2"
        android:textColor="@color/color_212121"
        android:textColorHighlight="@color/transparent"
        app:etv_EnableToggleClick="true"
        app:etv_ExtendClickScope="true"
        app:etv_MaxLinesOnShrink="5"
        app:etv_ToExpandHint="@string/more_cap"
        app:etv_ToExpandHintColor="@color/color_757575"
        app:etv_ToShrinkHint="@string/collapse_cap"
        app:etv_ToShrinkHintColor="@color/color_757575"
        app:layout_constraintEnd_toEndOf="@id/guide_right"
        app:layout_constraintStart_toStartOf="@id/tv_nickname"
        app:layout_constraintTop_toBottomOf="@id/iv_more_btn"
        tools:text="reply content" />

    <androidx.cardview.widget.CardView
        android:id="@+id/cv_image_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_8"
        android:visibility="gone"
        app:cardBackgroundColor="@color/transparent"
        app:cardCornerRadius="@dimen/dp_8"
        app:cardElevation="0dp"
        app:layout_constraintStart_toStartOf="@id/tv_nickname"
        app:layout_constraintTop_toBottomOf="@id/tv_content"
        app:layout_goneMarginTop="@dimen/dp_4"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/iv_image_1"
            android:layout_width="@dimen/dp_86"
            android:layout_height="@dimen/dp_86"
            android:scaleType="centerCrop" />

        <ImageView
            android:id="@+id/iv_image_2"
            android:layout_width="@dimen/dp_86"
            android:layout_height="@dimen/dp_86"
            android:layout_marginStart="@dimen/dp_88"
            android:scaleType="centerCrop" />

        <ImageView
            android:id="@+id/iv_image_3"
            android:layout_width="@dimen/dp_86"
            android:layout_height="@dimen/dp_86"
            android:layout_marginStart="@dimen/dp_176"
            android:scaleType="centerCrop" />

    </androidx.cardview.widget.CardView>

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_time"
        style="@style/MetaTextView.S12"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_8"
        android:textColor="@color/color_757575"
        app:layout_constraintStart_toStartOf="@id/tv_nickname"
        app:layout_constraintTop_toBottomOf="@id/cv_image_container"
        tools:text="Nov 12, 2022 14:12" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_like_count"
        style="@style/MetaTextView.S12"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawableStart="@drawable/selector_ugc_comment_like"
        android:drawablePadding="@dimen/dp_2"
        android:gravity="center_vertical"
        android:textColor="@color/color_ugc_comment_like"
        app:layout_constraintBottom_toBottomOf="@id/tv_time"
        app:layout_constraintEnd_toEndOf="@id/guide_right"
        app:layout_constraintTop_toTopOf="@id/tv_time"
        tools:text="26" />

    <Space
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_8"
        app:layout_constraintTop_toBottomOf="@id/tv_time" />

</androidx.constraintlayout.widget.ConstraintLayout>