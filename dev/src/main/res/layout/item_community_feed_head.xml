<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="@dimen/dp_16">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivPortrait"
        android:layout_width="@dimen/dp_47"
        android:layout_height="@dimen/dp_47"
        android:layout_marginTop="@dimen/dp_14"
        android:scaleType="centerCrop"
        android:src="@drawable/icon_default_avatar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/circleStyle" />

    <ImageView
        android:id="@+id/ivState"
        android:layout_width="@dimen/dp_10"
        android:layout_height="@dimen/dp_10"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginEnd="@dimen/dp_1"
        android:layout_marginBottom="@dimen/dp_1"
        android:src="@drawable/shape_green_point_white_stroke"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/ivPortrait"
        app:layout_constraintEnd_toEndOf="@id/ivPortrait"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvName"
        style="@style/MetaTextView.S14.PoppinsSemiBold600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_12"
        android:ellipsize="end"
        android:maxLines="1"
        android:singleLine="true"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@id/tvTime"
        app:layout_constraintEnd_toStartOf="@id/ivCertification"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@id/ivPortrait"
        app:layout_constraintTop_toTopOf="@id/ivPortrait"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="AAAAAAAAAAAAAAAAAAAAAAAAAA" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvTime"
        style="@style/MetaTextView.S12.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_12"
        android:drawablePadding="@dimen/dp_4"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:singleLine="true"
        android:textColor="@color/neutral_color_4"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@id/ivPortrait"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toEndOf="@id/ivPortrait"
        app:layout_constraintTop_toBottomOf="@id/tvName"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="Nov 12, 2022  14:12 AM" />

    <com.socialplay.gpark.ui.view.UserLabelView
        android:id="@+id/ivCertification"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_12"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tvName"
        app:layout_constraintEnd_toStartOf="@id/ivPin"
        app:layout_constraintStart_toEndOf="@id/tvName"
        app:layout_constraintTop_toTopOf="@id/tvName"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/ivPin"
        android:layout_width="@dimen/dp_20"
        android:layout_height="@dimen/dp_20"
        android:src="@drawable/ic_post_pin"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/layerUser"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/layerUser"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.ExpandableTextView
        android:id="@+id/tvContent"
        style="@style/MetaTextView.S14.PoppinsRegular400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_12"
        android:textColor="@color/neutral_color_2"
        app:etv_EnableToggleClick="false"
        app:etv_ExtendClickScope="true"
        app:etv_MaxLinesOnShrink="5"
        app:etv_ToExpandHint="@string/more_cap"
        app:etv_ToExpandHintBold="false"
        app:etv_ToExpandHintColor="@color/secondary_color_1"
        app:layout_constraintTop_toBottomOf="@id/ivPortrait"
        tools:text="aaaaaaaaaaaaaaa" />

    <View
        android:id="@+id/layerUser"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@id/tvTime"
        app:layout_constraintEnd_toEndOf="@id/tvName"
        app:layout_constraintStart_toStartOf="@id/ivPortrait"
        app:layout_constraintTop_toTopOf="@id/ivPortrait" />

    <View
        android:id="@+id/vMask"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:clickable="true"
        app:layout_constraintBottom_toBottomOf="@id/tvContent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>