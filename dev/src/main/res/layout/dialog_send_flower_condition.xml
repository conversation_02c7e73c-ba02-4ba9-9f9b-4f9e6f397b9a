<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:background="@color/black_60">

    <Space
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/dialogLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="@dimen/dp_42"
        android:background="@drawable/bg_white_round_38"
        android:paddingHorizontal="@dimen/dp_15"
        android:paddingBottom="@dimen/dp_32">

        <!-- 送花功能开启状态: dialog_send_flowers_feature_title -->
        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvTitle"
            style="@style/MetaTextView.S18.PoppinsSemiBold600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_32"
            android:text="@string/dialog_send_flowers_conditions_title"
            android:textColor="@color/black"
            android:visibility="gone"
            android:layout_marginEnd="@dimen/dp_8"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toStartOf="@id/ivClose"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/ivClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_32"
            android:src="@drawable/icon_dialog_close"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 送花条件不满足的时候显示 -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layoutConditionContent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_16"
            android:background="@drawable/bg_f9f5ff_round_16"
            android:padding="@dimen/dp_16"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/tvTitle"
            tools:visibility="gone">

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvConditionTitle"
                style="@style/MetaTextView.S14.PoppinsRegular400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/dialog_send_flowers_conditions_content_title"
                android:textColor="@color/color_1A1A1A"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.airbnb.epoxy.EpoxyRecyclerView
                android:id="@+id/rvConditions"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintTop_toBottomOf="@id/tvConditionTitle"
                tools:layout_height="@dimen/dp_100"
                tools:listitem="@layout/item_send_flower_condition" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- 送花条件不满足的时候显示 -->
        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvConditionGiftingTerms"
            style="@style/MetaTextView.S14.PoppinsRegular400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginTop="@dimen/dp_16"
            android:text="@string/dialog_send_flowers_conditions_terms"
            android:textColor="@color/color_1A1A1A"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layoutConditionContent"
            tools:visibility="gone" />

        <!-- 送花功能可用的时候显示 -->
        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvSendFlowerEnableDesc1"
            style="@style/MetaTextView.S14.PoppinsRegular400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginTop="@dimen/dp_16"
            android:text="@string/dialog_send_flowers_feature_text1"
            android:textColor="@color/color_333333"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvTitle"
            tools:visibility="gone" />

        <!-- 送花功能可用的时候显示 -->
        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvSendFlowerEnableDesc2"
            style="@style/MetaTextView.S14.PoppinsRegular400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginTop="@dimen/dp_16"
            android:text="@string/dialog_send_flowers_feature_text2"
            android:textColor="@color/color_333333"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvSendFlowerEnableDesc1"
            tools:visibility="gone" />

        <!-- 送花功能不可用的时候显示 -->
        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvSendFlowerDisableDesc1"
            style="@style/MetaTextView.S14.PoppinsRegular400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginTop="@dimen/dp_16"
            android:text="@string/dialog_send_flowers_feature_disable_text1"
            android:textColor="@color/color_333333"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvTitle"
            tools:visibility="visible" />

        <!-- 送花功能不可用的时候显示 -->
        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvSendFlowerDisableDesc2"
            style="@style/MetaTextView.S16.PoppinsRegular400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginTop="@dimen/dp_16"
            android:text="@string/dialog_send_flowers_feature_disable_text2"
            android:textColor="@color/color_9242FF"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvSendFlowerDisableDesc1"
            tools:visibility="visible" />

        <!-- 送花功能不可用的时候显示 -->
        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvSendFlowerDisableDesc3"
            style="@style/MetaTextView.S14.PoppinsRegular400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginTop="@dimen/dp_16"
            android:text="@string/dialog_send_flowers_feature_disable_text3"
            android:textColor="@color/color_333333"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvSendFlowerDisableDesc2"
            tools:visibility="visible" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/barrier"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="tvConditionGiftingTerms,tvSendFlowerEnableDesc2,tvSendFlowerDisableDesc3" />

        <!-- 送花条件不满足/送花功能可用的时候显示 -->
        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvOk"
            style="@style/MetaTextView.S16.PoppinsMedium500"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48"
            android:layout_marginTop="@dimen/dp_16"
            android:background="@drawable/bg_9242ff_round_40"
            android:gravity="center"
            android:text="@string/dialog_send_flowers_confirm_text"
            android:textColor="@color/white"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/barrier"
            tools:visibility="visible" />

        <!-- 送花功能不可用的时候显示 -->
        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvEnableFeature"
            style="@style/MetaTextView.S16.PoppinsMedium500"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48"
            android:layout_marginTop="@dimen/dp_16"
            android:background="@drawable/bg_9242ff_round_40"
            android:gravity="center"
            android:text="@string/dialog_send_flowers_feature_enable_confirm"
            android:textColor="@color/white"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/barrier"
            tools:visibility="gone" />

        <!-- 送花功能可用的时候显示 -->
        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvTurnOff"
            style="@style/MetaTextView.S16.PoppinsMedium500"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48"
            android:layout_marginTop="@dimen/dp_16"
            android:background="@drawable/bg_f0f0f0_corner_40"
            android:gravity="center"
            android:text="@string/dialog_send_flowers_feature_turn_off_text"
            android:textColor="@color/color_1A1A1A"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/tvOk"
            tools:visibility="visible" />

        <com.socialplay.gpark.ui.view.LoadingView
            android:id="@+id/loadingView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ivClose"
            tools:visibility="gone" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>