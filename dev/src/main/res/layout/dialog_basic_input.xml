<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:gravity="bottom"
    android:isScrollContainer="true"
    android:orientation="vertical">


    <LinearLayout
        android:id="@+id/input_all_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/dp_16"
        android:paddingVertical="@dimen/dp_12">


        <EditText
            android:id="@+id/et_inputMessage"
            style="@style/Button.S14.PoppinsRegular400"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/bg_corner_detail_comment"
            android:gravity="start|center_vertical"
            android:maxHeight="80dp"
            android:maxLength="400"
            android:minHeight="36dp"
            android:paddingLeft="12dp"
            android:paddingTop="5dp"
            android:paddingBottom="5dp"
            android:paddingRight="12dp"
            android:textColor="@color/neutral_color_2"
            android:textColorHint="@color/neutral_color_3"
            android:textFontWeight="1"
            tools:hint="Reply jesse" />


        <TextView
            android:id="@+id/tv_send"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_6"
            style="@style/MetaTextView.S14.PoppinsMedium500"
            android:layout_gravity="bottom"
            android:background="@drawable/bg_basic_input_reply"
            android:paddingHorizontal="@dimen/dp_20"
            android:paddingVertical="@dimen/dp_7"
            android:minHeight="@dimen/sp_36"
            android:textColor="@color/white"
            tools:text="Send" />

    </LinearLayout>
</LinearLayout>