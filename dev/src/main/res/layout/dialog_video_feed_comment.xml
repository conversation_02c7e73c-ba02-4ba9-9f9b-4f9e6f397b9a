<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_corner_top_20_white"
    android:minHeight="580dp">

    <View
        android:id="@+id/v_comment_top_indicator"
        android:layout_width="@dimen/dp_36"
        android:layout_height="@dimen/dp_4"
        android:layout_marginVertical="@dimen/dp_12"
        android:background="@drawable/bg_video_feed_comment_top_indicator"
        android:paddingVertical="@dimen/dp_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <FrameLayout
        android:id="@+id/fl_total_comment_count_container"
        app:layout_constraintTop_toBottomOf="@id/v_comment_top_indicator"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_comment_count_hang"
            style="@style/MetaTextView.S13.PoppinsSemiBold600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_16"
            android:paddingVertical="@dimen/dp_12"
            tools:text="26 Comments" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_comment_sort_hang"
            style="@style/MetaTextView.S12.PoppinsRegular400.secondary"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical|end"
            android:drawableEnd="@drawable/ic_post_sort_arrow"
            android:drawablePadding="@dimen/dp_2"
            android:paddingHorizontal="@dimen/dp_16"
            android:paddingVertical="@dimen/dp_12" />

    </FrameLayout>


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_comment"
        android:layout_width="match_parent"
        app:layout_constraintTop_toBottomOf="@+id/fl_total_comment_count_container"
        app:layout_constraintBottom_toTopOf="@+id/v_comment_split"
        android:layout_height="0dp"
        android:overScrollMode="never"
        app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior" />


    <View
        android:id="@+id/v_comment_split"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/color_F0F0F0"
        app:layout_constraintBottom_toTopOf="@id/v_comment_bg" />

    <View
        android:id="@+id/v_comment_bg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="-12dp"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_comment" />

    <View
        android:id="@+id/v_comment_cover"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/v_comment_split"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_comment"
        style="@style/MetaTextView.S14"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginVertical="@dimen/dp_12"
        android:background="@drawable/bg_f0f0f0_corner_20"
        android:gravity="center_vertical"
        android:hint="@string/post_reply"
        android:lineSpacingMultiplier="1.2"
        android:maxHeight="@dimen/dp_186"
        android:maxLength="300"
        android:paddingVertical="@dimen/dp_8"
        android:paddingStart="@dimen/dp_12"
        android:paddingEnd="@dimen/dp_12"
        android:textColorHint="@color/color_666666"
        android:textCursorDrawable="@drawable/bg_cursor"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_goneMarginEnd="@dimen/dp_16" />


    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/lv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="@+id/rv_comment"
        app:layout_constraintTop_toTopOf="@+id/rv_comment"
        tools:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>