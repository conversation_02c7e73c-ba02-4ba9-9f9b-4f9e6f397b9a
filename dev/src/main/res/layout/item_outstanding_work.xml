<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/dp_8"
    android:layout_marginEnd="@dimen/dp_8"
    android:layout_marginBottom="@dimen/dp_16">

    <!-- 封面图片 -->
    <com.socialplay.gpark.ui.view.RoundImageView
        android:id="@+id/ivCover"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/dp_6"
        android:scaleType="centerCrop"
        app:cornerRadii="@dimen/dp_12"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@color/default_shadow_color" />

    <!-- Outstanding标识 -->
    <LinearLayout
        android:id="@+id/layoutOutstandingTag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="-5dp"
        android:background="@drawable/bg_home_template_child_item"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:visibility="visible"
        app:layout_constraintStart_toStartOf="@id/ivCover"
        app:layout_constraintTop_toTopOf="@id/ivCover"
        tools:visibility="visible">

        <TextView
            android:id="@+id/tvOutstanding"
            style="@style/MetaTextView.S8.PoppinsBlack900"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_2"
            android:text="Outstanding"
            android:textColor="@color/white" />
    </LinearLayout>

    <!-- 点赞和播放量 -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginBottom="@dimen/dp_8"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="@id/ivCover"
        app:layout_constraintStart_toStartOf="@id/ivCover">

        <com.socialplay.gpark.ui.view.MetaLikeView
            android:id="@+id/likeView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:likeText="22.2k" />

        <com.socialplay.gpark.ui.view.MetaLikeView
            android:id="@+id/tvPlayCount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_4"
            app:likeText="22.2k Plays"
            app:showIcon="false" />
    </LinearLayout>

    <!-- 标题 -->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvTitle"
        style="@style/MetaTextView.S13.PoppinsMedium500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_6"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/Gray_1000"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivCover"
        tools:text="Enchilada Casserole Tutorial" />

    <!-- 作者信息 -->
    <LinearLayout
        android:id="@+id/layoutAuthor"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_4"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvTitle">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/sivAuthorAvatar"
            android:layout_width="@dimen/dp_18"
            android:layout_height="@dimen/dp_18"
            android:foreground="@drawable/avatar_inner_stroke_border"
            android:src="@color/color_8792B2"
            android:visibility="visible"
            app:shapeAppearance="@style/circleStyle" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvAuthorName"
            style="@style/MetaTextView.S12.PoppinsRegular400"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_4"
            android:layout_weight="1"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/color_757575"
            tools:text="SEThomsont2" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout> 