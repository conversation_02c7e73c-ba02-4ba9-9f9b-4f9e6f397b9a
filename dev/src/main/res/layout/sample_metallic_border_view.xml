<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#1A1A1A"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 调试View展示区域 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:text="金属边框调试展示"
            android:textColor="#FFFFFF"
            android:textSize="18sp"
            android:textStyle="bold" />

        <!-- 第一对：原版 vs 边框专注版 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="原版 MetallicBorderView vs 边框专注版"
            android:textColor="#FFFF00"
            android:textSize="14sp"
            android:textStyle="bold" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:orientation="horizontal"
            android:clipToOutline="false"
            android:padding="@dimen/dp_12">

            <com.socialplay.gpark.ui.view.MetallicBorderView
                android:id="@+id/metallicBorder1"
                android:layout_width="0dp"
                android:clipToOutline="false"
                android:layout_height="80dp"
                android:layout_margin="@dimen/dp_12"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                app:borderThickness="6dp"
                app:cornerRadius="12dp"
                app:highlightColor="#FFFFFF"
                app:highlightIntensity="0.9"
                app:lightAngle="45"
                app:metallicBaseColor="#C0C0C0"
                app:shadowColor="#000000"
                app:shadowIntensity="0.7" />

            <com.socialplay.gpark.ui.view.BorderFocusedMetallicView
                android:id="@+id/borderFocusedMetallic"
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:layout_weight="1"
                android:clipToOutline="false"
                android:layout_margin="@dimen/dp_12"
                app:borderThickness="6dp"
                app:cornerRadius="12dp"
                app:highlightIntensity="1.0"
                app:lightAngle="45"
                app:shadowIntensity="0.8" />

        </LinearLayout>

        <!-- 第二对：简化版 vs 金色版 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="简化版 vs 金色金属边框"
            android:textColor="#FFFF00"
            android:textSize="14sp"
            android:textStyle="bold" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:orientation="horizontal"
            android:padding="@dimen/dp_12">

            <com.socialplay.gpark.ui.view.SimpleMetallicBorderView
                android:id="@+id/simpleMetallicBorder"
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                app:borderThickness="6dp"
                app:cornerRadius="12dp"
                app:highlightIntensity="1.0"
                app:lightAngle="45"
                app:shadowIntensity="0.8" />

            <com.socialplay.gpark.ui.view.MetallicBorderView
                android:id="@+id/metallicBorder2"
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                app:borderThickness="6dp"
                app:cornerRadius="12dp"
                app:highlightColor="#FFFF99"
                app:highlightIntensity="1.0"
                app:lightAngle="45"
                app:metallicBaseColor="#FFD700"
                app:shadowColor="#B8860B"
                app:shadowIntensity="0.8" />

        </LinearLayout>

        <!-- 第三对：铜色版 vs 紫色版 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="铜色边框 vs 紫色边框"
            android:textColor="#FFFF00"
            android:textSize="14sp"
            android:textStyle="bold" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:orientation="horizontal"
            android:padding="@dimen/dp_12">

            <com.socialplay.gpark.ui.view.MetallicBorderView
                android:id="@+id/metallicBorder3"
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                app:borderThickness="6dp"
                app:cornerRadius="12dp"
                app:highlightColor="#FFA500"
                app:highlightIntensity="0.8"
                app:lightAngle="45"
                app:metallicBaseColor="#CD853F"
                app:shadowColor="#8B4513"
                app:shadowIntensity="0.9" />

            <com.socialplay.gpark.ui.view.MetallicBorderView
                android:id="@+id/metallicBorder4"
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                app:borderThickness="6dp"
                app:cornerRadius="12dp"
                app:highlightColor="#E6E6FA"
                app:highlightIntensity="0.9"
                app:lightAngle="45"
                app:metallicBaseColor="#9370DB"
                app:shadowColor="#4B0082"
                app:shadowIntensity="0.8" />

        </LinearLayout>

        <!-- 调试控制区域 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp"
            android:text="调试控制面板"
            android:textColor="#FFFFFF"
            android:textSize="18sp"
            android:textStyle="bold" />

        <!-- 光照角度调节 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:background="#2A2A2A"
            android:orientation="vertical"
            android:padding="12dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="光照角度调节"
                android:textColor="#FFFF00"
                android:textSize="14sp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="角度: "
                    android:textColor="#FFFFFF"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tvLightAngle"
                    android:layout_width="40dp"
                    android:layout_height="wrap_content"
                    android:text="45°"
                    android:textColor="#00FF00"
                    android:textSize="12sp"
                    android:textStyle="bold" />

                <SeekBar
                    android:id="@+id/seekBarLightAngle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="8dp"
                    android:layout_weight="1"
                    android:max="360"
                    android:progress="45" />

            </LinearLayout>

        </LinearLayout>

        <!-- 圆角半径调节 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:background="#2A2A2A"
            android:orientation="vertical"
            android:padding="12dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="圆角半径调节"
                android:textColor="#FFFF00"
                android:textSize="14sp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="半径: "
                    android:textColor="#FFFFFF"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tvCornerRadius"
                    android:layout_width="40dp"
                    android:layout_height="wrap_content"
                    android:text="12dp"
                    android:textColor="#00FF00"
                    android:textSize="12sp"
                    android:textStyle="bold" />

                <SeekBar
                    android:id="@+id/seekBarCornerRadius"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="8dp"
                    android:layout_weight="1"
                    android:max="40"
                    android:progress="12" />

            </LinearLayout>

        </LinearLayout>

        <!-- 边框厚度调节 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:background="#2A2A2A"
            android:orientation="vertical"
            android:padding="12dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="边框厚度调节"
                android:textColor="#FFFF00"
                android:textSize="14sp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="厚度: "
                    android:textColor="#FFFFFF"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tvBorderThickness"
                    android:layout_width="40dp"
                    android:layout_height="wrap_content"
                    android:text="6dp"
                    android:textColor="#00FF00"
                    android:textSize="12sp"
                    android:textStyle="bold" />

                <SeekBar
                    android:id="@+id/seekBarBorderThickness"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="8dp"
                    android:layout_weight="1"
                    android:max="20"
                    android:progress="6" />

            </LinearLayout>

        </LinearLayout>

        <!-- 主光晕调节 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:background="#2A2A2A"
            android:orientation="vertical"
            android:padding="12dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="主光晕调节"
                android:textColor="#FFFF00"
                android:textSize="14sp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="强度: "
                    android:textColor="#FFFFFF"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tvHighlightIntensity"
                    android:layout_width="40dp"
                    android:layout_height="wrap_content"
                    android:text="90%"
                    android:textColor="#00FF00"
                    android:textSize="12sp"
                    android:textStyle="bold" />

                <SeekBar
                    android:id="@+id/seekBarHighlightIntensity"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="8dp"
                    android:layout_weight="1"
                    android:max="100"
                    android:progress="90" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="角度: "
                    android:textColor="#FFFFFF"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tvHighlightAngle"
                    android:layout_width="40dp"
                    android:layout_height="wrap_content"
                    android:text="45°"
                    android:textColor="#00FF00"
                    android:textSize="12sp"
                    android:textStyle="bold" />

                <SeekBar
                    android:id="@+id/seekBarHighlightAngle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="8dp"
                    android:layout_weight="1"
                    android:max="360"
                    android:progress="45" />

            </LinearLayout>

        </LinearLayout>

        <!-- 补光光晕调节 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:background="#2A2A2A"
            android:orientation="vertical"
            android:padding="12dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="补光光晕调节（暗区补光）"
                android:textColor="#FFFF00"
                android:textSize="14sp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="强度: "
                    android:textColor="#FFFFFF"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tvFillLightIntensity"
                    android:layout_width="40dp"
                    android:layout_height="wrap_content"
                    android:text="30%"
                    android:textColor="#00FF00"
                    android:textSize="12sp"
                    android:textStyle="bold" />

                <SeekBar
                    android:id="@+id/seekBarFillLightIntensity"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="8dp"
                    android:layout_weight="1"
                    android:max="100"
                    android:progress="30" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="角度: "
                    android:textColor="#FFFFFF"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tvFillLightAngle"
                    android:layout_width="40dp"
                    android:layout_height="wrap_content"
                    android:text="225°"
                    android:textColor="#00FF00"
                    android:textSize="12sp"
                    android:textStyle="bold" />

                <SeekBar
                    android:id="@+id/seekBarFillLightAngle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="8dp"
                    android:layout_weight="1"
                    android:max="360"
                    android:progress="225" />

            </LinearLayout>

        </LinearLayout>

        <!-- 外发光调节 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:background="#2A2A2A"
            android:orientation="vertical"
            android:padding="12dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="外发光调节"
                android:textColor="#FFFF00"
                android:textSize="14sp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="强度: "
                    android:textColor="#FFFFFF"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tvGlowIntensity"
                    android:layout_width="40dp"
                    android:layout_height="wrap_content"
                    android:text="50%"
                    android:textColor="#00FF00"
                    android:textSize="12sp"
                    android:textStyle="bold" />

                <SeekBar
                    android:id="@+id/seekBarGlowIntensity"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="8dp"
                    android:layout_weight="1"
                    android:max="100"
                    android:progress="50" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="颜色: "
                    android:textColor="#FFFFFF"
                    android:textSize="12sp" />

                <Button
                    android:id="@+id/btnGlowColorRed"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_marginEnd="4dp"
                    android:background="#FF0000"
                    android:text="" />

                <Button
                    android:id="@+id/btnGlowColorBlue"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_marginHorizontal="4dp"
                    android:background="#0080FF"
                    android:text="" />

                <Button
                    android:id="@+id/btnGlowColorGreen"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_marginHorizontal="4dp"
                    android:background="#00FF00"
                    android:text="" />

                <Button
                    android:id="@+id/btnGlowColorPurple"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_marginHorizontal="4dp"
                    android:background="#FF00FF"
                    android:text="" />

                <Button
                    android:id="@+id/btnGlowColorWhite"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_marginStart="4dp"
                    android:background="#FFFFFF"
                    android:text="" />

            </LinearLayout>

        </LinearLayout>

        <!-- 调试操作按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:background="#2A2A2A"
            android:orientation="vertical"
            android:padding="12dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="调试操作"
                android:textColor="#FFFF00"
                android:textSize="14sp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/btnDebugMode"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_marginEnd="8dp"
                    android:layout_weight="1"
                    android:text="调试模式"
                    android:textSize="12sp" />

                <Button
                    android:id="@+id/btnReset"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_marginStart="8dp"
                    android:layout_weight="1"
                    android:text="重置效果"
                    android:textSize="12sp" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</ScrollView>
