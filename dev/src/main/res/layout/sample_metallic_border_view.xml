<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="#1A1A1A">

    <!-- 示例1：基础金属边框 -->
    <com.socialplay.gpark.ui.view.MetallicBorderView
        android:id="@+id/metallicBorder1"
        android:layout_width="200dp"
        android:layout_height="100dp"
        android:layout_marginBottom="16dp"
        app:borderThickness="2dp"
        app:lightAngle="45"
        app:cornerRadius="8dp"
        app:highlightColor="#FFFFFF"
        app:shadowColor="#000000"
        app:metallicBaseColor="#C0C0C0"
        app:highlightIntensity="0.8"
        app:shadowIntensity="0.3" />

    <!-- 示例2：金色边框 -->
    <com.socialplay.gpark.ui.view.MetallicBorderView
        android:id="@+id/metallicBorder2"
        android:layout_width="150dp"
        android:layout_height="150dp"
        android:layout_marginBottom="16dp"
        app:borderThickness="3dp"
        app:lightAngle="135"
        app:cornerRadius="75dp"
        app:highlightColor="#FFFF99"
        app:shadowColor="#B8860B"
        app:metallicBaseColor="#FFD700"
        app:highlightIntensity="0.9"
        app:shadowIntensity="0.4" />

    <!-- 示例3：铜色边框 -->
    <com.socialplay.gpark.ui.view.MetallicBorderView
        android:id="@+id/metallicBorder3"
        android:layout_width="180dp"
        android:layout_height="80dp"
        android:layout_marginBottom="16dp"
        app:borderThickness="1.5dp"
        app:lightAngle="225"
        app:cornerRadius="12dp"
        app:highlightColor="#FFA500"
        app:shadowColor="#8B4513"
        app:metallicBaseColor="#CD853F"
        app:highlightIntensity="0.7"
        app:shadowIntensity="0.5" />

    <!-- 示例4：带内容的容器 -->
    <FrameLayout
        android:layout_width="250dp"
        android:layout_height="120dp"
        android:layout_marginBottom="16dp">

        <com.socialplay.gpark.ui.view.MetallicBorderView
            android:id="@+id/metallicBorder4"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:borderThickness="4dp"
            app:lightAngle="315"
            app:cornerRadius="16dp"
            app:highlightColor="#E6E6FA"
            app:shadowColor="#4B0082"
            app:metallicBaseColor="#9370DB"
            app:highlightIntensity="0.8"
            app:shadowIntensity="0.3" />

        <!-- 内容区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="4dp"
            android:background="#80000000"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="金属边框容器"
                android:textColor="#FFFFFF"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="这里可以放置任何内容"
                android:textColor="#CCCCCC"
                android:textSize="12sp" />

        </LinearLayout>

    </FrameLayout>

    <!-- 控制按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="16dp">

        <Button
            android:id="@+id/btnRotateLight"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:text="旋转光照"
            android:textSize="12sp" />

        <Button
            android:id="@+id/btnChangeBorder"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="改变边框"
            android:textSize="12sp" />

    </LinearLayout>

</LinearLayout>
