<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#1A1A1A"
    android:fillViewport="true">

<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- 调试View展示区域 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="金属边框调试展示"
        android:textColor="#FFFFFF"
        android:textSize="18sp"
        android:textStyle="bold" />

    <!-- 第一对：原版 vs 边框专注版 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:text="原版 MetallicBorderView vs 边框专注版"
        android:textColor="#FFFF00"
        android:textSize="14sp"
        android:textStyle="bold" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <com.socialplay.gpark.ui.view.MetallicBorderView
            android:id="@+id/metallicBorder1"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            app:borderThickness="6dp"
            app:lightAngle="45"
            app:cornerRadius="12dp"
            app:highlightColor="#FFFFFF"
            app:shadowColor="#000000"
            app:metallicBaseColor="#C0C0C0"
            app:highlightIntensity="0.9"
            app:shadowIntensity="0.7" />

        <com.socialplay.gpark.ui.view.BorderFocusedMetallicView
            android:id="@+id/borderFocusedMetallic"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            app:borderThickness="6dp"
            app:lightAngle="45"
            app:cornerRadius="12dp"
            app:highlightIntensity="1.0"
            app:shadowIntensity="0.8" />

    </LinearLayout>

    <!-- 第二对：简化版 vs 金色版 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:text="简化版 vs 金色金属边框"
        android:textColor="#FFFF00"
        android:textSize="14sp"
        android:textStyle="bold" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <com.socialplay.gpark.ui.view.SimpleMetallicBorderView
            android:id="@+id/simpleMetallicBorder"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            app:borderThickness="6dp"
            app:lightAngle="45"
            app:cornerRadius="12dp"
            app:highlightIntensity="1.0"
            app:shadowIntensity="0.8" />

        <com.socialplay.gpark.ui.view.MetallicBorderView
            android:id="@+id/metallicBorder2"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            app:borderThickness="6dp"
            app:lightAngle="45"
            app:cornerRadius="12dp"
            app:highlightColor="#FFFF99"
            app:shadowColor="#B8860B"
            app:metallicBaseColor="#FFD700"
            app:highlightIntensity="1.0"
            app:shadowIntensity="0.8" />

    </LinearLayout>

    <!-- 第三对：铜色版 vs 紫色版 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:text="铜色边框 vs 紫色边框"
        android:textColor="#FFFF00"
        android:textSize="14sp"
        android:textStyle="bold" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <com.socialplay.gpark.ui.view.MetallicBorderView
            android:id="@+id/metallicBorder3"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            app:borderThickness="6dp"
            app:lightAngle="45"
            app:cornerRadius="12dp"
            app:highlightColor="#FFA500"
            app:shadowColor="#8B4513"
            app:metallicBaseColor="#CD853F"
            app:highlightIntensity="0.8"
            app:shadowIntensity="0.9" />

        <com.socialplay.gpark.ui.view.MetallicBorderView
            android:id="@+id/metallicBorder4"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            app:borderThickness="6dp"
            app:lightAngle="45"
            app:cornerRadius="12dp"
            app:highlightColor="#E6E6FA"
            app:shadowColor="#4B0082"
            app:metallicBaseColor="#9370DB"
            app:highlightIntensity="0.9"
            app:shadowIntensity="0.8" />

    </LinearLayout>

    <!-- 调试控制区域 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        android:text="调试控制面板"
        android:textColor="#FFFFFF"
        android:textSize="18sp"
        android:textStyle="bold" />

    <!-- 光照角度调节 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="#2A2A2A"
        android:padding="12dp"
        android:layout_marginBottom="12dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="光照角度调节"
            android:textColor="#FFFF00"
            android:textSize="14sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="角度: "
                android:textColor="#FFFFFF"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/tvLightAngle"
                android:layout_width="40dp"
                android:layout_height="wrap_content"
                android:text="45°"
                android:textColor="#00FF00"
                android:textSize="12sp"
                android:textStyle="bold" />

            <SeekBar
                android:id="@+id/seekBarLightAngle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginHorizontal="8dp"
                android:max="360"
                android:progress="45" />

        </LinearLayout>

    </LinearLayout>

    <!-- 圆角半径调节 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="#2A2A2A"
        android:padding="12dp"
        android:layout_marginBottom="12dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="圆角半径调节"
            android:textColor="#FFFF00"
            android:textSize="14sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="半径: "
                android:textColor="#FFFFFF"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/tvCornerRadius"
                android:layout_width="40dp"
                android:layout_height="wrap_content"
                android:text="12dp"
                android:textColor="#00FF00"
                android:textSize="12sp"
                android:textStyle="bold" />

            <SeekBar
                android:id="@+id/seekBarCornerRadius"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginHorizontal="8dp"
                android:max="40"
                android:progress="12" />

        </LinearLayout>

    </LinearLayout>

    <!-- 边框厚度调节 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="#2A2A2A"
        android:padding="12dp"
        android:layout_marginBottom="12dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="边框厚度调节"
            android:textColor="#FFFF00"
            android:textSize="14sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="厚度: "
                android:textColor="#FFFFFF"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/tvBorderThickness"
                android:layout_width="40dp"
                android:layout_height="wrap_content"
                android:text="6dp"
                android:textColor="#00FF00"
                android:textSize="12sp"
                android:textStyle="bold" />

            <SeekBar
                android:id="@+id/seekBarBorderThickness"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginHorizontal="8dp"
                android:max="20"
                android:progress="6" />

        </LinearLayout>

    </LinearLayout>

    <!-- 主光晕调节 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="#2A2A2A"
        android:padding="12dp"
        android:layout_marginBottom="12dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="主光晕调节"
            android:textColor="#FFFF00"
            android:textSize="14sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="强度: "
                android:textColor="#FFFFFF"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/tvHighlightIntensity"
                android:layout_width="40dp"
                android:layout_height="wrap_content"
                android:text="90%"
                android:textColor="#00FF00"
                android:textSize="12sp"
                android:textStyle="bold" />

            <SeekBar
                android:id="@+id/seekBarHighlightIntensity"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginHorizontal="8dp"
                android:max="100"
                android:progress="90" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="角度: "
                android:textColor="#FFFFFF"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/tvHighlightAngle"
                android:layout_width="40dp"
                android:layout_height="wrap_content"
                android:text="45°"
                android:textColor="#00FF00"
                android:textSize="12sp"
                android:textStyle="bold" />

            <SeekBar
                android:id="@+id/seekBarHighlightAngle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginHorizontal="8dp"
                android:max="360"
                android:progress="45" />

        </LinearLayout>

    </LinearLayout>

    <!-- 补光光晕调节 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="#2A2A2A"
        android:padding="12dp"
        android:layout_marginBottom="12dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="补光光晕调节（暗区补光）"
            android:textColor="#FFFF00"
            android:textSize="14sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="强度: "
                android:textColor="#FFFFFF"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/tvFillLightIntensity"
                android:layout_width="40dp"
                android:layout_height="wrap_content"
                android:text="30%"
                android:textColor="#00FF00"
                android:textSize="12sp"
                android:textStyle="bold" />

            <SeekBar
                android:id="@+id/seekBarFillLightIntensity"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginHorizontal="8dp"
                android:max="100"
                android:progress="30" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="角度: "
                android:textColor="#FFFFFF"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/tvFillLightAngle"
                android:layout_width="40dp"
                android:layout_height="wrap_content"
                android:text="225°"
                android:textColor="#00FF00"
                android:textSize="12sp"
                android:textStyle="bold" />

            <SeekBar
                android:id="@+id/seekBarFillLightAngle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginHorizontal="8dp"
                android:max="360"
                android:progress="225" />

        </LinearLayout>

    </LinearLayout>

    <!-- 快捷操作按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="#2A2A2A"
        android:padding="12dp"
        android:layout_marginBottom="12dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="快捷操作"
            android:textColor="#FFFF00"
            android:textSize="14sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <!-- 第一行：角度和动画 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <Button
                android:id="@+id/btnPresetAngles"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_weight="1"
                android:layout_marginEnd="4dp"
                android:text="预设角度"
                android:textSize="10sp" />

            <Button
                android:id="@+id/btnToggleAnimation"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                android:text="开始动画"
                android:textSize="10sp" />

        </LinearLayout>

        <!-- 第二行：效果调节 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <Button
                android:id="@+id/btnIncreaseHighlight"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_weight="1"
                android:layout_marginEnd="2dp"
                android:text="增强高光"
                android:textSize="10sp" />

            <Button
                android:id="@+id/btnIncreaseShadow"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_weight="1"
                android:layout_marginHorizontal="2dp"
                android:text="增强阴影"
                android:textSize="10sp" />

            <Button
                android:id="@+id/btnChangeBorder"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_weight="1"
                android:layout_marginStart="2dp"
                android:text="改变边框"
                android:textSize="10sp" />

        </LinearLayout>

        <!-- 第三行：调试功能 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btnDebugMode"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_weight="1"
                android:layout_marginEnd="2dp"
                android:text="调试模式"
                android:textSize="10sp" />

            <Button
                android:id="@+id/btnTestHighlight"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_weight="1"
                android:layout_marginHorizontal="2dp"
                android:text="测试高光"
                android:textSize="10sp" />

            <Button
                android:id="@+id/btnReset"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_weight="1"
                android:layout_marginStart="2dp"
                android:text="重置效果"
                android:textSize="10sp" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>

</ScrollView>
