<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_72"
    android:layout_marginTop="@dimen/dp_8"
    android:background="@drawable/bg_e6e6e6_round_14_stroke_05"
    android:paddingHorizontal="@dimen/dp_16">

    <ImageView
        android:id="@+id/ivItemCoin"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/icon_item_buy_coins_g"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvItemCoinsCount"
        style="@style/MetaTextView.S10.PoppinsSemiBold600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_12"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/color_1A1A1A"
        android:textSize="@dimen/sp_14"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@id/ivItemCoin"
        app:layout_constraintBottom_toTopOf="@id/tvItemCoinsBaseCount"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toEndOf="@id/ivItemCoin"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="9,980" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvItemCoinsBaseCount"
        style="@style/MetaTextView.S10.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_15"
        android:layout_marginTop="@dimen/dp_4"
        android:background="@drawable/bg_194ab4ff_round_10"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:paddingHorizontal="@dimen/dp_4"
        android:textColor="@color/color_4AB4FF"
        android:textSize="@dimen/sp_10"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@id/tvItemCoinsCount"
        app:layout_constraintTop_toBottomOf="@id/tvItemCoinsCount"
        tools:text="9,980"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/ivItemCoinsAdd"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_4"
        android:src="@drawable/ic_text_add_symbol"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@id/tvItemCoinsBaseCount"
        app:layout_constraintStart_toEndOf="@id/tvItemCoinsBaseCount"
        app:layout_constraintTop_toTopOf="@id/tvItemCoinsBaseCount"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvItemCoinsAwardCount"
        style="@style/MetaTextView.S10.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_15"
        android:layout_marginStart="@dimen/dp_4"
        android:layout_marginTop="@dimen/dp_4"
        android:background="@drawable/bg_199242ff_round_10"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:paddingHorizontal="@dimen/dp_4"
        android:textColor="@color/color_9242FF"
        android:textSize="@dimen/sp_10"
        app:layout_constraintTop_toBottomOf="@id/tvItemCoinsCount"
        app:layout_constrainedWidth="true"
        app:layout_constraintStart_toEndOf="@id/ivItemCoinsAdd"
        tools:text="Bonus 300"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvItemPrice"
        style="@style/MetaTextView.S10.PoppinsSemiBold600"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_30"
        android:layout_marginStart="@dimen/dp_12"
        android:background="@drawable/bg_ffef30_round_17"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:minWidth="@dimen/dp_84"
        android:paddingHorizontal="@dimen/dp_17"
        android:textColor="@color/color_1A1A1A"
        android:textSize="@dimen/sp_12"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="US$4.99" />

</androidx.constraintlayout.widget.ConstraintLayout>