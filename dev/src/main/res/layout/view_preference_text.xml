<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_72"
    android:background="@drawable/selector_preference_item">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clLabel"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_16"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tv_text"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_label"
            style="@style/MetaTextView.S14.PoppinsRegular400.CenterVertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="@color/neutral_color_1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="@string/mine_item_user_agreement" />

        <View
            android:id="@+id/labelRedDot"
            android:layout_width="@dimen/dp_8"
            android:layout_height="@dimen/dp_8"
            android:background="@drawable/bg_red_corner_360"
            android:visibility="invisible"
            tools:visibility="visible"
            app:layout_constraintStart_toEndOf="@id/tv_label"
            app:layout_constraintTop_toTopOf="@id/tv_label" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_text"
        style="@style/MetaTextView.S14.PoppinsRegular400.CenterVertical"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/neutral_color_4"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/iv_navigate"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Jailyn" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_navigate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_10"
        android:src="@drawable/icon_more_arrow_gray"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/textColorSecondary" />

    <View
        android:id="@+id/v_divider"
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:layout_marginHorizontal="@dimen/dp_20"
        android:background="@color/color_preference_divider"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>