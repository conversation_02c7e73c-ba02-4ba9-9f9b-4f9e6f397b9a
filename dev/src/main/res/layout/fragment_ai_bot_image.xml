<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/black_50"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_image"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_16"

            android:fontFamily="@font/poppins_semi_bold_600"
            android:text="@string/ai_bot_upload_image"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_14"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/img_photo"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginLeft="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_13"
            android:layout_marginRight="@dimen/dp_16"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_content"
            android:background="@drawable/bg_line_ai_bot_ugc"
            android:scaleType="centerCrop"
             />
        <ImageView
            android:id="@+id/bg_line_ai_bot_ugc_loading"
            android:layout_width="277dp"
            android:scaleType="centerCrop"
            android:layout_marginLeft="@dimen/dp_16"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            android:layout_height="277dp"/>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_upload"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="@+id/img_photo"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/img_photo">

            <ImageView
                android:id="@+id/img_upload"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/icon_upload_image"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_8"
                android:fontFamily="@font/poppins_regular_400"
                android:gravity="center"
                android:text="@string/ai_bot_upload_image_text"
                android:textColor="@color/white"
                app:layout_constraintTop_toBottomOf="@+id/img_upload" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <ImageView
            android:id="@+id/img_reload"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="@dimen/dp_16"
            android:padding="@dimen/dp_16"
            android:visibility="gone"
            tools:visibility="visible"
            android:src="@drawable/icon_reload_image"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/img_photo" />
        <FrameLayout
            android:id="@+id/loading_progress"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:visibility="gone"
            android:layout_marginLeft="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_13"
            android:layout_marginRight="@dimen/dp_16"
            app:layout_constraintBottom_toBottomOf="parent"
            android:background="@drawable/bg_black_50_s12"
            app:layout_constraintTop_toBottomOf="@+id/tv_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="horizontal">

                <ProgressBar
                    android:id="@+id/pb"
                    android:layout_width="@dimen/dp_20"
                    android:layout_height="@dimen/dp_20"
                    android:layout_marginEnd="@dimen/dp_4"
                    android:indeterminateTint="@color/white" />

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/loading_text"
                    style="@style/MetaTextView.S15.PoppinsRegular400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dp_4"
                    android:text="@string/loading"
                    android:textColor="@color/white" />
            </LinearLayout>


        </FrameLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>