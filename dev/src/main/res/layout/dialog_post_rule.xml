<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_white_top_round_16">

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_225"
            android:background="@drawable/bg_gradient_community_rule_2" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_125"
            android:layout_marginTop="@dimen/dp_100"
            android:background="@drawable/bg_gradient_community_rule_1" />

        <View
            android:id="@+id/v_bar"
            android:layout_width="@dimen/dp_36"
            android:layout_height="@dimen/dp_4"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_12"
            android:background="@drawable/sp_b3b3b3_corner_18" />

        <com.socialplay.gpark.ui.view.InterceptConstraintLayout
            android:id="@+id/cl_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/dp_28">

            <com.airbnb.epoxy.EpoxyRecyclerView
                android:id="@+id/rv"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginBottom="@dimen/dp_10"
                android:overScrollMode="never"
                android:paddingHorizontal="@dimen/dp_20"
                android:scrollbarSize="@dimen/dp_4"
                android:scrollbarThumbVertical="@drawable/sp_b3b3b3_corner_18"
                android:scrollbars="vertical"
                app:layout_constraintBottom_toTopOf="@id/tv_continue_btn"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_goneMarginBottom="0dp" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tv_continue_btn"
                style="@style/MetaTextView.S16.PoppinsMedium500"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_20"
                android:layout_marginBottom="@dimen/dp_20"
                android:background="@drawable/bg_ffef30_round_40"
                android:gravity="center"
                android:paddingVertical="@dimen/dp_13"
                android:text="@string/post_rule_confirm_btn"
                app:layout_constraintBottom_toBottomOf="parent" />

        </com.socialplay.gpark.ui.view.InterceptConstraintLayout>

    </FrameLayout>

</FrameLayout>