<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.motion.widget.MotionLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"

    app:layoutDescription="@xml/scene_fullscreen_videoplayer">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
            android:id="@+id/sphVideo"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/v_top_mask"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_videoplayer_controlview_mask_top"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/ib_back"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/title_bar_height"
            android:paddingHorizontal="@dimen/dp_16"
            android:src="@drawable/icon_back_array_bold_black"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/sphVideo"
            app:tint="@color/white" />

        <View
            android:id="@+id/v_bottom_mask"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_100"
            android:background="@drawable/bg_videoplayer_controlview_mask_bottom"
            app:layout_constraintBottom_toBottomOf="parent" />

        <ImageView
            android:id="@+id/iv_play"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp_8"
            android:src="@drawable/icon_fullscreen_videoplayer_play"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <ImageView
            android:id="@+id/iv_mute"
            android:layout_width="@dimen/dp_34"
            android:layout_height="@dimen/dp_34"
            android:layout_marginEnd="@dimen/dp_12"
            android:layout_marginBottom="@dimen/dp_47"
            android:padding="@dimen/dp_4"
            android:src="@drawable/icon_feed_video_unmute"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <TextView
            android:id="@+id/tv_progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="monospace"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_12"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/iv_play"
            app:layout_constraintStart_toEndOf="@id/iv_play"
            app:layout_constraintTop_toTopOf="@id/iv_play"
            tools:text="00:00" />

        <TextView
            android:id="@+id/tv_duration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="monospace"
            android:padding="@dimen/dp_8"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_12"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/iv_mute"
            app:layout_constraintEnd_toStartOf="@+id/iv_mute"
            app:layout_constraintTop_toTopOf="@id/iv_mute"
            tools:text="20:00" />


        <com.socialplay.gpark.ui.view.SeekFirstSeekBar
            android:id="@+id/sb_progress_bar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:clickable="true"
            android:focusable="true"
            android:paddingStart="@dimen/dp_16"
            android:paddingEnd="@dimen/dp_4"
            android:progressDrawable="@drawable/bg_videoplayer_seekbar_progress"
            android:splitTrack="false"
            android:thumb="@drawable/bg_videoplayer_seekbar_thumb"
            app:layout_constraintBottom_toBottomOf="@id/iv_mute"
            app:layout_constraintEnd_toStartOf="@id/tv_duration"
            app:layout_constraintStart_toEndOf="@id/tv_progress"
            app:layout_constraintTop_toTopOf="@+id/iv_mute"
            tools:max="100"
            tools:progress="80" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_uncontrollable_elements"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_big_paused_button"
            android:layout_width="@dimen/dp_42"
            android:layout_height="@dimen/dp_42"
            android:src="@drawable/icon_fullscreen_videoplayer_big_play"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

        <ProgressBar
            android:id="@+id/loadingIndicator"
            android:layout_width="@dimen/dp_42"
            android:layout_height="@dimen/dp_42"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:indeterminateTint="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:visibility="gone" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.motion.widget.MotionLayout>