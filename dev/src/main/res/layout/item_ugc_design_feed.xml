<?xml version="1.0" encoding="utf-8"?>
<com.socialplay.gpark.ui.view.RoundConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/dp_6"
    android:layout_marginBottom="@dimen/dp_12"
    app:cornerRadii="@dimen/dp_12"
    tools:layout_width="@dimen/dp_166">

    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_outfit"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_title"
        style="@style/MetaTextView.S13.PoppinsMedium500"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_8"
        android:layout_marginTop="@dimen/dp_8"
        android:ellipsize="end"
        android:maxLines="2"
        app:layout_constraintTop_toBottomOf="@id/iv_outfit"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.FlowLayout
        android:id="@+id/fl_tags"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_8"
        android:layout_marginTop="@dimen/dp_8"
        app:horizontalSpacing="@dimen/dp_4"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        app:maxLines="1"
        app:verticalSpacing="@dimen/dp_4" />

    <ImageView
        android:id="@+id/iv_avatar"
        android:layout_width="@dimen/dp_18"
        android:layout_height="@dimen/dp_18"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginTop="@dimen/dp_8"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/fl_tags"
        tools:src="@drawable/icon_default_avatar" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_username"
        style="@style/MetaTextView.S12"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_4"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/neutral_color_3"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
        app:layout_constraintEnd_toStartOf="@id/tv_like_count"
        app:layout_constraintStart_toEndOf="@id/iv_avatar"
        app:layout_constraintTop_toTopOf="@id/iv_avatar"
        tools:text="Ugc Outfit Title" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_like_count"
        style="@style/MetaTextView.S12"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawableStart="@drawable/selector_ugc_comment_like"
        android:drawablePadding="@dimen/dp_2"
        android:gravity="center_vertical"
        android:padding="@dimen/dp_8"
        android:textColor="@color/color_ugc_comment_like"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_avatar"
        tools:text="56" />

</com.socialplay.gpark.ui.view.RoundConstraintLayout>