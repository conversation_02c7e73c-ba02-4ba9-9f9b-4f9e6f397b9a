<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    app:layout_constraintBottom_toBottomOf="parent"
    tools:background="#000"
    tools:layout_height="match_parent"
    >


    <com.socialplay.gpark.ui.view.AnimatedSeekBar
        android:id="@+id/pb_progress_bar"
        style="@style/Widget.AppCompat.ProgressBar.Horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_10"
        android:indeterminate="false"
        android:max="0"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:progress="0"
        android:paddingBottom="@dimen/dp_1"
        android:paddingTop="@dimen/dp_10"
        android:progressDrawable="@drawable/bg_video_feed_progress_bar"
        android:splitTrack="false"
        android:thumb="@drawable/seekbar_thumb_video_feed_unpressed_to_pressed"
        android:thumbOffset="@dimen/dp_8"
        app:layout_constraintBottom_toBottomOf="parent" />


    <LinearLayout
        android:id="@+id/ll_text_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_14"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toTopOf="@id/pb_progress_bar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <TextView
            android:id="@+id/tv_current"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="00:10"
            android:textSize="@dimen/sp_20"
            android:shadowColor="#42000000"
            android:shadowDy="1"
            android:shadowRadius="1"
            android:textColor="@color/white" />

        <TextView
            android:id="@+id/tv_divider"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_12"
            android:text="@string/slash"
            android:textColor="@color/white_40"
            android:shadowColor="#42000000"
            android:shadowDy="1"
            android:shadowRadius="1"
            android:textSize="@dimen/sp_12" />

        <TextView
            android:id="@+id/tv_total"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="00:28"
            android:shadowColor="#42000000"
            android:shadowDy="1"
            android:shadowRadius="1"
            android:textColor="@color/white_40"
            android:textSize="@dimen/sp_20" />
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>