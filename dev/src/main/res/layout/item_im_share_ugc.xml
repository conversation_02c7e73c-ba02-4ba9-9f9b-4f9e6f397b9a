<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cl_root"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_f0f0f0_corner_16"
    android:padding="@dimen/dp_10">

    <com.socialplay.gpark.ui.view.RoundImageView
        android:id="@+id/iv_icon"
        android:layout_width="@dimen/dp_68"
        android:layout_height="@dimen/dp_68"
        android:scaleType="centerCrop"
        app:cornerRadii="@dimen/dp_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_title"
        style="@style/MetaTextView.S14.PoppinsBold700"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_10"
        android:layout_marginEnd="@dimen/dp_13"
        android:ellipsize="end"
        android:maxLines="1"
        android:singleLine="true"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toEndOf="@id/iv_icon"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_min="@dimen/dp_87" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_desc"
        style="@style/MetaTextView.S14"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_10"
        android:layout_marginTop="@dimen/dp_4"
        android:ellipsize="end"
        android:maxLines="2"
        android:textColor="@color/neutral_color_3"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toEndOf="@id/iv_icon"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        app:layout_constraintWidth_min="@dimen/dp_100" />

</androidx.constraintlayout.widget.ConstraintLayout>