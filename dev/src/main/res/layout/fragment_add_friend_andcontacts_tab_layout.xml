<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/MainBackground"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/placeHolderView"
        android:layout_width="match_parent"
        android:layout_height="0dp" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_height">

        <androidx.appcompat.widget.AppCompatImageButton
            android:id="@+id/ib_back"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/title_bar_height"
            android:background="?attr/actionBarItemBackground"
            android:paddingHorizontal="@dimen/dp_16"
            app:layout_constraintBottom_toBottomOf="parent"
            android:paddingVertical="@dimen/dp_13"
            android:src="@drawable/icon_back_array_bold_black"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tabLayout"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_50"
            android:layout_marginStart="@dimen/dp_14"
            android:background="@color/transparent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tabGravity="center"
            app:tabIndicator="@null"
            app:tabIndicatorColor="@color/textColorPrimary"
            app:tabIndicatorFullWidth="false"
            app:tabIndicatorGravity="bottom"
            app:tabIndicatorHeight="0dp"

            app:tabMode="fixed"
            app:tabPaddingBottom="-1dp"
            app:tabPaddingEnd="-1dp"
            app:tabPaddingStart="-1dp"
            app:tabPaddingTop="-1dp"
            app:tabRippleColor="@color/transparent" />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.socialplay.gpark.ui.view.viewpager.AllowStateLossFrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tabLayout">

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewPager"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </com.socialplay.gpark.ui.view.viewpager.AllowStateLossFrameLayout>

</LinearLayout>
