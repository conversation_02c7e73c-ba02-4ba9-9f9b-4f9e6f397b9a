<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/color_1C1C1C"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="top"
        android:layout_marginTop="@dimen/dp_12"
        android:orientation="vertical"
        >

        <RelativeLayout
            android:id="@+id/mgsExpandTabRoom"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_30"
           >


            <View
                android:id="@+id/mgsExpandTabRoomIndicator"
                android:layout_width="@dimen/dp_90"
                android:layout_height="@dimen/dp_30"
                android:layout_centerVertical="true"
                android:visibility="visible"
                android:background="@drawable/bg_button_normal_1" />

            <ImageView
                android:id="@+id/ivMgsExpandTabRoom"
                android:layout_width="@dimen/dp_22"
                android:layout_height="@dimen/dp_22"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:layout_marginLeft="@dimen/dp_5"
                android:src="@drawable/icon_mgs_member" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvMgsExpandTabRoom"
                style="@style/MetaTextView.S12.PoppinsRegular400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="@dimen/dp_2"
                android:layout_toRightOf="@id/ivMgsExpandTabRoom"
                android:gravity="center"
                android:text="@string/mgs_room_tab"
                android:textColor="@drawable/color_mgs_game_tab"
                android:textSize="@dimen/sp_12" />

        </RelativeLayout>


        <RelativeLayout
            android:id="@+id/mgsExpandTabFriend"
            android:layout_width="match_parent"
            android:layout_marginTop="@dimen/dp_22"
            android:layout_height="@dimen/dp_30"
           >

            <View
                android:id="@+id/mgsExpandTabFriendIndicator"
                android:layout_width="@dimen/dp_90"
                android:layout_height="@dimen/dp_30"
                android:layout_centerVertical="true"
                android:background="@drawable/bg_button_normal_1"
                android:visibility="visible" />

            <ImageView
                android:id="@+id/ivMgsExpandTabFriend"
                android:layout_width="@dimen/dp_22"
                android:layout_height="@dimen/dp_22"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:layout_marginLeft="@dimen/dp_5"
                android:src="@drawable/icon_mgs_invite" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvMgsExpandTabFriend"
                style="@style/MetaTextView.S12.PoppinsLight300"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="@dimen/dp_2"
                android:layout_toRightOf="@+id/ivMgsExpandTabFriend"
                android:drawablePadding="@dimen/dp_2"
                android:gravity="center"
                android:text="@string/mgs_room_friend"
                android:textColor="@drawable/color_mgs_game_tab"
                android:textSize="@dimen/sp_12" />
        </RelativeLayout>
    </LinearLayout>
</merge>