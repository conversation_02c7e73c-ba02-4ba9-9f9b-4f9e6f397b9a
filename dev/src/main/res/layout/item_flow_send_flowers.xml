<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/itemSendGift"
    android:layout_width="wrap_content"
    android:layout_height="@dimen/dp_40"
    android:layout_marginHorizontal="@dimen/dp_16"
    app:layout_constrainedWidth="true"
    android:background="@drawable/bg_send_gift_flow"
    tools:background="@drawable/bg_send_gift_flow_mine">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivUserAvatar"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_marginStart="@dimen/dp_16"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tvSendFlowerDesc"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/circleStyle"
        app:strokeColor="@color/white"
        app:strokeWidth="@dimen/dp_1"
        tools:src="@drawable/icon_default_avatar" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvSendFlowerDesc"
        style="@style/MetaTextView.S14.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_8"
        android:ellipsize="middle"
        android:maxLines="1"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tvSendFlowerCount"
        app:layout_constraintStart_toEndOf="@id/ivUserAvatar"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="@string/send_flower_dialog_flow_send_a_flowers" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvSendFlowerCount"
        style="@style/MetaTextView.S10.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_17"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginEnd="@dimen/dp_16"
        android:background="@drawable/bg_white_19"
        android:drawableStart="@drawable/ic_single_flower_size_13"
        android:gravity="center_vertical"
        android:paddingHorizontal="@dimen/dp_6"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvSendFlowerDesc"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="x1" />
</androidx.constraintlayout.widget.ConstraintLayout>