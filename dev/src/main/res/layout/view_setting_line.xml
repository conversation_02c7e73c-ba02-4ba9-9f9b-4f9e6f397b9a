<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="@dimen/dp_60"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">


    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/fl_left"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginLeft="@dimen/dp_24"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="@id/v_space"
        app:layout_constraintStart_toStartOf="@id/v_space"
        app:layout_constraintTop_toTopOf="@id/v_space">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_title"
            style="@style/MetaTextView.S14.PoppinsRegular400.CenterVertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:gravity="center_vertical"
            tools:text="手机绑定"
            tools:visibility="visible" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_title_desc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/MetaTextView.S14.PoppinsRegular400.CenterVertical"
            android:textSize="@dimen/sp_14"
            android:visibility="gone"
            tools:text="saasdasdasdasdasd" />

    </androidx.appcompat.widget.LinearLayoutCompat>


    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_arrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginRight="@dimen/dp_8"
        android:paddingLeft="3dp"
        android:paddingRight="16dp"
        android:visibility="gone"
        android:src="@drawable/icon_right_arrow"
        app:layout_constraintBottom_toBottomOf="@id/fl_left"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/textColorSecondary" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_desc"
        style="@style/MetaTextView.S15.PoppinsMedium500.CenterVertical"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:gravity="center"
        android:textColor="#6A6A6A"
        android:textSize="@dimen/sp_14"
        app:layout_constraintBottom_toBottomOf="@id/fl_left"
        app:layout_constraintEnd_toStartOf="@id/iv_arrow"
        app:layout_constraintTop_toTopOf="@+id/fl_left"
        tools:text="未绑定"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.MetaSwitchCompat
        android:id="@+id/setting_switch"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="@dimen/dp_24"
        android:checked="false"
        android:thumb="@drawable/bg_setting_thumb"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/fl_left"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:switchMinWidth="@dimen/dp_45"
        app:track="@drawable/green_switch_track_selector" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_bottom_desc"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginHorizontal="@dimen/dp_24"
        android:paddingTop="@dimen/dp_8"
        android:paddingBottom="@dimen/dp_8"
        android:textColor="@color/color_080D2D_30"
        android:textSize="@dimen/sp_12"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@+id/v_space"
        tools:text="Bottom_Desc" />

    <View
        android:id="@+id/v_line"
        android:layout_width="0dp"
        android:layout_height="0.3dp"
        android:layout_marginHorizontal="@dimen/dp_24"
        android:background="@color/color_F6F6F6"
        app:layout_constraintBottom_toBottomOf="@id/v_space"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/v_space" />

    <Space
        android:id="@+id/v_space"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_72" />

</merge>