<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <!-- This will be the background that gets blurred -->
    <ScrollView
        android:id="@+id/background_scrollview"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="300dp"
                android:scaleType="centerCrop"
                android:src="@drawable/bg_splash_src" />

            <TextView
                style="@style/MetaTextView.S5.White"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="16dp"
                android:text="Lorem ipsum dolor sit amet, consectetur adipiscing elit. ... (long text) ..." />

        </LinearLayout>
    </ScrollView>


    <!-- This is the BlurView that will blur the content behind it -->
    <eightbitlab.com.blurview.BlurView
        android:id="@+id/blurView"
        android:layout_width="300dp"
        android:layout_height="200dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.4">

        <!-- Any content inside BlurView will not be blurred. -->
        <!-- It will be drawn on top of the blurred background. -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/text_on_blur"
                style="@style/MetaTextView.S5.White"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="I am on top of the blur!"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </eightbitlab.com.blurview.BlurView>

</androidx.constraintlayout.widget.ConstraintLayout> 