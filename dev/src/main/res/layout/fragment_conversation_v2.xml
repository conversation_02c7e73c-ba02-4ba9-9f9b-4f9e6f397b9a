<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/placeHolderView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent" />

    <FrameLayout
        android:id="@+id/titleBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/placeHolderView"
        tools:layout="@layout/layout_group_chat_title" />

    <RelativeLayout
        android:id="@+id/rong_content"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/rc_extension"
        app:layout_constraintTop_toBottomOf="@id/titleBarLayout">

        <com.socialplay.gpark.ui.view.AutoRefreshListView
            android:id="@+id/rc_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@android:color/transparent"
            android:cacheColorHint="@android:color/transparent"
            android:divider="@android:color/transparent"
            android:dividerHeight="0dp"
            android:listSelector="@android:color/transparent" />

        <LinearLayout
            android:id="@+id/rcUnreadMessageLayout"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:layout_gravity="end"
            android:layout_marginTop="30dp"
            android:background="@drawable/rc_unread_msg_bg_style"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:visibility="gone"
            tools:visibility="visible">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="7dp"
                android:background="@drawable/rc_unread_msg_arrow" />

            <TextView
                android:id="@+id/rcUnreadMessageCount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="3dp"
                android:layout_marginEnd="5dp"
                android:text="@string/chat_page_unread_message_count"
                android:textColor="#0195ff"
                android:textSize="14sp" />
        </LinearLayout>

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="4dp">

            <ImageButton
                android:id="@+id/rc_new_message_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/rc_conversation_newmsg"
                android:gravity="center"
                android:visibility="gone"
                tools:visibility="visible"/>

            <TextView
                android:id="@+id/rc_new_message_number"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignLeft="@id/rc_new_message_count"
                android:layout_alignTop="@id/rc_new_message_count"
                android:layout_alignRight="@id/rc_new_message_count"
                android:layout_alignBottom="@id/rc_new_message_count"
                android:gravity="center"
                android:paddingBottom="5dp"
                android:textColor="#fff"
                android:textSize="@dimen/sp_12"
                android:visibility="gone"
                tools:visibility="visible"
                tools:text="9"/>
        </RelativeLayout>

        <FrameLayout
            android:id="@+id/v_connection_status_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:background="@color/color_FCF0ED"
            android:visibility="gone"
            tools:visibility="visible">

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tv_connection_status"
                style="@style/MetaTextView.S14"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:drawableStart="@drawable/icon_im_conversation_error"
                android:drawablePadding="@dimen/dp_8"
                android:gravity="center"
                android:paddingTop="@dimen/dp_8"
                android:paddingBottom="@dimen/dp_8"
                android:text="@string/im_conversation_connection_error"
                android:textColor="@color/color_7E7678"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </FrameLayout>

    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_05"
        android:background="@color/color_F0F0F0"
        app:layout_constraintBottom_toTopOf="@id/rvAT" />

    <com.airbnb.epoxy.EpoxyRecyclerView
        android:id="@+id/rvAT"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toTopOf="@id/rc_extension"
        app:layout_constraintHeight_max="@dimen/dp_192"
        tools:listitem="@layout/item_group_chat" />

    <LinearLayout
        android:id="@+id/rc_extension"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerVertical="true"
        android:orientation="vertical"
        android:paddingTop="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_10"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:ignore="MissingDefaultResource">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_16"
            android:layout_marginRight="@dimen/dp_16"
            android:gravity="center_vertical">

            <EditText
                android:id="@+id/rc_edit_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginRight="@dimen/dp_14"
                android:layout_toLeftOf="@+id/rc_send_toggle"
                android:background="@drawable/bg_imrongyun_input"
                android:gravity="center_vertical"
                android:hint="@string/imrongyun_converstation_input_tip"
                android:includeFontPadding="false"
                android:maxHeight="@dimen/dp_119"
                android:maxLength="2000"
                android:minHeight="@dimen/dp_37"
                android:paddingLeft="@dimen/dp_15"
                android:paddingTop="@dimen/dp_12"
                android:paddingRight="@dimen/dp_15"
                android:paddingBottom="@dimen/dp_12"
                android:textColor="@color/colorAccent"
                android:textColorHint="@color/color_B3B3B3"
                android:textSize="@dimen/sp_14" />

            <ImageView
                android:id="@+id/rc_send_toggle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:alpha="0.5"
                android:src="@drawable/icon_conversation_send"
                android:visibility="visible">

            </ImageView>
        </RelativeLayout>

        <Space
            android:id="@+id/keyboardSpace"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_0" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>