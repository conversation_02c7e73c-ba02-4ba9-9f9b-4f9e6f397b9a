<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="@dimen/dp_44">

    <TextView
        android:id="@+id/tv_normal"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_44"
        android:layout_gravity="center"
        android:gravity="center"
        android:maxLines="1"
        android:singleLine="true"
        android:textColor="@color/color_666666"
        android:textSize="@dimen/sp_16"
        android:textStyle="bold"
        tools:text="Like 100" />

    <TextView
        android:id="@+id/tv_selected"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_44"
        android:layout_gravity="center"
        android:gravity="center"
        android:maxLines="1"
        android:singleLine="true"
        android:textColor="@color/color_1A1A1A"
        android:textSize="@dimen/sp_16"
        android:textStyle="bold"
        android:visibility="invisible"
        tools:text="Like 100" />

</FrameLayout>
