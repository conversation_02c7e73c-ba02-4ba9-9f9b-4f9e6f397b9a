<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/black_70">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_white_top_round_24"
        app:layout_constraintBottom_toBottomOf="parent">

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_title"
            style="@style/MetaTextView.S18.PoppinsExtraBold800"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_4"
            android:gravity="center_vertical"
            android:paddingTop="@dimen/dp_28"
            android:text="@string/how_old_are_you"
            android:textColor="@color/neutral_color_1"
            app:layout_constraintBottom_toTopOf="@id/tv_desc"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_desc"
            style="@style/MetaTextView.S14"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_40"
            android:layout_marginEnd="@dimen/dp_41"
            android:layout_marginBottom="@dimen/dp_28"
            android:gravity="center"
            android:text="@string/how_old_are_you_desc"
            android:textColor="@color/neutral_color_5"
            app:layout_constraintBottom_toTopOf="@id/rv"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintWidth_max="@dimen/dp_294" />

        <com.airbnb.epoxy.EpoxyRecyclerView
            android:id="@+id/rv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_29"
            android:layout_marginEnd="@dimen/dp_30"
            android:layout_marginBottom="@dimen/dp_29"
            android:minHeight="@dimen/dp_180"
            android:overScrollMode="never"
            app:layout_constraintBottom_toTopOf="@id/tv_next_btn"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintWidth_max="@dimen/dp_316" />

        <View
            android:id="@+id/v_cover"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="@id/rv"
            app:layout_constraintEnd_toEndOf="@id/rv"
            app:layout_constraintStart_toStartOf="@id/rv"
            app:layout_constraintTop_toTopOf="@id/rv" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_next_btn"
            style="@style/Button.S18.PoppinsBlack900.Height48"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:layout_marginBottom="@dimen/dp_40"
            android:text="@string/text_confirm_uppercase"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_goneMarginTop="@dimen/dp_40" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>