<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/placeHolderView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/rl_chat_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/placeHolderView">

        <ImageView
            android:id="@+id/img_chat_back"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/title_bar_height"
            android:background="?attr/actionBarItemBackground"
            android:paddingHorizontal="@dimen/dp_16"
            android:src="@drawable/icon_back_array_bold_black"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/llBarInfo"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_centerHorizontal="true"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintBottom_toTopOf="@id/tv_friend_status"
            app:layout_constraintEnd_toStartOf="@id/ivTitleSetting"
            app:layout_constraintStart_toEndOf="@id/img_chat_back"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed">

            <TextView
                android:id="@+id/tv_chat_name"
                style="@style/MetaTextView.S15.PoppinsMedium500.CenterVertical"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_4"
                android:ellipsize="end"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:singleLine="true"
                tools:text="YangtuoYtuo" />

            <com.socialplay.gpark.ui.view.UserLabelView
                android:id="@+id/label_group"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

        </LinearLayout>

        <View
            android:id="@+id/iv_online"
            android:layout_width="@dimen/dp_6"
            android:layout_height="@dimen/dp_6"
            android:background="@drawable/sp_online_dot"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/tv_friend_status"
            app:layout_constraintEnd_toStartOf="@id/tv_friend_status"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="@id/llBarInfo"
            app:layout_constraintTop_toTopOf="@id/tv_friend_status"
            tools:visibility="visible" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_friend_status"
            style="@style/MetaTextView.S12.PoppinsMedium500.Secondary"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_4"
            android:layout_marginTop="@dimen/dp_2"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:visibility="gone"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@id/llBarInfo"
            app:layout_constraintStart_toEndOf="@id/iv_online"
            app:layout_constraintTop_toBottomOf="@id/llBarInfo"
            tools:text="amazing g wtg wtfa"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/ivTitleSetting"
            android:layout_width="@dimen/dp_44"
            android:layout_height="@dimen/title_bar_height"
            android:layout_marginEnd="@dimen/dp_10"
            android:background="?attr/actionBarItemBackground"
            android:paddingHorizontal="@dimen/dp_8"
            android:scaleType="fitCenter"
            android:src="@drawable/icon_group_chat_title_setting"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/color_F0F2F4" />

    <RelativeLayout
        android:id="@+id/rong_content"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <com.socialplay.gpark.ui.view.AutoRefreshListView
            android:id="@+id/rc_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@android:color/transparent"
            android:cacheColorHint="@android:color/transparent"
            android:divider="@android:color/transparent"
            android:dividerHeight="0dp"
            android:listSelector="@android:color/transparent" />

        <LinearLayout
            android:id="@+id/rc_unread_message_layout"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_alignParentTop="true"
            android:layout_alignParentRight="true"
            android:layout_gravity="end"
            android:layout_marginTop="30dp"
            android:background="@drawable/rc_unread_msg_bg_style"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:visibility="gone">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="7dp"
                android:background="@drawable/rc_unread_msg_arrow" />

            <TextView
                android:id="@+id/rc_unread_message_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="3dp"
                android:layout_marginRight="5dp"
                android:text="@string/rc_new_messages"
                android:textColor="#0195ff"
                android:textSize="14sp" />
        </LinearLayout>

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="4dp">

            <ImageButton
                android:id="@+id/rc_new_message_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/rc_conversation_newmsg"
                android:gravity="center"
                android:visibility="gone" />

            <TextView
                android:id="@+id/rc_new_message_number"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignLeft="@id/rc_new_message_count"
                android:layout_alignTop="@id/rc_new_message_count"
                android:layout_alignRight="@id/rc_new_message_count"
                android:layout_alignBottom="@id/rc_new_message_count"
                android:gravity="center"
                android:paddingBottom="5dp"
                android:textColor="#fff"
                android:textSize="12dp"
                android:visibility="gone" />
        </RelativeLayout>

        <FrameLayout
            android:id="@+id/v_connection_status_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:background="@color/color_FCF0ED"
            android:visibility="gone">

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tv_connection_status"
                style="@style/MetaTextView.S14"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:drawableStart="@drawable/icon_im_conversation_error"
                android:drawablePadding="@dimen/dp_8"
                android:gravity="center"
                android:paddingTop="@dimen/dp_8"
                android:paddingBottom="@dimen/dp_8"
                android:text="@string/im_conversation_connection_error"
                android:textColor="@color/color_7E7678"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </FrameLayout>

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/rc_extension"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerVertical="true"
        android:orientation="horizontal"
        android:paddingTop="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_10"
        tools:ignore="MissingDefaultResource">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_16"
            android:layout_marginRight="@dimen/dp_16"
            android:gravity="center_vertical">

            <EditText
                android:id="@+id/rc_edit_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginRight="@dimen/dp_14"
                android:layout_toLeftOf="@+id/rc_send_toggle"
                android:background="@drawable/bg_imrongyun_input"
                android:gravity="center_vertical"
                android:hint="@string/imrongyun_converstation_input_tip"
                android:includeFontPadding="false"
                android:maxHeight="@dimen/dp_119"
                android:maxLength="2000"
                android:minHeight="@dimen/dp_37"
                android:paddingLeft="@dimen/dp_15"
                android:paddingTop="@dimen/dp_12"
                android:paddingRight="@dimen/dp_15"
                android:paddingBottom="@dimen/dp_12"
                android:textColor="@color/colorAccent"
                android:textColorHint="@color/color_B3B3B3"
                android:textSize="@dimen/sp_14" />

            <ImageView
                android:id="@+id/rc_send_toggle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:alpha="0.5"
                android:src="@drawable/icon_conversation_send"
                android:visibility="visible">

            </ImageView>
        </RelativeLayout>


    </LinearLayout>


</LinearLayout>