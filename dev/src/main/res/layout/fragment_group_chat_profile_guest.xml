<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/statusBar"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/ivTitleBack"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/title_bar_height"
        android:background="?attr/actionBarItemBackground"
        android:paddingHorizontal="@dimen/dp_16"
        android:src="@drawable/icon_back_array_bold_black"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/statusBar" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/ivTitleBack">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/ivGroupAvatar"
                android:layout_width="@dimen/dp_90"
                android:layout_height="@dimen/dp_90"
                android:layout_marginStart="@dimen/dp_13"
                android:layout_marginTop="@dimen/dp_21"
                android:scaleType="centerCrop"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:shapeAppearance="@style/shapeRound45Style"
                tools:src="@drawable/icon_item_group_chat_avatar" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvGroupName"
                style="@style/MetaTextView.S24.PoppinsBold700"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_12"
                android:layout_marginEnd="@dimen/dp_16"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/color_17191C"
                android:textSize="@dimen/sp_24"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toTopOf="@id/tvGroupId"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toEndOf="@id/ivGroupAvatar"
                app:layout_constraintTop_toTopOf="@id/ivGroupAvatar"
                app:layout_constraintVertical_chainStyle="packed"
                app:layout_goneMarginEnd="@dimen/dp_16"
                tools:text="Jhon Martin" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvGroupId"
                style="@style/MetaTextView.S12.PoppinsLight300"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@string/group_chat_profile_page_group_id"
                android:textColor="@color/color_B3B3B3"
                android:textSize="@dimen/sp_12"
                app:layout_constraintBottom_toBottomOf="@id/ivGroupAvatar"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toStartOf="@id/tvGroupName"
                app:layout_constraintTop_toBottomOf="@id/tvGroupName" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layoutMembers"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_69"
                android:layout_marginTop="@dimen/dp_9"
                app:layout_constraintTop_toBottomOf="@id/ivGroupAvatar">

                <com.socialplay.gpark.ui.view.MetaTextView
                    style="@style/MetaTextView.S14.PoppinsRegular400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/dp_16"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="@string/group_chat_profile_page_members_title"
                    android:textColor="@color/color_1A1A1A"
                    android:textSize="@dimen/sp_14"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tvMembersCount"
                    style="@style/MetaTextView.S14.PoppinsRegular400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical|end"
                    android:layout_marginEnd="@dimen/dp_16"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="@color/color_B3B3B3"
                    android:textSize="@dimen/sp_14"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="23" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_05"
                    android:background="@color/color_F0F0F0"
                    app:layout_constraintBottom_toBottomOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layoutDesc"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toBottomOf="@id/layoutMembers">

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tvDescTitle"
                    style="@style/MetaTextView.S14.PoppinsRegular400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/dp_16"
                    android:layout_marginTop="@dimen/dp_24"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="@string/group_chat_profile_page_desc_title"
                    android:textColor="@color/color_1A1A1A"
                    android:textSize="@dimen/sp_14"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tvDescContent"
                    style="@style/MetaTextView.S14.PoppinsRegular400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginTop="@dimen/dp_16"
                    android:layout_marginEnd="@dimen/dp_16"
                    android:ellipsize="end"
                    android:maxLines="2"
                    android:textColor="@color/color_666666"
                    android:textSize="@dimen/sp_14"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toStartOf="@id/tvDescTitle"
                    app:layout_constraintTop_toBottomOf="@id/tvDescTitle"
                    tools:text="This is a group announcement. This is a group announcement.This is a group announcement.This is a group announcement." />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_05"
                    android:layout_marginTop="@dimen/dp_24"
                    android:background="@color/color_F0F0F0"
                    app:layout_constraintTop_toBottomOf="@id/tvDescContent" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <com.socialplay.gpark.ui.view.LoadingButton
        android:id="@+id/loadingBtn"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_48"
        android:layout_marginHorizontal="@dimen/dp_26"
        android:layout_marginBottom="@dimen/dp_26"
        android:background="@drawable/bg_ffef30_round_40"
        app:buttonText="@string/group_profile_join_btn"
        app:buttonTextStyle="@style/MetaTextView.S16.PoppinsSemiBold600"
        app:layout_constraintBottom_toBottomOf="parent"
        app:loadingText="@string/loading"
        app:loadingTextStyle="@style/MetaTextView.S15.PoppinsRegular400" />
</androidx.constraintlayout.widget.ConstraintLayout>