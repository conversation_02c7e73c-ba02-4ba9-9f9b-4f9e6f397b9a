<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/v_status_bar_placeholder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />


    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/titleBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:isDividerVisible="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:back_icon="@drawable/icon_back_array_bold_black"
        app:title_text_color="@color/colorAccent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/v_status_bar_placeholder"
        app:title_text="@string/friend_apply_title" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivAvatar"
        style="@style/Avatar.Round"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_24"
        android:src="@drawable/icon_default_avatar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/titleBar"
        app:shapeAppearance="@style/circleStyle" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvFriendName"
        style="@style/MetaTextView.S14.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_12"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:includeFontPadding="false"
        android:maxLines="2"
        app:layout_constraintBottom_toTopOf="@+id/tv_friend_id"
        app:layout_constraintEnd_toStartOf="@id/label_group"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@+id/ivAvatar"
        app:layout_constraintTop_toTopOf="@id/ivAvatar"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_constrainedWidth="true"
        app:layout_goneMarginEnd="@dimen/dp_12"
        tools:text="skiroseikiroseikiroseikiroseo" />

    <com.socialplay.gpark.ui.view.UserLabelView
        android:id="@+id/label_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_16"
        app:layout_constraintBottom_toBottomOf="@id/tvFriendName"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvFriendName"
        app:layout_constraintTop_toTopOf="@id/tvFriendName"/>


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_friend_id"
        style="@style/MetaTextView.S12.PoppinsRegular400.secondary"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_2"
        android:layout_marginRight="@dimen/dp_16"
        android:gravity="center_vertical"
        android:includeFontPadding="false"
        android:singleLine="true"
        android:textColor="@color/color_B3B3B3"
        app:layout_constraintBottom_toBottomOf="@id/ivAvatar"
        app:layout_constraintLeft_toLeftOf="@+id/tvFriendName"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvFriendName"
        tools:text="Id: fdw3r2qtrewgrewfdw3r2qtrewgrewfdw3r2qtrewgrewfdw3r2qtrewgrew" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvApplyPrompt"
        style="@style/MetaTextView.S12.PoppinsRegular400.secondary"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_22"
        android:includeFontPadding="false"
        android:text="@string/friend_apply_add_prompt"
        android:textColor="@color/color_9B9FA6"
        app:layout_constraintStart_toStartOf="@+id/ivAvatar"
        app:layout_constraintTop_toBottomOf="@+id/ivAvatar" />

    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_8"
        android:layout_marginEnd="@dimen/dp_16"
        android:background="@drawable/bg_f0f2f4_9"
        app:layout_constraintBottom_toBottomOf="@id/tv_letter_count"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvApplyPrompt" />

    <EditText
        android:id="@+id/etApply"
        style="@style/EditText"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_150"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_8"
        android:layout_marginEnd="@dimen/dp_16"
        android:background="@null"
        android:gravity="top|start"
        android:maxLength="250"
        android:paddingHorizontal="@dimen/dp_16"
        android:paddingTop="@dimen/dp_12"
        android:paddingBottom="@dimen/dp_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvApplyPrompt"
        tools:text="@tools:sample/lorem/random" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_letter_count"
        style="@style/MetaTextView.S14"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingEnd="@dimen/dp_16"
        android:paddingBottom="@dimen/dp_12"
        android:textColor="@color/color_999999"
        app:layout_constraintEnd_toEndOf="@id/etApply"
        app:layout_constraintTop_toBottomOf="@id/etApply" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvSend"
        style="@style/Button.S18.PoppinsBold600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_46"
        android:layout_marginBottom="@dimen/dp_20"
        android:gravity="center"
        android:minHeight="@dimen/dp_48"
        android:text="@string/friend_apply_send"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_letter_count"
        app:textAllCaps="true" />

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/lv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />
</androidx.constraintlayout.widget.ConstraintLayout>