<?xml version="1.0" encoding="utf-8"?>
<com.socialplay.gpark.ui.view.WrapNestedScrollableHost xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
            android:id="@+id/statusBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent" />

        <com.socialplay.gpark.ui.view.TitleBarLayout
            android:id="@+id/tblTitleBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:isDividerVisible="false"
            app:layout_constraintTop_toBottomOf="@id/statusBar"
            app:title_text="@string/friends_title" />

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toTopOf="@id/spaceConfirmLayout"
            app:layout_constraintTop_toBottomOf="@id/tblTitleBar">


            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout
                    android:id="@+id/sl_refresh_layout"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_friend_list"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:visibility="visible" />

                </com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout>
            </FrameLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/v_empty_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="visible">

                <ImageView
                    android:id="@+id/iv_empty_tip_img"
                    android:layout_width="@dimen/dp_150"
                    android:layout_height="@dimen/dp_150"
                    android:src="@drawable/icon_no_contacts"
                    app:layout_constraintBottom_toTopOf="@+id/tv_empty_tip_text"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_chainStyle="packed" />

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tv_empty_tip_text"
                    style="@style/MetaTextView.S15.PoppinsRegular400.CenterVertical.Secondary"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/dp_80"
                    android:text="@string/no_friends"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/iv_empty_tip_img" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </FrameLayout>

        <com.socialplay.gpark.ui.view.AlphabetIndexView
            android:id="@+id/aivIndexBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end|center_vertical"
            android:layout_marginEnd="@dimen/dp_8"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <Space
            android:id="@+id/spaceConfirmLayout"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_90"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:visibility="visible" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/confirmLayout"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_90"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:visibility="visible">

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="@color/color_F0F0F0"
                app:layout_constraintTop_toTopOf="parent" />

            <com.socialplay.gpark.ui.view.LoadingButton
                android:id="@+id/loadingBtn"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_48"
                android:layout_marginHorizontal="@dimen/dp_26"
                android:layout_marginTop="@dimen/dp_16"
                android:background="@drawable/bg_ffef30_round_100"
                app:buttonText="@string/new_group_page_confirm_btn"
                app:buttonTextStyle="@style/MetaTextView.S16.PoppinsSemiBold600"
                app:layout_constraintTop_toTopOf="parent"
                app:loadingText="@string/loading"
                app:loadingTextStyle="@style/MetaTextView.S15.PoppinsRegular400" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvCheckCount"
                style="@style/MetaTextView.S12.PoppinsSemiBold600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp_26"
                android:gravity="center"
                android:textColor="@color/color_1A1A1A"
                android:textSize="@dimen/sp_12"
                app:layout_constraintBottom_toBottomOf="@id/loadingBtn"
                app:layout_constraintEnd_toEndOf="@id/loadingBtn"
                app:layout_constraintTop_toTopOf="@id/loadingBtn"
                tools:text="(3/10)" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

</com.socialplay.gpark.ui.view.WrapNestedScrollableHost>