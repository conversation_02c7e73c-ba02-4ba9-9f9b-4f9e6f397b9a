<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <TextView
        android:id="@+id/tvSearch"
        android:layout_width="@dimen/dp_80"
        android:layout_height="@dimen/dp_40"
        android:layout_marginEnd="@dimen/dp_10"
        android:background="@drawable/selector_dev_action_item"
        android:gravity="center"
        android:text="@string/debug_search_now"
        android:textColor="#333333"
        app:layout_constraintBottom_toBottomOf="@id/etSearch"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintLeft_toRightOf="@id/etSearch"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/etSearch" />

    <TextView
        android:id="@+id/tvSave"
        android:layout_width="@dimen/dp_80"
        android:layout_height="@dimen/dp_40"
        android:layout_marginEnd="@dimen/dp_10"
        android:background="@drawable/selector_dev_action_item"
        android:gravity="center"
        android:text="@string/debug_save"
        android:textColor="#333333"
        app:layout_constraintBottom_toBottomOf="@id/sw_toggle"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/sw_toggle" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/etSearch"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_45"
        android:layout_marginStart="@dimen/dp_10"
        android:layout_marginEnd="@dimen/dp_5"
        android:background="#EEEEEE"
        android:imeOptions="actionSearch"
        android:paddingStart="@dimen/dp_5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/tvSearch"
        app:layout_constraintTop_toBottomOf="@id/sw_toggle"
        tools:text="Search Text" />

    <androidx.appcompat.widget.SwitchCompat
        android:id="@+id/sw_toggle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_10"
        android:textColor="@color/color_game_detail_desc_text"
        android:text="@string/debug_local_toggle"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:switchPadding="@dimen/dp_10" />

    <com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout
        android:id="@+id/refreshLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/dp_5"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/etSearch">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:listitem="@layout/adapter_developer_pandora_toggle" />
    </com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout>

</androidx.constraintlayout.widget.ConstraintLayout>