<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginHorizontal="@dimen/dp_4"

    >

    <ImageView
        android:id="@+id/img_style"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_margin="@dimen/dp_4"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="70:96"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/view_space"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@drawable/bg_b884ff_s_8"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="70:96"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">


    </View>


</androidx.constraintlayout.widget.ConstraintLayout>