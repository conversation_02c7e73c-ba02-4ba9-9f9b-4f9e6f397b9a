<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/statusBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:isDividerVisible="false"
        app:layout_constraintTop_toBottomOf="@id/statusBar" />

    <com.socialplay.gpark.ui.view.MinWidthTabLayout
        android:id="@+id/tabLayoutGroups"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/dp_50"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="@id/title"
        app:layout_constraintTop_toTopOf="@id/title"
        app:tabContentStart="@dimen/dp_16"
        app:tabGravity="center"
        app:tabIndicator="@drawable/indicator_hut_tab"
        app:tabIndicatorColor="@color/neutral_color_2"
        app:tabIndicatorFullWidth="false"
        app:tabIndicatorHeight="@dimen/dp_4"
        app:tabMinWidth="0dp"
        app:tabMode="fixed"
        app:tabPaddingBottom="0dp"
        app:tabPaddingEnd="@dimen/dp_10"
        app:tabPaddingStart="@dimen/dp_10"
        app:tabPaddingTop="0dp"
        app:tabRippleColor="@null" />

    <com.socialplay.gpark.ui.view.viewpager.AllowStateLossFrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tabLayoutGroups">

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewPagerGroups"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white" />

    </com.socialplay.gpark.ui.view.viewpager.AllowStateLossFrameLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_05"
        android:background="@color/color_F0F2F4"
        app:layout_constraintTop_toBottomOf="@id/title" />
</androidx.constraintlayout.widget.ConstraintLayout>