<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.socialplay.gpark.ui.view.MinWidthTabLayout
        android:id="@+id/tabLayout"
        android:layout_width="0dp"
        android:layout_height="44dp"
        android:layout_marginTop="5dp"
        android:background="@null"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/ivFilter"
        app:layout_constraintTop_toTopOf="parent"
        app:tabContentStart="@dimen/dp_16"
        app:tabGravity="start"
        app:tabIndicator="@drawable/indicator_hut_tab"
        app:tabIndicatorColor="#000"
        app:tabIndicatorHeight="4dp"
        app:tabMinWidth="@dimen/dp_10"
        app:tabMode="scrollable"
        app:tabPaddingEnd="@dimen/dp_10"
        app:tabPaddingStart="@dimen/dp_10"
        app:tabRippleColor="@null" />

    <ImageView
        android:id="@+id/ivFilter"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="16dp"
        android:padding="10dp"
        android:visibility="gone"
        android:src="@drawable/search_icon_filter"
        app:layout_constraintBottom_toBottomOf="@id/tabLayout"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/tabLayout" />

    <View
        android:id="@+id/vLine"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#eee"
        app:layout_constraintTop_toBottomOf="@id/tabLayout" />

    <com.socialplay.gpark.ui.view.viewpager.AllowStateLossFrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/vLine">

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/vp"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </com.socialplay.gpark.ui.view.viewpager.AllowStateLossFrameLayout>

    <LinearLayout
        android:id="@+id/llFilter"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="#B3000000"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/vLine"
        tools:visibility="visible">

        <com.airbnb.epoxy.EpoxyRecyclerView
            android:id="@+id/rv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#fff"
            android:orientation="vertical"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:listitem="@layout/item_search_filter" />

        <View
            android:layout_width="match_parent"
            android:layout_height="18dp"
            android:background="#fff" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>