<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:id="@+id/ll_like"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/icon_item_like_bg"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingStart="@dimen/dp_6"
        android:paddingTop="@dimen/dp_2"
        android:paddingEnd="@dimen/dp_6"
        android:paddingBottom="@dimen/dp_2"
        app:layout_constraintStart_toStartOf="parent">

        <ImageView
            android:id="@+id/iv_like"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/icon_item_like" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_zan"
            style="@style/MetaTextView.S9.PoppinsMedium500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_2"
            android:gravity="center"
            android:shadowColor="@color/black_15"
            android:shadowDx="0"
            android:shadowDy="1"
            android:shadowRadius="1"
            android:textColor="@color/white"
            tools:text="a321515135k" />
    </LinearLayout>

</merge> 