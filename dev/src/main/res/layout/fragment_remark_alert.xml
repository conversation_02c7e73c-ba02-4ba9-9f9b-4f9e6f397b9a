<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:fitsSystemWindows="true">

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/titleBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCommit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingRight="@dimen/sp_12"
        android:text="@string/done"
        android:textColor="@color/colorAccent"
        android:textSize="@dimen/sp_15"
        app:layout_constraintBottom_toBottomOf="@id/titleBar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/titleBar" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/etRemarlk"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="10dp"
        android:background="@drawable/shape_remark_bg"
        android:textSize="@dimen/sp_14"
        android:paddingLeft="16dp"
        android:textColorHint="@color/color_remark_text_hint"
        android:textColor="@color/color_remark_text"
        tools:text="asdfgsdgsdgfdgdsfghdfhshddfhf"
        android:lines="1"
        android:maxLength="16"
        android:maxLines="1"
        android:paddingEnd="@dimen/dp_16"
        android:gravity="center_vertical"
        android:singleLine="true"
        app:layout_constraintTop_toBottomOf="@+id/titleBar" />

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/lv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"/>
</androidx.constraintlayout.widget.ConstraintLayout>