<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivAvatar"
        style="@style/Avatar.Round"
        android:layout_marginVertical="@dimen/dp_13"
        android:layout_marginStart="@dimen/dp_12"
        android:src="@drawable/icon_default_avatar"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvFriendName"
        style="@style/MetaTextView.S14.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_12"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:includeFontPadding="false"
        android:maxLines="1"
        app:layout_constraintBottom_toTopOf="@+id/tv233Count"
        app:layout_constraintEnd_toStartOf="@id/label_group"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@id/ivAvatar"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_constrainedWidth="true"
        app:layout_goneMarginEnd="@dimen/dp_12"
        tools:text="Harry Potter is watchiarry Potter is watching" />

    <View
        android:id="@+id/v_click"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginEnd="@dimen/dp_12"
        app:layout_constraintBottom_toBottomOf="@id/ivAvatar"
        app:layout_constraintEnd_toStartOf="@id/tv_add"
        app:layout_constraintStart_toStartOf="@id/ivAvatar"
        app:layout_constraintTop_toTopOf="@id/ivAvatar" />

    <com.socialplay.gpark.ui.view.UserLabelView
        android:id="@+id/label_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_12"
        app:layout_constraintBottom_toBottomOf="@id/tvFriendName"
        app:layout_constraintEnd_toStartOf="@id/tv_add"
        app:layout_constraintStart_toEndOf="@id/tvFriendName"
        app:layout_constraintTop_toTopOf="@id/tvFriendName"/>


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv233Count"
        android:layout_width="0dp"
        android:layout_marginHorizontal="@dimen/dp_12"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center_vertical|start"
        android:includeFontPadding="false"
        android:maxLines="1"
        style="@style/MetaTextView.S12.PoppinsRegular400.secondary"
        app:layout_constraintBottom_toTopOf="@id/tvRecentlyPlayed"
        app:layout_constraintEnd_toStartOf="@id/tv_add"
        app:layout_constraintStart_toEndOf="@id/ivAvatar"
        app:layout_constraintTop_toBottomOf="@+id/tvFriendName"
        tools:text="Id: afsafc" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvRecentlyPlayed"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toStartOf="@id/tv_add"
        android:layout_marginHorizontal="@dimen/dp_12"
        app:layout_constraintStart_toEndOf="@id/ivAvatar"
        android:ellipsize="end"
        android:gravity="start|center_vertical"
        android:includeFontPadding="false"
        android:maxLines="1"
        style="@style/MetaTextView.S12.PoppinsRegular400.secondary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@+id/tv233Count"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv233Count"
        tools:text="Played: " />


    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_add"
        style="@style/Button.S12.PoppinsBold600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minHeight="@dimen/dp_28"
        android:minWidth="@dimen/dp_60"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:text="@string/add"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>