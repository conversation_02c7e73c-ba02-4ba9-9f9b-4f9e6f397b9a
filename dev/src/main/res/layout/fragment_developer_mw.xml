<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_1A1A1A"
    android:fitsSystemWindows="true">

    <EditText
        android:id="@+id/et_game_id"
        android:layout_width="0dp"
        android:layout_height="45dp"
        android:layout_marginStart="10dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="10dp"
        android:background="#004D40"
        android:gravity="center"
        android:hint="@string/debug_input_game_id"
        android:textColor="#CCFFFFFF"
        android:textColorHint="#CCAAAAAA"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@id/btn_start_game"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/view_top_line" />

    <ProgressBar
        android:id="@+id/pb_download_progress"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_5"
        android:layout_marginStart="10dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="10dp"
        android:max="100"
        android:progressDrawable="@drawable/shape_progress_drawable"
        app:layout_constraintEnd_toStartOf="@id/tv_progress"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ue_engine_version"
        tools:progress="50" />

    <TextView
        android:id="@+id/btn_start_game"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginEnd="10dp"
        android:background="#EF6C00"
        android:gravity="center"
        android:paddingStart="@dimen/dp_15"
        android:paddingEnd="@dimen/dp_15"
        android:text="@string/debug_open_game"
        android:textColor="#CCFFFFFF"
        app:layout_constraintBottom_toBottomOf="@id/et_game_id"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/et_game_id" />

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="35dp"
        android:layout_height="35dp"
        android:layout_marginStart="10dp"
        android:background="@drawable/shape_circle_unread"
        android:gravity="center"
        android:text="@string/debug_mw"
        android:textColor="@color/white"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/ue_engine_version"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/ue_core_version" />

    <TextView
        android:id="@+id/tv_change_engine"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="10dp"
        android:layout_marginTop="30dp"
        android:layout_marginEnd="10dp"
        android:background="#EF6C00"
        android:gravity="center_vertical"
        android:padding="10dp"
        android:text="@string/debug_change_engine"
        android:textColor="#FFFFFF"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_progress_status" />

    <TextView
        android:id="@+id/tv_change_engine_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/debug_change_engine"
        android:textColor="#CCFFFFFF"
        android:textSize="13sp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/tv_change_engine"
        app:layout_constraintStart_toStartOf="@id/tv_change_engine" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/debug_change_server"
        android:textColor="#CCFFFFFF"
        android:textSize="13sp"
        app:layout_constraintBottom_toTopOf="@id/tv_select_service"
        app:layout_constraintStart_toStartOf="@id/tv_select_service" />

    <TextView
        android:id="@+id/tv_select_service"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="10dp"
        android:layout_marginTop="30dp"
        android:layout_marginEnd="10dp"
        android:background="#EF6C00"
        android:gravity="center_vertical"
        android:padding="10dp"
        android:text="@string/debug_change_server"
        android:textColor="#FFFFFF"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_change_engine" />


    <TextView
        android:id="@+id/tv_progress"
        android:layout_width="40dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="10dp"
        android:gravity="center"
        android:text="@string/debug_0_percent"
        android:textColor="#CCFFFFFF"
        app:layout_constraintBottom_toBottomOf="@id/pb_download_progress"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/pb_download_progress"
        tools:text="100%" />

    <TextView
        android:id="@+id/ue_core_version"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginTop="10dp"
        android:text="@string/debug_core"
        android:textColor="#CCFFFFFF"
        android:textIsSelectable="true"
        android:textSize="14sp"
        app:layout_constraintLeft_toRightOf="@id/tv_name"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/ue_engine_version"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:text="@string/debug_engine"
        android:textColor="#CCFFFFFF"
        android:textIsSelectable="true"
        android:textSize="14sp"
        app:layout_constraintLeft_toLeftOf="@id/ue_core_version"
        app:layout_constraintTop_toBottomOf="@id/ue_core_version" />

    <TextView
        android:id="@+id/tv_progress_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginTop="5dp"
        android:background="#EF6C00"
        android:padding="@dimen/dp_5"
        android:text="@string/debug_load_complete"
        android:textColor="#CC0000FF"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/pb_download_progress" />


    <View
        android:id="@+id/view_top_line"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginTop="5dp"
        android:background="#AAB71C1C"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_select_service" />

    <EditText
        android:id="@+id/et_replace_ue_view_game_id"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_40"
        android:background="#00BFA5"
        android:layout_marginLeft="@dimen/dp_10"
        android:layout_marginVertical="@dimen/dp_10"
        android:gravity="center"
        android:hint="@string/debug_input_gameid"
        app:layout_constraintHorizontal_weight="1"
        android:textColor="#FFFFFF"
        android:textColorHint="#80FFFFFF"
        app:layout_constraintEnd_toStartOf="@id/btn_replace_ue_view_game_id"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/et_game_id" />

    <TextView
        android:id="@+id/btn_replace_ue_view_game_id"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_40"
        android:layout_margin="@dimen/dp_10"
        android:background="#00B8D4"
        android:gravity="center"
        android:text="@string/debug_replace_view"
        android:textColor="@color/white"
        app:layout_constraintHorizontal_weight="0.5"
        app:layout_constraintEnd_toStartOf="@+id/btn_clear_ue_view_game_id"
        app:layout_constraintStart_toEndOf="@+id/et_replace_ue_view_game_id"
        app:layout_constraintTop_toBottomOf="@+id/et_game_id" />

    <TextView
        android:id="@+id/btn_clear_ue_view_game_id"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_40"
        android:layout_margin="@dimen/dp_10"
        android:background="#00B8D4"
        android:gravity="center"
        android:text="@string/debug_clear_replace_id"
        android:textColor="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="0.5"
        app:layout_constraintStart_toEndOf="@+id/btn_replace_ue_view_game_id"
        app:layout_constraintTop_toBottomOf="@+id/et_game_id" />

    <EditText
        android:id="@+id/et_replace_plaza_id"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_40"
        android:background="#00BFA5"
        android:layout_marginLeft="@dimen/dp_10"
        android:layout_marginVertical="@dimen/dp_10"
        android:gravity="center"
        android:hint="@string/debug_input_gameid"
        app:layout_constraintHorizontal_weight="1"
        android:textColor="#FFFFFF"
        android:textColorHint="#80FFFFFF"
        app:layout_constraintEnd_toStartOf="@id/btn_replace_plaza_id"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/et_replace_ue_view_game_id" />

    <TextView
        android:id="@+id/btn_replace_plaza_id"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_40"
        android:layout_margin="@dimen/dp_10"
        android:background="#00B8D4"
        android:gravity="center"
        android:text="@string/debug_replace_plaza"
        android:textColor="@color/white"
        app:layout_constraintHorizontal_weight="0.5"
        app:layout_constraintEnd_toStartOf="@+id/btn_clear_plaza_id"
        app:layout_constraintStart_toEndOf="@+id/et_replace_plaza_id"
        app:layout_constraintTop_toBottomOf="@+id/et_replace_ue_view_game_id" />

    <TextView
        android:id="@+id/btn_clear_plaza_id"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_40"
        android:layout_margin="@dimen/dp_10"
        android:background="#00B8D4"
        android:gravity="center"
        android:text="@string/debug_clear_replace_id"
        android:textColor="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="0.5"
        app:layout_constraintStart_toEndOf="@+id/btn_replace_plaza_id"
        app:layout_constraintTop_toBottomOf="@+id/et_replace_ue_view_game_id" />

    <com.socialplay.gpark.ui.view.MetaSwitchCompat
        android:id="@+id/switchHideRoleMask"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_margin="@dimen/dp_10"
        android:thumb="@drawable/bg_setting_thumb"
        app:track="@drawable/green_switch_track_selector"
        app:layout_constraintTop_toBottomOf="@id/et_replace_plaza_id"/>

    <TextView
        android:id="@+id/tvHideRoleMask"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/MetaTextView.S14.PoppinsBold700"
        android:textColor="@color/white"
        android:text="@string/debug_hide_mask"
        android:layout_marginLeft="@dimen/dp_12"
        app:layout_constraintTop_toTopOf="@id/switchHideRoleMask"
        app:layout_constraintBottom_toBottomOf="@id/switchHideRoleMask"
        app:layout_constraintLeft_toRightOf="@id/switchHideRoleMask"/>

    <EditText
        android:id="@+id/et_replace_ds_version"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_40"
        android:layout_marginVertical="@dimen/dp_10"
        android:layout_marginLeft="@dimen/dp_10"
        android:background="#00BFA5"
        android:gravity="center"
        android:hint="@string/debug_input_ds_version"
        android:textColor="#FFFFFF"
        android:textColorHint="#80FFFFFF"
        app:layout_constraintTop_toBottomOf="@id/tvHideRoleMask"
        app:layout_constraintEnd_toStartOf="@id/btn_replace_ds_version"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"/>

    <TextView
        android:id="@+id/btn_replace_ds_version"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_40"
        android:layout_margin="@dimen/dp_5"
        android:background="#00B8D4"
        android:gravity="center"
        android:padding="@dimen/dp_10"
        android:text="@string/debug_save"
        android:textColor="@color/white"
        app:layout_constraintBottom_toBottomOf="@+id/et_replace_ds_version"
        app:layout_constraintEnd_toStartOf="@+id/btn_clear_ds_version"
        app:layout_constraintHorizontal_weight="0.5"
        app:layout_constraintStart_toEndOf="@+id/et_replace_ds_version"
        app:layout_constraintTop_toTopOf="@+id/et_replace_ds_version" />

    <TextView
        android:id="@+id/btn_clear_ds_version"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_40"
        android:layout_margin="@dimen/dp_5"
        android:background="#00B8D4"
        android:gravity="center"
        android:padding="@dimen/dp_10"
        android:text="@string/debug_clear"
        android:textColor="@color/white"
        app:layout_constraintBottom_toBottomOf="@+id/et_replace_ds_version"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/btn_replace_ds_version"
        app:layout_constraintTop_toTopOf="@+id/et_replace_ds_version" />

    <TextView
        android:id="@+id/btn_clean_ts_first_event"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_40"
        android:layout_margin="@dimen/dp_10"
        android:background="#00B8D4"
        android:gravity="center"
        android:text="@string/debug_clear_ts_mark"
        android:textColor="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/et_replace_ds_version" />

    <EditText
        android:id="@+id/etCustomEnv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_10"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginEnd="@dimen/dp_10"
        android:background="#00BFA5"
        android:hint="输入传给MW的自定义参数"
        android:inputType="textMultiLine"
        android:padding="@dimen/dp_2"
        android:maxHeight="@dimen/dp_200"
        android:minHeight="@dimen/dp_50"
        android:textColor="#FFFFFF"
        android:textSize="@dimen/sp_14"
        android:textColorHint="#AAFFFFFF"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btn_clean_ts_first_event" />

    <TextView
        android:id="@+id/btnCustomEnv"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_40"
        android:layout_margin="@dimen/dp_10"
        android:background="#00B8D4"
        android:gravity="center"
        android:text="保存传给MW的自定义参数"
        android:textColor="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/etCustomEnv" />



</androidx.constraintlayout.widget.ConstraintLayout>