<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:background="@color/black_60">

    <Space
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/dialogLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_42"
        android:background="@drawable/bg_white_round_38"
        android:padding="@dimen/dp_20">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/ivAvatar"
            android:layout_width="@dimen/dp_60"
            android:layout_height="@dimen/dp_60"
            android:scaleType="centerCrop"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeAppearance="@style/shapeRound30Style"
            tools:src="@drawable/icon_item_group_chat_avatar" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvUserName"
            style="@style/MetaTextView.S16.PoppinsBold700"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_14"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/color_1A1A1A"
            android:textSize="@dimen/sp_16"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ivAvatar"
            tools:text="Ohayo" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvJoinGroupDesc"
            style="@style/MetaTextView.S14.PoppinsMedium500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_2"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/sp_14"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvUserName"
            tools:text="Want to Join Biubiubiu group" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvJoinGroupRequestInfo"
            style="@style/MetaTextView.S14.PoppinsRegular400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_2"
            android:gravity="center"
            android:textColor="@color/color_B3B3B3"
            android:textSize="@dimen/sp_14"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvJoinGroupDesc"
            tools:text="I'd love to join you! Your group seems like such a joyful community, and I'm eager to grow with you." />


        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvSave"
            style="@style/MetaTextView.S16.PoppinsMedium500"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48"
            android:layout_marginTop="@dimen/dp_16"
            android:background="@drawable/bg_ffef30_round_40"
            android:gravity="center"
            android:text="@string/request_join_group_dialog_agree"
            android:textColor="@color/color_1A1A1A"
            android:textSize="@dimen/sp_16"
            app:layout_constraintTop_toBottomOf="@id/tvJoinGroupRequestInfo" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvCancel"
            style="@style/MetaTextView.S16.PoppinsMedium500"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48"
            android:layout_marginTop="@dimen/dp_16"
            android:background="@drawable/bg_f0f0f0_corner_40"
            android:gravity="center"
            android:text="@string/request_join_group_dialog_disagree"
            android:textColor="@color/color_1A1A1A"
            android:textSize="@dimen/sp_16"
            app:layout_constraintTop_toBottomOf="@id/tvSave" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>