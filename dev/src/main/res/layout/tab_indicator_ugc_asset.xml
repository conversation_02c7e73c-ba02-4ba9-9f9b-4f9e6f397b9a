<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_30">

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_normal"
        style="@style/MetaTextView.S14"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:minHeight="@dimen/dp_21"
        android:paddingHorizontal="@dimen/dp_12"
        android:textColor="@color/color_CCCCCC"
        android:textSize="@dimen/dp_14"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:uiLineHeight="@dimen/dp_21"
        tools:text="All" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_selected"
        style="@style/MetaTextView.S16.PoppinsSemiBold600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:minHeight="@dimen/dp_24"
        android:textSize="@dimen/dp_16"
        android:textStyle="bold"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:uiLineHeight="@dimen/dp_24"
        tools:text="All"
        tools:visibility="invisible" />

    <View
        android:id="@+id/v_red_dot"
        android:layout_width="@dimen/dp_10"
        android:layout_height="@dimen/dp_10"
        android:layout_marginEnd="@dimen/dp_2"
        android:background="@drawable/sp_red_dot"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/tv_normal"
        app:layout_constraintTop_toTopOf="@id/tv_normal"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>