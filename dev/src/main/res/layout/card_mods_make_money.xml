<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/bg_top"
        android:layout_width="0dp"
        android:layout_height="112dp"
        android:background="@drawable/bg_gradient_mods_card"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <ImageView
        android:id="@+id/img_character"
        android:layout_width="127dp"
        android:layout_height="140dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        android:src="@drawable/img_mods_card_character"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <ImageView
        android:id="@+id/star1"
        android:layout_width="8dp"
        android:layout_height="8dp"
        android:layout_marginStart="28dp"
        android:layout_marginTop="73dp"
        android:src="@drawable/ic_mods_card_star1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <ImageView
        android:id="@+id/star2"
        android:layout_width="9dp"
        android:layout_height="9dp"
        android:layout_marginStart="68dp"
        android:layout_marginTop="127dp"
        android:src="@drawable/ic_mods_card_star2"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <ImageView
        android:id="@+id/star3"
        android:layout_width="6dp"
        android:layout_height="6dp"
        android:layout_marginStart="10dp"
        android:layout_marginTop="125dp"
        android:src="@drawable/ic_mods_card_star3"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/MetaTextView.S15.PoppinsSemiBold"
        android:layout_width="167dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="78dp"
        android:text="@string/mods_card_title"
        android:textColor="@color/neutra_gray_1000"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.cardview.widget.CardView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="140dp"
        android:layout_marginEnd="16dp"
        app:cardBackgroundColor="@android:color/white"
        app:cardCornerRadius="16dp"
        app:cardElevation="0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingStart="16dp"
            android:paddingTop="16dp"
            android:paddingEnd="16dp"
            android:paddingBottom="24dp">

            <TextView
                android:id="@+id/tv_description"
                style="@style/MetaTextView.S14"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:lineSpacingExtra="8dp"
                android:text="@string/mods_card_description"
                android:textColor="@color/neutra_gray_900" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_try_now"
                style="@style/Widget.MaterialComponents.Button.UnelevatedButton"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginTop="16dp"
                android:insetTop="0dp"
                android:insetBottom="0dp"
                android:text="@string/mods_card_try_now"
                android:textAppearance="@style/MetaTextView.S16.PoppinsSemiBold"
                android:textColor="@color/neutra_gray_1000"
                app:backgroundTint="@color/brand_mind"
                app:cornerRadius="40dp" />
        </LinearLayout>
    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout> 