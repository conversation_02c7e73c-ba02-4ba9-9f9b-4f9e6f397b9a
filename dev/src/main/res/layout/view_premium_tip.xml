<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#B3000000">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="343dp"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_dialog_white_corner_32_bg"
        android:paddingHorizontal="@dimen/dp_20"
        android:paddingBottom="@dimen/dp_20"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_title"
            style="@style/MetaTextView.S16.PoppinsBold700"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_32"
            android:text="@string/premium_title_game"
            android:textColor="@color/color_1A1A1A"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_content"
            style="@style/MetaTextView.S14.PoppinsRegular400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_8"
            android:gravity="center"
            android:text="@string/premium_content_game"
            android:textColor="@color/color_333333"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_title" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_sure"
            style="@style/Button.S16.PoppinsMedium500"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48"
            android:layout_marginTop="@dimen/dp_16"
            android:gravity="center"
            android:text="@string/premium_game_sure"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_content" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_cancel"
            style="@style/Button.S16.PoppinsMedium500.CancelPrimary"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48"
            android:layout_marginTop="@dimen/dp_16"
            android:text="@string/cancel"
            android:gravity="center"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_sure" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>