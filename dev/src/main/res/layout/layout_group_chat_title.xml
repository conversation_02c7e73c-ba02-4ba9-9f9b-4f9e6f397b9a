<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/ivTitleBack"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/title_bar_height"
        android:background="?attr/actionBarItemBackground"
        android:paddingHorizontal="@dimen/dp_16"
        android:src="@drawable/icon_back_array_bold_black"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!--  设置较大的marginHorizontal, 放置顶部文本遮挡右侧的图标  -->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvTitle"
        style="@style/MetaTextView.S12.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_122"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/color_1A1A1A"
        android:textSize="@dimen/sp_15"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@id/tvMembers"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="Biubiubiubiubiubiubiubiubiubiubiubiu" />

    <ImageView
        android:id="@+id/ivNotification"
        android:layout_width="@dimen/dp_16"
        android:layout_height="@dimen/dp_16"
        android:layout_marginStart="@dimen/dp_8"
        android:background="?attr/actionBarItemBackground"
        android:scaleType="fitCenter"
        android:src="@drawable/icon_group_chat_title_notification_closed"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tvTitle"
        app:layout_constraintStart_toEndOf="@id/tvTitle"
        app:layout_constraintTop_toTopOf="@id/tvTitle"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvMembers"
        style="@style/MetaTextView.S12.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/color_999999"
        android:textSize="@dimen/sp_10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/ivNotification"
        app:layout_constraintStart_toStartOf="@id/tvTitle"
        app:layout_constraintTop_toBottomOf="@id/tvTitle"
        tools:text="23 Members" />

    <ImageView
        android:id="@+id/ivTitleMessages"
        android:layout_width="@dimen/dp_44"
        android:layout_height="@dimen/title_bar_height"
        android:background="?attr/actionBarItemBackground"
        android:paddingHorizontal="@dimen/dp_6"
        android:scaleType="fitCenter"
        android:src="@drawable/icon_group_chat_title_messages"
        android:visibility="gone"
        app:layout_constraintEnd_toStartOf="@id/ivTitleSetting"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/ivTitleSetting"
        android:layout_width="@dimen/dp_44"
        android:layout_height="@dimen/title_bar_height"
        android:layout_marginEnd="@dimen/dp_10"
        android:background="?attr/actionBarItemBackground"
        android:paddingHorizontal="@dimen/dp_6"
        android:scaleType="fitCenter"
        android:src="@drawable/icon_group_chat_title_setting"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_05"
        android:background="@color/color_F0F2F4"
        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>