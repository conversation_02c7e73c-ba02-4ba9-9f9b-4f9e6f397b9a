<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/black_60">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom">

        <ImageView
            android:id="@+id/v_figure_bg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/ic_guide_tips_female_bg"
            android:translationX="-20dp"
            android:translationY="@dimen/dp_64"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@id/iv_figure"
            app:layout_constraintStart_toStartOf="@id/iv_figure" />

        <ImageView
            android:id="@+id/iv_figure"
            android:layout_width="@dimen/dp_110"
            android:layout_height="@dimen/dp_234"
            android:layout_marginEnd="@dimen/dp_16"
            android:src="@drawable/ic_guide_tips_female"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <ImageView
            android:id="@+id/iv_guide_tri"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_10"
            android:src="@drawable/ic_guide_tips_tri"
            app:layout_constraintStart_toEndOf="@id/tv_guide_content"
            app:layout_constraintTop_toTopOf="@id/tv_guide_content" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_guide_content"
            style="@style/MetaTextView.S14.PoppinsSemiBold600"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_9"
            android:layout_marginBottom="@dimen/dp_13"
            android:background="@drawable/bg_ffef30_round_10"
            android:padding="@dimen/dp_8"
            android:text="@string/module_guide_add_tips"
            app:layout_constraintBottom_toTopOf="@id/iv_add"
            app:layout_constraintEnd_toStartOf="@id/iv_figure"
            app:layout_constraintStart_toStartOf="parent" />

        <ImageView
            android:id="@+id/iv_add"
            android:layout_width="@dimen/dp_75"
            android:layout_height="@dimen/dp_75"
            android:layout_gravity="top|center_horizontal"
            android:background="@drawable/bg_white_circle"
            android:padding="@dimen/dp_18"
            android:src="@drawable/ic_home_tab_add"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>