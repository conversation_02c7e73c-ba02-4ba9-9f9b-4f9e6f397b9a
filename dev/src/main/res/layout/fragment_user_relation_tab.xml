<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/sbphv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/tbl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:back_icon="@drawable/icon_back_array_bold_black"
        app:isDividerVisible="true"
        app:layout_constraintTop_toBottomOf="@id/sbphv"
        app:showRightText="false"
        app:title_divider_color="@color/color_F0F0F0" />

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tl"
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_height"
        android:layout_marginHorizontal="@dimen/dp_56"
        android:background="@color/transparent"
        app:layout_constraintBottom_toBottomOf="@id/tbl"
        app:layout_constraintTop_toTopOf="@id/tbl"
        app:tabGravity="center"
        app:tabIndicator="@drawable/indicator_user_relation"
        app:tabIndicatorColor="@color/color_FFDE70"
        app:tabIndicatorFullWidth="false"
        app:tabIndicatorGravity="bottom"
        app:tabIndicatorHeight="@dimen/dp_8"
        app:tabMinWidth="@dimen/dp_10"
        app:tabMode="scrollable"
        app:tabPaddingBottom="0dp"
        app:tabPaddingEnd="@dimen/dp_10"
        app:tabPaddingStart="@dimen/dp_10"
        app:tabPaddingTop="0dp"
        app:tabRippleColor="@null" />

    <androidx.appcompat.widget.AppCompatImageButton
        android:id="@+id/ibSetting"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/title_bar_height"
        android:background="?attr/actionBarItemBackground"
        android:paddingHorizontal="@dimen/dp_16"
        android:src="@drawable/ic_feat_24_1a1a1a_settings"
        app:layout_constraintBottom_toBottomOf="@id/tbl"
        app:layout_constraintEnd_toEndOf="@id/tbl"
        app:layout_constraintTop_toTopOf="@id/tbl" />

    <com.socialplay.gpark.ui.view.viewpager.AllowStateLossFrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tbl">

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/vp"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </com.socialplay.gpark.ui.view.viewpager.AllowStateLossFrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>