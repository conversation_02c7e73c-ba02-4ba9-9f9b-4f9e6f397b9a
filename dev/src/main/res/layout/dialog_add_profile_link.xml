<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:isScrollContainer="true"
    android:gravity="bottom|center_horizontal"
    tools:background="@color/black_10"
    android:orientation="vertical">
    <View
        android:id="@+id/outView"
        android:layout_width="match_parent"
        android:layout_weight="1"
        android:layout_height="0dp"/>
    <LinearLayout
        android:layout_width="match_parent"
        android:gravity="bottom|center_horizontal"
        android:background="@drawable/bg_white_top_round_24"
        android:orientation="vertical"
        android:layout_height="wrap_content">
        <TextView
            android:id="@+id/tv_add_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/poppins_semi_bold_600"
            android:text="@string/profile_link_add"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_marginTop="@dimen/dp_16"
            android:textSize="@dimen/sp_16" />

        <com.airbnb.epoxy.EpoxyRecyclerView
            android:id="@+id/ry"
            android:layout_width="match_parent"
            android:layout_height="55dp"
            app:layout_constraintTop_toBottomOf="@+id/tv_add_title"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:layout_marginTop="@dimen/sp_16"
            android:orientation="horizontal"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintLeft_toLeftOf="parent"
            tools:itemCount="3"
            tools:listitem="@layout/item_link_logo" />

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/input_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_16"
            android:background="@drawable/bg_f6_corner_12"
            android:hint="@string/profile_link_title"
            app:boxStrokeWidth="0dp"
            app:boxStrokeWidthFocused="0dp"
            app:hintAnimationEnabled="true"
            android:textColorHint="@color/color_757575"
            app:hintTextAppearance="@style/hintLinkAppearence"
            app:hintTextColor="@color/color_757575"
            app:layout_constraintTop_toBottomOf="@id/ry">

            <com.socialplay.gpark.ui.view.AutoFillTextInputEditText
                android:id="@+id/et_title"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="#00FFFFFF"
                android:fontFamily="@font/poppins_semi_bold_600"
                android:maxLines="1"
                android:singleLine="true"
                android:textCursorDrawable="@drawable/bg_cursor"
                android:textColorHint="@color/color_757575"
                android:textColor="@color/color_212121"
                android:textSize="@dimen/sp_14" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/input_account"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_12"
            android:background="@drawable/bg_f6_corner_12"
            android:hint="@string/profile_link_url"
            app:boxStrokeWidth="0dp"
            app:boxStrokeWidthFocused="0dp"
            app:hintAnimationEnabled="true"
            app:hintTextAppearance="@style/hintLinkAppearence"
            app:hintTextColor="@color/color_757575"
            android:textColorHint="@color/color_757575"
            app:layout_constraintTop_toBottomOf="@id/input_title">

            <com.socialplay.gpark.ui.view.AutoFillTextInputEditText
                android:id="@+id/et_url"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="#00FFFFFF"
                android:fontFamily="@font/poppins_semi_bold_600"
                android:focusableInTouchMode="true"
                android:focusable="true"
                android:singleLine="true"
                android:textCursorDrawable="@drawable/bg_cursor"
                android:textColor="@color/color_212121"
                android:textSize="@dimen/sp_14" />
        </com.google.android.material.textfield.TextInputLayout>

        <TextView
            android:id="@+id/tv_error_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="left"
            android:layout_marginLeft="@dimen/dp_16"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/input_account"
            app:layout_constraintLeft_toLeftOf="parent"
            android:fontFamily="@font/poppins_regular_400"
            android:text="@string/profile_link_add_url_error"
            android:textColor="@color/color_FF4C45"
            android:textSize="@dimen/sp_12" />


        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_add"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@id/tv_error_tip"
            app:layout_constraintLeft_toLeftOf="parent"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_12"
            android:layout_marginBottom="@dimen/dp_21"
            android:background="@drawable/selector_button"
            android:fontFamily="@font/poppins_semi_bold_600"
            android:gravity="center"
            android:paddingVertical="@dimen/dp_13"
            android:text="@string/text_confirm"
            android:textSize="@dimen/sp_16"
            app:layout_constraintBottom_toBottomOf="parent" />
    </LinearLayout>



</LinearLayout>