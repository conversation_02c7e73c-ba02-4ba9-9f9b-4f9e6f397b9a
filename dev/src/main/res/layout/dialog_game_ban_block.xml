<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="@dimen/dp_270"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_common_simple_dialog"
        android:padding="@dimen/dp_24"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:textColor="@color/color_333333"
            android:textSize="@dimen/sp_16"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="标题标题标题标题标题标题标题标题标题标题标题标题标题标题标题标题标题标题标题标题" />

        <View
            android:id="@+id/line_horizontal"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginTop="@dimen/dp_24"
            android:background="@color/color_080D2D_5"
            android:visibility="invisible"
            app:layout_constraintTop_toBottomOf="@id/title" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/btnRight"
            style="@style/MetaTextView.S15.PoppinsBlack900"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/bg_common_dialog_confirm"
            android:gravity="center"
            android:layout_marginHorizontal="@dimen/dp_30"
            android:paddingVertical="@dimen/dp_12"
            android:text="@string/dialog_confirm"
            android:textColor="@color/common_dialog_confirm_text"
            android:textSize="@dimen/sp_15"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintTop_toBottomOf="@id/line_horizontal" />


    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>