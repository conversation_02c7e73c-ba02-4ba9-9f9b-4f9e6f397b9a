<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="16dp"
    tools:background="#1A1A1A">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        android:text="Realistic Metallic Border"
        android:textColor="@android:color/white"
        android:textSize="24sp"
        android:textStyle="bold" />

    <com.socialplay.gpark.ui.view.MetallicBorderView
        android:id="@+id/metallic_border_view"
        android:layout_width="300dp"
        android:layout_height="150dp"
        android:padding="20dp"
        app:borderThickness="8dp"
        app:cornerRadius="30dp"
        app:highlightColor="#EAEAEA"
        app:highlightIntensity="0.9"
        app:lightAngle="315"
        app:metallicBaseColor="#707070"
        app:shadowColor="#000000"
        app:shadowIntensity="0.8">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#252525"
            android:gravity="center"
            android:text="Content Area"
            android:textColor="@android:color/white"
            android:textSize="18sp" />

    </com.socialplay.gpark.ui.view.MetallicBorderView>

    <SeekBar
        android:id="@+id/angle_slider"
        android:layout_width="300dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:max="360"
        android:progress="315" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="Adjust Light Angle (0-360°)"
        android:textColor="#CCCCCC" />

</LinearLayout> 