<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FF1A1A1A"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="@dimen/dp_32">

    <com.socialplay.gpark.ui.view.MetaTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_24"
        android:text="金属边框高光效果演示"
        android:textColor="@color/white"
        android:textSize="18sp"
        android:textStyle="bold" />

    <!-- 基础金属边框高光 -->
    <View
        android:layout_width="120dp"
        android:layout_height="80dp"
        android:layout_marginBottom="@dimen/dp_16"
        android:background="@drawable/bg_glossy_oval_effect" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_24"
        android:text="基础金属边框高光"
        android:textColor="@color/white_70"
        android:textSize="12sp" />

    <!-- 增强金属边框高光 -->
    <View
        android:layout_width="120dp"
        android:layout_height="80dp"
        android:layout_marginBottom="@dimen/dp_16"
        android:background="@drawable/bg_glossy_oval_enhanced" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_24"
        android:text="多层金属边框高光"
        android:textColor="@color/white_70"
        android:textSize="12sp" />

    <!-- 简洁金属边框 -->
    <View
        android:layout_width="120dp"
        android:layout_height="80dp"
        android:layout_marginBottom="@dimen/dp_16"
        android:background="@drawable/bg_metallic_border_highlight" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_24"
        android:text="简洁金属边框"
        android:textColor="@color/white_70"
        android:textSize="12sp" />

    <!-- 简单金属边框 -->
    <View
        android:layout_width="120dp"
        android:layout_height="80dp"
        android:layout_marginBottom="@dimen/dp_16"
        android:background="@drawable/bg_simple_metallic_border" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_24"
        android:text="简单金属边框"
        android:textColor="@color/white_70"
        android:textSize="12sp" />

    <!-- 高级金属边框 -->
    <View
        android:layout_width="120dp"
        android:layout_height="80dp"
        android:layout_marginBottom="@dimen/dp_16"
        android:background="@drawable/bg_premium_metallic_border" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="高级金属边框高光"
        android:textColor="@color/white_70"
        android:textSize="12sp" />

</LinearLayout>
