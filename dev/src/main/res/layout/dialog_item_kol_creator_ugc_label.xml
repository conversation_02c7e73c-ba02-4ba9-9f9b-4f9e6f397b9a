<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/llRoot"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_f0f0f0_corner_360"
    android:orientation="horizontal"
    android:paddingHorizontal="@dimen/dp_8"
    android:paddingVertical="@dimen/dp_4">

    <ImageView
        android:id="@+id/ivLabel"
        android:layout_width="@dimen/dp_15"
        android:layout_height="@dimen/dp_15"
        android:layout_gravity="center"
        android:layout_marginEnd="@dimen/dp_4" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:fontFamily="@font/poppins_regular_400"
        android:gravity="center"
        android:textSize="@dimen/dp_12"
        tools:text="PlayfulPlayful" />
</LinearLayout>