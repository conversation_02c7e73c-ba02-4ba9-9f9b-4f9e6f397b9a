<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingBottom="4.5dp">

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_normal"
        style="@style/MetaTextView.S14.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="center_horizontal|bottom"
        android:gravity="center_horizontal|bottom"
        android:maxLines="1"
        android:paddingVertical="@dimen/dp_3"
        android:singleLine="true"
        android:textColor="@color/color_CCCCCC"
        android:textSize="@dimen/sp_14"
        tools:text="Add Friend" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_selected"
        style="@style/MetaTextView.S16.PoppinsSemiBold600"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="center_horizontal|bottom"
        android:gravity="center_horizontal|bottom"
        android:maxLines="1"
        android:paddingVertical="@dimen/dp_3"
        android:singleLine="true"
        android:textColor="@color/color_1A1A1A"
        android:textSize="@dimen/sp_16"
        android:visibility="invisible"
        tools:text="Add Friend" />

</FrameLayout>




