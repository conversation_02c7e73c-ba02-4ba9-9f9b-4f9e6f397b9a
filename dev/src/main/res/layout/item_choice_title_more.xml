<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingHorizontal="@dimen/dp_16"
    android:paddingTop="@dimen/dp_12"
    android:paddingBottom="@dimen/dp_8">

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_card_title"
        style="@style/MetaTextView.S18.PoppinsSemiBold600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:singleLine="true"
        android:textColor="@color/textColorPrimary"
        app:layout_constraintEnd_toStartOf="@id/tv_card_more"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Live rooms" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_card_more"
        style="@style/MetaTextView.S12.PoppinsRegular400.CenterVertical"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingStart="@dimen/dp_5"
        android:paddingEnd="@dimen/dp_5"
        android:paddingVertical="@dimen/dp_2"
        android:text="@string/see_all"
        android:textColor="@color/textColorPrimaryLight"
        app:layout_constraintBottom_toBottomOf="@id/tv_card_title"
        app:layout_constraintRight_toLeftOf="@id/iv_arrow_right"
        app:layout_constraintTop_toTopOf="@id/tv_card_title" />

    <ImageView
        android:id="@+id/iv_arrow_right"
        android:layout_width="@dimen/dp_18"
        android:layout_height="@dimen/dp_18"
        android:src="@drawable/icon_arrow_right"
        android:padding="@dimen/dp_2"
        app:layout_constraintBottom_toBottomOf="@id/tv_card_title"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_card_title"
        />

</androidx.constraintlayout.widget.ConstraintLayout>