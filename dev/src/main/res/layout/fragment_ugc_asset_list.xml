<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/sbphv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/tbl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:isDividerVisible="false"
        app:layout_constraintTop_toBottomOf="@id/sbphv" />

    <ImageView
        android:id="@+id/iv_more_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_6"
        android:src="@drawable/ic_ugc_asset_list_more"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tbl"
        app:layout_constraintEnd_toEndOf="@id/tbl"
        app:layout_constraintTop_toTopOf="@id/tbl" />

    <com.socialplay.gpark.ui.view.MinWidthTabLayout
        android:id="@+id/tl"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_40"
        android:background="@color/transparent"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/tl_visibility"
        app:tabGravity="start"
        app:tabIndicator="@drawable/indicator_ugc_asset_list"
        app:tabIndicatorColor="@color/black"
        app:tabIndicatorFullWidth="false"
        app:tabIndicatorHeight="@dimen/dp_4"
        app:tabMode="scrollable"
        app:tabPaddingBottom="0dp"
        app:tabPaddingEnd="@dimen/dp_16"
        app:tabPaddingStart="@dimen/dp_16"
        app:tabPaddingTop="@dimen/dp_6"
        app:tabRippleColor="@color/transparent" />

    <com.socialplay.gpark.ui.view.MinWidthTabLayout
        android:id="@+id/tl_visibility"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_40"
        android:background="@color/white"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/tbl"
        app:tabGravity="start"
        app:tabIndicator="@drawable/indicator_ugc_asset_list"
        app:tabIndicatorColor="@color/black"
        app:tabIndicatorFullWidth="false"
        app:tabIndicatorHeight="@dimen/dp_4"
        app:tabMode="scrollable"
        app:tabPaddingBottom="0dp"
        app:tabPaddingEnd="@dimen/dp_16"
        app:tabPaddingStart="@dimen/dp_16"
        app:tabPaddingTop="@dimen/dp_6"
        app:tabRippleColor="@color/transparent" />

    <View
        android:id="@+id/v_divider_tl"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/color_E6E6E6"
        app:layout_constraintTop_toBottomOf="@id/tl" />

    <com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout
        android:id="@+id/refresh"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v_divider_tl">

        <!-- 用于位移loadingView -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.airbnb.epoxy.EpoxyRecyclerView
                android:id="@+id/rv_item"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false"
                android:orientation="vertical"
                android:paddingStart="@dimen/dp_16"
                android:paddingTop="@dimen/dp_12"
                android:paddingEnd="@dimen/dp_8"
                android:paddingBottom="@dimen/dp_4" />

            <com.socialplay.gpark.ui.view.LoadingView
                android:id="@+id/lv"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone" />

        </FrameLayout>

    </com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout>

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_submit_btn"
        style="@style/MetaTextView.S12.PoppinsSemiBold600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_16"
        android:background="@drawable/bg_ffef30_round_100"
        android:gravity="center"
        android:paddingHorizontal="@dimen/dp_20"
        android:paddingVertical="6.5dp"
        android:text="@string/done"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tbl"
        app:layout_constraintEnd_toEndOf="@id/tbl"
        app:layout_constraintTop_toTopOf="@id/tbl"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>