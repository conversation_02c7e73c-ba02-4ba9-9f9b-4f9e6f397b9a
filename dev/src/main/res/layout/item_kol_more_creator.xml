<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingVertical="@dimen/dp_12"
    android:paddingStart="@dimen/dp_16">

    <include
        android:id="@+id/includeItem"
        layout="@layout/include_common_user_list"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toStartOf="@id/tvAction"
        app:layout_constraintStart_toStartOf="parent" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvAction"
        style="@style/MetaTextView.S12"
        android:layout_width="@dimen/dp_76"
        android:layout_height="@dimen/dp_25"
        android:layout_marginEnd="@dimen/dp_16"
        android:background="@drawable/bg_b884ff_round_16"
        android:gravity="center"
        android:paddingStart="0dp"
        android:paddingEnd="0dp"
        android:textColor="@color/white"
        app:layout_constraintBottom_toBottomOf="@id/includeItem"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/includeItem"
        app:layout_constraintTop_toTopOf="@id/includeItem"
        tools:text="@string/view_cap" />

</androidx.constraintlayout.widget.ConstraintLayout>