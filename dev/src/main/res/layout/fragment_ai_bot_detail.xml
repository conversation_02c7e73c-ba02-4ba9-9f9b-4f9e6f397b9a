<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:speed_monitor="true"
    >

    <ImageView
        android:id="@+id/img_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <View
        android:layout_width="match_parent"
        android:id="@+id/viewTop"
        android:background="@drawable/bg_ai_bit_detail_top"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_height="300dp"/>

    <View
        android:id="@+id/viewBg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="280dp"
        android:background="@drawable/bg_ai_bit_detail"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <androidx.core.widget.NestedScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="visible"

        >

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">
            <View
                android:id="@+id/viewBottom"
                android:layout_width="match_parent"
                android:layout_height="1920dp"
                android:layout_marginTop="300dp"
                android:background="@drawable/bg_ai_bit_detail"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                android:layout_marginTop="350dp"

              >

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_user_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dp_16"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tv_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/poppins_semi_bold_600"
                android:textColor="@color/white_90"

                android:textSize="@dimen/sp_36"
                android:gravity="left|center_vertical"
                app:uiLineHeight="@dimen/dp_40"
                android:includeFontPadding="false"
                app:layout_constrainedWidth="true"
                app:layout_constraintEnd_toStartOf="@id/img_gender"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Obama111111111111111111" />

                    <ImageView
                        android:id="@+id/img_gender"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginBottom="@dimen/dp_8"
                        android:src="@drawable/icon_gender_male"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="@+id/tv_name"
                        app:layout_constraintEnd_toStartOf="@+id/space_end"
                        app:layout_constraintStart_toEndOf="@+id/tv_name" />

                    <View

                        android:id="@+id/space_end"
                        android:layout_width="@dimen/dp_16"
                        android:layout_height="0dp"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>


                <TextView
                    android:id="@+id/tv_id"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dp_16"
                    android:layout_marginTop="@dimen/dp_16"
                    android:drawableLeft="@drawable/icon_ai_bot_id"
                    android:drawablePadding="@dimen/dp_4"
                    android:fontFamily="@font/poppins_regular_400"
                    android:gravity="center"
                    android:textColor="@color/white_80"
                    android:textSize="@dimen/sp_12"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/cl_user_name"
                    tools:text="ZNEaFHuzxb" />

                <ImageView
                    android:id="@+id/img_space"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dp_8"
                    android:src="@drawable/icon_ai_bot_space"
                    app:layout_constraintBottom_toBottomOf="@+id/tv_id"
                    app:layout_constraintLeft_toRightOf="@+id/tv_id"
                    app:layout_constraintTop_toTopOf="@id/tv_id" />

                <TextView
                    android:id="@+id/tv_bot_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dp_8"
                    android:layout_marginTop="@dimen/dp_16"
                    android:drawableLeft="@drawable/icon_ai_bot_name"
                    android:drawablePadding="@dimen/dp_4"
                    android:fontFamily="@font/poppins_regular_400"
                    android:gravity="center"
                    android:textColor="@color/white_80"
                    android:textSize="@dimen/sp_12"
                    app:layout_constraintLeft_toRightOf="@+id/img_space"
                    app:layout_constraintTop_toBottomOf="@+id/cl_user_name"
                    tools:text="ZNEaFHuzxb" />

                <TextView
                    android:id="@id/tv_message_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16dp"
                    android:layout_marginTop="@dimen/dp_32"
                    android:fontFamily="@font/poppins_semi_bold_600"
                    android:textColor="@color/white_80"
                    android:textSize="@dimen/sp_16"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_bot_name"
                    tools:text="600" />

                <TextView
                    android:id="@+id/tv_connection"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_4"
                    android:fontFamily="@font/poppins_regular_400"
                    android:text="@string/ai_bot_connectors"
                    android:textColor="@color/white_60"
                    android:textSize="@dimen/sp_12"
                    app:layout_constraintLeft_toLeftOf="@+id/tv_message_count"
                    app:layout_constraintTop_toBottomOf="@+id/tv_message_count" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_follow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16dp"
                    android:layout_marginTop="@dimen/dp_32"
                    app:layout_constraintLeft_toRightOf="@+id/tv_connection"
                    app:layout_constraintTop_toBottomOf="@+id/tv_bot_name">

                    <TextView
                        android:id="@id/tv_follow_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/poppins_semi_bold_600"
                        android:textColor="@color/white_80"
                        android:textSize="@dimen/sp_16"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="600" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp_4"
                        android:fontFamily="@font/poppins_regular_400"
                        android:text="@string/ai_bot_followers"
                        android:textColor="@color/white_60"
                        android:textSize="@dimen/sp_12"
                        app:layout_constraintLeft_toLeftOf="@+id/tv_follow_count"
                        app:layout_constraintTop_toBottomOf="@+id/tv_follow_count" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <TextView
                    android:id="@+id/tv_follow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="@dimen/dp_16"
                    android:background="@drawable/bg_white_50_round_360_stroke_1"
                    android:fontFamily="@font/poppins_regular_400"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/dp_28"
                    android:paddingVertical="@dimen/dp_6"
                    android:text="@string/follow"
                    android:textColor="@color/white_90"
                    android:textSize="@dimen/sp_14"
                    app:layout_constraintBottom_toBottomOf="@+id/cl_follow"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/cl_follow" />

                <com.socialplay.gpark.ui.view.FlowLayout
                    android:id="@+id/ry_label"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dp_16"
                    android:layout_marginTop="22dp"
                    app:horizontalSpacing="8dp"
                    app:layout_constraintTop_toBottomOf="@+id/cl_follow"
                    app:verticalSpacing="8dp" />

                <Button
                    android:id="@+id/chat"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dp_16"
                    android:layout_marginTop="32dp"
                    android:background="@drawable/bg_white_corner_12"
                    android:paddingVertical="@dimen/dp_12"
                    android:text="@string/chat_cap"
                    android:textColor="@color/color_1A1A1A"
                    android:textSize="@dimen/sp_14"
                    app:layout_constraintTop_toBottomOf="@+id/ry_label" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_desc"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/dp_16"
                    android:background="@drawable/bg_white_5_corner_12"
                    android:padding="@dimen/dp_16"
                    app:layout_constraintTop_toBottomOf="@id/chat">

                    <TextView
                        android:id="@+id/tv_shot_des_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/poppins_semi_bold_600"
                        android:text="@string/ai_bot_des"
                        android:textColor="@color/white_90"
                        android:textSize="@dimen/sp_14"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tv_shot_des"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:fontFamily="@font/poppins_regular_400"
                        android:paddingBottom="@dimen/dp_32"
                        android:text="@string/ai_bot_des"
                        android:textColor="@color/white_80"
                        android:textSize="@dimen/sp_14"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tv_shot_des_title" />

                    <TextView
                        android:id="@+id/tv_shot_pro_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/poppins_semi_bold_600"
                        android:text="@string/ai_bot_prologue"
                        android:textColor="@color/white_90"
                        android:textSize="@dimen/sp_14"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tv_shot_des" />

                    <TextView
                        android:id="@+id/tv_shot_pro"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:fontFamily="@font/poppins_regular_400"
                        android:text="@string/ai_bot_des"
                        android:textColor="@color/white_80"
                        android:textSize="@dimen/sp_14"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tv_shot_pro_title" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="-8dp"
                        android:src="@drawable/icon_ai_bot_intro"
                        app:layout_constraintBottom_toBottomOf="@+id/tv_shot_pro"
                        app:layout_constraintRight_toRightOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>


                <FrameLayout
                    android:id="@+id/flReview"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:clickable="true"
                    android:layout_marginTop="@dimen/dp_24"
                    app:layout_constraintTop_toBottomOf="@id/cl_desc" />

                <LinearLayout
                    android:id="@+id/tvAllGameReviews"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_46"
                    android:layout_marginHorizontal="16dp"
                    android:layout_marginTop="@dimen/dp_20"
                    android:layout_marginBottom="@dimen/dp_22"
                    android:gravity="center_horizontal"
                    android:orientation="horizontal"
                    android:visibility="visible"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/flReview"
                    app:layout_goneMarginTop="@dimen/dp_2"
                    tools:visibility="visible">

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/dp_21"
                        android:layout_marginEnd="@dimen/dp_6"
                        android:text="@string/see_all_ratings"
                        android:textColor="@color/white_80"
                        android:textSize="@dimen/sp_14"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:layout_width="@dimen/dp_6"
                        android:layout_height="@dimen/dp_21"
                        android:scaleType="fitCenter"
                        android:src="@drawable/icon_arrow_right_999999"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </LinearLayout>


            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.core.widget.NestedScrollView>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_top"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/img_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_8"
            android:layout_marginTop="@dimen/dp_36"
            android:padding="@dimen/dp_8"
            android:src="@drawable/icon_back_white_round"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"

            />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_create"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_16"
            android:paddingVertical="@dimen/dp_8"
            android:paddingLeft="@dimen/dp_16"
            app:layout_constraintBottom_toBottomOf="@+id/img_close"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/img_close">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="@dimen/dp_8"
                android:src="@drawable/icon_upload_progress"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toLeftOf="@+id/tv_create"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_create"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/recommend_create"
                android:textColor="@color/white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_create_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/icon_ai_bot_entrance"
            android:fontFamily="@font/poppins_regular_400"
            android:gravity="center_vertical"
            android:paddingHorizontal="@dimen/dp_16"
            android:text="@string/ai_bot_create_tip"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_14"
            android:visibility="gone"
            app:layout_constraintRight_toRightOf="@+id/cl_create"
            app:layout_constraintTop_toBottomOf="@+id/cl_create" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <FrameLayout
        android:id="@+id/flAllReviewList"
        android:layout_width="match_parent"
        android:layout_height="0dp"

        android:visibility="gone"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/rl_title"

        />
    <RelativeLayout
        android:id="@+id/rl_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent">

        <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
            android:id="@+id/sbphv_placeholder"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <com.socialplay.gpark.ui.view.TitleBarLayout
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/sbphv_placeholder"
            app:back_icon="@drawable/icon_back_left_arrow"
            app:background_color="@color/white"
            app:isDividerVisible="false"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:showRightText="false"
            app:title_text="" />
    </RelativeLayout>

</androidx.constraintlayout.widget.ConstraintLayout>