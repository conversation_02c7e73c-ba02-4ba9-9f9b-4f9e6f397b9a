<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cv_root"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:cardBackgroundColor="@color/color_F0F0F0"
    app:cardCornerRadius="@dimen/dp_16"
    app:cardElevation="0dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingBottom="@dimen/dp_8">

        <ImageView
            android:id="@+id/iv_cover"
            android:layout_width="@dimen/dp_178"
            android:layout_height="@dimen/dp_154"
            android:scaleType="centerCrop"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_play_btn"
            android:layout_width="@dimen/dp_36"
            android:layout_height="@dimen/dp_36"
            android:layout_marginStart="@dimen/dp_12"
            android:layout_marginBottom="@dimen/dp_12"
            android:src="@drawable/ic_play_s36_black80"
            app:layout_constraintBottom_toBottomOf="@id/iv_cover"
            app:layout_constraintStart_toStartOf="@id/iv_cover" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_title"
            style="@style/MetaTextView.S14.PoppinsBold700"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_8"
            android:layout_marginTop="@dimen/dp_8"
            android:ellipsize="end"
            android:maxLines="2"
            app:layout_constraintEnd_toEndOf="@id/iv_cover"
            app:layout_constraintStart_toStartOf="@id/iv_cover"
            app:layout_constraintTop_toBottomOf="@id/iv_cover" />

        <ImageView
            android:id="@+id/iv_avatar"
            android:layout_width="@dimen/dp_24"
            android:layout_height="@dimen/dp_24"
            android:layout_marginStart="@dimen/dp_8"
            android:layout_marginTop="@dimen/dp_6"
            android:scaleType="centerCrop"
            app:layout_constraintStart_toStartOf="@id/iv_cover"
            app:layout_constraintTop_toBottomOf="@id/tv_title" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_uname"
            style="@style/MetaTextView.S14.PoppinsBold700"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_6"
            android:layout_marginTop="@dimen/dp_8"
            android:layout_marginEnd="@dimen/dp_8"
            android:ellipsize="end"
            android:maxLines="1"
            android:singleLine="true"
            android:textColor="@color/neutral_color_3"
            app:layout_constraintEnd_toEndOf="@id/iv_cover"
            app:layout_constraintStart_toEndOf="@id/iv_avatar"
            app:layout_constraintTop_toBottomOf="@id/tv_title" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>