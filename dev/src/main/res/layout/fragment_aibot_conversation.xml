<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/img_cover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop" />

        <View
            android:id="@+id/view_top"
            android:layout_width="match_parent"
            android:layout_height="166dp"
            app:layout_constraintTop_toTopOf="parent"
            android:background="@drawable/bg_ai_bot_conversation_top" />

        <View
            android:id="@+id/view_bottom"
            android:layout_width="match_parent"
            android:layout_height="250dp"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_alignParentBottom="true"
            android:background="@drawable/bg_ai_bot_conversation_bottom" />

        <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
            android:id="@+id/sbphv_placeholder"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="@dimen/dp_60"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/rl_send"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toTopOf="@+id/rv_other_layout"
            android:layout_marginLeft="@dimen/dp_16"
            android:layout_marginRight="@dimen/dp_16"
            android:layout_marginBottom="@dimen/dp_16">

            <EditText
                android:id="@+id/et_content"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp_16"
                android:background="@drawable/bg_black_10_corner_8"
                android:fontFamily="@font/poppins_regular_400"
                android:maxHeight="@dimen/dp_80"
                android:paddingHorizontal="@dimen/dp_16"
                android:paddingVertical="@dimen/dp_10"
                android:textColor="@color/white"
                android:textCursorDrawable="@drawable/bg_cursor_yellow"
                android:textSize="@dimen/sp_14"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintHeight_min="@dimen/dp_40"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toLeftOf="@+id/img_send"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/img_send"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/icon_ai_bot_add"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
        <com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout
            android:id="@+id/refresh_layout"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toTopOf="@+id/rl_send"
            app:layout_constraintTop_toBottomOf="@+id/sbphv_placeholder"
            android:layout_marginBottom="@dimen/dp_8">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="bottom"
                android:paddingTop="@dimen/dp_55">

                <com.airbnb.epoxy.EpoxyRecyclerView
                    android:id="@+id/ry"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:clipChildren="false"
                    android:clipToPadding="false"
                    />


            </LinearLayout>

        </com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_top"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@+id/sbphv_placeholder">

            <ImageView
                android:id="@+id/img_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="@dimen/dp_16"
                android:src="@drawable/icon_white_close"
                app:layout_constraintBottom_toBottomOf="@+id/cl_user_all"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@id/cl_user_all" />
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_user_all"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/img_close"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_user_info"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dp_16"
                    android:layout_marginTop="@dimen/dp_9"
                    android:layout_marginRight="@dimen/dp_16"
                    android:background="@drawable/bg_black_30_corner_360"
                    android:padding="@dimen/dp_4"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent">


                    <com.google.android.material.imageview.ShapeableImageView
                        android:id="@+id/img_user"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:layout_marginLeft="@dimen/dp_4"
                        android:scaleType="centerCrop"
                        android:src="@drawable/icon_default_avatar"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:shapeAppearance="@style/circleStyle" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/cl_user"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/dp_8"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toRightOf="@+id/img_user"
                        app:layout_constraintRight_toLeftOf="@+id/img_follow"
                        app:layout_constraintTop_toTopOf="parent">

                        <TextView
                            android:id="@+id/tv_user_name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:ellipsize="end"
                            android:fontFamily="@font/poppins_medium_500"
                            android:maxLines="1"
                            android:textColor="@color/white"
                            android:textSize="@dimen/sp_12"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintWidth_max="205dp"
                            tools:text="12111111111111111111111111111111111, Connectors">

                        </TextView>

                        <TextView
                            android:id="@+id/tv_user_desc"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:ellipsize="end"
                            android:fontFamily="@font/poppins_regular_400"
                            android:maxLines="1"
                            android:textColor="@color/white_70"
                            android:textSize="@dimen/sp_10"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/tv_user_name"
                            app:layout_constraintWidth_max="205dp"
                            tools:text="12,316 Connectors">

                        </TextView>
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <ImageView
                        android:id="@+id/img_follow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/dp_12"
                        android:layout_marginRight="@dimen/dp_4"
                        android:src="@drawable/icon_ai_bot_follow"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toRightOf="@+id/cl_user"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>


        </androidx.constraintlayout.widget.ConstraintLayout>




        <RelativeLayout
            android:id="@+id/rv_other_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginHorizontal="@dimen/dp_9">

            <TextView
                android:id="@+id/tv_reset"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableTop="@drawable/icon_ai_reset"
                android:fontFamily="@font/poppins_regular_400"
                android:padding="@dimen/dp_14"
                android:text="@string/ai_bot_reset"
                android:textColor="@color/white"
                android:textSize="12dp"
                android:visibility="gone" />
        </RelativeLayout>

        <com.socialplay.gpark.ui.view.LoadingView
            android:id="@+id/loading_view"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>