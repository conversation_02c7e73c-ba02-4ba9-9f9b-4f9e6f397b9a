<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <Space
        android:id="@+id/spaceTop"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_16"
        app:layout_constraintTop_toTopOf="parent" />

    <Space
        android:id="@+id/spaceStart"
        android:layout_width="5.5dp"
        android:layout_height="1dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <Space
        android:id="@+id/spaceEnd"
        android:layout_width="5.5dp"
        android:layout_height="1dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_img"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:scaleType="centerCrop"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintEnd_toStartOf="@id/spaceEnd"
        app:layout_constraintStart_toEndOf="@id/spaceStart"
        app:layout_constraintTop_toBottomOf="@id/spaceTop"
        app:shapeAppearance="@style/round_corner_12dp" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_plot_mask"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_40"
        android:background="@drawable/bg_gradient_black_87"
        app:layout_constraintBottom_toBottomOf="@id/iv_img"
        app:layout_constraintEnd_toEndOf="@id/iv_img"
        app:layout_constraintStart_toStartOf="@id/iv_img"
        app:shapeAppearance="@style/round_corner_bottom_16dp" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/iv_short_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_12"
        android:layout_marginBottom="@dimen/dp_10"
        android:src="@drawable/plot_short_icon"
        app:layout_constraintBottom_toBottomOf="@id/iv_plot_mask"
        app:layout_constraintStart_toStartOf="@id/iv_plot_mask"
        app:layout_constraintTop_toTopOf="@id/iv_plot_mask"
        app:layout_constraintVertical_bias="1" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_short_num"
        style="@style/MetaTextView.S12.PoppinsRegular400.White"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_2"
        app:layout_constraintBottom_toBottomOf="@id/iv_short_icon"
        app:layout_constraintStart_toEndOf="@id/iv_short_icon"
        app:layout_constraintTop_toTopOf="@id/iv_short_icon"
        tools:text="809 players" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_name"
        style="@style/MetaTextView.S14"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_8"
        android:ellipsize="end"
        android:gravity="top|start"
        android:textColor="@color/color_1A1A1A"
        app:layout_constraintEnd_toEndOf="@id/iv_img"
        app:layout_constraintStart_toStartOf="@id/iv_img"
        app:layout_constraintTop_toBottomOf="@id/iv_img"
        tools:text="Campma Yang  Drama Yang cer ruileDuo Drama Yang cer ruile " />

</androidx.constraintlayout.widget.ConstraintLayout>