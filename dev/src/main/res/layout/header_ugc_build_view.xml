<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingEnd="@dimen/dp_11">

    <androidx.cardview.widget.CardView
        android:id="@+id/cv_banner"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_16"
        android:visibility="gone"
        app:cardBackgroundColor="@null"
        app:cardCornerRadius="@dimen/dp_12"
        app:cardElevation="0dp"
        tools:visibility="visible">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <com.socialplay.gpark.ui.view.WrapBanner
                android:id="@+id/wb"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                app:banner_auto_loop="true"
                app:banner_infinite_loop="true"
                app:banner_orientation="horizontal"
                app:layout_constraintDimensionRatio="343:126"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.zhpan.indicator.IndicatorView
                android:id="@+id/indicator_banner"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/dp_7"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:vpi_orientation="horizontal"
                app:vpi_slide_mode="normal"
                app:vpi_slider_checked_color="@color/white"
                app:vpi_slider_normal_color="@color/white_50"
                app:vpi_style="round_rect" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:id="@+id/layout_build_card"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_48"
        android:layout_marginVertical="@dimen/dp_16"
        app:cardBackgroundColor="@color/color_FFEF30"
        app:cardCornerRadius="@dimen/dp_24"
        app:cardElevation="0dp"
        app:layout_constraintBottom_toTopOf="@id/tv_build_list_title"
        app:layout_constraintTop_toTopOf="parent">

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_build"
            style="@style/MetaTextView.S16.PoppinsSemiBold600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:drawablePadding="@dimen/dp_4"
            android:gravity="center"
            android:text="@string/create_v2_build"
            android:textColor="@color/color_212121"
            app:drawableStartCompat="@drawable/go_build" />

    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/tv_build_list_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_12"
        android:text="@string/build_list_title"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_16"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_build_card" />

</LinearLayout>
