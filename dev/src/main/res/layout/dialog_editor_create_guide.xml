<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clRoot"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="#000">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clMove"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_84"
        android:layout_marginHorizontal="@dimen/dp_8"
        android:background="@drawable/placeholder_corner_16"
        android:backgroundTint="#fff"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.cardview.widget.CardView
            android:id="@+id/layout_build_card"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:layout_marginVertical="@dimen/dp_16"
            app:cardBackgroundColor="@color/color_FFEF30"
            app:cardCornerRadius="@dimen/dp_24"
            app:cardElevation="0dp"
            app:layout_constraintTop_toTopOf="parent">

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvBuild"
                style="@style/MetaTextView.S16.PoppinsSemiBold600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:drawablePadding="@dimen/dp_4"
                android:gravity="center"
                android:text="@string/create_v2_build"
                android:textColor="@color/color_212121"
                app:drawableStartCompat="@drawable/go_build" />

        </androidx.cardview.widget.CardView>
        <!--        <com.google.android.material.imageview.ShapeableImageView-->
        <!--            android:id="@+id/iv"-->
        <!--            android:layout_width="120dp"-->
        <!--            android:layout_height="120dp"-->
        <!--            android:layout_marginTop="8dp"-->
        <!--            android:background="@drawable/placeholder_corner_12"-->
        <!--            app:layout_constraintLeft_toLeftOf="parent"-->
        <!--            app:layout_constraintRight_toRightOf="parent"-->
        <!--            app:layout_constraintTop_toTopOf="parent"-->
        <!--            app:shapeAppearance="@style/round_corner_12dp" />-->

        <!--        <TextView-->
        <!--            android:id="@+id/tvBuild"-->
        <!--            style="@style/MetaTextView.S13.PoppinsMedium500"-->
        <!--            android:layout_width="93dp"-->
        <!--            android:layout_height="32dp"-->
        <!--            android:layout_marginTop="18dp"-->
        <!--            android:background="@drawable/placeholder_corner_16"-->
        <!--            android:backgroundTint="#8886EE"-->
        <!--            android:gravity="center"-->
        <!--            android:text="@string/create_v2_build"-->
        <!--            android:textColor="#fff"-->
        <!--            app:layout_constraintLeft_toLeftOf="parent"-->
        <!--            app:layout_constraintRight_toRightOf="parent"-->
        <!--            app:layout_constraintTop_toBottomOf="@id/iv" />-->

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl2"
            android:layout_width="@dimen/dp_120"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.cardview.widget.CardView
                android:id="@+id/cv"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                app:cardBackgroundColor="@null"
                app:cardCornerRadius="@dimen/dp_12"
                app:cardElevation="0dp"
                app:layout_constraintDimensionRatio="1:1"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/iv_cover"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    tools:src="@android:color/holo_green_dark" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_36"
                    android:layout_gravity="bottom"
                    android:background="@drawable/bg_gradient_black_87" />

            </androidx.cardview.widget.CardView>

            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/iv_avatar"
                android:layout_width="@dimen/dp_24"
                android:layout_height="@dimen/dp_24"
                android:layout_margin="@dimen/dp_6"
                android:padding="@dimen/dp_1"
                app:layout_constraintBottom_toBottomOf="@id/cv"
                app:layout_constraintStart_toStartOf="@id/cv"
                app:shapeAppearance="@style/circleStyle"
                app:strokeColor="@color/white"
                app:strokeWidth="@dimen/dp_1"
                tools:src="@drawable/icon_default_avatar" />

            <TextView
                android:id="@+id/tv_author_name"
                style="@style/MetaTextView.S12"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_4"
                android:layout_marginEnd="@dimen/dp_10"
                android:singleLine="true"
                android:textColor="@color/white"
                app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/iv_avatar"
                app:layout_constraintTop_toTopOf="@id/iv_avatar"
                tools:text="Author name" />

            <TextView
                android:id="@+id/tv_title"
                style="@style/MetaTextView.S13.PoppinsMedium500"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_2"
                android:layout_marginTop="@dimen/dp_8"
                android:singleLine="true"
                app:layout_constraintTop_toBottomOf="@id/cv"
                tools:text="Work name" />

            <TextView
                android:id="@+id/tv_pv"
                style="@style/MetaTextView.S11"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_2"
                android:layout_marginTop="@dimen/dp_8"
                android:drawableStart="@drawable/ic_fire_42"
                android:drawablePadding="@dimen/dp_2"
                android:gravity="center_vertical"
                android:singleLine="true"
                android:textColor="@color/color_B3B3B3"
                app:layout_constraintTop_toBottomOf="@id/tv_title"
                tools:text="0 players" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/ivArrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="62dp"
        android:layout_marginTop="4dp"
        android:src="@drawable/icon_ugc_guide_arrow"
        app:layout_constraintLeft_toLeftOf="@id/clMove"
        app:layout_constraintTop_toBottomOf="@id/clMove" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clGuide1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="24dp"
        android:layout_marginTop="5dp"
        android:background="@drawable/bg_yellow_corner_16"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivArrow">

        <ImageView
            android:id="@+id/ivIc1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_6"
            android:layout_marginBottom="-1dp"
            android:src="@drawable/ic_build_guide"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvContent1"
            style="@style/MetaTextView.S14.PoppinsSemiBold600"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="@dimen/dp_6"
            android:layout_marginVertical="@dimen/dp_17"
            android:text="@string/build_guide_1"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tvOk1"
            app:layout_constraintStart_toEndOf="@id/ivIc1"
            app:layout_constraintTop_toTopOf="parent" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvOk1"
            style="@style/MetaTextView.S14.PoppinsSemiBold600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_12"
            android:background="@drawable/bg_1a1a1a_round"
            android:gravity="center"
            android:minWidth="@dimen/dp_72"
            android:minHeight="@dimen/dp_32"
            android:text="@string/ok"
            android:textColor="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clGuide2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="24dp"
        android:layout_marginTop="5dp"
        android:background="@drawable/bg_yellow_corner_16"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivArrow">

        <ImageView
            android:id="@+id/ivIc2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_use_controller"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvContent2"
            style="@style/MetaTextView.S14.PoppinsSemiBold600"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/dp_9"
            android:layout_marginEnd="@dimen/dp_17"
            android:paddingVertical="@dimen/dp_16"
            android:text="@string/build_guide_2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tvOk2"
            app:layout_constraintStart_toEndOf="@id/ivIc2"
            app:layout_constraintTop_toTopOf="parent" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvOk2"
            style="@style/MetaTextView.S14.PoppinsSemiBold600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_12"
            android:background="@drawable/bg_1a1a1a_round"
            android:gravity="center"
            android:minWidth="@dimen/dp_72"
            android:minHeight="@dimen/dp_32"
            android:text="@string/ok"
            android:textColor="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>