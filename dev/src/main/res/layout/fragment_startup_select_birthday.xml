<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_0E0922"
    android:fitsSystemWindows="true">

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/tbl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:back_icon_tint="@color/white"
        app:background_color="@color/transparent"
        app:isDividerVisible="false"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_avatar"
        android:layout_width="@dimen/dp_74"
        android:layout_height="@dimen/dp_74"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tbl"
        app:shapeAppearance="@style/circleStyle" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_title"
        style="@style/MetaTextView.S18.PoppinsExtraBold800"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_12"
        android:gravity="center"
        android:text="@string/intl_how_old_are_you"
        android:textColor="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_avatar"
        app:layout_goneMarginTop="@dimen/dp_39" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_desc"
        style="@style/MetaTextView.S14"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_40"
        android:layout_marginTop="@dimen/dp_8"
        android:gravity="center"
        android:text="@string/intl_how_old_are_you_tips"
        android:textColor="@color/white_45"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        app:layout_constraintWidth_max="@dimen/dp_294" />

    <com.airbnb.epoxy.EpoxyRecyclerView
        android:id="@+id/rv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_30"
        android:layout_marginTop="@dimen/dp_16"
        android:overScrollMode="never"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_desc"
        app:layout_constraintWidth_max="@dimen/dp_316" />

    <View
        android:id="@+id/v_cover"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@id/rv"
        app:layout_constraintEnd_toEndOf="@id/rv"
        app:layout_constraintStart_toStartOf="@id/rv"
        app:layout_constraintTop_toTopOf="@id/rv" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_next_btn"
        style="@style/Button.S18.PoppinsBlack900.Height48"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_45"
        android:layout_marginTop="@dimen/dp_32"
        android:text="@string/intl_go_next"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/rv"
        app:layout_constraintWidth_max="@dimen/dp_285"
        app:layout_goneMarginTop="@dimen/dp_40" />

</androidx.constraintlayout.widget.ConstraintLayout>