<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#F6EFFF"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:minHeight="@dimen/dp_48">

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvDesc"
        style="@style/MetaTextView.S12.PoppinsRegular400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginEnd="@dimen/dp_8"
        android:ellipsize="end"
        android:maxLines="2"
        android:text="@string/home_feedback_desc"
        android:textColor="#B884FF"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tvEnter"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvEnter"
        style="@style/MetaTextView.S12.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_16"
        android:background="@drawable/placeholder_corner_16"
        android:backgroundTint="#B884FF"
        android:gravity="center"
        android:minHeight="@dimen/dp_28"
        android:textColor="#fff"
        android:paddingHorizontal="@dimen/dp_16"
        android:text="@string/let_us_know"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>