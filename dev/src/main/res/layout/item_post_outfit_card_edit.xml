<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_66"
    android:background="@drawable/bg_f6f6f6_round_16"
    android:orientation="horizontal"
    android:padding="@dimen/dp_8">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_outfit_thumbnail"
        android:layout_width="@dimen/dp_50"
        android:layout_height="@dimen/dp_50"
        android:background="@color/white"
        android:scaleType="fitCenter"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/round_corner_8dp" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_outfit_desc"
        style="@style/MetaTextView.S14"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_8"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:lineSpacingMultiplier="1.2"
        android:maxLines="2"
        android:minHeight="@dimen/dp_22"
        android:text="@string/outfit_preview"
        android:textSize="@dimen/dp_14"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tv_outfit_edit"
        app:layout_constraintStart_toEndOf="@id/iv_outfit_thumbnail"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginEnd="@dimen/dp_4"
        app:uiLineHeight="@dimen/dp_22" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_outfit_edit"
        style="@style/MetaTextView.S12.PoppinsRegular400.secondary"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:drawableEnd="@drawable/ic_tri_right_arrow_s_20_c_999999"
        android:gravity="center_vertical"
        android:minHeight="@dimen/dp_18"
        android:paddingHorizontal="@dimen/dp_8"
        android:text="@string/edit"
        android:textSize="@dimen/dp_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:uiLineHeight="@dimen/dp_18" />

</androidx.constraintlayout.widget.ConstraintLayout>