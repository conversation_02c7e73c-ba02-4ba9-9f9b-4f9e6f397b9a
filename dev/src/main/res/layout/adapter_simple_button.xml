<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/btnCancel"
        style="@style/Button.S16.PoppinsMedium500.CancelPrimary"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:gravity="center"
        android:paddingVertical="@dimen/dp_12"
        android:text="@string/dialog_cancel"
        android:textColor="#1A1A1A"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>