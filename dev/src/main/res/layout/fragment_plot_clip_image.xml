<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_0F0F0F">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/sbphvCrop"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/color_0F0F0F"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/titleCrop"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_48"
        app:back_icon_tint="@color/white"
        app:background_color="@color/color_0F0F0F"
        app:isDividerVisible="false"
        app:title_text="@string/crop_background"
        app:layout_constraintTop_toBottomOf="@id/sbphvCrop"
        app:title="@string/crop_background"
        app:title_text_color="@color/white" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvConfirm"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_16"
        style="@style/Button.S12.PoppinsBold600"
        android:gravity="center"
        android:minWidth="@dimen/dp_62"
        android:minHeight="@dimen/dp_28"
        android:text="@string/dialog_confirm"
        android:textSize="@dimen/sp_12"
        app:layout_constraintBottom_toBottomOf="@id/titleCrop"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/titleCrop" />

    <com.socialplay.gpark.ui.view.crop.ImageCropView
        android:id="@+id/ivCrop"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/color_252525"
        app:bgColor="@color/color_252525"
        app:enableDoubleClick="false"
        app:enableZoom="false"
        android:layout_marginVertical="@dimen/dp_8"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titleCrop"
        app:cropBoxHorPadding="@dimen/dp_16"
        app:maskColor="@color/black_60" />

    <View
        android:id="@+id/vMask"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:clickable="true"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titleCrop" />

    <LinearLayout
        android:id="@+id/loadingReview"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_black_80_s8"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/dp_12"
        android:paddingVertical="@dimen/dp_8"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible">

        <ProgressBar
            android:id="@+id/pbReview"
            android:layout_width="@dimen/dp_24"
            android:layout_height="@dimen/dp_24"
            android:layout_marginEnd="@dimen/dp_2"
            android:indeterminateDrawable="@drawable/animated_white_loading_progress" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:layout_width="wrap_content"
            style="@style/MetaTextView.S14.PoppinsMedium500"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:gravity="center_vertical"
            android:text="@string/loading"
            android:textColor="@color/white" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>