<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="#B3000000">

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scrollView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_dialog_white_corner_32_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/ll"
            android:layout_width="327dp"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="@string/iap_title_insufficient_balance"
                android:layout_marginTop="21dp"
                android:textColor="#1A1A1A"
                android:textSize="16sp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="24dp"
                android:layout_marginTop="12dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvGoodsPrice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="@string/iap_goods_price"
                    android:textColor="#999999"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tvNeedPay"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:drawableRight="@drawable/icon_coin"
                    android:drawablePadding="8dp"
                    android:gravity="center"
                    android:textColor="#1a1a1a"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    tools:text="99" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="24dp"
                android:layout_marginTop="15dp"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="@string/iap_pg_need_additional"
                    android:textColor="#999999"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tvNeedAdditional"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:drawableRight="@drawable/icon_coin"
                    android:drawablePadding="8dp"
                    android:gravity="center"
                    android:textColor="#1a1a1a"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    tools:text="99" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                android:layout_marginTop="15dp"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/iap_buy_coins"
                    android:textColor="#1A1A1A"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/memberFlagTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:text="@string/iap_premium_offer"
                    android:textColor="#7A19FF"
                    android:textSize="14sp"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </LinearLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="52dp"
                android:layout_marginHorizontal="24dp"
                android:layout_marginTop="12dp"
                android:background="@drawable/bg_f6f6f6_round_8"
                android:paddingHorizontal="16dp">

                <ImageView
                    android:id="@+id/coinIcon"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:scaleType="centerCrop"
                    android:src="@drawable/icon_coin"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/coinAmountTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginStart="@dimen/dp_8"
                    android:textColor="#1A1A1A"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toTopOf="@id/coinAwardTv"
                    app:layout_constraintStart_toEndOf="@id/coinIcon"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_chainStyle="packed"
                    tools:text="99" />

                <TextView
                    android:id="@+id/coinAwardTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_weight="1"
                    android:textColor="#7A19FF"
                    android:textSize="12sp"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="@id/coinAmountTv"
                    app:layout_constraintTop_toBottomOf="@id/coinAmountTv"
                    tools:text="801+Bonus 99"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/payPriceTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="#1A1A1A"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="$99" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginHorizontal="24dp"
                android:minHeight="48dp">

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tvPayDescSandbox"
                    style="@style/MetaTextView.S10.PoppinsRegular400.CenterVertical14"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_12"
                    android:text="@string/pay_sand_box_desc"
                    android:textColor="@color/color_666666"
                    android:textSize="@dimen/sp_10"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:visibility="gone" />

                <TextView
                    android:id="@+id/tvConfirm"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:layout_gravity="center"
                    android:layout_marginTop="15dp"
                    android:background="@drawable/iap_shape_pay_confirm"
                    android:gravity="center"
                    android:padding="10dp"
                    android:textColor="#1A1A1A"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvPayDescSandbox"
                    app:layout_goneMarginTop="15dp"
                    tools:text="Pay $99" />

                <TextView
                    android:id="@+id/tvConfirmSandbox"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginBottom="-10dp"
                    android:background="@drawable/pay_sandbox_title_bg"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/dp_4"
                    android:paddingBottom="@dimen/dp_4"
                    android:text="@string/pay_sand_box_title"
                    android:textColor="@color/white"
                    android:textSize="11sp"
                    android:visibility="gone"
                    app:layout_constraintBottom_toTopOf="@id/tvConfirm"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.7"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/tvConfirm"
                    tools:visibility="visible" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <LinearLayout
                android:id="@+id/rechargePrivacyLayout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="14dp"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:text="@string/iap_recharge_protocol_accept"
                    android:textColor="#99000000"
                    android:textSize="11sp" />

                <TextView
                    android:id="@+id/rechargePrivacy"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:text="@string/iap_recharge_protocol_clickable_text"
                    android:textColor="#0083FA"
                    android:textSize="11sp" />
            </LinearLayout>

            <Space
                android:layout_width="match_parent"
                android:layout_height="15dp" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <ImageView
        android:id="@+id/ivCancel"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_gravity="end"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="10dp"
        android:src="@drawable/close_icon"
        app:layout_constraintTop_toTopOf="@id/scrollView"
        app:layout_constraintEnd_toEndOf="@id/scrollView"/>

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/loading"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/scrollView"
        app:layout_constraintLeft_toLeftOf="@id/scrollView"
        app:layout_constraintRight_toRightOf="@id/scrollView"
        app:layout_constraintTop_toTopOf="@id/scrollView" />

    <FrameLayout
        android:id="@+id/rechargeLoading"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/transparent"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/scrollView"
        app:layout_constraintLeft_toLeftOf="@id/scrollView"
        app:layout_constraintRight_toRightOf="@id/scrollView"
        app:layout_constraintTop_toTopOf="@id/scrollView">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="38dp"
            android:layout_gravity="center"
            android:background="@drawable/bg_80000000_round_19"
            android:orientation="horizontal"
            android:paddingHorizontal="16dp">

            <ImageView
                android:id="@+id/rechargeLoadingIv"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_gravity="center_vertical"
                android:src="@drawable/icon_recharge_loading" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="10dp"
                android:text="@string/iap_recharge_loading"
                android:textColor="@color/white"
                android:textSize="15sp" />
        </LinearLayout>
    </FrameLayout>


</androidx.constraintlayout.widget.ConstraintLayout>