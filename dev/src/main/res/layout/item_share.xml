<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cl_share"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_share"
        style="@style/MetaTextView.S12"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawableTop="@drawable/ic_share_friend"
        android:drawablePadding="@dimen/dp_4"
        android:gravity="center"
        android:text="@string/contacts"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>