<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal"
    tools:background="@color/black_40">

    <View
        android:id="@+id/viewClickBack"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_player_card"
        android:layout_width="362dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/bg_white_round_32"
        android:paddingBottom="@dimen/dp_20">

        <LinearLayout
            android:id="@+id/ll_report"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_20"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/icon_report" />

            <com.socialplay.gpark.ui.view.MetaTextView
                style="@style/MetaTextView.S12.PoppinsRegular400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_5"
                android:text="@string/report" />

        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/rl_ugc_user"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_8"
            android:layout_marginRight="@dimen/dp_60"
            android:layout_marginBottom="@dimen/dp_15"
            app:layout_constraintEnd_toStartOf="@+id/iv_close_dialog"
            app:layout_constraintTop_toBottomOf="@+id/ll_report">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_player_head"
                android:layout_width="@dimen/dp_48"
                android:layout_height="@dimen/dp_48"
                android:layout_centerVertical="true"
                android:src="@drawable/placeholder_corner_360"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:shapeAppearance="@style/circleStyle" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tv_user_name"
                style="@style/MetaTextView.S14.PoppinsSemiBold600"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_8"
                android:lineHeight="@dimen/dp_24"
                android:lines="1"
                android:ellipsize="end"
                android:textColor="@color/color_1A1A1A"
                app:layout_constraintBottom_toTopOf="@id/tv_user_number"
                app:layout_constraintEnd_toStartOf="@id/label_group"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toEndOf="@id/iv_player_head"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_chainStyle="packed"
                app:layout_constraintWidth_default="wrap"
                tools:text="nikaNamenikaNameikaNe" />

            <com.socialplay.gpark.ui.view.UserLabelView
                android:id="@+id/label_group"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="@id/tv_user_name"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tv_user_name"
                app:layout_constraintTop_toTopOf="@id/tv_user_name" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tv_user_number"
                style="@style/MetaTextView.S12.PoppinsRegular400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_8"
                android:layout_marginTop="@dimen/dp_2"
                android:lineHeight="@dimen/dp_20"
                android:textColor="@color/color_B3B3B3"
                android:textSize="@dimen/sp_12"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/iv_player_head"
                app:layout_constraintTop_toBottomOf="@id/tv_user_name"
                tools:text="ID : 7489279875" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <ImageView
            android:id="@+id/iv_close_dialog"
            android:layout_width="@dimen/dp_28"
            android:layout_height="@dimen/dp_28"
            android:layout_marginRight="@dimen/dp_20"
            android:src="@drawable/icon_mgs_dialog_close"
            app:layout_constraintBottom_toBottomOf="@id/ll_report"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/ll_report" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/v_building"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_15"
            android:layout_marginEnd="@dimen/dp_20"
            android:background="@drawable/bg_f0f0f0_corner_16"
            android:paddingLeft="@dimen/dp_10"
            android:paddingTop="@dimen/dp_8"
            android:paddingBottom="@dimen/dp_10"
            android:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/rl_ugc_user">

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvPlayerBuilding"
                style="@style/MetaTextView.S10.PoppinsSemiBold600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/construction_cap"
                android:textColor="@color/color_333333"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvPlayerBuilding"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_81"
                android:layout_marginTop="@dimen/dp_6"
                android:overScrollMode="never"
                app:layout_constraintStart_toStartOf="@id/tvPlayerBuilding"
                app:layout_constraintTop_toBottomOf="@id/tvPlayerBuilding" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:id="@+id/ll_friend_button"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_51"
            android:layout_marginLeft="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_15"
            android:layout_marginRight="@dimen/dp_20"
            android:background="@drawable/bg_button_normal_36"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintTop_toBottomOf="@+id/v_building">

            <ImageView
                android:id="@+id/img_add"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/icon_mgs_add_friend" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tv_desc"
                style="@style/MetaTextView.S14.PoppinsBold700"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:gravity="center"
                android:text="@string/add"
                android:textColor="@color/color_1A1A1A"
                android:textSize="@dimen/sp_14"
                android:textStyle="bold" />
        </LinearLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>


</RelativeLayout>