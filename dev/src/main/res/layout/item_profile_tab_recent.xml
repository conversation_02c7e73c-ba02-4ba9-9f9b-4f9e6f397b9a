<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/recent_game_item_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_white_15"
    android:paddingHorizontal="@dimen/dp_16"
    android:paddingTop="@dimen/dp_12"
    android:paddingBottom="@dimen/dp_12">

    <ImageView
        android:id="@+id/iv_icon"
        android:layout_width="@dimen/dp_60"
        android:layout_height="@dimen/dp_60"

        android:src="@drawable/placeholder_corner_14"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_game_name"
        style="@style/MetaTextView.S14.PoppinsBold700"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_68"
        android:ellipsize="end"
        android:singleLine="true"
        app:layout_constraintBottom_toTopOf="@id/tv_play_time"
        app:layout_constraintTop_toTopOf="@id/iv_icon"
        app:layout_constraintVertical_chainStyle="packed"
        app:uiLineHeight="@dimen/dp_16"
        tools:text="Title of map" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_play_time"
        style="@style/MetaTextView.S12.PoppinsRegular400.secondary"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_68"
        android:layout_marginTop="@dimen/dp_6"
        android:drawableStart="@drawable/icon_time"
        android:drawablePadding="4dp"
        android:ellipsize="end"
        android:gravity="center_vertical|start"
        android:singleLine="true"
        android:textColor="@color/neutral_color_4"
        app:drawableTint="@color/neutral_color_5"
        app:layout_constraintBottom_toBottomOf="@id/iv_icon"
        app:layout_constraintTop_toBottomOf="@id/tv_game_name"
        app:uiLineHeight="@dimen/dp_20"
        tools:text="999 yrs" />

</androidx.constraintlayout.widget.ConstraintLayout>