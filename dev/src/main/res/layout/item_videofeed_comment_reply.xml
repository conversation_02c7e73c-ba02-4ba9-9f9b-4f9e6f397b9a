<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_reply"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/siv_user_avatar"
        android:layout_width="@dimen/dp_34"
        android:layout_height="@dimen/dp_34"
        android:layout_centerVertical="true"
        android:layout_marginLeft="@dimen/dp_56"
        android:padding="0.5dp"
        android:scaleType="centerCrop"
        android:src="@drawable/icon_default_avatar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/circleStyle" />

    <TextView
        android:id="@+id/tv_username"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        app:uiLineHeight="@dimen/sp_18"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginEnd="@dimen/dp_16"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        style="@style/MetaTextView.S12.PoppinsMedium500"
        android:textColor="@color/neutral_color_5"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toEndOf="@id/siv_user_avatar"
        app:layout_constraintTop_toTopOf="@id/siv_user_avatar"
        tools:text="Author" />

    <TextView
        android:id="@+id/tv_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_12"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:background="@drawable/bg_comment_user_label_self"
        android:maxLines="1"
        tools:text="Author"
        android:visibility="gone"
        style="@style/MetaTextView.S10.PoppinsRegular400"
        tools:visibility="visible"
        android:paddingVertical="@dimen/dp_1"
        android:paddingHorizontal="@dimen/dp_6"
        android:textColor="@color/neutral_color_5"
        android:textSize="@dimen/sp_10"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@id/tv_username"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@id/tv_username"
        app:layout_constraintTop_toTopOf="@id/tv_username" />



    <com.socialplay.gpark.ui.view.ExpandableTextView
        android:id="@+id/tv_content"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_24"
        android:includeFontPadding="false"
        android:paddingBottom="@dimen/dp_4"
        app:etv_InitState="shrink"
        app:etv_MaxLinesOnShrink="4"
        app:etv_ToExpandHint="@string/more_with_space"
        app:etv_ToShrinkHint="@string/collapse_with_space"
        app:etv_ToExpandHintOffset="@dimen/dp_100"
        app:etv_EnableToggleClick="true"
        app:etv_ToExpandHintColor="@color/neutral_color_5"
        app:etv_ToShrinkHintColor="@color/neutral_color_5"
        app:etv_ToExpandHintColorBgPressed="@color/transparent"
        app:etv_ToExpandHintShow="true"
        android:textColor="@color/neutral_color_2"
        style="@style/MetaTextView.S13.PoppinsRegular400"
        app:etv_ToShrinkHintColorBgPressed="@color/transparent"
        app:etv_ToShrinkHintShow="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_username"
        app:layout_constraintTop_toBottomOf="@id/tv_username"
        tools:maxLength="500"
        tools:text="评论内容评论内容评论内容评论内容评论内容评论内容评论内容评论内容评论内容评论内容评论内容评论内容" />


    <TextView
        android:id="@+id/tv_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:textColor="@color/neutral_color_5"
        android:layout_marginTop="@dimen/dp_8"
        android:gravity="center"
        style="@style/MetaTextView.S12.PoppinsRegular400"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_content"
        app:layout_constraintStart_toStartOf="@id/tv_username"
        app:layout_goneMarginStart="0dp"
        tools:text="10分钟" />

    <ImageView
        android:id="@+id/iv_more"
        android:layout_width="wrap_content"
        android:layout_marginEnd="@dimen/dp_24"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_review_more"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <ImageView
        android:id="@+id/iv_like"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_ugc_comment_like_selected"
        android:paddingRight="@dimen/dp_2"
        app:layout_constraintEnd_toStartOf="@id/tv_like_count"
        app:layout_constraintTop_toTopOf="@id/tv_time"
        app:layout_constraintBottom_toBottomOf="@id/tv_time"
        app:layout_goneMarginStart="0dp" />



    <TextView
        android:id="@+id/tv_like_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/dp_24"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/zero_int"
        android:textColor="@color/neutral_color_5"
        android:textSize="@dimen/sp_11"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/iv_like"
        app:layout_constraintTop_toTopOf="@id/iv_like" />

</androidx.constraintlayout.widget.ConstraintLayout>