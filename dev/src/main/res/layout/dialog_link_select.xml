<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:fitsSystemWindows="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="bottom"
        android:background="@drawable/bg_white_top_round_24">

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvTitle"
            style="@style/MetaTextView.S15.PoppinsMedium600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_16"
            android:text="@string/profile_link_select"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/ivCloseBtn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_4"
            android:padding="@dimen/dp_12"
            android:src="@drawable/ic_close_select_link"
            app:layout_constraintBottom_toBottomOf="@id/tvTitle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tvTitle" />

        <com.airbnb.epoxy.EpoxyRecyclerView
            android:id="@+id/rv"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_320"
            android:layout_marginTop="@dimen/dp_12"
            android:fadeScrollbars="false"
            android:orientation="vertical"
            android:overScrollMode="ifContentScrolls"
            android:scrollbarSize="@dimen/dp_2"
            android:scrollbarStyle="insideInset"
            android:scrollbarThumbVertical="@drawable/shape_d9d9d9_round"
            android:scrollbars="vertical"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintTop_toBottomOf="@id/tvTitle"
            tools:itemCount="4"
            tools:listitem="@layout/item_select_link" />

        <Space
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_4"
            app:layout_constraintTop_toBottomOf="@id/rv" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>