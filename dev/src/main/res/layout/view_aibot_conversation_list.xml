<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="@dimen/dp_20"
  >

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_16"
        android:fontFamily="@font/poppins_semi_bold_600"
        android:text="@string/ai_bot_conversation_list_tile"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_16"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.airbnb.epoxy.EpoxyRecyclerView
        android:id="@+id/ry_ai_bot"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_6"
        android:layout_marginTop="@dimen/dp_16"
        android:layout_marginRight="@dimen/dp_6"
        android:orientation="horizontal"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        tools:listitem="item_ai_bot_conversation" />

</androidx.constraintlayout.widget.ConstraintLayout>