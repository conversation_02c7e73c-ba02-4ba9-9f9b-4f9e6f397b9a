<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="16dp">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/img_icon"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginTop="8dp"
        android:background="@drawable/placeholder_corner_360"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_time"
        app:shapeAppearance="@style/circleStyle" />

    <TextView
        android:id="@+id/tv_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="#BDBDBD"
        android:textSize="@dimen/sp_11"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="2023-1-23" />

    <LinearLayout
        android:id="@+id/llContent"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="8dp"
        android:layout_marginRight="41dp"
        android:background="@drawable/placeholder_corner_8"
        android:backgroundTint="#fff"
        android:orientation="vertical"
        android:paddingBottom="12dp"
        app:layout_constraintLeft_toRightOf="@id/img_icon"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/img_icon">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/ivContentImage"
            android:layout_width="match_parent"
            android:layout_height="123dp"
            android:background="#eee"
            app:shapeAppearance="@style/round_corner_top_8" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_12"
            android:layout_marginTop="12dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="#222222"
            android:textSize="@dimen/sp_14"
            android:textStyle="bold"
            tools:text="互动消息互动消息互互" />

        <com.socialplay.gpark.ui.view.FolderTextView
            android:id="@+id/tv_message"
            style="@style/MetaTextView.S12.PoppinsRegular400"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_8"
            android:textColor="#888888"
            android:textSize="@dimen/sp_12"
            app:canFoldAgain="true"
            app:foldLine="4"
            app:tailTextColor="#888888"
            app:unFoldText="..." />

        <TextView
            android:id="@+id/tv_jump"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="12dp"
            android:layout_marginTop="8dp"
            android:text="@string/view_more_cap"
            android:textColor="#4AB4FF"
            android:textSize="12sp" />

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>