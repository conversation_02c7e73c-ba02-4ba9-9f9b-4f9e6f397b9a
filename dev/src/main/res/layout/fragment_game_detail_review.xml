<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/statusBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/titleBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/statusBar" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvRatingsReviews"
        style="@style/MetaTextView.S18.PoppinsExtraBold800.LeftTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:text="@string/rating_cap"
        android:textSize="@dimen/sp_18"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titleBar" />


    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvRatingNum"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_5"
        android:textColor="@color/color_999999"
        android:textSize="@dimen/sp_14"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tvRatingsReviews"
        app:layout_constraintLeft_toRightOf="@id/tvRatingsReviews"
        app:layout_constraintTop_toTopOf="@id/tvRatingsReviews" />

    <LinearLayout
        android:id="@+id/tvSeeAll"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_27"
        android:layout_marginHorizontal="16dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:visibility="invisible"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible">

        <com.socialplay.gpark.ui.view.MetaTextView
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_21"
            android:layout_marginEnd="@dimen/dp_6"
            android:text="@string/see_all"
            android:textColor="@color/color_999999"
            android:textSize="@dimen/sp_14"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:layout_width="@dimen/dp_6"
            android:layout_height="@dimen/dp_21"
            android:scaleType="fitCenter"
            android:src="@drawable/icon_arrow_right_999999"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </LinearLayout>

    <include
        android:id="@+id/includeRating"
        layout="@layout/layout_game_review_rating"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_105"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="@dimen/dp_10"
        app:layout_constraintTop_toBottomOf="@+id/tvRatingsReviews" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clEdit"
        android:layout_width="match_parent"
        android:layout_height="94dp"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="@dimen/dp_12"
        android:background="@drawable/game_detail_shape_grey_corner_16_bg"
        app:layout_constraintTop_toBottomOf="@id/includeRating">

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvEdit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_22"
            android:text="@string/tap_to_rate"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/clEdit" />

        <com.socialplay.gpark.ui.view.RatingView
            android:id="@+id/ratingbar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_17"
            android:layout_marginTop="@dimen/dp_8"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvEdit"
            app:rating="0"
            app:ratingCount="5"
            android:visibility="visible"
            tools:visibility="visible"
            app:ratingEmpty="@drawable/ic_empty_start_4ab4ff"
            app:ratingFilled="@drawable/ic_empty_start_4ab4ff"
            app:ratingMargin="@dimen/dp_8"
            app:ratingSize="@dimen/dp_24" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvGameReviews"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:orientation="vertical"
        android:overScrollMode="never"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintTop_toBottomOf="@id/clEdit"
        tools:itemCount="1"
        tools:listitem="@layout/item_game_review_all" />

    <LinearLayout
        android:id="@+id/tvAllGameReviews"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_46"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="@dimen/dp_20"
        android:layout_marginBottom="@dimen/dp_22"
        android:gravity="center_horizontal"
        android:orientation="horizontal"
        android:visibility="invisible"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/rvGameReviews"
        app:layout_goneMarginTop="@dimen/dp_2"
        tools:visibility="visible">

        <com.socialplay.gpark.ui.view.MetaTextView
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_21"
            android:layout_marginEnd="@dimen/dp_6"
            android:text="@string/see_all_ratings"
            android:textColor="@color/color_999999"
            android:textSize="@dimen/sp_14"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:layout_width="@dimen/dp_6"
            android:layout_height="@dimen/dp_21"
            android:scaleType="fitCenter"
            android:src="@drawable/icon_arrow_right_999999"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </LinearLayout>

    <Space
        android:id="@+id/space_empty"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_16"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/tvAllGameReviews" />

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/loading"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />
</androidx.constraintlayout.widget.ConstraintLayout>