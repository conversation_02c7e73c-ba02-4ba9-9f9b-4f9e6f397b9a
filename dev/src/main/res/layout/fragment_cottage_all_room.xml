<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:background="@color/white"
    android:layout_height="match_parent">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/statusBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:isDividerVisible="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/statusBar"
        app:title_text="@string/all_room" />

    <com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout
        android:id="@+id/refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:paddingTop="@dimen/dp_16"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title">

        <com.airbnb.epoxy.EpoxyRecyclerView
            android:id="@+id/recyclerView"
            android:clipChildren="false"
            android:layout_marginLeft="@dimen/dp_8"
            android:layout_marginRight="@dimen/dp_8"
            android:paddingBottom="@dimen/dp_88"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout>
    <com.lihang.ShadowLayout
        android:id="@+id/sl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_31"
        android:layout_marginRight="@dimen/dp_31"
        app:hl_shadowColor="@color/black"
        app:hl_shadowLimit="@dimen/dp_27"
        app:hl_shadowSymmetry="false"
        app:hl_cornerRadius="@dimen/dp_30"
        app:hl_shadowOffsetY="8dp"
        app:hl_shadowOffsetX="@dimen/dp_4"
        android:focusable="false"
        app:clickable="false"
        android:clickable="false"

        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        >
    <LinearLayout
        android:id="@+id/ll_home_entry"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_48"

        android:background="@drawable/bg_white_round_30"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <com.socialplay.gpark.ui.view.MetaTextView
            style="@style/MetaTextView.S16.PoppinsSemiBold600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableLeft="@drawable/icon_cottage_me"
            android:drawablePadding="@dimen/dp_8"
            android:gravity="center"
            android:text="@string/my_home_party" />

    </LinearLayout>
    </com.lihang.ShadowLayout>

</androidx.constraintlayout.widget.ConstraintLayout>