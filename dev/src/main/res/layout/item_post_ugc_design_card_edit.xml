<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_66"
    android:background="@drawable/bg_f6f6f6_round_16"
    android:orientation="horizontal"
    android:padding="@dimen/dp_8">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_outfit_thumbnail"
        android:layout_width="@dimen/dp_50"
        android:layout_height="@dimen/dp_50"
        android:background="@color/white"
        android:scaleType="fitCenter"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/round_corner_8dp" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_outfit_desc"
        style="@style/MetaTextView.S14"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginEnd="@dimen/dp_4"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:lineSpacingMultiplier="1.2"
        android:maxLines="2"
        android:minHeight="@dimen/dp_22"
        android:text="@string/outfit_preview"
        android:textSize="@dimen/dp_14"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_outfit_thumbnail"
        app:layout_constraintTop_toTopOf="parent"
        app:uiLineHeight="@dimen/dp_22" />

</androidx.constraintlayout.widget.ConstraintLayout>