<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/sbphv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/title_bar_height"
        android:paddingHorizontal="@dimen/dp_16"
        android:src="@drawable/ic_publish_post_close"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/sbphv" />

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/vp"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tab_layout" />

    <com.socialplay.gpark.ui.view.MinWidthTabLayout
        android:id="@+id/tab_layout"
        android:layout_width="0dp"
        android:layout_height="@dimen/title_bar_height"
        android:layout_marginEnd="@dimen/dp_56"
        android:background="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_close"
        app:layout_constraintTop_toBottomOf="@id/sbphv"
        app:tabGravity="center"
        app:tabIndicator="@drawable/indicator_create_v3"
        app:tabIndicatorColor="@color/black"
        app:tabIndicatorFullWidth="false"
        app:tabIndicatorGravity="bottom"
        app:tabIndicatorHeight="@dimen/dp_4"
        app:tabMode="scrollable"
        app:tabPadding="0dp"
        app:tabRippleColor="@color/transparent" />

</androidx.constraintlayout.widget.ConstraintLayout>