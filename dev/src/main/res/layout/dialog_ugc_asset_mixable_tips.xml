<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clContainer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <View
            android:id="@+id/vMixableTipsBg"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/ic_asset_mixable_bg"
            app:layout_constraintBottom_toBottomOf="@id/tvMixableTips"
            app:layout_constraintEnd_toEndOf="@id/tvMixableTips"
            app:layout_constraintStart_toStartOf="@id/tvMixableTips"
            app:layout_constraintTop_toTopOf="@id/tvMixableTips" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvMixableTips"
            style="@style/MetaTextView.S10"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingHorizontal="@dimen/dp_6"
            android:paddingTop="@dimen/dp_3"
            android:paddingBottom="@dimen/dp_8"
            android:text="@string/ugc_asset_remix_allowed_tips"
            android:textColor="@color/white"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_max="@dimen/dp_164" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>