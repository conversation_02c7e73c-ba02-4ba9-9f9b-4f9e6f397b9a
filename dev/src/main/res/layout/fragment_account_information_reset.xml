<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:fitsSystemWindows="true">

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/tbl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:back_icon="@drawable/icon_back_array_bold_black"
        app:background_color="@color/transparent"
        app:isDividerVisible="false"
        app:layout_constraintTop_toTopOf="parent"
        app:title_text="@string/text_information"
        app:title_text_color="@color/black"
        tools:layout_height="@dimen/dp_48" />

    <RelativeLayout
        android:id="@+id/rlInformation"
        android:layout_width="match_parent"
        android:layout_height="125dp"
        android:layout_marginHorizontal="@dimen/dp_24"
        android:layout_marginTop="@dimen/dp_28"
        android:background="@drawable/bg_game_review_edit"
        android:paddingVertical="12dp"
        app:layout_constraintTop_toBottomOf="@id/tbl">

        <EditText
            android:id="@+id/etInformation"
            style="@style/EditText"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@id/tv_intro_count"
            android:layout_alignParentTop="true"
            android:layout_centerHorizontal="true"
            android:background="@null"
            android:hint="@string/profile_infomation_hint_text"
            android:importantForAutofill="noExcludeDescendants"
            android:inputType="textMultiLine|textNoSuggestions"
            android:maxLength="100"
            android:paddingHorizontal="16dp" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_intro_count"
            style="@style/MetaTextView.S13.PoppinsRegular400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_alignParentBottom="true"
            android:paddingHorizontal="12dp"
            android:textColor="@color/neutral_color_5" />
    </RelativeLayout>

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_limit_tips"
        style="@style/MetaTextView.S12.PoppinsRegular400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_6"
        android:fontFamily="@font/poppins_regular_400"
        android:text="@string/profile_infomation_more_than_100_characters"
        android:textColor="@color/homochromy_color_2"
        android:textSize="@dimen/sp_12"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/rlInformation"
        app:layout_constraintStart_toStartOf="@id/rlInformation"
        app:layout_constraintTop_toBottomOf="@id/rlInformation" />

    <TextView
        android:id="@+id/tvSure"
        style="@style/Button.S18.PoppinsBlack900.Height46"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_40"
        android:enabled="false"
        android:minHeight="@dimen/dp_48"
        android:text="@string/text_confirm_uppercase"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/rlInformation"
        app:layout_constraintStart_toStartOf="@id/rlInformation" />

</androidx.constraintlayout.widget.ConstraintLayout>