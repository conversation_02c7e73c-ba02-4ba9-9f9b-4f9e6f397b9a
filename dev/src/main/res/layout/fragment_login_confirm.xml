<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#17191c">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/sbphv_placeholder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/tbl_title_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/title_bar_height"
        app:background_color="@color/transparent"
        app:layout_constraintTop_toBottomOf="@id/sbphv_placeholder"
        app:title_text="@string/confirm_to_login_title" />

    <androidx.constraintlayout.widget.ConstraintLayout

        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_36"
        android:layout_marginTop="@dimen/dp_16"
        android:background="@drawable/bg_login_confirm"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tbl_title_bar">

        <com.google.android.material.imageview.ShapeableImageView
            app:shapeAppearance="@style/shapeRound16Style"
            android:id="@+id/iv_brand_icon"
            android:layout_width="@dimen/dp_83"
            android:layout_height="@dimen/dp_83"
            android:layout_marginTop="@dimen/dp_112"
            android:src="@drawable/placeholder_corner_16"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginVertical="@dimen/dp_14"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/white_66"
            android:textSize="@dimen/sp_16"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_brand_icon"
            tools:text="@string/login_to_p12" />

        <TextView
            android:id="@+id/tv_login"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:enabled="false"
            android:layout_marginHorizontal="@dimen/dp_28"
            android:layout_marginTop="@dimen/dp_138"
            android:background="@drawable/bg_confirm_login_login"
            android:gravity="center"
            android:minHeight="@dimen/dp_40"
            android:text="@string/confirm_to_login_login"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_16"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_tips" />

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp_28"
            android:layout_marginTop="@dimen/dp_15"
            android:gravity="center"
            android:text="@string/confirm_to_login_cancel"
            android:textColor="@color/white_30"
            android:textSize="@dimen/sp_16"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_login" />

        <Space
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_31"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_cancel" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>