<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="52dp"
    android:layout_height="52dp"
    android:layout_marginLeft="@dimen/dp_16"
    android:background="@drawable/shape_f5f5f5_corner_10">

    <ImageView
        android:id="@+id/img_logo"
        android:layout_width="@dimen/dp_32"
        android:layout_height="@dimen/dp_32"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>