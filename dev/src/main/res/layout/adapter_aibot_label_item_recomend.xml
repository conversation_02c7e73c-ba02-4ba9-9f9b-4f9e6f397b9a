<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="@dimen/dp_20"
    android:paddingEnd="@dimen/dp_4">

    <TextView
        android:id="@+id/tvTableName"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:background="@drawable/bg_white_20_corner_360"
        android:gravity="center"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:fontFamily="@font/poppins_regular_400"
        android:paddingHorizontal="@dimen/dp_8"
        android:textColor="@color/white"
        android:textSize="@dimen/dp_10"
        tools:text="全部" />


</FrameLayout>
