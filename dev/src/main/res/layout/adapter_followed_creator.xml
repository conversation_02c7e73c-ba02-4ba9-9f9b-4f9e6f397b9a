<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginEnd="@dimen/dp_10"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_width="@dimen/dp_64"
        tools:src="@drawable/home_friends_add" />


    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_online"
        android:layout_width="@dimen/dp_14"
        android:layout_height="@dimen/dp_14"
        android:src="@color/color_18CC35"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/iv"
        app:layout_constraintEnd_toEndOf="@id/iv"
        app:shapeAppearance="@style/circleStyle"
        app:strokeColor="@color/white"
        app:strokeWidth="@dimen/dp_2"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_user_name"
        style="@style/MetaTextView.S12.PoppinsRegular400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_4"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:singleLine="true"
        android:textColor="@color/neutral_color_1"
        app:layout_constraintEnd_toEndOf="@id/iv"
        app:layout_constraintStart_toStartOf="@id/iv"
        app:layout_constraintTop_toBottomOf="@id/iv"
        tools:text="User NameName" />

</androidx.constraintlayout.widget.ConstraintLayout>