<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <!-- 状态栏占位 -->
    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/statusBarPlaceholder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <!-- 标题栏 -->
    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/titleBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:isDividerVisible="false"
        app:title_text="模板列表"
        app:title_text_color="@color/textColorPrimary" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <!-- 模板封面图片 -->
            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_211">

                <com.socialplay.gpark.ui.view.RoundImageView
                    android:id="@+id/ivTemplateCover"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="centerCrop"
                    tools:src="@color/default_shadow_color" />

                <!-- 复用现有的模板标签 -->
                <LinearLayout
                    android:id="@+id/layoutTemplateTag"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_home_template_item_inner"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingRight="@dimen/dp_4">

                    <TextView
                        android:id="@+id/tvTemplate"
                        style="@style/MetaTextView.S12_TBMC"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/dp_6"
                        android:gravity="center"
                        android:text="@string/game_template"
                        android:textColor="@color/white"
                        android:textSize="11sp"
                        tools:text="模板作品优秀游戏Template" />

                    <ImageView
                        android:id="@+id/ivStar1"
                        android:layout_width="@dimen/dp_11"
                        android:layout_height="@dimen/dp_11"
                        android:layout_marginStart="@dimen/dp_3"
                        android:src="@drawable/ic_star_white" />

                    <ImageView
                        android:id="@+id/ivStar2"
                        android:layout_width="@dimen/dp_11"
                        android:layout_height="@dimen/dp_11"
                        android:layout_marginStart="@dimen/dp_2"
                        android:src="@drawable/ic_star_white" />

                    <ImageView
                        android:id="@+id/ivStar3"
                        android:layout_width="@dimen/dp_11"
                        android:layout_height="@dimen/dp_11"
                        android:layout_marginStart="@dimen/dp_2"
                        android:alpha="0.3"
                        android:src="@drawable/ic_star_white" />
                </LinearLayout>

                <!-- 底部点赞和播放量 -->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom|start"
                    android:layout_marginStart="@dimen/dp_8"
                    android:layout_marginBottom="@dimen/dp_8"
                    android:orientation="horizontal">

                    <com.socialplay.gpark.ui.view.MetaLikeView
                        android:id="@+id/likeView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:likeText="22.23k" />

                    <com.socialplay.gpark.ui.view.MetaLikeView
                        android:id="@+id/playersView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_4"
                        android:visibility="gone"
                        app:likeText="22.23k played"
                        app:showIcon="false" />
                </LinearLayout>
            </FrameLayout>

            <!-- 模板标题、标签区域和Create按钮 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_16"
                android:layout_marginEnd="@dimen/dp_16"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <!-- 左侧：标题和标签的垂直组合 -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <!-- 模板标题 -->
                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tvTemplateTitle"
                        style="@style/MetaTextView.S16.PoppinsMedium500"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:textColor="@color/Gray_1000"
                        tools:text="Enchilada Casserole Template" />

                    <!-- 标签区域 -->
                    <com.socialplay.gpark.ui.view.TagContainerView
                        android:id="@+id/tagContainer"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp_4" />
                </LinearLayout>

                <!-- 右侧：创建按钮 -->
                <Button
                    android:id="@+id/btnCreate"
                    style="@style/Button.S12.PoppinsBold600"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dp_34"
                    android:text="@string/create_title" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_05"
                android:layout_marginStart="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_16"
                android:layout_marginEnd="@dimen/dp_16"
                android:background="@color/grey_400" />
            <!-- 说明文字 -->
            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvDescription"
                style="@style/MetaTextView.S14.PoppinsRegular400"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_16"
                android:layout_marginEnd="@dimen/dp_16"
                android:textColor="@color/Gray_900"
                tools:text="This is a wonderful template for creating amazing content..." />

            <!-- 优秀作品列表 -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvOutstandingWorks"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_16"
                android:clipToPadding="false"
                android:paddingStart="@dimen/dp_8"
                android:paddingEnd="@dimen/dp_8"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:layout_constraintTop_toBottomOf="@id/tvDescription"
                app:spanCount="2"
                tools:itemCount="4"
                tools:listitem="@layout/item_outstanding_work" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</LinearLayout> 