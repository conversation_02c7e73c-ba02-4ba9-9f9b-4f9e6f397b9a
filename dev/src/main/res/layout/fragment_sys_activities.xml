<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clRoot"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/statusBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/titleBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/statusBar"
        app:showRightText="false"
        app:title_text="@string/activities" />

    <com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout
        android:id="@+id/refresh"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/rvTab"
        app:layout_constraintTop_toBottomOf="@id/titleBar">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            android:orientation="vertical"
            android:paddingBottom="20dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            tools:listitem="@layout/adapter_sys_activities"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvTab"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent" />

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/lv"
        app:layout_constraintTop_toBottomOf="@id/titleBar"
        app:layout_constraintBottom_toTopOf="@id/rvTab"
        android:layout_width="match_parent"
        android:layout_height="0dp"/>

</androidx.constraintlayout.widget.ConstraintLayout>