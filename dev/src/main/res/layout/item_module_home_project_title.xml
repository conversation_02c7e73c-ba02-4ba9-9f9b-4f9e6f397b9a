<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/dp_16"
    android:orientation="horizontal">

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_draft_count"
        style="@style/MetaTextView.S16.PoppinsSemiBold600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/ugc_module_project_count"
        android:textStyle="bold" />

    <ImageView
        android:id="@+id/iv_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="@dimen/dp_4"
        android:src="@drawable/ic_ugc_module_project_max_tips" />

</LinearLayout>