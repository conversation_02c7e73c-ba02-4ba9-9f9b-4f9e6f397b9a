<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="@dimen/dp_15">

    <include
        android:id="@+id/titleHeader"
        layout="@layout/adapter_choice_card_provider_linear_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/titleHeader"
        android:background="@drawable/bg_white_round_16"
        android:paddingHorizontal="@dimen/dp_10">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/ivGameCover"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:scaleType="center"
            android:src="@color/color_EFEFEF"
            app:layout_constraintDimensionRatio="343:140"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeAppearance="@style/shapeRound16Style" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_50"
            android:background="@drawable/shape_gradient_bottom_trans_black"
            android:clickable="false"
            app:layout_constraintBottom_toBottomOf="@id/ivGameCover"
            app:layout_constraintLeft_toLeftOf="@id/ivGameCover"
            app:layout_constraintRight_toRightOf="@id/ivGameCover" />

        <LinearLayout
            android:id="@+id/llPlayerContainer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_12"
            android:layout_marginRight="@dimen/dp_4"
            android:layout_marginBottom="@dimen/dp_12"
            android:minHeight="@dimen/dp_28"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="@+id/ivGameCover"
            app:layout_constraintLeft_toLeftOf="@id/ivGameCover"
            app:layout_constraintRight_toLeftOf="@+id/tvGameName"
            tools:background="@color/color_B5FD1D">

            <com.google.android.material.imageview.ShapeableImageView
                android:layout_width="@dimen/dp_28"
                android:layout_height="@dimen/dp_28"
                android:foreground="@drawable/shape_transparent_round"
                android:padding="1dp"
                android:visibility="gone"
                app:shapeAppearance="@style/circleStyle"
                tools:background="@color/color_FF5F42"
                tools:src="@color/color_527AFE"
                tools:visibility="visible" />

            <com.google.android.material.imageview.ShapeableImageView
                android:layout_width="@dimen/dp_28"
                android:layout_height="@dimen/dp_28"
                android:layout_marginLeft="-9dp"
                android:foreground="@drawable/shape_transparent_round"
                android:padding="1dp"
                android:src="@drawable/placeholder_round"
                android:visibility="gone"
                app:shapeAppearance="@style/circleStyle"
                tools:background="@color/color_FF5F42"
                tools:visibility="visible" />

            <com.google.android.material.imageview.ShapeableImageView
                android:layout_width="@dimen/dp_28"
                android:layout_height="@dimen/dp_28"
                android:layout_marginLeft="-9dp"
                android:foreground="@drawable/shape_transparent_round"
                android:padding="1dp"
                android:src="@drawable/icon_login_facebook"
                android:visibility="gone"
                app:shapeAppearance="@style/circleStyle"
                tools:background="@color/color_FF5F42"
                tools:visibility="visible" />
        </LinearLayout>

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvGameName"
            style="@style/MetaTextView.S12.PoppinsBold700.EditorCard"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginRight="@dimen/dp_6"
            android:ellipsize="end"
            android:maxLines="1"
            android:minLines="1"
            android:textColor="@color/white"
            android:textSize="@dimen/dp_14"
            app:layout_constraintBottom_toBottomOf="@+id/llPlayerContainer"
            app:layout_constraintLeft_toRightOf="@+id/llPlayerContainer"
            app:layout_constraintRight_toLeftOf="@+id/tvBtnJoin"
            app:layout_constraintTop_toTopOf="@+id/llPlayerContainer"
            tools:text="爆炸的房 炸的房 的房间可炸的房间" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvBtnJoin"
            android:layout_width="@dimen/dp_64"
            android:layout_height="@dimen/dp_30"
            android:layout_marginRight="@dimen/dp_12"
            android:layout_marginBottom="@dimen/dp_12"
            android:background="@drawable/selector_enable_button"
            android:fontFamily="@font/poppins_black_900"
            android:gravity="center"
            android:text="@string/game_detail_room_join"
            android:textColor="@color/textColorPrimary"
            android:textSize="@dimen/dp_14"
            app:layout_constraintBottom_toBottomOf="@+id/ivGameCover"
            app:layout_constraintLeft_toRightOf="@+id/tvGameName"
            app:layout_constraintRight_toRightOf="@id/ivGameCover" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</RelativeLayout>