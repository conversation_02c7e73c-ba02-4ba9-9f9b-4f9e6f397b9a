<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv_friend_empty"
        android:layout_width="@dimen/dp_150"
        android:layout_height="@dimen/dp_150"
        android:layout_marginTop="@dimen/dp_2"
        android:src="@drawable/icon_no_recent_activity"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_friend_empty"
        style="@style/MetaTextView.S15.PoppinsRegular400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_20"
        android:text="@string/no_friends"
        android:textColor="@color/color_9B9FA6"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/iv_friend_empty"
        app:layout_constraintStart_toStartOf="@id/iv_friend_empty"
        app:layout_constraintTop_toBottomOf="@id/iv_friend_empty" />


</androidx.constraintlayout.widget.ConstraintLayout>