<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="@dimen/dp_44"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:visibility="visible">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_voice"
        app:layout_constraintTop_toTopOf="parent"
        android:visibility="visible"
        android:layout_centerVertical="true"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
        <com.socialplay.gpark.ui.view.voice.WaveVoiceView
            android:id="@+id/voiceView"
            android:layout_width="@dimen/dp_44"
            android:layout_height="@dimen/dp_34"
            android:clipToPadding="false"
            android:clipChildren="false"
            app:layout_constraintEnd_toStartOf="@id/msc_switch"
            app:layout_constraintTop_toTopOf="parent" />
        <ImageView
            android:id="@+id/voiceShield"
            android:layout_width="@dimen/dp_44"
            android:layout_height="@dimen/dp_34"
            android:visibility="gone"
            android:src="@drawable/icon_audio_shield"
            app:layout_constraintEnd_toStartOf="@id/msc_switch"
            app:layout_constraintTop_toTopOf="parent"
            />

        <ImageView
            android:id="@+id/msc_switch"
            android:layout_width="@dimen/dp_34"
            android:layout_height="@dimen/dp_34"
            android:layout_centerVertical="true"
            android:src="@drawable/icon_all_open"
            android:visibility="visible"
            app:layout_constraintStart_toEndOf="@+id/voiceView"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/etInput"
        style="@style/MetaTextView.S12.PoppinsRegular400.White"
        android:layout_width="@dimen/dp_220"
        android:layout_height="30dp"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:layout_toRightOf="@+id/cl_voice"
        android:background="@drawable/bg_mgs_input"
        android:gravity="center_vertical"
        android:hint="@string/mgs_game_input_hint"
        android:paddingHorizontal="@dimen/dp_10"
        android:paddingVertical="@dimen/dp_5"
        android:textColorHint="@color/white_50" />
</RelativeLayout>