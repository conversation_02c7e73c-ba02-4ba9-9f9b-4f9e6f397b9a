<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingVertical="@dimen/dp_7">

    <ImageView
        android:id="@+id/iv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="166:136"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv"
        android:layout_width="match_parent"
        android:layout_height="21dp"
        android:layout_margin="8dp"
        app:layout_constraintBottom_toBottomOf="@id/iv"
        app:layout_constraintLeft_toLeftOf="@id/iv"
        tools:listitem="@layout/adapter_ai_bot_tag" />

    <TextView
        android:id="@+id/tvTitle"
        style="@style/MetaTextView.S13.PoppinsRegular400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_4"
        android:singleLine="true"
        app:layout_constraintLeft_toLeftOf="@id/iv"
        app:layout_constraintRight_toRightOf="@id/iv"
        app:layout_constraintTop_toBottomOf="@id/iv"
        tools:text="Copycat ware SmoothieCopycat ware SmoothieCopycat ware SmoothieCopycat ware SmoothieCopycat ware SmoothieCopycat ware SmoothieCopycat ware SmoothieCopycat ware SmoothieCopycat ware Smoothie" />

    <ImageView
        android:id="@+id/ivIcon"
        android:layout_width="18dp"
        android:layout_height="18dp"
        android:layout_marginTop="@dimen/dp_4"
        app:layout_constraintLeft_toLeftOf="@id/iv"
        app:layout_constraintTop_toBottomOf="@id/tvTitle" />

    <TextView
        android:id="@+id/tvName"
        style="@style/MetaTextView.S12.PoppinsRegular400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="4dp"
        android:layout_marginTop="@dimen/dp_4"
        android:singleLine="true"
        android:textColor="#666"
        app:layout_constraintLeft_toRightOf="@id/ivIcon"
        app:layout_constraintRight_toRightOf="@id/iv"
        app:layout_constraintTop_toBottomOf="@id/tvTitle"
        tools:text="Copycat ware SmoothieCopycat ware SmoothieCopycat ware SmoothieCopycat ware SmoothieCopycat ware SmoothieCopycat ware SmoothieCopycat ware SmoothieCopycat ware SmoothieCopycat ware Smoothie" />

</androidx.constraintlayout.widget.ConstraintLayout>