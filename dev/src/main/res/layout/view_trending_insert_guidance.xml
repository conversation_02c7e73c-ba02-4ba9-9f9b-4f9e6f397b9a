<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false"
    android:clipToPadding="false">

    <ImageView
        android:id="@+id/lottieTop"
        android:layout_width="@dimen/dp_57"
        android:layout_height="@dimen/dp_50"
        android:layout_marginStart="@dimen/dp_80"
        android:rotation="110"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clTrendingGuidance"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_82"
        android:layout_marginTop="@dimen/dp_5"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/lottieTop">

        <View
            android:id="@+id/bgGuidance"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="@dimen/dp_18"
            android:background="@drawable/placeholder_corner_16"
            android:backgroundTint="@color/color_FFE929" />

        <ImageView
            android:id="@+id/ivGuidance"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_use_controller"
            app:layout_constraintBottom_toBottomOf="@id/bgGuidance"
            app:layout_constraintStart_toStartOf="@id/bgGuidance"
            app:layout_constraintTop_toTopOf="@id/bgGuidance" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvGotIt"
            style="@style/Button.S16.PoppinsBold600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_16"
            android:background="@drawable/bg_1a1a1a_round"
            android:minWidth="@dimen/dp_72"
            android:minHeight="@dimen/dp_32"
            android:text="@string/got_it"
            android:textColor="@color/white"
            app:layout_constraintBottom_toBottomOf="@id/bgGuidance"
            app:layout_constraintEnd_toEndOf="@id/bgGuidance"
            app:layout_constraintTop_toTopOf="@id/bgGuidance" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvGuidanceDesc"
            style="@style/MetaTextView.S14.PoppinsSemiBold600"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_8"
            android:text="@string/trending_insert_desc"
            app:layout_constraintBottom_toBottomOf="@id/bgGuidance"
            app:layout_constraintEnd_toStartOf="@id/tvGotIt"
            app:layout_constraintStart_toEndOf="@id/ivGuidance"
            app:layout_constraintTop_toTopOf="@id/bgGuidance" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/lottieBottom"
        android:layout_width="@dimen/dp_50"
        android:layout_height="@dimen/dp_57"
        android:layout_marginStart="@dimen/dp_128"
        android:visibility="gone"
        android:layout_marginTop="@dimen/dp_5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/clTrendingGuidance" />

</androidx.constraintlayout.widget.ConstraintLayout>