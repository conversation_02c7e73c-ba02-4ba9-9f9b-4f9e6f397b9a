<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv_room_item_bg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_16"
        android:layout_marginEnd="@dimen/dp_16"
        android:background="@drawable/shape_room_list_item_bg_1"
        app:layout_constraintBottom_toBottomOf="@id/view_bottom"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_room_member"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_16"
        android:layout_marginEnd="@dimen/dp_16"
        android:drawablePadding="@dimen/dp_3"
        android:textColor="#66000000"
        app:drawableStartCompat="@drawable/room_detail_member_icon"
        app:layout_constraintStart_toStartOf="@id/iv_room_item_bg"
        app:layout_constraintTop_toTopOf="@id/iv_room_item_bg"
        tools:text="0/20" />

    <View
        android:id="@+id/view_bottom"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/dp_15"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_room_join" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_room_user"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_13"
        android:layout_marginEnd="@dimen/dp_16"
        android:orientation="horizontal"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        app:layout_constraintEnd_toEndOf="@id/iv_room_item_bg"
        app:layout_constraintStart_toStartOf="@id/iv_room_item_bg"
        app:layout_constraintTop_toBottomOf="@id/tv_room_member"
        tools:itemCount="8"
        tools:listitem="@layout/item_detail_room_user" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_room_join"
        style="@style/MetaTextView.S16.PoppinsExtraBold800.LeftTitle"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_41"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_16"
        android:layout_marginEnd="@dimen/dp_16"
        android:background="@drawable/shape_room_join_bg"
        android:gravity="center"
        android:text="@string/game_detail_room_join"
        android:textColor="#17191C"
        android:textSize="@dimen/sp_15"
        app:layout_constraintEnd_toEndOf="@id/iv_room_item_bg"
        app:layout_constraintStart_toStartOf="@id/iv_room_item_bg"
        app:layout_constraintTop_toBottomOf="@id/rv_room_user" />

</androidx.constraintlayout.widget.ConstraintLayout>