<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
       android:id="@+id/load_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black">

        <ImageView
            android:id="@+id/bg_line_ai_bot_ugc_back"
            android:layout_width="354dp"
            android:layout_height="354dp"
            android:scaleType="centerCrop"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/img_bg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="@dimen/dp_22"
        android:orientation="horizontal"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toTopOf="@+id/tv_generate"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/view_bg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@drawable/bg_ai_bot_ugc_top"
        app:layout_constraintDimensionRatio="375:142"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/statusBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/img_back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/dp_16"
        android:src="@drawable/ic_back"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/statusBar" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/poppins_semi_bold_600"
        android:text="@string/welcome_to_app"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16"
        app:layout_constraintBottom_toBottomOf="@+id/img_back"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/img_back" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_loading"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:paddingHorizontal="@dimen/dp_16"
        android:paddingTop="@dimen/dp_19"
        android:paddingBottom="@dimen/dp_33"
        app:layout_constraintBottom_toTopOf="@id/tv_generate"
        app:layout_constraintTop_toBottomOf="@+id/tv_title">

        <View
            android:id="@+id/view_loading"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/bg_line_ai_bot_ugc" />
        <ImageView
            android:id="@+id/bg_line_ai_bot_ugc_loading"
            android:layout_width="277dp"
            android:scaleType="centerCrop"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            android:layout_height="277dp"/>

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/img_loading"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginTop="@dimen/dp_6"
            android:layout_marginBottom="@dimen/dp_6"
            android:gravity="center"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:lottie_fileName="ai_bots_loding.zip"
            android:visibility="visible" />

        <TextView
            android:id="@+id/tv_progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/poppins_regular_400"
            android:text="@string/ai_bot_generate_loading"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_14"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/img_loading" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <View
        android:id="@+id/view_select"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@drawable/bg_ai_bot_ugc_bottom"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="375:256" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/ry_image"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_22"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:orientation="horizontal"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toTopOf="@+id/tv_generate" />



    <androidx.constraintlayout.widget.Group
        android:id="@+id/result"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="view_select,ry_image,img_bg,view_bg" />

    <TextView
        android:id="@+id/tv_generate"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_24"
        android:alpha="0.5"
        android:background="@drawable/shape_white_corner"
        android:enabled="false"
        android:fontFamily="@font/poppins_semi_bold_600"
        android:gravity="center"
        android:paddingVertical="13dp"
        android:text="@string/ai_bot_generate"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_16"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>