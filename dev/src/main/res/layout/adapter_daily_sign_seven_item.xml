<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@drawable/bg_fffbd0_round_12"
    android:paddingHorizontal="@dimen/dp_12">

    <TextView
        android:id="@+id/tv_day_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/color_1A1A1A"
        android:textSize="@dimen/sp_10"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="1 Day" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginVertical="@dimen/dp_16"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:layout_width="@dimen/dp_32"
            android:layout_height="@dimen/dp_32"
            android:src="@drawable/icon_g_point_group_green" />


        <TextView
            android:id="@+id/tv_reward_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_2"
            android:textColor="@color/color_1A1A1A"
            android:textSize="@dimen/sp_18"
            android:textStyle="bold"
            tools:text="12" />
    </LinearLayout>

    <ImageView
        android:id="@+id/iv_get"
        android:layout_width="@dimen/dp_30"
        android:layout_height="@dimen/dp_30"
        android:src="@drawable/icon_daily_get"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>