<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:clipChildren="false"
    android:id="@+id/fl_message"
    android:layout_height="wrap_content">
    <com.ly123.tes.mgs.im.view.AutoLinkTextView
        android:id="@+id/tv_message"
        style="@style/MetaTextView"
        tools:text="Yes,Look"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:autoLink="web|email|phone"
        android:gravity="center_vertical|left"
        android:paddingLeft="@dimen/dp_14"
        android:paddingRight="@dimen/dp_28"
        android:paddingTop="@dimen/dp_14"
        android:paddingBottom="@dimen/dp_14"
        android:includeFontPadding="false"
        android:textColorLink="@color/color_4AB4FF"
        app:RCMaxWidth="@dimen/dp_320" />
</FrameLayout>