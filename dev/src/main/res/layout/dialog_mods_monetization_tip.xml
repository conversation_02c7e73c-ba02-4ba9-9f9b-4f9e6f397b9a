<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    android:gravity="center"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="300dp"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_white_round_16"
        android:paddingBottom="24dp"
        tools:ignore="DpHeight,HardcodedText">

        <View
            android:id="@+id/top_yellow_bg"
            android:layout_width="0dp"
            android:layout_height="112dp"
            android:background="@drawable/bg_mods_monetization_tip_top"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_character"
            android:layout_width="127dp"
            android:layout_height="140dp"
            android:src="@drawable/img_mods_monetization_character"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_title"
            style="@style/MetaTextView.S15.PoppinsSemiBold600"
            android:layout_width="167dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:text="@string/mods_monetization_tip_title"
            android:textColor="@color/color_1A1A1A"
            app:layout_constraintStart_toStartOf="@id/top_yellow_bg"
            app:layout_constraintTop_toTopOf="@id/top_yellow_bg" />

        <TextView
            android:id="@+id/tv_description"
            style="@style/MetaTextView.S14.PoppinsRegular400"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="16dp"
            android:text="@string/mods_monetization_tip_description"
            android:textColor="@color/color_333333"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/top_yellow_bg"
            app:uiLineHeight="22sp" />

        <TextView
            android:id="@+id/btn_try_it_now"
            style="@style/MetaTextView.S16.PoppinsSemiBold600"
            android:layout_width="220dp"
            android:layout_height="48dp"
            android:layout_marginTop="24dp"
            android:background="@drawable/bg_mods_monetization_tip_button"
            android:gravity="center"
            android:text="@string/mods_monetization_tip_action"
            android:textColor="@color/color_1A1A1A"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_description" />


        <!-- Star decorations -->
        <ImageView
            android:layout_width="8dp"
            android:layout_height="8dp"
            android:layout_marginStart="28dp"
            android:layout_marginTop="11dp"
            android:src="@drawable/ic_star_decoration"
            app:layout_constraintStart_toStartOf="@id/top_yellow_bg"
            app:layout_constraintTop_toTopOf="@id/top_yellow_bg" />

        <ImageView
            android:layout_width="9dp"
            android:layout_height="9dp"
            android:src="@drawable/ic_star_decoration"
            android:layout_marginStart="68dp"
            android:layout_marginTop="65dp"
            app:layout_constraintStart_toStartOf="@id/top_yellow_bg"
            app:layout_constraintTop_toTopOf="@id/top_yellow_bg" />

        <ImageView
            android:layout_width="7dp"
            android:layout_height="7dp"
            android:src="@drawable/ic_star_decoration"
            android:layout_marginStart="10dp"
            android:layout_marginTop="63dp"
            app:layout_constraintStart_toStartOf="@id/top_yellow_bg"
            app:layout_constraintTop_toTopOf="@id/top_yellow_bg" />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/btn_close"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginTop="24dp"
        android:src="@drawable/icon_dialog_close" />

</LinearLayout> 