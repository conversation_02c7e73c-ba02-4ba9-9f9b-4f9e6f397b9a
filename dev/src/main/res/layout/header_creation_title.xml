<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingTop="20dp">

    <ImageView
        android:id="@+id/ivCreationsTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="1dp"
        android:src="@drawable/icon_creations_title"
        android:layout_marginHorizontal="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvCreationsTitle" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvCreationsTitle"
        style="@style/MetaTextView.S18.PoppinsBold700"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:gravity="center_vertical"
        android:text="@string/creation_title"
        app:layout_constraintStart_toEndOf="@id/ivCreationsTitle"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvCreationsNum1"
        style="@style/MetaTextView.S12.PoppinsExtraBold800"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="2dp"
        android:gravity="center_vertical"
        android:text="@string/left_parenthesis"
        app:layout_constraintBottom_toBottomOf="@id/tvCreationsTitle"
        app:layout_constraintStart_toEndOf="@id/tvCreationsTitle"
        app:layout_constraintTop_toTopOf="@id/tvCreationsTitle" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvCreationsNum2"
        style="@style/MetaTextView.S16.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="2dp"
        android:gravity="center_vertical"
        app:layout_constraintBottom_toBottomOf="@id/tvCreationsNum1"
        app:layout_constraintStart_toEndOf="@id/tvCreationsNum1"
        app:layout_constraintTop_toTopOf="@id/tvCreationsNum1"
        tools:text="8" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvCreationsNum3"
        style="@style/MetaTextView.S12.PoppinsExtraBold800"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="2dp"
        android:gravity="center_vertical"
        android:text="@string/right_parenthesis"
        app:layout_constraintBottom_toBottomOf="@id/tvCreationsNum2"
        app:layout_constraintStart_toEndOf="@id/tvCreationsNum2"
        app:layout_constraintTop_toTopOf="@id/tvCreationsNum2" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupCreationNum"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="tvCreationsNum1,tvCreationsNum2,tvCreationsNum3"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/ivEmpty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        app:layout_constraintTop_toBottomOf="@id/tvCreationsTitle"
        app:layout_constraintStart_toStartOf="parent"
        android:src="@drawable/empty_build_list"/>

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/loadingErrorCreation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="100dp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/tvCreationsTitle" />
</androidx.constraintlayout.widget.ConstraintLayout>