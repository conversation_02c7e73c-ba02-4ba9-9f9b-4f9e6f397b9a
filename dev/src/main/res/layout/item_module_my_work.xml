<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginEnd="@dimen/dp_8"
    android:layout_marginBottom="@dimen/dp_8"
    tools:layout_width="@dimen/dp_80">

    <androidx.cardview.widget.CardView
        android:id="@+id/cv_thumb"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:cardBackgroundColor="@color/color_F6F6F6"
        app:cardCornerRadius="@dimen/dp_20"
        app:cardElevation="0dp"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_thumb"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:scaleType="centerCrop" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_review_status_label"
            style="@style/MetaTextView.S8"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/sp_ugc_module_under_review"
            android:minHeight="@dimen/dp_12"
            android:paddingVertical="@dimen/dp_4"
            android:paddingStart="@dimen/dp_8"
            android:paddingEnd="@dimen/dp_6"
            android:text="@string/under_review"
            android:textColor="@color/color_FF7210"
            android:textSize="@dimen/dp_8"
            android:visibility="gone"
            app:uiLineHeight="@dimen/dp_12" />

        <ImageView
            android:id="@+id/iv_invisible_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:layout_margin="@dimen/dp_5"
            android:src="@drawable/ic_ugc_asset_lock" />

    </androidx.cardview.widget.CardView>

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_title"
        style="@style/MetaTextView.S10"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_4"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:singleLine="true"
        app:layout_constraintTop_toBottomOf="@id/cv_thumb"
        tools:text="ModuleName" />

</androidx.constraintlayout.widget.ConstraintLayout>