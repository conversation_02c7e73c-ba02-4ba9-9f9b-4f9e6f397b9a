<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rl_mgs_room_my_friend_content"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:background="@color/textColorPrimary">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:paddingHorizontal="@dimen/dp_13"
        android:paddingTop="@dimen/dp_8"
        android:paddingBottom="@dimen/dp_8">

        <ImageView
            android:id="@+id/iv_mgs_room_my_friend_avatar"
            android:layout_width="@dimen/dp_32"
            android:layout_height="@dimen/dp_32"
            android:src="@drawable/placeholder_corner_360"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_mgs_room_my_friend_name"
            style="@style/MetaTextView.S12.PoppinsBold700"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_8"
            android:ellipsize="end"
            android:gravity="start|center_vertical"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_12"
            app:layout_constraintBottom_toTopOf="@id/tv_mgs_room_my_friend_status"
            app:layout_constraintEnd_toStartOf="@id/ivCertification"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toEndOf="@id/iv_mgs_room_my_friend_avatar"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constrainedWidth="true"
            app:layout_goneMarginEnd="@dimen/dp_8"
            tools:text="Harry Potter is Died, HaHaHaHatter is Died, HaHaHaHatter is Died, HaHaHaHarry " />

        <ImageView
            android:id="@+id/ivCertification"
            android:layout_width="@dimen/dp_14"
            android:layout_height="@dimen/dp_14"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/dp_4"
            android:layout_marginEnd="@dimen/dp_8"
            android:src="@drawable/official_certification"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/tv_mgs_room_my_friend_name"
            app:layout_constraintEnd_toStartOf="@id/tvMgsRoomInvite"
            app:layout_constraintStart_toEndOf="@id/tv_mgs_room_my_friend_name"
            app:layout_constraintTop_toTopOf="@id/tv_mgs_room_my_friend_name"
            tools:visibility="visible" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_mgs_room_my_friend_status"
            style="@style/MetaTextView.S10.PoppinsRegular400.CenterVertical14"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_8"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/color_61EB22"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tvMgsRoomInvite"
            android:gravity="start|center_vertical"
            app:layout_constraintStart_toEndOf="@id/iv_mgs_room_my_friend_avatar"
            app:layout_constraintTop_toBottomOf="@id/tv_mgs_room_my_friend_name"
            tools:text="OnlineOnlineOnlineeOnlineOnlineOnline" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvMgsRoomInvite"
            style="@style/MetaTextView.S12.PoppinsBold700"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_8"
            android:layout_marginEnd="@dimen/dp_3"
            android:background="@drawable/selector_button_enable2"
            android:gravity="center"
            android:includeFontPadding="false"
            android:minWidth="@dimen/dp_50"
            android:minHeight="@dimen/dp_24"
            android:text="@string/invite_friend"
            android:textColor="@color/selector_button_primary_text_color"
            android:textSize="@dimen/sp_10"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</RelativeLayout>
