<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    app:speed_monitor="true">

    <FrameLayout
        android:id="@+id/bgViewTopLayout"
        android:layout_width="match_parent"
        android:layout_height="439dp"
        android:background="@color/white"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="gone">

        <View
            android:id="@+id/bgViewTop"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/bg_game_detail_top_white_foreground" />
    </FrameLayout>

    <com.socialplay.gpark.ui.view.refresh.MetaVerticalCoordinatorRefreshLayout
        android:id="@+id/mrl"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/v_divider_enter"
        app:layout_constraintTop_toBottomOf="@id/tbl">

        <com.socialplay.gpark.ui.view.VerticalCoordinatorLayout
            android:id="@+id/vcl"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.google.android.material.appbar.AppBarLayout
                android:id="@+id/abl"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/transparent"
                app:layout_behavior="com.google.android.material.appbar.CustomAppBarBehavior">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_top"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_scrollFlags="scroll">

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_game_name"
                        style="@style/MetaTextView.S24.PoppinsSemiBold600"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_18"
                        android:lineSpacingMultiplier="1.2"
                        android:maxLines="3"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="Extreme Long Title" />

                    <com.socialplay.gpark.ui.view.WrapEpoxyBanner
                        android:id="@+id/gameBanner"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_marginTop="@dimen/dp_8"
                        app:layout_constraintDimensionRatio="343:195"
                        app:layout_constraintTop_toBottomOf="@id/tv_game_name"
                        tools:visibility="gone" />

                    <com.zhpan.indicator.IndicatorView
                        android:id="@+id/gameBannerIndicator"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_8"
                        android:visibility="gone"
                        app:layout_constraintEnd_toEndOf="@id/gameBanner"
                        app:layout_constraintStart_toStartOf="@id/gameBanner"
                        app:layout_constraintTop_toBottomOf="@id/gameBanner"
                        app:vpi_orientation="horizontal"
                        app:vpi_slide_mode="smooth"
                        app:vpi_slider_checked_color="@color/color_D9D9D9"
                        app:vpi_slider_normal_color="@color/color_F6F6F6"
                        app:vpi_style="round_rect"
                        tools:visibility="gone" />

                    <FrameLayout
                        android:id="@+id/updateAndBuildLayout"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/dp_48"
                        android:layout_marginStart="@dimen/dp_16"
                        app:layout_constraintBottom_toBottomOf="@id/dpbEnter2"
                        app:layout_constraintEnd_toStartOf="@id/dpbEnter2"
                        app:layout_constraintHorizontal_weight="119"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="@id/dpbEnter2">

                        <com.socialplay.gpark.ui.view.MetaTextView
                            android:id="@+id/updateBtn"
                            style="@style/MetaTextView.S15.PoppinsMedium600"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/bg_round_100_4ab4ff_stroke_1_color_eff7ff"
                            android:gravity="center"
                            android:text="@string/game_detail_page_update_btn"
                            android:textColor="@color/color_4AB4FF"
                            android:visibility="gone"
                            tools:visibility="visible" />

                        <FrameLayout
                            android:id="@+id/buildBtnLayout"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/bg_round_100_b57eff_stroke_1_color_f9f5ff"
                            android:visibility="gone"
                            tools:visibility="gone">

                            <com.socialplay.gpark.ui.view.MetaTextView
                                android:id="@+id/buildBtn"
                                style="@style/MetaTextView.S15.PoppinsMedium600"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:drawableStart="@drawable/icon_game_detail_build"
                                android:drawablePadding="@dimen/dp_8"
                                android:paddingTop="1.5dp"
                                android:text="@string/game_detail_page_build_btn"
                                android:textColor="@color/color_B57EFF"
                                tools:visibility="visible" />
                        </FrameLayout>
                    </FrameLayout>

                    <com.socialplay.gpark.ui.view.DownloadProgressButton
                        android:id="@+id/dpbEnter2"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/dp_48"
                        android:layout_marginStart="@dimen/dp_12"
                        android:layout_marginTop="@dimen/dp_28"
                        android:layout_marginEnd="@dimen/dp_16"
                        android:fontFamily="@font/poppins_semi_bold_600"
                        android:gravity="center"
                        android:includeFontPadding="false"
                        android:textSize="@dimen/sp_15"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_weight="212"
                        app:layout_constraintStart_toEndOf="@id/updateAndBuildLayout"
                        app:layout_constraintTop_toBottomOf="@id/gameBanner"
                        app:progress_btn_background_color_end="@color/colorAccentPrimary"
                        app:progress_btn_background_color_start="@color/colorAccentPrimary"
                        app:progress_btn_background_second_color="@color/color_game_detail_desc_bg"
                        app:progress_btn_current_text="@string/game_detail_page_play_btn"
                        app:progress_btn_radius="@dimen/dp_24"
                        app:progress_btn_show_bolder="false"
                        app:progress_btn_text_color="@color/textColorPrimary"
                        app:progress_btn_text_cover_color="@color/textColorPrimary"
                        app:progress_btn_text_size="15"
                        tools:background="@drawable/bg_ffef30_round_100"
                        tools:text="@string/game_detail_page_play_btn" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_mw_not_compatible"
                        style="@style/MetaTextView.S12.PoppinsRegular400"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_16"
                        android:background="@drawable/shape_mw_not_compatible"
                        android:paddingHorizontal="@dimen/dp_16"
                        android:paddingVertical="@dimen/dp_13"
                        android:textColor="@color/color_FF5F42"
                        android:visibility="gone"
                        app:layout_constraintTop_toBottomOf="@id/dpbEnter2"
                        tools:text="The experience version is outdated. Please enter again after the creator updates the experience."
                        tools:visibility="visible" />

                    <Space
                        android:id="@+id/topBgEndLine"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_0"
                        android:layout_marginTop="@dimen/dp_16"
                        app:layout_constraintTop_toBottomOf="@id/tv_mw_not_compatible" />

                    <ImageView
                        android:id="@+id/iv_avatar"
                        android:layout_width="@dimen/dp_39"
                        android:layout_height="@dimen/dp_39"
                        android:layout_marginStart="@dimen/dp_16"
                        android:src="@drawable/icon_default_avatar"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/topBgEndLine" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_username"
                        style="@style/MetaTextView.S14.PoppinsMedium500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_6"
                        android:ellipsize="end"
                        android:gravity="center_vertical"
                        android:maxLines="1"
                        app:layout_constrainedWidth="true"
                        app:layout_constraintBottom_toTopOf="@id/tv_portfolio"
                        app:layout_constraintEnd_toStartOf="@id/ulv"
                        app:layout_constraintHorizontal_bias="0"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        app:layout_constraintStart_toEndOf="@id/iv_avatar"
                        app:layout_constraintTop_toTopOf="@id/iv_avatar"
                        app:layout_constraintVertical_chainStyle="packed"
                        app:layout_goneMarginEnd="@dimen/dp_16"
                        tools:text="Longlonglonglonglonglonglonglonglonglong" />

                    <com.socialplay.gpark.ui.view.UserLabelView
                        android:id="@+id/ulv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:layout_constraintBottom_toBottomOf="@id/tv_username"
                        app:layout_constraintEnd_toStartOf="@id/space_follow"
                        app:layout_constraintStart_toEndOf="@id/tv_username"
                        app:layout_constraintTop_toTopOf="@id/tv_username" />

                    <View
                        android:id="@+id/v_author_click"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
                        app:layout_constraintEnd_toEndOf="@id/tv_username"
                        app:layout_constraintStart_toStartOf="@id/iv_avatar"
                        app:layout_constraintTop_toTopOf="@id/iv_avatar" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tvUserId"
                        style="@style/MetaTextView.S10.PoppinsRegular400"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp_8"
                        android:ellipsize="end"
                        android:gravity="center_vertical"
                        android:maxLines="1"
                        android:textColor="@color/color_999999"
                        android:textSize="@dimen/sp_12"
                        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
                        app:layout_constraintEnd_toStartOf="@id/tv_portfolio"
                        app:layout_constraintHorizontal_bias="0"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        app:layout_constraintStart_toStartOf="@id/tv_username"
                        app:layout_constraintTop_toBottomOf="@id/tv_username"
                        app:layout_goneMarginEnd="@dimen/dp_16"
                        tools:text="ID: 2716834" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_portfolio"
                        style="@style/MetaTextView.S10.PoppinsRegular400"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp_8"
                        android:ellipsize="end"
                        android:gravity="center_vertical"
                        android:maxLines="1"
                        android:textColor="@color/color_999999"
                        android:textSize="@dimen/sp_12"
                        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
                        app:layout_constraintEnd_toStartOf="@id/tv_follow_btn"
                        app:layout_constraintStart_toEndOf="@id/tvUserId"
                        app:layout_constraintTop_toBottomOf="@id/tv_username"
                        app:layout_goneMarginEnd="@dimen/dp_16"
                        tools:text="Maps: 5" />

                    <Space
                        android:id="@+id/space_follow"
                        android:layout_width="@dimen/dp_8"
                        android:layout_height="@dimen/dp_8"
                        app:layout_constraintBottom_toBottomOf="@id/tv_username"
                        app:layout_constraintEnd_toStartOf="@id/tv_follow_btn"
                        app:layout_constraintTop_toTopOf="@id/tv_username"
                        app:layout_goneMarginEnd="0dp" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_follow_btn"
                        style="@style/MetaTextView.S14.PoppinsMedium500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp_16"
                        android:background="@drawable/bg_corner_s100_stroke_c1a1a1a_s05"
                        android:drawableStart="@drawable/ic_add_1a1a1a_s12"
                        android:drawablePadding="@dimen/dp_4"
                        android:gravity="center"
                        android:paddingHorizontal="@dimen/dp_13"
                        android:paddingVertical="@dimen/dp_6"
                        android:textColor="@color/color_1A1A1A"
                        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@id/iv_avatar"
                        tools:text="Follow" />

                    <View
                        android:id="@+id/honorPanel"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_57"
                        android:layout_marginHorizontal="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_24"
                        android:background="@drawable/bg_eff7ff_8"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/iv_avatar" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/honorLikeLayout"
                        android:layout_width="wrap_content"
                        android:layout_height="0dp"
                        app:layout_constraintBottom_toBottomOf="@id/honorPanel"
                        app:layout_constraintEnd_toStartOf="@id/honorDivider0"
                        app:layout_constraintStart_toStartOf="@id/honorPanel"
                        app:layout_constraintTop_toTopOf="@id/honorPanel">

                        <com.socialplay.gpark.ui.view.MetaTextView
                            android:id="@+id/tvHonorLikeCount"
                            style="@style/MetaTextView.S14.PoppinsMedium500"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            app:layout_constraintBottom_toTopOf="@id/tvHonorLikeCountDesc"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintVertical_chainStyle="packed"
                            tools:text="0" />

                        <com.socialplay.gpark.ui.view.MetaTextView
                            android:id="@+id/tvHonorLikeCountDesc"
                            style="@style/MetaTextView.S10.PoppinsRegular400"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/dp_4"
                            android:text="@string/game_detail_page_honor_like"
                            android:textColor="@color/color_999999"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/tvHonorLikeCount" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <View
                        android:id="@+id/honorDivider0"
                        android:layout_width="@dimen/dp_05"
                        android:layout_height="@dimen/dp_16"
                        android:background="@color/color_B8DFFF"
                        app:layout_constraintBottom_toBottomOf="@id/honorPanel"
                        app:layout_constraintEnd_toStartOf="@id/honorCreateTimeLayout"
                        app:layout_constraintStart_toEndOf="@id/honorLikeLayout"
                        app:layout_constraintTop_toTopOf="@id/honorPanel" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/honorCreateTimeLayout"
                        android:layout_width="wrap_content"
                        android:layout_height="0dp"
                        app:layout_constraintBottom_toBottomOf="@id/honorPanel"
                        app:layout_constraintEnd_toStartOf="@id/honorDivider1"
                        app:layout_constraintStart_toEndOf="@id/honorDivider0"
                        app:layout_constraintTop_toTopOf="@id/honorPanel">

                        <com.socialplay.gpark.ui.view.MetaTextView
                            android:id="@+id/tvHonorCreateTime"
                            style="@style/MetaTextView.S14.PoppinsMedium500"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            app:layout_constraintBottom_toTopOf="@id/tvHonorCreateTimeDesc"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintVertical_chainStyle="packed"
                            tools:text="22 min" />

                        <com.socialplay.gpark.ui.view.MetaTextView
                            android:id="@+id/tvHonorCreateTimeDesc"
                            style="@style/MetaTextView.S10.PoppinsRegular400"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/dp_4"
                            android:text="@string/game_detail_page_honor_creation_time"
                            android:textColor="@color/color_999999"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/tvHonorCreateTime" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <View
                        android:id="@+id/honorDivider1"
                        android:layout_width="@dimen/dp_05"
                        android:layout_height="@dimen/dp_16"
                        android:background="@color/color_B8DFFF"
                        app:layout_constraintBottom_toBottomOf="@id/honorPanel"
                        app:layout_constraintEnd_toStartOf="@id/honorPlayCountLayout"
                        app:layout_constraintStart_toEndOf="@id/honorCreateTimeLayout"
                        app:layout_constraintTop_toTopOf="@id/honorPanel" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/honorPlayCountLayout"
                        android:layout_width="wrap_content"
                        android:layout_height="0dp"
                        app:layout_constraintBottom_toBottomOf="@id/honorPanel"
                        app:layout_constraintEnd_toStartOf="@id/honorDivider2"
                        app:layout_constraintStart_toEndOf="@id/honorDivider1"
                        app:layout_constraintTop_toTopOf="@id/honorPanel">

                        <com.socialplay.gpark.ui.view.MetaTextView
                            android:id="@+id/tvHonorPlayCount"
                            style="@style/MetaTextView.S14.PoppinsMedium500"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            app:layout_constraintBottom_toTopOf="@id/tvHonorPlayCountDesc"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintVertical_chainStyle="packed"
                            tools:text="1.1m" />

                        <com.socialplay.gpark.ui.view.MetaTextView
                            android:id="@+id/tvHonorPlayCountDesc"
                            style="@style/MetaTextView.S10.PoppinsRegular400"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/dp_4"
                            android:text="@string/game_detail_page_honor_play"
                            android:textColor="@color/color_999999"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/tvHonorPlayCount" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <View
                        android:id="@+id/honorDivider2"
                        android:layout_width="@dimen/dp_05"
                        android:layout_height="@dimen/dp_16"
                        android:background="@color/color_B8DFFF"
                        app:layout_constraintBottom_toBottomOf="@id/honorPanel"
                        app:layout_constraintEnd_toStartOf="@id/honorFlowersLayout"
                        app:layout_constraintStart_toEndOf="@id/honorPlayCountLayout"
                        app:layout_constraintTop_toTopOf="@id/honorPanel" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/honorFlowersLayout"
                        android:layout_width="wrap_content"
                        android:layout_height="0dp"
                        android:visibility="visible"
                        app:layout_constraintBottom_toBottomOf="@id/honorPanel"
                        app:layout_constraintEnd_toStartOf="@id/honorDivider3"
                        app:layout_constraintStart_toEndOf="@id/honorDivider2"
                        app:layout_constraintTop_toTopOf="@id/honorPanel"
                        tools:visibility="visible">

                        <com.socialplay.gpark.ui.view.MetaTextView
                            android:id="@+id/tvHonorFlowersCount"
                            style="@style/MetaTextView.S14.PoppinsMedium500"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            app:layout_constraintBottom_toTopOf="@id/tvHonorFlowersCountDesc"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintVertical_chainStyle="packed"
                            tools:text="24" />

                        <com.socialplay.gpark.ui.view.MetaTextView
                            android:id="@+id/tvHonorFlowersCountDesc"
                            style="@style/MetaTextView.S10.PoppinsRegular400"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/dp_4"
                            android:drawableEnd="@drawable/icon_game_detail_honor_item_arrow"
                            android:drawablePadding="@dimen/dp_2"
                            android:text="@string/game_detail_page_honor_flowers"
                            android:textColor="@color/color_999999"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/tvHonorFlowersCount" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <View
                        android:id="@+id/honorFlowersClick"
                        android:layout_width="@dimen/dp_0"
                        android:layout_height="@dimen/dp_0"
                        android:visibility="visible"
                        app:layout_constraintBottom_toBottomOf="@id/honorPanel"
                        app:layout_constraintEnd_toStartOf="@id/honorDivider3"
                        app:layout_constraintStart_toEndOf="@id/honorDivider2"
                        app:layout_constraintTop_toTopOf="@id/honorPanel"
                        tools:visibility="visible" />

                    <View
                        android:id="@+id/honorDivider3"
                        android:layout_width="@dimen/dp_05"
                        android:layout_height="@dimen/dp_16"
                        android:visibility="visible"
                        android:background="@color/color_B8DFFF"
                        app:layout_constraintBottom_toBottomOf="@id/honorPanel"
                        app:layout_constraintEnd_toStartOf="@id/honorPlayerAndLikesLayout"
                        app:layout_constraintStart_toEndOf="@id/honorFlowersLayout"
                        app:layout_constraintTop_toTopOf="@id/honorPanel"
                        tools:visibility="visible" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/honorPlayerAndLikesLayout"
                        android:layout_width="wrap_content"
                        android:layout_height="0dp"
                        app:layout_constraintBottom_toBottomOf="@id/honorPanel"
                        app:layout_constraintEnd_toEndOf="@id/honorPanel"
                        app:layout_constraintStart_toEndOf="@id/honorDivider3"
                        app:layout_constraintTop_toTopOf="@id/honorPanel">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/ivUserAvatars"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            app:layout_constraintBottom_toTopOf="@id/tvHonorPlayerAndLikesDesc"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintVertical_chainStyle="packed">

                            <com.google.android.material.imageview.ShapeableImageView
                                android:id="@+id/ivUserAvatar1"
                                android:layout_width="@dimen/dp_22"
                                android:layout_height="@dimen/dp_22"
                                android:scaleType="centerCrop"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                app:shapeAppearance="@style/circleStyle"
                                app:strokeColor="@color/white"
                                app:strokeWidth="@dimen/dp_1"
                                tools:src="@drawable/default_user_avatar_1" />

                            <com.google.android.material.imageview.ShapeableImageView
                                android:id="@+id/ivUserAvatar2"
                                android:layout_width="@dimen/dp_22"
                                android:layout_height="@dimen/dp_22"
                                android:layout_marginStart="@dimen/dp_14"
                                android:scaleType="centerCrop"
                                app:layout_constraintBottom_toBottomOf="@id/ivUserAvatar1"
                                app:layout_constraintStart_toStartOf="@id/ivUserAvatar1"
                                app:layout_constraintTop_toTopOf="@id/ivUserAvatar1"
                                app:shapeAppearance="@style/circleStyle"
                                app:strokeColor="@color/white"
                                app:strokeWidth="@dimen/dp_1"
                                tools:src="@drawable/default_user_avatar_2" />

                            <com.google.android.material.imageview.ShapeableImageView
                                android:id="@+id/ivUserAvatar3"
                                android:layout_width="@dimen/dp_22"
                                android:layout_height="@dimen/dp_22"
                                android:layout_marginStart="@dimen/dp_14"
                                android:scaleType="centerCrop"
                                app:layout_constraintBottom_toBottomOf="@id/ivUserAvatar2"
                                app:layout_constraintStart_toStartOf="@id/ivUserAvatar2"
                                app:layout_constraintTop_toTopOf="@id/ivUserAvatar2"
                                app:shapeAppearance="@style/circleStyle"
                                app:strokeColor="@color/white"
                                app:strokeWidth="@dimen/dp_1"
                                tools:src="@drawable/default_user_avatar_3" />
                        </androidx.constraintlayout.widget.ConstraintLayout>

                        <com.socialplay.gpark.ui.view.MetaTextView
                            android:id="@+id/tvHonorPlayerAndLikesDesc"
                            style="@style/MetaTextView.S10.PoppinsRegular400"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/dp_4"
                            android:drawableEnd="@drawable/icon_game_detail_honor_item_arrow"
                            android:drawablePadding="@dimen/dp_2"
                            android:text="@string/game_detail_page_honor_player_and_likes"
                            android:textColor="@color/color_999999"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/ivUserAvatars" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <View
                        android:id="@+id/honorPlayerAndLikesClick"
                        android:layout_width="@dimen/dp_0"
                        android:layout_height="@dimen/dp_0"
                        app:layout_constraintBottom_toBottomOf="@id/honorPanel"
                        app:layout_constraintEnd_toEndOf="@id/honorPanel"
                        app:layout_constraintStart_toEndOf="@id/honorDivider3"
                        app:layout_constraintTop_toTopOf="@id/honorPanel" />

                    <Space
                        android:id="@+id/midHonorLikeLayout"
                        android:layout_width="0.01dp"
                        android:layout_height="1dp"
                        app:layout_constraintEnd_toEndOf="@id/honorLikeLayout"
                        app:layout_constraintStart_toStartOf="@id/honorLikeLayout"
                        app:layout_constraintTop_toTopOf="@id/honorLikeLayout" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tvHonorNewLikesHotTips"
                        style="@style/MetaTextView.S10.PoppinsRegular400"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_8"
                        android:layout_marginBottom="-6dp"
                        android:background="@drawable/bg_game_detail_hot_tips_tag"
                        android:paddingHorizontal="@dimen/dp_4"
                        android:paddingVertical="@dimen/dp_2"
                        android:textColor="@color/white"
                        android:visibility="gone"
                        app:layout_constraintBottom_toTopOf="@id/honorPanel"
                        app:layout_constraintStart_toEndOf="@id/midHonorLikeLayout"
                        tools:visibility="visible"
                        tools:text="@string/game_detail_page_new_likes_hot_tips" />

                    <Space
                        android:id="@+id/midHonorFlowersLayout"
                        android:layout_width="0.01dp"
                        android:layout_height="1dp"
                        app:layout_constraintEnd_toEndOf="@id/honorFlowersLayout"
                        app:layout_constraintStart_toStartOf="@id/honorFlowersLayout"
                        app:layout_constraintTop_toTopOf="@id/honorFlowersLayout" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tvHonorNewFlowersHotTips"
                        style="@style/MetaTextView.S10.PoppinsRegular400"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_8"
                        android:layout_marginBottom="-6dp"
                        android:background="@drawable/bg_game_detail_hot_tips_tag"
                        android:paddingHorizontal="@dimen/dp_4"
                        android:paddingVertical="@dimen/dp_2"
                        android:textColor="@color/white"
                        android:visibility="gone"
                        app:layout_constraintBottom_toTopOf="@id/honorPanel"
                        app:layout_constraintStart_toEndOf="@id/midHonorFlowersLayout"
                        tools:visibility="visible"
                        tools:text="@string/game_detail_page_new_flowers_hot_tips" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_description_label"
                        style="@style/MetaTextView.S16.PoppinsSemiBold600"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_16"
                        android:text="@string/game_detail_page_description"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/honorPanel" />

                    <com.socialplay.gpark.ui.view.ExpandableTextView
                        android:id="@+id/tv_description"
                        style="@style/MetaTextView.S14"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_12"
                        android:lineSpacingMultiplier="1.2"
                        android:textColor="@color/color_212121"
                        android:textColorHighlight="@color/transparent"
                        app:etv_EnableToggleClick="true"
                        app:etv_MaxLinesOnShrink="3"
                        app:etv_ToExpandHint="@string/more_cap"
                        app:etv_ToExpandHintBold="true"
                        app:etv_ToExpandHintColor="@color/color_003B70"
                        app:etv_ToShrinkHint="@string/collapse_cap"
                        app:etv_ToShrinkHintBold="true"
                        app:etv_ToShrinkHintColor="@color/color_003B70"
                        app:etv_ToShrinkHintShow="false"
                        app:layout_constraintTop_toBottomOf="@id/tv_description_label" />

                    <View
                        android:id="@+id/viewRankBg"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:background="@drawable/bg_game_detail_rank_tag"
                        app:layout_constraintBottom_toBottomOf="@id/viewRankPrefix"
                        app:layout_constraintEnd_toEndOf="@id/tvRank"
                        app:layout_constraintStart_toStartOf="@id/viewRankPrefix"
                        app:layout_constraintTop_toTopOf="@id/viewRankPrefix" />

                    <View
                        android:id="@+id/viewRankPrefix"
                        android:layout_width="@dimen/dp_22"
                        android:layout_height="@dimen/dp_20"
                        android:layout_marginStart="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_8"
                        android:background="@drawable/bg_game_detail_rank_prefix"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_description" />
                    <ImageView
                        android:id="@+id/ivRankLabel"
                        app:layout_constraintStart_toStartOf="@id/viewRankPrefix"
                        app:layout_constraintTop_toTopOf="@id/viewRankPrefix"
                        app:layout_constraintBottom_toBottomOf="@id/viewRankPrefix"
                        app:layout_constraintEnd_toEndOf="@id/viewRankPrefix"
                        android:src="@drawable/icon_game_detail_rank_prefix"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tvRank"
                        style="@style/MetaTextView.S14.PoppinsBold800"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawableEnd="@drawable/icon_game_detail_rank_tag_arrow"
                        android:drawablePadding="@dimen/dp_4"
                        android:paddingHorizontal="@dimen/dp_6"
                        android:textColor="@color/color_FF5F42"
                        app:layout_constraintBottom_toBottomOf="@id/viewRankPrefix"
                        app:layout_constraintStart_toEndOf="@id/viewRankPrefix"
                        app:layout_constraintTop_toTopOf="@id/viewRankPrefix"
                        tools:text="23" />

                    <androidx.constraintlayout.widget.Group
                        android:id="@+id/groupRank"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        app:constraint_referenced_ids="viewRankBg, viewRankPrefix, ivRankLabel,
                        tvRank" />

                    <com.socialplay.gpark.ui.view.WrapEpoxyRecyclerView
                        android:id="@+id/rvTags"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp_8"
                        android:clipToPadding="false"
                        android:orientation="horizontal"
                        android:overScrollMode="ifContentScrolls"
                        android:paddingStart="@dimen/dp_16"
                        android:paddingEnd="@dimen/dp_12"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_description"
                        tools:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:listitem="@layout/item_game_detail_tag" />

                    <View
                        android:id="@+id/vTagsMask"
                        android:layout_width="@dimen/dp_4"
                        android:layout_height="0dp"
                        android:background="@drawable/shape_gradient_white_start_to_end"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="@id/rvTags"
                        app:layout_constraintStart_toStartOf="@id/rvTags"
                        app:layout_constraintTop_toTopOf="@id/rvTags" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_update_time"
                        style="@style/MetaTextView.S12"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_8"
                        android:textColor="@color/color_757575"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/rvTags"
                        tools:text="Update Oct 21, 2024" />

                    <com.airbnb.epoxy.EpoxyRecyclerView
                        android:id="@+id/rvNotice"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_24"
                        android:background="@drawable/bg_eff7ff_8"
                        android:nestedScrollingEnabled="false"
                        android:paddingHorizontal="@dimen/dp_12"
                        android:paddingTop="@dimen/dp_12"
                        android:visibility="gone"
                        app:layout_constraintTop_toBottomOf="@id/tv_update_time" />

                    <View
                        android:id="@+id/v_divider_comment"
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:layout_marginTop="@dimen/dp_24"
                        android:background="@color/color_EEEEEE"
                        app:layout_constraintTop_toBottomOf="@id/rvNotice" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_comment_label"
                        style="@style/MetaTextView.S16.PoppinsSemiBold600"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_24"
                        android:text="@string/comment_cap"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/v_divider_comment" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_comment_count"
                        style="@style/MetaTextView.S14"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_6"
                        android:gravity="center_vertical"
                        android:textColor="@color/color_999999"
                        app:layout_constraintBottom_toBottomOf="@id/tv_comment_label"
                        app:layout_constraintStart_toEndOf="@id/tv_comment_label"
                        app:layout_constraintTop_toTopOf="@id/tv_comment_label"
                        tools:text="23" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_sort_btn"
                        style="@style/MetaTextView.S14"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_16"
                        android:drawableEnd="@drawable/ic_game_detail_common_sort_arrow"
                        android:drawablePadding="@dimen/dp_6"
                        android:padding="@dimen/dp_16"
                        android:text="@string/sort_default"
                        android:textColor="@color/color_757575"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="@id/tv_comment_label"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@id/tv_comment_label"
                        tools:visibility="visible" />

                    <ImageView
                        android:id="@+id/iv_my_avatar"
                        android:layout_width="@dimen/dp_38"
                        android:layout_height="@dimen/dp_38"
                        android:layout_marginStart="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_24"
                        android:visibility="gone"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_comment_label"
                        tools:src="@drawable/icon_default_avatar"
                        tools:visibility="visible" />

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tv_reply_hint"
                        style="@style/MetaTextView.S14"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_8"
                        android:layout_marginEnd="@dimen/dp_16"
                        android:background="@drawable/shape_f5f5f5_corner_360"
                        android:paddingHorizontal="@dimen/dp_16"
                        android:paddingVertical="@dimen/dp_9"
                        android:text="@string/post_reply"
                        android:textColor="@color/color_BDBDBD"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="@id/iv_my_avatar"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/iv_my_avatar"
                        app:layout_constraintTop_toTopOf="@id/iv_my_avatar"
                        tools:visibility="visible" />

                    <ImageView
                        android:id="@+id/iv_image_btn"
                        android:layout_width="@dimen/dp_36"
                        android:layout_height="0dp"
                        android:paddingStart="@dimen/dp_6"
                        android:paddingEnd="@dimen/dp_6"
                        android:src="@drawable/ic_game_detail_common_image"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="@id/tv_reply_hint"
                        app:layout_constraintEnd_toStartOf="@id/iv_emoji_btn"
                        app:layout_constraintTop_toTopOf="@id/tv_reply_hint"
                        tools:visibility="visible" />

                    <ImageView
                        android:id="@+id/iv_emoji_btn"
                        android:layout_width="@dimen/dp_36"
                        android:layout_height="0dp"
                        android:layout_marginEnd="@dimen/dp_10"
                        android:paddingStart="@dimen/dp_6"
                        android:paddingEnd="@dimen/dp_6"
                        android:src="@drawable/ic_game_detail_common_emoji"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="@id/tv_reply_hint"
                        app:layout_constraintEnd_toEndOf="@id/tv_reply_hint"
                        app:layout_constraintTop_toTopOf="@id/tv_reply_hint"
                        tools:visibility="visible" />

                    <Space
                        android:id="@+id/space_detail_bottom"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_16"
                        app:layout_constraintTop_toBottomOf="@id/iv_my_avatar" />

                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layout_constraintTop_toBottomOf="@id/space_detail_bottom">

                        <LinearLayout
                            android:id="@+id/ll_comment_refresh"
                            android:layout_width="match_parent"
                            android:layout_height="0dp"
                            android:gravity="center"
                            android:orientation="vertical">

                            <com.airbnb.lottie.LottieAnimationView
                                android:id="@+id/lav_progress"
                                android:layout_width="@dimen/dp_28"
                                android:layout_height="@dimen/dp_28"
                                app:lottie_loop="true"
                                app:lottie_rawRes="@raw/circle_loading" />

                            <com.socialplay.gpark.ui.view.MetaTextView
                                android:id="@+id/tv_loading"
                                style="@style/MetaTextView.S12"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_4"
                                android:gravity="center_vertical"
                                android:text="@string/loading" />

                        </LinearLayout>

                    </FrameLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

            </com.google.android.material.appbar.AppBarLayout>

            <FrameLayout
                android:id="@+id/fl_comment_container"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

                <com.airbnb.epoxy.EpoxyRecyclerView
                    android:id="@+id/rv_comment"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:overScrollMode="never" />

            </FrameLayout>

        </com.socialplay.gpark.ui.view.VerticalCoordinatorLayout>
    </com.socialplay.gpark.ui.view.refresh.MetaVerticalCoordinatorRefreshLayout>

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/sbphv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/tbl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:background_color="@color/transparent"
        app:layout_constraintTop_toBottomOf="@id/sbphv" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clTitleBar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_48"
        app:layout_constraintBottom_toBottomOf="@id/tbl"
        app:layout_constraintEnd_toStartOf="@id/ivMoreBtn"
        app:layout_constraintStart_toStartOf="@id/tbl"
        app:layout_constraintTop_toTopOf="@id/tbl">

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvTitleBarGameName"
            style="@style/MetaTextView.S14.PoppinsSemiBold600"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:alpha="0"
            android:ellipsize="end"
            android:maxLines="1"
            android:translationY="@dimen/dp_12"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Extreme Long Title"
            tools:visibility="gone" />

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/ivAuthorAvatar"
            android:layout_width="@dimen/dp_24"
            android:layout_height="@dimen/dp_24"
            android:scaleType="centerCrop"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tvAuthorName"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeAppearance="@style/round_corner_top_12dp"
            tools:src="@drawable/icon_default_avatar" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvAuthorName"
            style="@style/MetaTextView.S14.PoppinsMedium500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_8"
            android:layout_marginEnd="@dimen/dp_8"
            android:ellipsize="end"
            android:maxLines="1"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tvTopFollowBtn"
            app:layout_constraintStart_toEndOf="@id/ivAuthorAvatar"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Michael" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvTopFollowBtn"
            style="@style/MetaTextView.S12.PoppinsMedium500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_10"
            android:background="@drawable/bg_corner_s100_stroke_c1a1a1a_s05"
            android:drawableStart="@drawable/ic_add_1a1a1a_s12"
            android:drawablePadding="@dimen/dp_4"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp_13"
            android:paddingVertical="@dimen/dp_6"
            android:textColor="@color/color_1A1A1A"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Follow"
            tools:visibility="visible"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!--    <com.socialplay.gpark.ui.view.MetaTextView-->
    <!--        android:id="@+id/tvBuildBtn"-->
    <!--        style="@style/MetaTextView.S14"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:background="@drawable/bg_f6f6f6_round_24"-->
    <!--        android:drawableStart="@drawable/ic_game_detail_common_build"-->
    <!--        android:drawablePadding="@dimen/dp_4"-->
    <!--        android:gravity="center_vertical"-->
    <!--        android:paddingHorizontal="@dimen/dp_8"-->
    <!--        android:paddingVertical="@dimen/dp_6"-->
    <!--        android:text="@string/create_v2_build"-->
    <!--        app:layout_constraintBottom_toBottomOf="@id/tbl"-->
    <!--        app:layout_constraintEnd_toStartOf="@id/ivMoreBtn"-->
    <!--        app:layout_constraintTop_toTopOf="@id/tbl"-->
    <!--        app:layout_goneMarginEnd="@dimen/dp_16"-->
    <!--        tools:visibility="gone" />-->

    <ImageView
        android:id="@+id/ivMoreBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingHorizontal="@dimen/dp_10"
        android:layout_marginEnd="@dimen/dp_6"
        android:paddingVertical="@dimen/dp_12"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tbl"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tbl"
        tools:src="@drawable/icon_game_detail_white_more_guest"
        tools:visibility="visible" />

    <View
        android:id="@+id/v_divider_enter"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginBottom="@dimen/dp_54"
        android:background="@color/color_EEEEEE"
        app:layout_constraintBottom_toBottomOf="parent" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvReplyHint2"
        style="@style/MetaTextView.S14"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_38"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_8"
        android:background="@drawable/shape_f5f5f5_corner_360"
        android:drawableStart="@drawable/icon_game_detail_comments"
        android:drawablePadding="@dimen/dp_4"
        android:gravity="center_vertical"
        android:paddingHorizontal="@dimen/dp_16"
        android:paddingVertical="@dimen/dp_9"
        android:text="@string/post_reply"
        android:textColor="@color/color_BDBDBD"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/iv_like"
        app:layout_constraintStart_toStartOf="parent"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/ivImageBtn2"
        android:layout_width="@dimen/dp_36"
        android:layout_height="0dp"
        android:paddingStart="@dimen/dp_6"
        android:paddingEnd="@dimen/dp_6"
        android:src="@drawable/ic_game_detail_common_image"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tvReplyHint2"
        app:layout_constraintEnd_toStartOf="@id/ivEmojiBtn2"
        app:layout_constraintTop_toTopOf="@id/tvReplyHint2"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/ivEmojiBtn2"
        android:layout_width="@dimen/dp_36"
        android:layout_height="0dp"
        android:layout_marginEnd="@dimen/dp_10"
        android:paddingStart="@dimen/dp_6"
        android:paddingEnd="@dimen/dp_6"
        android:src="@drawable/ic_game_detail_common_emoji"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tvReplyHint2"
        app:layout_constraintEnd_toEndOf="@id/tvReplyHint2"
        app:layout_constraintTop_toTopOf="@id/tvReplyHint2"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/iv_like"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_marginStart="@dimen/dp_20"
        android:src="@drawable/ic_game_detail_common_like"
        app:layout_constraintBottom_toTopOf="@id/tv_like"
        app:layout_constraintStart_toEndOf="@id/tvReplyHint2"
        app:layout_constraintTop_toTopOf="@id/tvReplyHint2"
        app:layout_constraintVertical_chainStyle="packed" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_like"
        style="@style/MetaTextView.S10.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/color_1A1A1A"
        android:textSize="@dimen/sp_10"
        app:layout_constraintBottom_toBottomOf="@id/tvReplyHint2"
        app:layout_constraintEnd_toEndOf="@id/iv_like"
        app:layout_constraintStart_toStartOf="@id/iv_like"
        app:layout_constraintTop_toBottomOf="@id/iv_like"
        tools:text="24" />

    <View
        android:id="@+id/layer_like"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="-10dp"
        android:layout_marginEnd="-10dp"
        app:constraint_referenced_ids="iv_like, tv_like"
        app:layout_constraintBottom_toBottomOf="@id/tv_like"
        app:layout_constraintEnd_toEndOf="@id/iv_like"
        app:layout_constraintStart_toStartOf="@id/iv_like"
        app:layout_constraintTop_toTopOf="@id/iv_like"
        tools:visibility="gone" />

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lav_like_anim"
        android:layout_width="@dimen/dp_36"
        android:layout_height="@dimen/dp_36"
        android:translationX="-0.3dp"
        android:translationY="-0.4dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/iv_like"
        app:layout_constraintEnd_toEndOf="@id/iv_like"
        app:layout_constraintStart_toStartOf="@id/iv_like"
        app:layout_constraintTop_toTopOf="@id/iv_like"
        app:lottie_autoPlay="false"
        app:lottie_fileName="game_detail_like.zip"
        app:lottie_progress="1" />

    <ImageView
        android:id="@+id/iv_light_up"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_marginStart="@dimen/dp_20"
        android:src="@drawable/ic_light_up"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/tv_light_up"
        app:layout_constraintStart_toEndOf="@id/iv_like"
        app:layout_constraintTop_toTopOf="@id/tvReplyHint2"
        app:layout_constraintVertical_chainStyle="packed"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_light_up"
        style="@style/MetaTextView.S10.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/color_1A1A1A"
        android:textSize="@dimen/sp_10"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tvReplyHint2"
        app:layout_constraintEnd_toEndOf="@id/iv_light_up"
        app:layout_constraintStart_toStartOf="@id/iv_light_up"
        app:layout_constraintTop_toBottomOf="@id/iv_light_up"
        tools:text="24"
        tools:visibility="visible" />

    <View
        android:id="@+id/layer_light_up"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="-10dp"
        android:layout_marginEnd="-10dp"
        android:visibility="gone"
        app:constraint_referenced_ids="iv_light_up, tv_light_up"
        app:layout_constraintBottom_toBottomOf="@id/tv_light_up"
        app:layout_constraintEnd_toEndOf="@id/iv_light_up"
        app:layout_constraintStart_toStartOf="@id/iv_light_up"
        app:layout_constraintTop_toTopOf="@id/iv_light_up"
        tools:visibility="gone" />

    <ImageView
        android:id="@+id/iv_comment"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_marginStart="@dimen/dp_20"
        android:src="@drawable/ic_game_detail_common_comment"
        android:visibility="visible"
        app:layout_constraintBottom_toTopOf="@id/tv_comment"
        app:layout_constraintStart_toEndOf="@id/iv_light_up"
        app:layout_constraintTop_toTopOf="@id/tvReplyHint2"
        app:layout_constraintVertical_chainStyle="packed"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_comment"
        style="@style/MetaTextView.S10.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="@id/tvReplyHint2"
        app:layout_constraintEnd_toEndOf="@id/iv_comment"
        app:layout_constraintStart_toStartOf="@id/iv_comment"
        app:layout_constraintTop_toBottomOf="@id/iv_comment"
        tools:text="24"
        tools:visibility="visible" />

    <View
        android:id="@+id/layer_comment"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="-10dp"
        android:layout_marginEnd="-10dp"
        android:visibility="visible"
        app:constraint_referenced_ids="iv_comment, tv_comment"
        app:layout_constraintBottom_toBottomOf="@id/tv_comment"
        app:layout_constraintEnd_toEndOf="@id/iv_comment"
        app:layout_constraintStart_toStartOf="@id/iv_comment"
        app:layout_constraintTop_toTopOf="@id/iv_comment"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/ivSendFlowers"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_marginStart="@dimen/dp_20"
        android:src="@drawable/ic_game_detail_common_send_flower"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/tvSendFlowers"
        app:layout_constraintEnd_toStartOf="@id/spaceEnd"
        app:layout_constraintStart_toEndOf="@id/iv_comment"
        app:layout_constraintTop_toTopOf="@id/tvReplyHint2"
        app:layout_constraintVertical_chainStyle="packed"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvSendFlowers"
        style="@style/MetaTextView.S10.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tvReplyHint2"
        app:layout_constraintEnd_toEndOf="@id/ivSendFlowers"
        app:layout_constraintStart_toStartOf="@id/ivSendFlowers"
        app:layout_constraintTop_toBottomOf="@id/ivSendFlowers"
        tools:text="24"
        tools:visibility="visible" />

    <View
        android:id="@+id/layerSendFlowers"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="-10dp"
        android:layout_marginEnd="-10dp"
        android:visibility="gone"
        app:constraint_referenced_ids="ivSendFlowers, tvSendFlowers"
        app:layout_constraintBottom_toBottomOf="@id/tvSendFlowers"
        app:layout_constraintEnd_toEndOf="@id/ivSendFlowers"
        app:layout_constraintStart_toStartOf="@id/ivSendFlowers"
        app:layout_constraintTop_toTopOf="@id/ivSendFlowers"
        tools:visibility="visible" />

    <Space
        android:id="@+id/spaceEnd"
        android:layout_width="@dimen/dp_20"
        android:layout_height="@dimen/dp_1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvReplyHint2" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clSendFlowerThanks"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/clTitleBar"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/iv_thanks_arrow"
            android:layout_width="@dimen/dp_14"
            android:layout_height="@dimen/dp_8"
            android:layout_marginStart="@dimen/dp_54"
            android:layout_marginTop="@dimen/dp_4"
            android:src="@drawable/send_flower_thanks_dialog_arrow"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_thanks"
            style="@style/MetaTextView.S14.PoppinsRegular400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_32"
            android:background="@drawable/send_flower_thanks_dialog_bg"
            android:maxWidth="@dimen/dp_232"
            android:padding="@dimen/dp_12"
            android:textColor="@color/white"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_thanks_arrow"
            tools:text="@string/send_flowers_thank_text" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/lv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/white"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tbl" />

</androidx.constraintlayout.widget.ConstraintLayout>