<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="@dimen/dp_104"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:orientation="horizontal"
    android:paddingEnd="@dimen/dp_8">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_screenshot"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_38"
        android:layout_marginStart="@dimen/dp_18"
        android:padding="@dimen/dp_1"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/round_corner_4dp"
        app:strokeColor="@color/color_d9d9d9"
        app:strokeWidth="@dimen/dp_1"
        tools:layout_width="@dimen/dp_78"
        tools:src="@color/white" />

    <View
        android:layout_width="@dimen/dp_1"
        android:layout_height="@dimen/dp_38"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="@dimen/dp_18"
        android:background="@drawable/bg_d9d9d9_round_05"
        app:layout_constraintBottom_toBottomOf="@id/iv_screenshot"
        app:layout_constraintStart_toEndOf="@id/iv_screenshot"
        app:layout_constraintTop_toTopOf="@id/iv_screenshot" />

</androidx.constraintlayout.widget.ConstraintLayout>
