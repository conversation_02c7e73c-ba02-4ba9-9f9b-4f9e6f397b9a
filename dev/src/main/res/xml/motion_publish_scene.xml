<?xml version="1.0" encoding="utf-8"?>
<MotionScene xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ConstraintSet android:id="@+id/start">

        <Constraint
            android:id="@+id/slChoosePublishScene"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/tab_layout_height"
            app:layout_constraintBottom_toTopOf="@id/space"
            app:layout_constraintEnd_toEndOf="parent" />
        <Constraint android:id="@+id/vBackground">
            <CustomAttribute
                app:attributeName="Alpha"
                app:customFloatValue="0.0" />
        </Constraint>
        <Constraint
            android:id="@+id/ivPublish"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="@id/slChoosePublishScene"
            app:layout_constraintEnd_toEndOf="@id/slChoosePublishScene"
            app:layout_constraintStart_toStartOf="@id/slChoosePublishScene"
            app:layout_constraintTop_toTopOf="@id/slChoosePublishScene">
            <CustomAttribute
                app:attributeName="Rotation"
                app:customFloatValue="-45" />
        </Constraint>
        <Constraint
            android:id="@+id/tvTemplate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:visibilityMode="ignore"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_16"
            android:layout_marginBottom="@dimen/dp_12"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@id/tvPost"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">
            <CustomAttribute
                app:attributeName="TranslationY"
                app:customDimension="@dimen/dp_100" />
            <CustomAttribute
                app:attributeName="Alpha"
                app:customFloatValue="0" />
        </Constraint>
        <Constraint
            android:id="@+id/tvPost"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_16"
            android:layout_marginBottom="@dimen/dp_13"
            app:layout_constraintBottom_toTopOf="@id/slChoosePublishScene"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">
            <CustomAttribute
                app:attributeName="TranslationY"
                app:customDimension="@dimen/dp_120" />
            <CustomAttribute
                app:attributeName="Alpha"
                app:customFloatValue="0" />
        </Constraint>
    </ConstraintSet>

    <ConstraintSet android:id="@+id/end">
        <Constraint
            android:id="@+id/slChoosePublishScene"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/tab_layout_height"
            app:layout_constraintBottom_toTopOf="@id/space"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
        <Constraint android:id="@+id/vBackground">
            <CustomAttribute
                app:attributeName="Alpha"
                app:customFloatValue="1.0" />
        </Constraint>
        <Constraint
            android:id="@+id/ivPublish"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="@id/slChoosePublishScene"
            app:layout_constraintEnd_toEndOf="@id/slChoosePublishScene"
            app:layout_constraintStart_toStartOf="@id/slChoosePublishScene"
            app:layout_constraintTop_toTopOf="@id/slChoosePublishScene">
            <CustomAttribute
                app:attributeName="Rotation"
                app:customFloatValue="-45" />
        </Constraint>
        <Constraint
            android:id="@+id/tvTemplate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:visibilityMode="ignore"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_16"
            android:visibility="gone"
            android:layout_marginBottom="@dimen/dp_12"
            app:layout_constraintBottom_toTopOf="@id/tvPost"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">
            <CustomAttribute
                app:attributeName="TranslationY"
                app:customDimension="0dp" />
            <CustomAttribute
                app:attributeName="Alpha"
                app:customFloatValue="1" />
        </Constraint>
        <Constraint
            android:id="@+id/tvPost"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_16"
            android:layout_marginBottom="@dimen/dp_13"
            app:layout_constraintBottom_toTopOf="@id/slChoosePublishScene"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">
            <CustomAttribute
                app:attributeName="TranslationY"
                app:customDimension="0dp" />
            <CustomAttribute
                app:attributeName="Alpha"
                app:customFloatValue="1" />
        </Constraint>
    </ConstraintSet>

    <Transition
        app:constraintSetEnd="@id/end"
        app:constraintSetStart="@id/start"
        app:duration="200"
        app:motionInterpolator="linear" />
</MotionScene>