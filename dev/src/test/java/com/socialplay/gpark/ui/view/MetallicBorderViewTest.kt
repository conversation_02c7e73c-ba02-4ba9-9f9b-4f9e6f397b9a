package com.socialplay.gpark.ui.view

import android.content.Context
import android.graphics.Color
import androidx.test.core.app.ApplicationProvider
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * MetallicBorderView 单元测试
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [28])
class MetallicBorderViewTest {

    private lateinit var context: Context
    private lateinit var metallicBorderView: MetallicBorderView

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        metallicBorderView = MetallicBorderView(context)
    }

    @Test
    fun testDefaultValues() {
        // 测试默认值
        assert(metallicBorderView.getLightAngle() == 45f)
        assert(metallicBorderView.getBorderThickness() > 0f)
        assert(metallicBorderView.getCornerRadius() == 0f)
    }

    @Test
    fun testSetLightAngle() {
        // 测试设置光照角度
        metallicBorderView.setLightAngle(90f)
        assert(metallicBorderView.getLightAngle() == 90f)

        // 测试角度超过360度的情况
        metallicBorderView.setLightAngle(450f)
        assert(metallicBorderView.getLightAngle() == 90f) // 450 % 360 = 90
    }

    @Test
    fun testSetBorderThickness() {
        // 测试设置边框厚度
        val thickness = 10f
        metallicBorderView.setBorderThickness(thickness)
        assert(metallicBorderView.getBorderThickness() == thickness)
    }

    @Test
    fun testSetCornerRadius() {
        // 测试设置圆角半径
        val radius = 16f
        metallicBorderView.setCornerRadius(radius)
        assert(metallicBorderView.getCornerRadius() == radius)
    }

    @Test
    fun testSetHighlightIntensity() {
        // 测试设置高光强度
        metallicBorderView.setHighlightIntensity(0.5f)
        // 由于没有getter方法，我们只测试方法不会崩溃
        
        // 测试边界值
        metallicBorderView.setHighlightIntensity(-0.1f) // 应该被限制为0
        metallicBorderView.setHighlightIntensity(1.1f)  // 应该被限制为1
    }

    @Test
    fun testSetShadowIntensity() {
        // 测试设置阴影强度
        metallicBorderView.setShadowIntensity(0.3f)
        // 由于没有getter方法，我们只测试方法不会崩溃
        
        // 测试边界值
        metallicBorderView.setShadowIntensity(-0.1f) // 应该被限制为0
        metallicBorderView.setShadowIntensity(1.1f)  // 应该被限制为1
    }

    @Test
    fun testSetMetallicBaseColor() {
        // 测试设置金属基础颜色
        metallicBorderView.setMetallicBaseColor(Color.BLUE)
        // 由于没有getter方法，我们只测试方法不会崩溃
    }

    @Test
    fun testAnimationMethods() {
        // 测试动画方法不会崩溃
        metallicBorderView.animateLightAngle(180f, 1000)
        metallicBorderView.startContinuousRotation(2000)
        metallicBorderView.stopContinuousRotation()
    }

    @Test
    fun testViewMeasurement() {
        // 测试View的测量
        val widthSpec = android.view.View.MeasureSpec.makeMeasureSpec(200, android.view.View.MeasureSpec.EXACTLY)
        val heightSpec = android.view.View.MeasureSpec.makeMeasureSpec(100, android.view.View.MeasureSpec.EXACTLY)
        
        metallicBorderView.measure(widthSpec, heightSpec)
        
        assert(metallicBorderView.measuredWidth == 200)
        assert(metallicBorderView.measuredHeight == 100)
    }
}
