package com.meta.box.party.douyinapi

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.bytedance.sdk.open.aweme.CommonConstants
import com.bytedance.sdk.open.aweme.common.handler.IApiEventHandler
import com.bytedance.sdk.open.aweme.common.model.BaseReq
import com.bytedance.sdk.open.aweme.common.model.BaseResp
import com.bytedance.sdk.open.aweme.share.Share
import com.bytedance.sdk.open.douyin.DouYinOpenApiFactory
import com.bytedance.sdk.open.douyin.api.DouYinOpenApi
import com.socialplay.gpark.data.model.share.ShareResult
import com.socialplay.gpark.function.share.callback.DouYinShareCallbackActivity
import com.socialplay.gpark.function.share.platform.ShareHelper
import timber.log.Timber

class DouYinEntryActivity : AppCompatActivity(), IApiEventHandler {
    private var douYinOpenApi: DouYinOpenApi? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Timber.tag("DouYinEntryActivity")
        douYinOpenApi = DouYinOpenApiFactory.create(this)
        douYinOpenApi?.handleIntent(intent, this)
        finish()
    }

    override fun onResp(resp: BaseResp?) {
        when (resp?.type) {
            CommonConstants.ModeType.SHARE_CONTENT_TO_TT_RESP -> {
                resp as Share.Response
                // 抖音940以后，错误码除了response.errorCode字段，还增加了response.subErrorCode字段，帮助三方排查错误原因
                Timber.d("分享失败,errorCode: ${resp.errorCode} subcode = ${resp.subErrorCode} Error Msg : ${resp.errorMsg}")

                if (resp.isSuccess) {
                    ShareResult.notifySuccess(
                        DouYinShareCallbackActivity.reqId,
                        DouYinShareCallbackActivity.platform,
                    )
                } else if (resp.isCancel) {
                    ShareResult.notifyCancel(
                        DouYinShareCallbackActivity.reqId,
                        DouYinShareCallbackActivity.platform,
                    )
                } else {
                    ShareResult.notifyFail(
                        DouYinShareCallbackActivity.reqId,
                        DouYinShareCallbackActivity.platform,
                        ShareHelper.CODE_SDK_ERROR,
                        resp.errorMsg,
                        resp.errorCode,
                        resp.subErrorCode
                    )
                }
            }

            CommonConstants.ModeType.SHARE_TO_CONTACT_RESP -> {
                if (resp.isSuccess) {
                    ShareResult.notifySuccess(
                        DouYinShareCallbackActivity.reqId,
                        DouYinShareCallbackActivity.platform,
                    )
                } else if (resp.isCancel) {
                    ShareResult.notifyCancel(
                        DouYinShareCallbackActivity.reqId,
                        DouYinShareCallbackActivity.platform,
                    )
                } else {
                    ShareResult.notifyFail(
                        DouYinShareCallbackActivity.reqId,
                        DouYinShareCallbackActivity.platform,
                        ShareHelper.CODE_SDK_ERROR,
                        resp.errorMsg,
                        resp.errorCode
                    )
                }
            }
        }
        finish()
    }

    override fun onErrorIntent(intent: Intent?) {
        Timber.d("douyin Intent error")
    }

    override fun onReq(req: BaseReq?) {
        Timber.d("douyin onReq: ${req?.type}")
    }
}