package com.socialplay.gpark.function.locale

import android.content.Context
import com.socialplay.gpark.data.model.locale.LanguageOption
import com.socialplay.gpark.data.model.locale.MetaLanguageItem
import com.socialplay.gpark.function.locale.MetaLanguages.getFollowSystemLanguage

object MetaLanguagesWrapper {
    fun getDefaultLanguage(): MetaLanguageItem {
        return MetaLanguageItem.SIMPLIFIED_CHINESE
    }

    fun getSupportLanguageOpts(context: Context): List<LanguageOption> {
        return listOf(
            LanguageOption(getFollowSystemLanguage(context), true),
            LanguageOption(MetaLanguageItem.SIMPLIFIED_CHINESE, false),
//            LanguageOption(MetaLanguageItem.TRADITIONAL_CHINESE, false),
//            LanguageOption(MetaLanguageItem.SIMPLIFIED_CHINESE, false)
        )
    }
}