package com.socialplay.gpark.function.deeplink.linkhandler

import android.net.Uri
import com.socialplay.gpark.function.MetaRouterWrapper
import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.deeplink.LinkHandleResult
import com.socialplay.gpark.function.deeplink.LinkHandler
import com.socialplay.gpark.function.deeplink.LinkHandlerChain
import com.socialplay.gpark.function.deeplink.MetaDeepLink
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.util.extension.is233Scheme
import com.socialplay.gpark.util.isHttp

class WebLinkLinkHandler : LinkHandler {
    override fun handle(chain: LinkHandlerChain, data: LinkData): LinkHandleResult {
        val uriString = data.uri.toString()
        val uriEncode = Uri.parse(uriString.replace("#", MetaRouterWrapper.Web.replaceChar))
        var url = kotlin.runCatching { uriString.split("url=").get(1) }.getOrNull()?:uriEncode.getQueryParameter("url")
        url = url?.replace(MetaRouterWrapper.Web.replaceChar, "#")
        url = Uri.decode(url)
        if (url.isNullOrEmpty()) {
            return LinkHandleResult.Failed("url invalid")
        }
        if (url.is233Scheme()) {
            chain.reset()
            return MetaDeepLink.handle(
                data.activity,
                data.navHost,
                data.selector,
                Uri.parse(url),
                data.source.orEmpty()
            )
        } else if (url.isHttp()) {
            MetaRouter.Web.navigate(data.navHost, url = url, isWebOutside = data.uri.getQueryParameter(MetaDeepLink.PARAM_BROWSER) == "1")
        } else {
            return LinkHandleResult.Failed("url invalid")
        }
        return LinkHandleResult.Success
    }
}