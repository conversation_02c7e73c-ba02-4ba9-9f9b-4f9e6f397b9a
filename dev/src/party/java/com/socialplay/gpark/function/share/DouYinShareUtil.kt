package com.socialplay.gpark.function.share

import android.app.Activity
import android.content.Intent
import androidx.core.content.FileProvider
import com.bytedance.sdk.open.aweme.base.ImageObject
import com.bytedance.sdk.open.aweme.base.MediaContent
import com.bytedance.sdk.open.aweme.base.MixObject
import com.bytedance.sdk.open.aweme.base.ShareParam
import com.bytedance.sdk.open.aweme.base.TitleObject
import com.bytedance.sdk.open.aweme.base.VideoObject
import com.bytedance.sdk.open.aweme.share.Share
import com.bytedance.sdk.open.douyin.DouYinOpenApiFactory
import com.bytedance.sdk.open.douyin.ShareToContact
import com.bytedance.sdk.open.douyin.api.DouYinOpenApi
import com.bytedance.sdk.open.douyin.model.ContactHtmlObject
import com.socialplay.gpark.R
import com.socialplay.gpark.util.FileProviderUtil
import com.socialplay.gpark.util.InstallUtil
import com.socialplay.gpark.util.ToastUtil
import timber.log.Timber
import java.io.File

object DouYinShareUtil {

    /**
     * https://developer.open-douyin.com/docs/resource/zh-CN/dop/develop/sdk/mobile-app/share/android
     */
    fun shareImagesOrVideosWithGrant(
        activity: Activity,
        paths: List<String>,
        hasVideo: Boolean,
        hasImage: Boolean,
        hashtags: List<String>?,
        title: String? = null,
        shortTitle: String? = null
    ): Boolean {
        val fileList = paths.map { File(it) }
        if (fileList.isEmpty()) {
            ToastUtil.showShort(R.string.share_failed_retry_later)
            return false
        }

        val uriList = fileList.map { FileProvider.getUriForFile(activity, activity.packageName + ".fileprovider", it) }.filterNotNull()
        uriList.forEach {
            activity.grantUriPermission(InstallUtil.PACKAGE_DOUYIN, it, Intent.FLAG_GRANT_READ_URI_PERMISSION)
            activity.grantUriPermission(InstallUtil.PACKAGE_DOUYIN_LITE, it, Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }
        val stringUri = uriList.map { it.toString() }
        Timber.d("check_img ${stringUri}")
        val api = DouYinOpenApiFactory.create(activity)
        val content = MediaContent()
        val mix = hasVideo && hasImage
        if (hasVideo && !mix) {
            // 分享图片
            content.mMediaObject = VideoObject().apply {
                mVideoPaths = ArrayList(stringUri)
            }
        } else if (hasImage && !mix){
            // 分享图片
            content.mMediaObject = ImageObject().apply {
                mImagePaths = ArrayList(stringUri)
            }
        } else if (mix && api.isAppSupportMixShare) {
            content.mMediaObject = MixObject().apply {
                mMediaPaths = ArrayList(stringUri)
            }
        } else {
            ToastUtil.showShort(R.string.share_failed_retry_later)
            return false
        }
        val request = Share.Request()
        request.mMediaContent = content
        request.mState = "ss"
        hashtags?.let {
            request.mHashTagList = ArrayList(it)
        }
        if (api.isAppSupportShareToPublish) {
            request.shareToPublish = true
        }
        if (!title.isNullOrBlank() || !shortTitle.isNullOrBlank()) {
            request.newShare = true
            request.shareParam = ShareParam()
            request.shareParam.titleObject = TitleObject()
            request.shareParam.titleObject.title = title
            request.shareParam.titleObject.shortTitle = shortTitle
        }
        api.share(request)
        return true
    }

    fun shareImageOrVideo(
        api: DouYinOpenApi,
        isVideo: Boolean,
        paths: ArrayList<String>,
        tagList: ArrayList<String>? = null,
        title: String? = null
    ): Boolean {
        // 初始化资源路径，路径请提供绝对路径.demo里有获取绝对路径的util代码

        val request = Share.Request()
        val content = MediaContent()
        if (isVideo) {
            // 分享视频
            val videoObject = VideoObject()
            videoObject.mVideoPaths = paths
            content.mMediaObject = videoObject
        } else {
            // 分享图片
            val imageObject = ImageObject()
            imageObject.mImagePaths = paths
            content.mMediaObject = imageObject
        }
        request.mMediaContent = content
        request.mState = "ss"
        //拼接分享内容
        request.mHashTagList = tagList
        if (!title.isNullOrBlank()) {
            request.newShare = true
            request.shareParam = ShareParam()
            request.shareParam.titleObject = TitleObject()
            request.shareParam.titleObject.title = title
        }
        // 调起分享
        return api.share(request)
    }

    fun shareWeb(
        activity: Activity,
        url: String,
        title: String,
        desc: String,
        icon: String?
    ): Boolean {
        val api = DouYinOpenApiFactory.create(activity)
        val htmlObject = ContactHtmlObject()
        htmlObject.html = url
        htmlObject.title = title
        htmlObject.discription = desc
        htmlObject.thumbUrl = icon
        val request = ShareToContact.Request()
        request.htmlObject = htmlObject
        return if (api.isAppSupportShareToContacts) {
            api.shareToContacts(request)
            true
        } else {
            ToastUtil.showShort(R.string.douyin_ver_not_supported)
            false
        }
    }

    fun shareImageMessage(
        activity: Activity,
        path: String
    ): Boolean {
        val api = DouYinOpenApiFactory.create(activity)
        if (!api.isAppSupportShareToContacts) {
            ToastUtil.showShort(R.string.douyin_ver_not_supported)
            return false
        }
        val stringUri = FileProviderUtil.getValidPathV2(
            activity,
            path,
            InstallUtil.PACKAGE_DOUYIN,
            InstallUtil.PACKAGE_DOUYIN_LITE
        )
        if (stringUri.isNullOrEmpty()) {
            ToastUtil.showShort(R.string.share_failed_retry_later)
            return false
        }
        Timber.d("check_img ${stringUri}")
        val imageObject = ImageObject(arrayListOf(stringUri))
        val mediaContent = MediaContent(imageObject)
        val request = ShareToContact.Request()
        request.mMediaContent = mediaContent
        api.shareToContacts(request)
        return true
    }
}