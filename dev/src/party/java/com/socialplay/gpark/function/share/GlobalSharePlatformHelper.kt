package com.socialplay.gpark.function.share

import android.content.Context
import com.socialplay.gpark.data.model.share.SharePlatform
import com.socialplay.gpark.data.model.share.ShareRawData
import com.socialplay.gpark.util.InstallUtil

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2025/01/22
 *     desc   :
 * </pre>
 */
object GlobalSharePlatformHelper {

    const val ENABLE_VIDEO_FEED_LANDING_PAGE = true

    fun checkInstallation(context: Context, platform: String): Boolean {
        return when (platform) {
            SharePlatform.PLATFORM_WECHAT_FRIEND,
            SharePlatform.PLATFORM_WECHAT_MOMENT -> {
                com.meta.share.util.InstallUtil.isWeChatInstalled(context)
            }

            SharePlatform.PLATFORM_QQ_FRIEND,
            SharePlatform.PLATFORM_QQ_ZONE -> {
                com.meta.share.util.InstallUtil.isQQFamiliesInstalled(context).first
            }

            SharePlatform.PLATFORM_DOUYIN_PUBLISH,
            SharePlatform.PLATFORM_DOUYIN_FRIEND -> {
                InstallUtil.isInstalledDouyinFamilies4ShareSdk(context).first
            }

            SharePlatform.PLATFORM_KUAISHOU_PUBLISH,
            SharePlatform.PLATFORM_KUAISHOU_FRIEND -> {
                InstallUtil.isInstalledKuaishouFamilies(context).first
            }

            SharePlatform.PLATFORM_XIAOHONGSHU_PUBLISH -> {
                InstallUtil.isInstalledXhs(context)
            }

            else -> {
                true
            }
        }
    }

    fun profile(isMe: Boolean, platforms: ArrayList<SharePlatform>) {
        if (isMe) {
            platforms.add(SharePlatform.friend())
            platforms.add(SharePlatform.wechatFriend())
            platforms.add(SharePlatform.qqFriend())
            platforms.add(SharePlatform.save())
            platforms.add(SharePlatform.system())
        } else {
            platforms.add(SharePlatform.friend())
        }
    }

    fun pgc(raw: ShareRawData, platforms: ArrayList<SharePlatform>) {
        platforms.add(SharePlatform.post())
        val hasImage = raw.hasImage
        if (hasImage) {
            platforms.add(SharePlatform.kuaishou())
            platforms.add(SharePlatform.xiaohongshu())
            platforms.add(SharePlatform.douyin())
        }
        platforms.add(SharePlatform.wechatMoments())
        platforms.add(SharePlatform.qzone())
        platforms.add(SharePlatform.friend())
        platforms.add(SharePlatform.wechatFriend())
        platforms.add(SharePlatform.qqFriend())
        platforms.add(SharePlatform.longImage())
        if (hasImage) {
            platforms.add(SharePlatform.save())
        }
        platforms.add(SharePlatform.link())
        if (hasImage) {
            platforms.add(SharePlatform.system())
        }
    }

    fun pgcLongImage(platforms: ArrayList<SharePlatform>) {
        platforms.add(SharePlatform.wechatMoments())
        platforms.add(SharePlatform.qzone())
        platforms.add(SharePlatform.wechatFriend())
        platforms.add(SharePlatform.qqFriend())
        platforms.add(SharePlatform.save())
    }

    fun ugc(raw: ShareRawData, platforms: ArrayList<SharePlatform>) {
        platforms.add(SharePlatform.post())
        val hasImage = raw.hasImage
        if (hasImage) {
            platforms.add(SharePlatform.kuaishou())
            platforms.add(SharePlatform.xiaohongshu())
            platforms.add(SharePlatform.douyin())
        }
        platforms.add(SharePlatform.wechatMoments())
        platforms.add(SharePlatform.qzone())
        platforms.add(SharePlatform.friend())
        platforms.add(SharePlatform.wechatFriend())
        platforms.add(SharePlatform.qqFriend())
        platforms.add(SharePlatform.longImage())
        if (hasImage) {
            platforms.add(SharePlatform.save())
        }
        platforms.add(SharePlatform.link())
        if (hasImage) {
            platforms.add(SharePlatform.system())
        }
    }

    fun ugcLongImage(platforms: ArrayList<SharePlatform>) {
        platforms.add(SharePlatform.wechatMoments())
        platforms.add(SharePlatform.qzone())
        platforms.add(SharePlatform.wechatFriend())
        platforms.add(SharePlatform.qqFriend())
        platforms.add(SharePlatform.save())
    }

    fun postDetail(raw: ShareRawData, platforms: ArrayList<SharePlatform>) {
        if (raw.hasMedia) {
            platforms.add(SharePlatform.kuaishou())
            platforms.add(SharePlatform.xiaohongshu())
            platforms.add(SharePlatform.douyin())
        }
        platforms.add(SharePlatform.wechatMoments())
        platforms.add(SharePlatform.qzone())
        platforms.add(SharePlatform.friend())
//        platforms.add(SharePlatform.kuaishouFriend())
        platforms.add(SharePlatform.douyinFriend())
        platforms.add(SharePlatform.wechatFriend())
        platforms.add(SharePlatform.qqFriend())
        platforms.add(SharePlatform.link())
        platforms.add(SharePlatform.system())
    }

    fun videoFeed(platforms: ArrayList<SharePlatform>) {
        platforms.add(SharePlatform.friend())
        platforms.add(SharePlatform.save())
        if (ENABLE_VIDEO_FEED_LANDING_PAGE) {
            platforms.add(SharePlatform.link())
        }
        platforms.add(SharePlatform.system())
    }

    fun ocMoment(raw: ShareRawData, platforms: ArrayList<SharePlatform>) {
        if (raw.hasVideo) {
            platforms.add(SharePlatform.kuaishou())
            platforms.add(SharePlatform.xiaohongshu())
            platforms.add(SharePlatform.douyin())
            platforms.add(SharePlatform.wechatMoments())
            platforms.add(SharePlatform.qzone())
            platforms.add(SharePlatform.wechatFriend())
            platforms.add(SharePlatform.qqFriend())
            platforms.add(SharePlatform.save())
            platforms.add(SharePlatform.system())
        } else if (raw.hasImage) {
            platforms.add(SharePlatform.kuaishou())
            platforms.add(SharePlatform.xiaohongshu())
            platforms.add(SharePlatform.douyin())
            platforms.add(SharePlatform.wechatMoments())
            platforms.add(SharePlatform.qzone())
            platforms.add(SharePlatform.wechatFriend())
            platforms.add(SharePlatform.qqFriend())
            platforms.add(SharePlatform.save())
            platforms.add(SharePlatform.system())
        }
    }

    fun screenshot(raw: ShareRawData, platforms: ArrayList<SharePlatform>) {
        if (raw.hasImage) {
            platforms.add(SharePlatform.kuaishou())
            platforms.add(SharePlatform.xiaohongshu())
            platforms.add(SharePlatform.douyin())
            platforms.add(SharePlatform.wechatMoments())
            platforms.add(SharePlatform.qzone())
            platforms.add(SharePlatform.friend())
            platforms.add(SharePlatform.wechatFriend())
            platforms.add(SharePlatform.qqFriend())
            platforms.add(SharePlatform.save())
            platforms.add(SharePlatform.system())
        }
    }

    fun assetDetail(platforms: ArrayList<SharePlatform>) {
        platforms.add(SharePlatform.post())
        platforms.add(SharePlatform.friend())
    }

    fun avatarShare(platforms: MutableList<SharePlatform>) {
        platforms.add(SharePlatform.kuaishou())
        platforms.add(SharePlatform.xiaohongshu())
        platforms.add(SharePlatform.douyin())
        platforms.add(SharePlatform.save())
        platforms.add(SharePlatform.system())
    }
}