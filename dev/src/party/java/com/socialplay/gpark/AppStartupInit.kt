package com.socialplay.gpark

import com.knightboot.spwaitkiller.SpWaitKiller
import com.meta.box.di.wechatModule
import com.meta.sdk.open.OpenApi
import com.meta.sdk.open.model.EnvType
import com.meta.share.MetaShare
import com.socialplay.gpark.app.initialize.BuglyInit
import com.socialplay.gpark.app.initialize.DouYinFlavorInit
import com.socialplay.gpark.app.initialize.DouYinInit
import com.socialplay.gpark.app.initialize.KwaiInit
import com.socialplay.gpark.app.initialize.TencentInit
import com.socialplay.gpark.app.initialize.XhsInit
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.DevEnvType
import com.socialplay.gpark.di.partyViewModelsModule
import com.socialplay.gpark.di.updateModule
import com.socialplay.gpark.function.overseabridge.bridge.AdSdkBridgeImpl
import com.socialplay.gpark.function.overseabridge.bridge.IAdSdkBridge
import com.socialplay.gpark.function.startup.StartupProcessType.AWO
import com.socialplay.gpark.function.startup.StartupProcessType.H
import com.socialplay.gpark.function.startup.StartupProcessType.M
import com.socialplay.gpark.function.startup.StartupProcessType.R
import com.socialplay.gpark.function.startup.core.plus
import com.socialplay.gpark.function.startup.core.project.Project
import com.socialplay.gpark.function.startup.dsl.task
import com.socialplay.gpark.function.startup.dsl.taskAsync
import com.socialplay.gpark.ui.dialog.DialogManagerInfo
import com.socialplay.gpark.ui.dialog.DialogScene
import com.socialplay.gpark.ui.dialog.DialogShowManager
import com.socialplay.gpark.ui.main.UpdateDialog
import com.socialplay.gpark.ui.realname.RealName
import com.socialplay.gpark.util.BugFixUtil
import com.socialplay.gpark.util.Reflection
import kotlinx.coroutines.Dispatchers
import org.koin.core.context.GlobalContext
import org.koin.core.module.Module
import org.koin.dsl.module
import timber.log.Timber

/**
 * 用于区分国内海外不同的初始化需求
 */
object AppStartupInit {
    fun koinModules(): List<Module> {
        // TODO 等功能迁移完之后才能动工这里
        return listOf(
            module {
                single<IAdSdkBridge> { AdSdkBridgeImpl() }
            },
            wechatModule,
            partyViewModelsModule,
            updateModule
        )
    }

    fun onCreateAnalyticsBefore(project: Project) {
    }

    fun onCreateAnalyticsAfter(project: Project) {

    }

    fun onCreate(project: Project) {
        // TODO 等功能迁移完之后才能动工这里
        project.bugly()
        project.leYuanAuthInit()
        project.douYinInit()
        project.douYinFlavorInit()
        project.kwaiInit()
        project.xhsInit()
        project.tencentInit()
        project.realName()
        project.shareInit()
    }

    fun onAttachContext(project: Project) {
        // TODO 等功能迁移完之后才能动工这里
        project.bugfix()
    }

    fun Project.bugly() = taskAsync("bugly", H + M + R, Dispatchers.IO) {
        BuglyInit.init(application, processType)
    }

    fun Project.douYinInit() = task("douYinInit", H) {
        DouYinInit.init()
    }

    fun Project.douYinFlavorInit() = task("douYinFlavorInit", H) {
        DouYinFlavorInit.init()
    }

    fun Project.kwaiInit() = task("kwaiInit", H) {
        KwaiInit.init(application)
    }

    fun Project.xhsInit() = taskAsync("xhsInit", H) {
        XhsInit.init(application)
    }

    fun Project.tencentInit() = taskAsync("tencentInit", H) {
        TencentInit.init()
    }

    fun Project.bugfix() = taskAsync("bugfix", AWO, Dispatchers.IO) {
        //豁免所有的反射黑名单限制，让我们可以通过反射访问黑名单中的API
        val isSuccess = Reflection.exemptAll()
        Timber.i("豁免hidden状态 $isSuccess")
        BugFixUtil.fixEmuiCrash()
        SpWaitKiller.builder(application)
            .build()
            .work()
    }

    fun Project.leYuanAuthInit() = task("leyuanInit", H) {
        val sdkEnvType = when (GlobalContext.get().get<MetaKV>().developer.envType) {
            DevEnvType.Dev, DevEnvType.Test -> EnvType.TEST
            DevEnvType.Pre -> EnvType.PRE
            else -> EnvType.ONLINE
        }
        val openSdkConfig = com.meta.sdk.open.model.OpenSdkConfig.Builder(BuildConfig.LE_YUAN_APP_KEY)
            .setLogDebug(BuildConfig.LOG_DEBUG)
            .setEnvType(sdkEnvType)
            .build()
        OpenApi.getInstance().init(application, openSdkConfig)
    }

    fun Project.realName() = taskAsync("realName", AWO, Dispatchers.IO) {
        RealName.init(application, matchProcess(H))
    }

    fun Project.shareInit() = task("shareInit", H) {
        MetaShare.init("${BuildConfig.APPLICATION_ID}.fileprovider")
    }

    suspend fun dialogManager() {
        DialogShowManager.registerDialog(UpdateDialog(), DialogManagerInfo(1000, listOf(DialogScene.MAIN_PAGE, DialogScene.GUIDE_LOGIN_PAGE)))
    }
}