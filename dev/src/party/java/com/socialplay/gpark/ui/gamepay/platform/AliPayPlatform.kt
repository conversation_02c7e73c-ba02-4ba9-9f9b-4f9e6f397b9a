package com.socialplay.gpark.ui.gamepay.platform

import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.LifecycleInteractor
import com.socialplay.gpark.data.model.pay.AgentPayType
import com.socialplay.gpark.data.model.pay.PayParamsParty
import com.socialplay.gpark.data.model.pay.PayResultEntity
import com.socialplay.gpark.function.pay.way.AliPay
import com.socialplay.gpark.function.pay.way.GamePayResultEvent
import com.socialplay.gpark.ui.gamepay.PayController
import com.socialplay.gpark.ui.gamepay.PayController.getCurrentActivity
import timber.log.Timber

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2021/06/02
 *     desc   : 支付宝支付
 *
 */
class AliPayPlatform : BasePayPlatformParty<PayParamsParty>() , IThirdPayCallBack<GamePayResultEvent> {
    override fun platformType(): Int = AgentPayType.PAY_TYPE_ALI

    override fun startPay(payResultEntity: PayResultEntity) {
        Timber.d("AliPayPlatform_startPay")
        if (getCurrentActivity() == null) {
            val errorMessage = LifecycleInteractor.activityRef?.get()?.getString(R.string.pay_fail_alipay_open_failed)?:""
            payFailed(errorMessage, AgentPayType.THIRD_PAY_FAILED)
            return
        }
        payInteractor.setGamePayResultCallBack(this)
        PayController.setIsThirdPaying(true)
        onStartThirdPay()
        AliPay.startPay(getCurrentActivity(), payResultEntity.aliPayInfo?.aliPayParam, payResultEntity.orderCode)
    }

    override fun onPayResult(gamePayResult: GamePayResultEvent) {
        if (getAgentPayParams()?.orderCode == gamePayResult.payOrderId) {
            Timber.d("支付宝支付结果:%s",gamePayResult.payStatus)
            setAgentPayParams(null)
            payInteractor.setGamePayResultCallBack(null)
            if (!PayController.getIsThirdPaying()) {
                return
            }
            PayController.setIsThirdPaying(false)
            if (gamePayResult.payStatus == 0) {
                paySuccess()
            } else {
                val errorMessage = LifecycleInteractor.activityRef?.get()?.getString(R.string.pay_fail_alipay_failed)?:""
                payFailed(errorMessage, AgentPayType.THIRD_PAY_FAILED)
            }
        }
    }
}