package com.socialplay.gpark.ui.recharge

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.meta.biz.ugc.model.RechargeArkMsg
import com.meta.biz.ugc.model.RechargeResultMgs
import com.socialplay.gpark.data.IMetaRepositoryWrapper
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.model.PartyEventConstants
import com.socialplay.gpark.data.model.Recharge
import com.socialplay.gpark.data.model.pay.AgentPayType
import com.socialplay.gpark.data.model.pay.AgentPayVersion
import com.socialplay.gpark.data.model.pay.MemberParams
import com.socialplay.gpark.data.model.pay.MemberVersion
import com.socialplay.gpark.data.model.pay.PayChannelInfo
import com.socialplay.gpark.data.model.pay.PayChannelList.Companion.getPayWayBean
import com.socialplay.gpark.data.model.pay.PayParamsParty
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.pay.IPayInteractor
import com.socialplay.gpark.function.pay.PartyPayInteractor
import com.socialplay.gpark.ui.gamepay.GamePayLifecycle
import com.socialplay.gpark.ui.gamepay.OnPayCallback
import com.socialplay.gpark.ui.gamepay.PayController
import com.socialplay.gpark.ui.task.PayQueryTask
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.extension.getCommodityId
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * create by: bin on 2022/12/8
 */
class RechargeViewModel(
    private val metaRepository: IMetaRepositoryWrapper,
    val application: Application,
    iPayInteractor: IPayInteractor
) : ViewModel() {

    private val payInteractor = iPayInteractor as PartyPayInteractor

    //    val recharges = metaRepository.getRecharges()
    private val _channelLiveData = MutableLiveData<List<PayChannelInfo>>()
    val channelLiveData = _channelLiveData
    private val _balance = MutableLiveData<Long?>()
    val balance: LiveData<Long?> = _balance
    val errorLiveData = MutableLiveData<Pair<Int, String>?>()
    val _extraLiveData = MutableLiveData<String?>()
    private val _isLoading = MutableLiveData(false)
    val isLoading: LiveData<Boolean> = _isLoading
    var arkMsg: RechargeArkMsg? = null
    var gid: Long = -1
    private var mPayParams: PayParamsParty? = null
    private var action: ((isSuccess:Boolean, params: PayParamsParty?, failedMessage:String?) -> Unit)? = null
    private val scopeIO = CoroutineScope(Dispatchers.IO)

    //    suspend fun gameInfo(gid:Long) = metaRepository.getGameInfoCache(gid)
    fun getPayChannel(selfPkgName: String) = viewModelScope.launch {
        metaRepository.getPayChannels(selfPkgName, AgentPayType.PAY_PARTY_SCENECODE, "", "")
            .collect() {
                val list = ArrayList<PayChannelInfo>()
                it.data?.channelList?.forEach { channel ->
                    val bean = getPayWayBean(application, channel, null)
                    if (bean != null) {
                        list.add(bean)
                    }
                }
                list.firstOrNull()?.isSelected = true
                _channelLiveData.value = list
            }
    }

    fun startPay(
        item: Recharge,
        payType: Int,
        gameId: String?,
        extra: String? = null,
        pageSource: String?= null,
        action: ((isSuccess:Boolean, params: PayParamsParty?, failedMessage:String?) -> Unit)? = null
    ) {
        _isLoading.value = true
        _extraLiveData.value = extra
        this.action = action
        // 会员支付相关参数
        val memberParams = MemberParams(
            amount = item.price ?: 0,
            productCode = item.id.toString(),
            payType = payType,
            grade = "",
            memberType = "",
            happyCoin = "",
            reward = item.name.toString(),
            sceneCode = AgentPayType.PAY_PARTY_SCENECODE,
            cpExtra = ""
        )
        val payParams = PayParamsParty(
            gameId = gameId,
            pName = memberParams.reward,
            pCode = memberParams.productCode,
            pCount = 1,
            pPrice = memberParams.amount,
            payChannel = memberParams.payType,
            memberParams = memberParams,
            agentPayVersion = AgentPayVersion.VERSION_OWN,
            cpExtra = memberParams.cpExtra,
            memberVersion = MemberVersion.VERSION_V1,
            pageSource = pageSource,
        )
        PayController.dispatchPay(payParams, object : OnPayCallback {
            override fun onPaySuccess(params: PayParamsParty?) {
                _isLoading.value = false
                Analytics.track(
                    PartyEventConstants.EVENT_PARTY_PAY_SUCCESS,
                    "balance" to ((balance.value) ?: 0),
                    "gameid" to gid.toString(),
                    "source" to if (gid <= 0) "personal" else "game",
                    "productid" to params?.pCode.toString(),
                    "product" to params?.pName.toString(),
                    "price" to params?.pPrice.toString(),
                    "cpoderid" to arkMsg?.cpOrderId.toString(),
                    "propprice" to arkMsg?.payAmount.toString(),
                    "orderid" to params?.orderCode.toString(),
                    "channel" to params?.payChannel.toString(),
                    "commodityid" to arkMsg?.getCommodityId().toString()
                )
//                ToastUtil.showShort(application.getString(R.string.pay_success))
                params?.getRealPrice()?.let { send2Ue(it, 200, "") }
            }

            override fun onPayFailed(params: PayParamsParty?, code: Int?, errorMessage: String?) {
                closeOrder(params)
                handlePayFailed(payParams, code, errorMessage)
            }

            override fun onStartThirdPay(params: PayParamsParty?) {
                mPayParams = params
                _isLoading.value = false
                Analytics.track(
                    PartyEventConstants.EVENT_PARTY_PAY_CLICK,
                    "source" to if (gid <= 0) "personal" else "game",
                    "gameid" to gid.toString(),
                    "price" to (item.price ?: 0),
                    "orderid" to arkMsg?.cpOrderId.toString(),
                    "productid" to arkMsg?.productCode.toString(),
                    "channel" to params?.payChannel.toString(),
                )
                Analytics.track(
                    PartyEventConstants.EVENT_PARTY_JUMP_THE_THIRD,
                    "source" to if (gid <= 0) "personal" else "game",
                    "gameid" to gid.toString(),
                    "price" to (item.price ?: 0),
                    "orderid" to arkMsg?.cpOrderId.toString(),
                    "productid" to arkMsg?.productCode.toString(),
                    "channel" to params?.payChannel.toString(),
                )
                rechargeLoop(params)
            }
        })
    }

    private fun handlePayFailed(params: PayParamsParty?, code: Int?, errorMessage: String?) {
        PayController.setIsPaying(false)
        PayController.setIsThirdPaying(false)
        _isLoading.value = false
        mPayParams = null
        params?.getRealPrice()?.let { send2Ue(it, code ?: -1, errorMessage) }
        Analytics.track(
            PartyEventConstants.EVENT_PARTY_PAY_FAIL,
            "balance" to ((balance.value) ?: 0),
            "gameid" to gid.toString(),
            "source" to if (gid <= 0) "personal" else "game",
            "productid" to params?.pCode.toString(),
            "product" to params?.pName.toString(),
            "price" to params?.pPrice.toString(),
            "cpoderid" to arkMsg?.cpOrderId.toString(),
            "propprice" to arkMsg?.payAmount.toString(),
            "orderid" to params?.orderCode.toString(),
            "channel" to params?.payChannel.toString(),
            "commodityid" to arkMsg?.getCommodityId().toString(),
            "failcode" to code.toString(),
            "failmassage" to errorMessage.toString(),
        )
        action?.invoke(false, params, errorMessage)
        action = null
    }

    fun cancelPay() {
        if (mPayParams != null && PayController.getIsThirdPaying()) {
            PayQueryTask.instance.setOnQueryCallback(null)
            PayQueryTask.instance.manualInterruptQuery()
            // 外部取消的支付不调用关闭订单的方法, 如果用户支付成功后, 因为网络问题, 轮询拿不到支付结果时, 容易出现订单误关闭
            // 所以这里不调用关闭订单的方法
            // closeOrder(mPayParams)
            handlePayFailed(mPayParams, AgentPayType.CHANEL_PAY, null)
        }
    }

    /**
     * 充值回调给游戏
     */
    fun send2Ue(amount: Int, code: Int, errorMessage: String?) {
        if (_extraLiveData.value != null) {
            // ts游戏支付回调
            val data = GsonUtil.gsonSafeParse<RechargeResultMgs>(_extraLiveData.value) ?: return
            val res = RechargeResultMgs(
                data.source ?: "",
                code,
                amount.toLong(),
                errorMessage,
                null,
                data.messageId ?: 0
            )
            GamePayLifecycle.sendRechargeResult2UE(res)
        }
    }

    /**
     * 充值结果轮询
     */
    fun rechargeLoop(params: PayParamsParty?) {
        if (params?.orderCode != null && PayController.getPay()) {
            PayQueryTask.instance.setOnQueryCallback(object : PayQueryTask.OnQueryCallback {
                override fun onPaySuccessCallback(orderId: String) {
//                    ToastUtil.showShort(application.getString(R.string.pay_success))
                    action?.invoke(true, params, null)
                    action = null
                    mPayParams = null
                    PayController.setIsPaying(false)
                    PayController.setIsThirdPaying(false)
                    viewModelScope.launch {
                        //发货有延时
                        delay(1200)
                        payInteractor.getArkNum { userBalance ->
                            _balance.value = userBalance?.leCoinNum
                            Analytics.track(
                                PartyEventConstants.EVENT_PARTY_RECHARGE_SUCCESS,
                                "balance" to (userBalance?.leCoinNum ?: 0),
                                "gameid" to gid.toString(),
                                "source" to if (gid <= 0) "personal" else "game",
                                "productid" to params.pCode.toString(),
                                "product" to params.pName.toString(),
                                "price" to params.pPrice.toString(),
                                "cpoderid" to arkMsg?.cpOrderId.toString(),
                                "propprice" to arkMsg?.payAmount.toString(),
                                "orderid" to orderId,
                                "channel" to params.payChannel,
                                "commodityid" to arkMsg?.getCommodityId().toString()
                            )
                        }

                    }
                    PayQueryTask.instance.setOnQueryCallback(null)
                    send2Ue(params.getRealPrice(), 200, "")
                }

            })
            //开始支付结果轮询
            PayQueryTask.instance.startWithTime(params.orderCode!!)
        }
    }

    private fun closeOrder(params: PayParamsParty?) {
        scopeIO.launch {
            params?.orderCode?.let { payInteractor.closeOrder(it).collect{} }
        }
    }


    override fun onCleared() {
        super.onCleared()
        PayQueryTask.instance.setOnQueryCallback(null)
    }

    fun setSelected(position: Int) {
        val value = _channelLiveData.value ?: return
        value.forEachIndexed { index, payChannelInfo ->
            payChannelInfo.isSelected = index == position
        }
        _channelLiveData.value = value
    }
}