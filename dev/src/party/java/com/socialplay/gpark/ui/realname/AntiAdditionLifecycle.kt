package com.socialplay.gpark.ui.realname

import android.app.Activity
import android.app.Application
import com.meta.lib.mwbiz.MWBizBridge
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.data.IMetaRepositoryWrapper
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.model.realname.PartyAnalytics
import com.socialplay.gpark.data.model.realname.RealNameSurplusGameTime
import com.socialplay.gpark.function.mw.lifecycle.MWLifecycle
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.core.context.GlobalContext
import timber.log.Timber

class AntiAdditionLifecycle(
    val metaApp: Application,
    private val exit: () -> Unit = {}
) : MWLifecycle() {
    private val userRepository: IMetaRepositoryWrapper by lazy {
        GlobalContext.get().get()
    }
    private val scope = MainScope()
    private var showLimitDialogJob: Job? = null

    override fun onActivityCreated(activity: Activity) {
        super.onActivityCreated(activity)
        startAntiAddiction(activity)
    }

    private fun startAntiAddiction(activity: Activity) = scope.launch {
        val gameId = MWBizBridge.currentGameId()
        Timber.d("startAntiAddiction gameId:$gameId")
        if (gameId.isNotEmpty() && gameId == "680131") {
            Timber.d("startAntiAddiction blank template, return")
            return@launch
        }
        val result = userRepository.getRealNameSurplusGameTimeV3(0, "")
        val data = result.data

        if (data != null) {
            Timber.d("startAntiAddiction ${data.popup}")
            if (data.popup != RealNameSurplusGameTime.Companion.Popup.NO) {
                val timeLimitChild = TimeLimitChild(
                    data.surplusGameTime,
                    data.message ?: "",
                    System.currentTimeMillis()
                )

                if (timeLimitChild.timeLeft <= 0) {
                    showLimitDialog(timeLimitChild, activity)
                } else {
                    scope.launch {
                        delay(timeLimitChild.timeLeft)
                        showLimitDialog(timeLimitChild, activity)
                    }
                }

            }
        }
    }


    private fun showLimitDialog(limit: TimeLimitChild, activity: Activity) {
        PartyAnalytics.trackRealNameInterceptShow(limit.age, "1")  // 0：登录完提示  1：游戏内提示 2: 启动App时提示
        AntiAdditionLimitGameDialog(limit, activity, metaApp, exit).show()
    }

    override fun onActivityDestroyed(activity: Activity) {
        super.onActivityDestroyed(activity)
        showLimitDialogJob?.cancel()
    }


}