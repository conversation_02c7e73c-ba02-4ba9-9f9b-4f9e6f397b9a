package com.socialplay.gpark.ui.main

import android.content.DialogInterface
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Parcelable
import android.provider.Settings
import android.view.Gravity
import android.view.WindowManager
import androidx.annotation.RequiresApi
import androidx.fragment.app.Fragment
import androidx.lifecycle.asFlow
import androidx.lifecycle.lifecycleScope
import com.airbnb.mvrx.args
import com.airbnb.mvrx.asMavericksArgs
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.UpdateAppInteractor
import com.socialplay.gpark.data.interactor.updateApkFile
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.UpdateInfo
import com.socialplay.gpark.data.model.realname.PartyAnalytics
import com.socialplay.gpark.databinding.DialogPartyUpdateBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstantsWrapper
import com.socialplay.gpark.ui.account.setting.SettingFragment
import com.socialplay.gpark.ui.account.setting.SettingViewModel
import com.socialplay.gpark.ui.core.BaseDialogFragment
import com.socialplay.gpark.ui.dialog.DialogScene
import com.socialplay.gpark.ui.dialog.IDialogManager
import com.socialplay.gpark.util.DeviceUtil
import com.socialplay.gpark.util.PackageUtilWrapper
import com.socialplay.gpark.util.extension.canShowDialog
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.parcelize.Parcelize
import org.koin.core.context.GlobalContext
import timber.log.Timber
import kotlin.system.exitProcess


/**
 * create by: bin on 2021/6/3
 */
@Parcelize
data class UpdateDialogArgs(
    val updateInfo: UpdateInfo
) : Parcelable

class UpdateDialog : BaseDialogFragment(), IDialogManager {
    var onDismissCallback: ((Boolean) -> Unit)? = null

    companion object {
        private var isShowedUpdate = false

        private var lastUpdateId: Long = -2

        fun showDialog(
            fragment: Fragment, updateInfo: UpdateInfo, onDismissCallback: (Boolean) -> Unit = {}
        ) {
            UpdateDialog().apply {
                arguments = UpdateDialogArgs(updateInfo).asMavericksArgs()
                this.onDismissCallback = onDismissCallback
            }.show(fragment.childFragmentManager, "update")
        }


        fun showFromSetting(
            fragment: SettingFragment, viewModel: SettingViewModel
        ) {
            val updateAppInteractor: UpdateAppInteractor = GlobalContext.get().get()
            val updateInfo = updateAppInteractor.updateInfoLiveData.value
            Analytics.track(
                EventConstantsWrapper.UPDATE_CHECK_CLICK, "hasUpdate" to (if (updateInfo != null) 1 else 0)
            )
            if (updateInfo != null) {
                showDialog(fragment, updateInfo)
            } else {
                fragment.toast(R.string.is_already_newest)
            }
        }
    }

    override val binding by viewBinding(DialogPartyUpdateBinding::inflate)

    private val args by args<UpdateDialogArgs>()
    private var updateInfo: UpdateInfo? = null

    override fun onDismiss(dialog: DialogInterface) {
        onDismissCallback?.invoke(true)
        onDismissCallback = null
        super.onDismiss(dialog)
    }

    override fun gravity(): Int = Gravity.CENTER

    override fun init() {
        val updateInfo = this.updateInfo ?: args.updateInfo
        val isMustUpdate = updateInfo.updateStrategy == 1 // 是否强更
        PartyAnalytics.trackUpdateDialogShow(isMustUpdate, updateInfo.targetVersion ?: "")
        dialog?.setCanceledOnTouchOutside(!isMustUpdate)
        dialog?.setCancelable(!isMustUpdate)

        binding.title.text = updateInfo.title
        binding.btnLeft.setText(if (isMustUpdate) R.string.real_name_btn_quit else R.string.not_update_yet)
        binding.btnLeft.setOnClickListener {
            PartyAnalytics.trackUpdateCloseClick(isMustUpdate)
            onDismissCallback?.invoke(true)
            onDismissCallback = null
            dismissAllowingStateLoss()
            if (isMustUpdate) {
                exitProcess(0)
            }
        }
        binding.btnRight.setOnAntiViolenceClickListener {
//            onDismissCallback?.invoke(false)
//            onDismissCallback = null
            PartyAnalytics.trackUpdateRenewClick(isMustUpdate)
            PackageUtilWrapper.installApp(requireContext(), updateApkFile)
            if (DeviceUtil.isEmui() && Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                compatEmui()
            }
        }
        val updateDescription = updateInfo.updateDescription ?: ""
        binding.content.text = updateDescription
        if (BuildConfig.DEBUG) {
            binding.title.setOnClickListener {
                onDismissCallback?.invoke(true)
                onDismissCallback = null
                dismissAllowingStateLoss()
            }
        }
    }

    override fun isBackPressedDismiss(): Boolean {
        return false
    }

    override fun isClickOutsideDismiss(): Boolean {
        return false
    }

    override fun isCancelable(): Boolean {
        return false
    }

    /**
     * 兼容emui谜一样的安装权限问题
     */
    @RequiresApi(Build.VERSION_CODES.O)
    private fun compatEmui() = viewLifecycleOwner.lifecycleScope.launch(Dispatchers.Main) {
        delay(200)
        if (isResumed && !requireContext().packageManager.canRequestPackageInstalls()) {
            val intent = Intent()
            val packageURI: Uri = Uri.parse("package:" + requireContext().packageName)
            intent.data = packageURI
            intent.action = Settings.ACTION_MANAGE_UNKNOWN_APP_SOURCES
            startActivity(intent)
        }
    }

    override fun windowHeight() = WindowManager.LayoutParams.MATCH_PARENT

    private val customScope = CoroutineScope(SupervisorJob() + Dispatchers.Default)
    var updateAppInteractor: UpdateAppInteractor = GlobalContext.get().get()
    var showData: UpdateInfo? = null
    override suspend fun initData(finishCallback: (Boolean) -> Unit) {
        finishCallback.invoke(true)
        Timber.d("UpdateInfoCheck initData: init fetch update info $customScope")
        // 这里要单独起一个协程，不然会卡住(而且需要用全局的协程)
        customScope.launch(Dispatchers.IO) {
            updateAppInteractor.updateInfoLiveData.asFlow().collect {
                Timber.d("UpdateInfoCheck initData: init fetch update info success $it")
                it?.let {
                    showData = it
                }
                customScope.cancel()
            }
        }
    }

    override fun needShow(fragment: Fragment, scene: DialogScene, args: Bundle?, needShowCallback: (Boolean) -> Unit) {
        if (scene != DialogScene.MAIN_PAGE && scene != DialogScene.GUIDE_LOGIN_PAGE) {
            needShowCallback.invoke(false)
            return
        }
        Timber.d("UpdateInfoCheck needShow: start fetching update info $showData")
        if (showData != null) {
            checkShowData(fragment, needShowCallback, showData)
            return
        }
        // 延时1秒再检查一下
        fragment.lifecycleScope.launch {
            delay(1200)
            Timber.d("UpdateInfoCheck needShow: delay 1000ms,fetch update info $showData")
            if (showData != null) {
                checkShowData(fragment, needShowCallback, showData)
            } else {
                needShowCallback.invoke(false)
            }
        }
    }

    private fun checkShowData(fragment: Fragment, function: ((Boolean) -> Unit)?, info: UpdateInfo?) {
        if (info == null) {
            function?.invoke(false)
            return
        }
        val metaKV = GlobalContext.get().get<MetaKV>()
        if (lastUpdateId == -2L) {
            lastUpdateId = metaKV.appKV.lastUpdateId
        }
        Timber.d("UpdateInfoCheck needShow: is it a force update? ${info.isForceUpdate}; has shown? ${lastUpdateId == info.uniqueTag}; safe to show a dialog now? ${fragment.canShowDialog}")
        if (info.isForceUpdate || lastUpdateId != info.uniqueTag) {
            function?.invoke(true)
        } else {
            function?.invoke(false)
        }
    }

    override fun showByDialogManager(fragment: Fragment, onDismissCallback: (Boolean) -> Unit) {
        if (!fragment.isAdded || fragment.isDetached) {
            return
        }
        showData?.let {
            updateInfo = it
            this.onDismissCallback = onDismissCallback
            val metaKV = GlobalContext.get().get<MetaKV>()
            lastUpdateId = it.uniqueTag
            metaKV.appKV.lastUpdateId = lastUpdateId
            show(fragment.childFragmentManager, "update")
        }
    }

    override fun exeDismiss() {
        this.dismissAllowingStateLoss()
    }
}