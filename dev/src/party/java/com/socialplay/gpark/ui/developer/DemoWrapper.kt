package com.socialplay.gpark.ui.developer

import android.content.Context
import androidx.lifecycle.LifecycleOwner
import com.tencent.bugly.crashreport.CrashReport

object DemoWrapper {

    fun clickTestFacebook(requireContext: Context) {

    }

    fun getActionList(): MutableMap<String, Int> {
        return mutableMapOf(

        )
    }

    fun handleAction(name: String, navId: Int, viewLifecycleOwner: LifecycleOwner): <PERSON><PERSON><PERSON> {

        return false
    }

    fun testJavaCrash() {
        CrashReport.testJavaCrash()
    }

    fun testNativeCrash() {
        CrashReport.testNativeCrash()

    }

    fun testANRCrash() {
        CrashReport.testANRCrash()

    }
}