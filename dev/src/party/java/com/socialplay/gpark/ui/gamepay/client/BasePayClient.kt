package com.socialplay.gpark.ui.gamepay.client

import android.os.Handler
import android.os.Looper
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.PartyEventConstants
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.code
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.LifecycleInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.pay.AgentPayType
import com.socialplay.gpark.data.model.pay.AgentPayVersion
import com.socialplay.gpark.data.model.pay.PayParamsParty
import com.socialplay.gpark.data.model.pay.PayResultEntity
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.pay.IPayInteractor
import com.socialplay.gpark.function.pay.PartyPayInteractor
import com.socialplay.gpark.ui.gamepay.OnPayCallback
import com.socialplay.gpark.ui.gamepay.PayController
import com.socialplay.gpark.ui.gamepay.platform.AliPayPlatform
import com.socialplay.gpark.ui.gamepay.platform.BasePayPlatformParty
import com.socialplay.gpark.ui.gamepay.platform.IPayCallbackParty
import com.socialplay.gpark.ui.gamepay.platform.PartySimulationPlatform
import com.socialplay.gpark.ui.gamepay.platform.WetChatPayPlatform
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2021/06/02
 *     desc   : 下单
 *
 */
abstract class BasePayClient {
    val payInteractor: PartyPayInteractor by lazy {
        GlobalContext.get().get<IPayInteractor>() as PartyPayInteractor
    }
    private var payPlatformMap: MutableMap<Int, BasePayPlatformParty<PayParamsParty>>? = null
    var currentParams: PayParamsParty? = null
    private var onPayCallback: OnPayCallback? = null
    private val metaKV by lazy { GlobalContext.get().get<MetaKV>() }
    private val handler by lazy { Handler(Looper.getMainLooper()) }
    private var flagStartPay: Boolean = false
    private val flagStartPayRunnable = {
        flagStartPay = false
//        metaKV.jerryAd.flagStartPay = false
    }

    init {
        registerPayPlatform(PartySimulationPlatform())
        registerPayPlatform(WetChatPayPlatform())
        registerPayPlatform(AliPayPlatform())
//        registerPayPlatform(QQPayPlatform())
//        registerPayPlatform(LeCoinPlatform())
//        registerPayPlatform(HelpPayPlatform())
//        registerPayPlatform(MobilePointsPlatform())
    }

    /**
     * 支付平台注册
     */
    private fun registerPayPlatform(platform: BasePayPlatformParty<PayParamsParty>) {
        if (payPlatformMap == null) {
            payPlatformMap = HashMap<Int, BasePayPlatformParty<PayParamsParty>>()
        }
        if (payPlatformMap?.containsKey(platform.platformType()) != true) {
            payPlatformMap?.put(platform.platformType(), platform)
        }
    }

    /**
     * 支付结果回调
     */
    fun setOnPayCallback(onPayCallback: OnPayCallback) {
        this.onPayCallback = onPayCallback
    }

    /**
     * 开始下单
     */
    abstract fun startPay(params: PayParamsParty)

    /**
     * 支付版本
     */
    abstract fun type(): AgentPayVersion

    /**
     * 调起第三方支付
     */
    fun startThirdPay(payResultEntity: DataResult<PayResultEntity?>, payType: Int, params: PayParamsParty) {
        // metaKV.jerryAd.flagStartPay = true
        flagStartPay = true
        if (payResultEntity.succeeded && payResultEntity.data != null) {
            // 下单成功 开始第三方支付
            currentParams?.orderCode = payResultEntity.data?.orderCode
            currentParams?.payResult = payResultEntity.data
            Timber.d("开始第三方支付payType=%s", payType)
            val platform = getPayPlatform(payType)
            if (platform == null) {
                val errorMessage = LifecycleInteractor.activityRef?.get()?.getString(R.string.pay_fail_payment_way_not_support)?:""
                payFailed(errorMessage, AgentPayType.THIRD_PAY_CHANNEL_FAILED)
            } else {
                Analytics.track(
                    PartyEventConstants.EVENT_PARTY_CLIENT_PAY_JUMP_THIRD,
                    "gamecode" to (params.gameId ?: "null"),
                    "price" to params.pPrice.toString(),
                    "channel" to payType.toString(),
                    "orderid" to (payResultEntity.data?.orderCode?:""),
                    "productid" to (params.pCode ?: ""),
                    "source" to (params.pageSource ?: ""),
                )
                currentParams?.let { platform.setAgentPayParams(it) }
                platform.setPayCallback(getIPayCallback())
                platform.startPay(payResultEntity.data!!)
            }
        } else {
            //下单失败
            Timber.d(
                "下单出错原因%s   %s",
                payResultEntity.message,
                PayController.getCurrentActivity()
            )
            val errorMessage = LifecycleInteractor.activityRef?.get()?.getString(R.string.pay_fail_create_order_failed)?:""
            payFailed(payResultEntity.message ?: errorMessage, payResultEntity.code)
        }

    }

    /**
     * @param payType 支付类型
     * @return 获取对应的支付平台
     */
    private fun getPayPlatform(payType: Int): BasePayPlatformParty<PayParamsParty>? {
        return payPlatformMap?.get(payType)
    }

    /**
     * @return 获取支付的结果回调
     * ::此处的支付回调区别去之前的回调，防止后期更改造成的大面积代码改动
     */
    private fun getIPayCallback(): IPayCallbackParty<PayParamsParty> {
        return object : IPayCallbackParty<PayParamsParty> {
            override fun onPaySuccess(param: PayParamsParty?) {
                paySuccess()
            }

            override fun onPayFailed(param: PayParamsParty?, errorMessage: String?, code: Int) {
                payFailed(errorMessage, code)
            }

            override fun onStartThirdPay(params: PayParamsParty?) {
                startThirdPay(params)
            }
        }
    }

    private fun resetHotAdFlag() {
        handler.removeCallbacks(flagStartPayRunnable)
        handler.postDelayed(flagStartPayRunnable, 2000)
    }

    /**
     * 支付失败
     */
    fun payFailed(errorMessage: String?, code: Int? = null) {
        resetHotAdFlag()
        if (onPayCallback != null) {
            onPayCallback?.onPayFailed(currentParams, code, errorMessage)
        }
    }

    /**
     * 支付成功
     */
    fun paySuccess() {
        resetHotAdFlag()
        if (onPayCallback != null) {
            onPayCallback?.onPaySuccess(currentParams)
        }
    }

    /**
     * 第三方支付
     */
    fun startThirdPay(params: PayParamsParty?) {
        if (onPayCallback != null) {
            onPayCallback?.onStartThirdPay(params)
        }
    }

}