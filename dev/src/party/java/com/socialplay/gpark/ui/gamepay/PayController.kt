package com.socialplay.gpark.ui.gamepay

import android.app.Activity
import com.socialplay.gpark.data.interactor.LifecycleInteractor
import com.socialplay.gpark.data.model.pay.PayParamsParty
import com.socialplay.gpark.ui.gamepay.client.BasePayClient
import com.socialplay.gpark.ui.gamepay.client.OwnPayClient
import com.socialplay.gpark.util.GsonUtil
import timber.log.Timber
import java.util.*
import java.util.concurrent.atomic.AtomicBoolean

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2021/06/02
 *     desc   : 选择完支付方式后去后端下单
 *
 */
object PayController {
    const val TAG = "PayController"

    /**
     * 是否正在支付
     */
    private val isPaying = AtomicBoolean()

    /**
     * 是否正在等待第三方支付结果
     */
    private val isThirdPay = AtomicBoolean()
    /**
     * 是否去乐币充值了
     */
    private val isLeCoinRecharge = AtomicBoolean()
    /**
     * 是否去充值会员了
     */
    val isVipRecharge = AtomicBoolean()

    /**
     * 支付类型缓存
     */
    private val BASE_PAY_CLIENT_ARRAY: MutableList<BasePayClient> = ArrayList<BasePayClient>()

    init {
        BASE_PAY_CLIENT_ARRAY.add(OwnPayClient())
    }

    fun getCurrentActivity(): Activity? {
        return LifecycleInteractor.activityRef?.get()
    }

    fun setIsPaying(status: Boolean) {
        isPaying.set(status)
    }

    fun getPay(): Boolean {
        return isPaying.get()
    }

    fun setIsThirdPaying(status: Boolean) {
        isThirdPay.set(status)
    }

    fun getIsThirdPaying(): Boolean {
        return isThirdPay.get()
    }
    fun setIsLeCoinRecharge(status: Boolean) {
        isLeCoinRecharge.set(status)
    }

    fun getLeCoinRecharge(): Boolean {
        return isLeCoinRecharge.get()
    }
    /**
     * 支付根据不同的sdk的版本分发
     */
    fun dispatchPay(payParams: PayParamsParty, callback: OnPayCallback) {
        Timber.d("PayController%s", GsonUtil.safeToJson(payParams))
        for (client in BASE_PAY_CLIENT_ARRAY) {
            if (client.type() === payParams.agentPayVersion) {
                client.setOnPayCallback(callback)
                setIsPaying(true)
                client.startPay(payParams)
                return
            }
        }
    }

}