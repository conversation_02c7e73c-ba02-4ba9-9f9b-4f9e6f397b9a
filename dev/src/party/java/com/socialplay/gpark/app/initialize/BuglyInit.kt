package com.socialplay.gpark.app.initialize

import android.app.Activity
import android.app.Application
import android.content.Context
import android.os.Bundle
import android.view.View
import androidx.collection.LruCache
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.navigation.fragment.NavHostFragment
import com.bumptech.glide.manager.SupportRequestManagerFragment
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.di.CommonParamsProvider
import com.socialplay.gpark.function.startup.StartupProcessType
import com.socialplay.gpark.function.startup.core.ProcessType
import com.socialplay.gpark.ui.main.MainActivity
import com.socialplay.gpark.util.extension.runWhenDestroyed
import com.meta.lib.mwbiz.MWBizBridge
import com.meta.pandora.Pandora
import com.meta.pandora.function.crash.CrashType
import com.socialplay.gpark.data.model.event.GameStateNoteEvent
import com.socialplay.gpark.function.analytics.kernel.PandoraInit
import com.socialplay.gpark.function.analytics.kernel.toPandoraType
import com.socialplay.gpark.util.Md5Util
import com.tencent.bugly.crashreport.BuglyLog
import com.tencent.bugly.crashreport.CrashReport
import com.tencent.bugly.crashreport.CrashReport.UserStrategy
import org.greenrobot.eventbus.Subscribe
import org.koin.core.context.GlobalContext
import timber.log.Timber
import java.lang.ref.WeakReference


object BuglyInit {
    private val commonParamsProvider by lazy { GlobalContext.get().get<CommonParamsProvider>() }

    fun init(context: Application, processType: ProcessType) {
        registerActivityCallback(context)
        val appId: String
        val version: String
        when (processType) {
            StartupProcessType.H -> {
                appId = if (BuildConfig.DEBUG) BuildConfig.BULGY_H_DEBUG else BuildConfig.BULGY_H
                version = commonParamsProvider.appVersionName
            }

            StartupProcessType.M -> {
                appId = BuildConfig.BULGY_M
                version = commonParamsProvider.metaverseEngineVersion
            }

            StartupProcessType.R -> {
                appId = BuildConfig.BULGY_R
                version = commonParamsProvider.metaverseEngineVersion
            }

            else -> return
        }

        Timber.e("bugly init:${processType.desc}")

        val strategy = with(commonParamsProvider) {
            val strategy = UserStrategy(context)
            strategy.deviceID = deviceId
            strategy.deviceModel = deviceModel
            strategy.appPackageName = selfPackageName
            strategy.appVersion = version
            strategy
        }
        strategy.setCrashHandleCallback(object : CrashReport.CrashHandleCallback() {
            override fun onCrashHandleStart(
                crashType: Int,
                errorType: String?,
                errorMessage: String?,
                errorStack: String?
            ): MutableMap<String, String> {
                Timber.e("bugly handle crash:$errorMessage")

                if (crashType == CRASHTYPE_ANR) {
                    val pandoraAnrType = when (processType) {
                        StartupProcessType.H -> CrashType.ANR_H
                        StartupProcessType.M -> CrashType.ANR_M
                        StartupProcessType.R -> CrashType.ANR_R
                        else -> CrashType.ANR_H

                    }
                    Pandora.send(pandoraAnrType) {
                        isImmediately = true
                        crashId(Md5Util.str2MD5(errorMessage ?: "unknown") ?: "unknown")
                        errorMessage(errorMessage ?: "unknown")
                        errorStack(errorStack ?: "unknown")
                        PandoraInit.setCrashParams(context, processType.toPandoraType(), this)
                    }
                }

                CrashReport.putUserData(context, "uid", commonParamsProvider.uid)
                CrashReport.putUserData(context, "metaverse", commonParamsProvider.metaverseVersion)
                CrashReport.putUserData(context, "metaverse_engine", commonParamsProvider.metaverseEngineVersion)
                CrashReport.putUserData(context, "flavor", "${BuildConfig.BUILD_TYPE}-${BuildConfig.DEBUG}")
                if (processType == StartupProcessType.M) {
                    CrashReport.putUserData(context, "ts_gameid", MWBizBridge.currentGameId())
                    CrashReport.putUserData(context, "ts_gamepkg", MWBizBridge.currentGamePkg())
                }

                return mutableMapOf()
            }

            override fun onCrashHandleStart2GetExtraDatas(
                crashType: Int,
                errorType: String?,
                errorMessage: String?,
                errorStack: String?
            ): ByteArray? {
                if (processType == StartupProcessType.H) {
                    return kotlin.runCatching {
                        pageStateLru.snapshot().keys.joinToString("\n").toByteArray()
                    }.getOrNull()
                }
                return null
            }
        })

        CrashReport.initCrashReport(context, appId, BuildConfig.DEBUG, strategy)
        CrashReport.setUserId(commonParamsProvider.deviceId)
    }

    private var currentActivity: WeakReference<Activity?> = WeakReference(null)
    private var currentActivityStatus: String = ""
    private var currentFragmentStatus: String = ""
    private var currentGameStatus: String = ""
    private val pageStateLru: LruCache<String, Int> by lazy { LruCache(128) }
    private const val VALUE = 0

    private fun registerActivityCallback(application: Application) {
        application.registerActivityLifecycleCallbacks(object : Application.ActivityLifecycleCallbacks {
            override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
                setCurrentStatus(activity, "Created", if (savedInstanceState == null) null else "saveState")
                currentActivity = WeakReference(activity)
                if (activity is MainActivity) {
                    registerFragmentCallback(activity)
                }
            }

            override fun onActivityStarted(activity: Activity) {
                setCurrentStatus(activity, "Started")
            }

            override fun onActivityResumed(activity: Activity) {
                setCurrentStatus(activity, "Resumed")
                currentActivity = WeakReference(activity)
            }

            override fun onActivityPaused(activity: Activity) {
                setCurrentStatus(activity, "Paused")
            }

            override fun onActivityStopped(activity: Activity) {
                setCurrentStatus(activity, "Stopped")
            }

            override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
                setCurrentStatus(activity, "SaveInstanceState")
            }

            override fun onActivityDestroyed(activity: Activity) {
                setCurrentStatus(activity, "Destroyed")
                if (currentActivity.get() == activity) {
                    currentActivity = WeakReference(null)
                }
            }

            private fun setCurrentStatus(activity: Activity, status: String, extra: String? = null) {
                val m = if (extra.isNullOrEmpty()) {
                    "onActivity$status ${activity.javaClass.name}-${activity.hashCode()}"
                } else {
                    "onActivity$status ${activity.javaClass.name}-${activity.hashCode()}-$extra"
                }
                BuglyLog.d("Page-Activity", m)
                pageStateLru.put(m, VALUE)
                currentActivityStatus = m
            }
        })
    }

    private fun registerFragmentCallback(mainActivity: MainActivity) {
        val fragmentManager = mainActivity.supportFragmentManager
        val callback = object : FragmentManager.FragmentLifecycleCallbacks() {
            override fun onFragmentAttached(fm: FragmentManager, f: Fragment, context: Context) {
                setCurrentStatus(fm, f, "Attached")
            }

            override fun onFragmentCreated(fm: FragmentManager, f: Fragment, savedInstanceState: Bundle?) {
                setCurrentStatus(fm, f, "Created", if (savedInstanceState == null) null else "saveState")
            }

            override fun onFragmentViewCreated(fm: FragmentManager, f: Fragment, v: View, savedInstanceState: Bundle?) {
                setCurrentStatus(fm, f, "ViewCreated", if (savedInstanceState == null) null else "saveState")
            }

            override fun onFragmentStarted(fm: FragmentManager, f: Fragment) {
                setCurrentStatus(fm, f, "Started")
            }

            override fun onFragmentResumed(fm: FragmentManager, f: Fragment) {
                setCurrentStatus(fm, f, "Resumed")
            }

            override fun onFragmentPaused(fm: FragmentManager, f: Fragment) {
                setCurrentStatus(fm, f, "Paused")
            }

            override fun onFragmentStopped(fm: FragmentManager, f: Fragment) {
                setCurrentStatus(fm, f, "Stopped")
            }

            override fun onFragmentSaveInstanceState(fm: FragmentManager, f: Fragment, outState: Bundle) {
                setCurrentStatus(fm, f, "SaveInstanceState")
            }

            override fun onFragmentViewDestroyed(fm: FragmentManager, f: Fragment) {
                setCurrentStatus(fm, f, "ViewDestroyed")
            }

            override fun onFragmentDestroyed(fm: FragmentManager, f: Fragment) {
                setCurrentStatus(fm, f, "Destroyed")
            }

            override fun onFragmentDetached(fm: FragmentManager, f: Fragment) {
                setCurrentStatus(fm, f, "Detached")
            }

            private fun setCurrentStatus(
                fm: FragmentManager,
                fragment: Fragment,
                status: String,
                extra: String? = null
            ) {
                val m = if (extra.isNullOrEmpty()) {
                    "onFragment$status ${fragment.javaClass.name}-${fragment.hashCode()}"
                } else {
                    "onFragment$status ${fragment.javaClass.name}-${fragment.hashCode()}-$extra"
                }
                BuglyLog.d("Page-Fragment", m)
                pageStateLru.put(m, VALUE)
                if (fragment is SupportRequestManagerFragment) {
                    return
                }
                if (fragment is NavHostFragment) {
                    return
                }
                currentFragmentStatus = m
            }
        }
        fragmentManager.registerFragmentLifecycleCallbacks(callback, true)
        mainActivity.runWhenDestroyed {
            fragmentManager.unregisterFragmentLifecycleCallbacks(callback)
        }
    }


    @Subscribe
    fun onGameActivityEvent(info: GameStateNoteEvent) {
        val m = if (info.isTsGame) {
            "onTsGame${info.state}-gameId:${info.tsGameId}-pid:${info.pid}-${info.activityName}"
        } else {
            "onApkGame${info.state}-pkgName:${info.apkPackageName}-pid:${info.pid}-${info.activityName}"
        }
        pageStateLru.put(m, VALUE)
        currentGameStatus = m
    }
}