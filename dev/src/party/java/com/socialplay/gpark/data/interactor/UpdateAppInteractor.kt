package com.socialplay.gpark.data.interactor

import android.app.Application
import android.content.Context
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.meituan.android.walle.ChannelWriterEx
import com.meta.lib.bspatch.ApkChannelUtil
import com.meta.lib.bspatch.BsPatch
import com.socialplay.gpark.data.IMetaRepositoryWrapper
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.model.UpdateInfo
import com.socialplay.gpark.data.model.UpdatePatch
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstantsWrapper
import com.socialplay.gpark.function.download.DownloadFileProvider.app
import com.socialplay.gpark.function.download.DownloadFileProvider.downloadRoot
import com.socialplay.gpark.function.download.SimpleDownloader
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.pandora.PandoraToggleWrapper
import com.socialplay.gpark.util.KeepClass
import com.socialplay.gpark.util.Md5Util.file2Md5Low
import com.tencent.bugly.crashreport.CrashReport
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.flatMapConcat
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import org.koin.core.context.GlobalContext
import timber.log.Timber
import java.io.File
import kotlin.coroutines.CoroutineContext
import kotlin.coroutines.resume

/**
 * create by: bin on 2021/6/6
 */
val bspatchDir: File by lazy { app.getDir("bspatch", Context.MODE_PRIVATE) }
val updateApkFile = File(downloadRoot, "update${File.separator}new233.apk")

class UpdateAppInteractor(private val repository: IMetaRepositoryWrapper) {

    private val deviceInteractor: DeviceInteractor by lazy { GlobalContext.get().get<DeviceInteractor>() }
    private val coroutineScope: CoroutineScope by lazy { CoroutineScope(Dispatchers.Main) }

    private val _updateInfoLiveData: MutableLiveData<UpdateInfo?> by lazy { MutableLiveData<UpdateInfo?>() }
    val updateInfoLiveData: LiveData<UpdateInfo?> = _updateInfoLiveData

    fun getUpdateInfo(ctx: CoroutineContext) = updateInfoLiveData


    init {
        // 添加设备ID初始化完成的回调
        deviceInteractor.addAndroidIdInitCallback {
            Timber.d("DeviceId initialized, refreshing update info")
            // 设备ID初始化完成后请求更新信息
            checkAndRequestUpdateInfo()
        }
    }

    /**
     * 检查并请求更新信息
     */
    private fun checkAndRequestUpdateInfo() {
        coroutineScope.launch(Dispatchers.IO) {
            val updateOpen = PandoraToggle.isUpdateOpen
            Timber.d("update-patch start updateOpen:$updateOpen")
            if (updateOpen) {
                try {
                    processUpdate()
                } catch (e: Exception) {
                    Timber.e(e, "update-patch Error during update process")
                    _updateInfoLiveData.postValue(null)
                }
            } else {
                _updateInfoLiveData.postValue(null)
            }
        }
    }

    private fun processUpdate(updateInfo: UpdateInfo) = flow {
        Timber.d("update-patch processUpdate")
        val type: String
        val process: UpdateProcess
        if (updateApkFile.checkFileCorrect(updateInfo.updateApkMd5)) {
            type = "cache"
            process = UpdateResult.succeed(updateInfo, type, false)
        } else if (updateInfo.patchInfo != null && isSupportPatch(updateInfo.patchInfo)) {
            type = "patch"
            process = ProcessPatch(updateInfo, updateInfo.patchInfo)
        } else {
            type = "all"
            process = DownloadApk(
                updateInfo,
                type,
                "hasPatch:${updateInfo.patchInfo != null}, open:${PandoraToggleWrapper.isOpenAppUpdateByPatch}, type:${updateInfo.patchInfo?.differenceType}, compress:${updateInfo.patchInfo?.compress}"
            )
        }
        trackUpdateStart(updateInfo, type)
        emit(process)
    }

    private fun isSupportPatch(patchInfo: UpdatePatch): Boolean {
        return PandoraToggleWrapper.isOpenAppUpdateByPatch && BsPatch.TYPE.equals(
            patchInfo.differenceType,
            true
        ) && BsPatch.COMPRESSOR_TYPE_BZIP2.equals(patchInfo.compress, true)
    }

    private fun UpdateProcess.toDownloadApk(msg: String): DownloadApk =
        DownloadApk(updateInfo, "patch", msg)

    private fun downloadPatchFile(process: DownloadPatch) = flow {
        Timber.d("update-patch downloadPatchFile")
        trackDownloadUpdateStart(process.updateInfo, "patch")
        val startTime = System.currentTimeMillis()
        val result =
            download(process.patchFile, process.patchInfo.patchUrl, process.patchInfo.patchMd5)
        if (result && process.patchFile.checkFileCorrect(process.patchInfo.patchMd5)) {
            trackDownloadUpdateEnd(
                process.updateInfo,
                System.currentTimeMillis() - startTime,
                "patch",
                true,
                "ok"
            )
            emit(PatchOldBaseApk(process.updateInfo, process.patchInfo))
        } else {
            val msg = "download patch error, result:$result"
            trackDownloadUpdateEnd(
                process.updateInfo,
                System.currentTimeMillis() - startTime,
                "patch",
                false,
                msg
            )
            throw SkipPatchException(process, msg)
        }
    }

    private fun processPatch(process: ProcessPatch) = flow {
        Timber.d("update-patch processPatch")
        val patchInfo = process.patchInfo

        val channelApk: File = process.channelApk
        if (channelApk.checkFileCorrect(process.updateInfo.updateApkMd5)) {
            val apkFile = process.apkFile
            if (apkFile.exists()) {
                apkFile.deleteRecursively()
            }
            channelApk.copyTo(apkFile)
            channelApk.deleteRecursively()
            if (apkFile.exists()) {
                emit(UpdateResult.succeed(process.updateInfo, "patch", true))
            } else {
                throw SkipPatchException(process, "failed by rename channel.apk to final")
            }
            return@flow
        }
        if (process.newBaseApk.checkFileCorrect(patchInfo.newBaseApkMd5)) {
            emit(CreateChannelApk(process.updateInfo, process.patchInfo))
            return@flow
        }
        if (process.oldBaseApk.checkFileCorrect(patchInfo.oldBaseApkMd5)) {
            if (process.patchFile.checkFileCorrect(process.patchInfo.patchMd5)) {
                emit(PatchOldBaseApk(process.updateInfo, patchInfo))
            } else {
                emit(DownloadPatch(process.updateInfo, patchInfo))
            }
        } else {
            emit(CreateOldBaseApk(process.updateInfo, patchInfo))
        }
    }

    private fun createOldBaseApk(process: CreateOldBaseApk) = flow {
        Timber.d("update-patch createOldBaseApk")
        val app: Application = GlobalContext.get().get()
        val apkPath = getApkPath(app)
        if (apkPath.isNullOrEmpty()) {
            throw SkipPatchException(process, "installed apk not found")
        }
        val oldBaseApk = process.oldBaseApk
        File(apkPath).copyTo(oldBaseApk, true)

        ChannelWriterEx.removeChannelInfo(oldBaseApk)

        if (oldBaseApk.checkFileCorrect(process.patchInfo.oldBaseApkMd5)) {
            if (process.patchFile.checkFileCorrect(process.patchInfo.patchMd5)) {
                emit(PatchOldBaseApk(process.updateInfo, process.patchInfo))
            } else {
                emit(DownloadPatch(process.updateInfo, process.patchInfo))
            }
        } else {
            oldBaseApk.deleteRecursively()
            throw SkipPatchException(process, "old base apk not correct")
        }
    }

    private fun getApkPath(context: Context): String? {
        var apkPath: String? = null
        try {
            val applicationInfo = context.applicationInfo ?: return null
            apkPath = applicationInfo.sourceDir
        } catch (e: Throwable) {
            // ignore
        }
        return apkPath
    }

    private fun patchOldBaseApk(process: PatchOldBaseApk) = flow {
        Timber.d("update-patch patchOldBaseApk")
        val newBaseApk = process.newBaseApk
        val result = BsPatch.patch(process.oldBaseApk, newBaseApk, process.patchFile)
        process.oldBaseApk.deleteRecursively()
        if (result.first && process.patchInfo.newBaseApkMd5.equals(newBaseApk.md5(), true)) {
            emit(CreateChannelApk(process.updateInfo, process.patchInfo))
        } else {
            throw SkipPatchException(process, "new base apk not correct, result:$result")
        }
    }

    private fun createChannelApk(process: CreateChannelApk) = flow {
        Timber.d("update-patch createChannelApk")
        val channelApk = process.channelApk
        process.newBaseApk.copyTo(channelApk, true)
        if (process.patchInfo.channelInfo.isNullOrEmpty()) {
            throw SkipPatchException(process, "channelInfo is null or empty")
        }
        ApkChannelUtil.putChannelInfo(channelApk, process.patchInfo.channelInfo, false)
        process.newBaseApk.deleteRecursively()
        val resultApk = process.apkFile
        channelApk.copyTo(resultApk, true)
        if (resultApk.checkFileCorrect(process.updateInfo.updateApkMd5)) {
            emit(UpdateResult.succeed(process.updateInfo, "patch", true))
        } else {
            resultApk.deleteRecursively()
            throw SkipPatchException(process, "channel apk not correct")
        }
    }

    private fun downloadApk(process: DownloadApk) = flow {
        Timber.d("update-patch downloadApk patchMsg:%s", process.patchMsg)
        trackDownloadUpdateStart(process.updateInfo, "all")
        val startTime = System.currentTimeMillis()
        val succeed = download(
            process.apkFile,
            process.updateInfo.updateApkUrl,
            process.updateInfo.updateApkMd5
        )
        if (succeed && process.apkFile.checkFileCorrect(process.updateInfo.updateApkMd5)) {
            trackDownloadUpdateEnd(
                process.updateInfo,
                System.currentTimeMillis() - startTime,
                "all",
                true,
                "ok"
            )
            emit(
                UpdateResult.succeed(
                    process.updateInfo,
                    process.updateType,
                    false,
                    process.patchMsg
                )
            )
        } else {
            val msg = "download all failed succeed:$succeed"
            trackDownloadUpdateEnd(
                process.updateInfo,
                System.currentTimeMillis() - startTime,
                "all",
                false,
                msg
            )
            emit(UpdateResult.failed(process.updateInfo, process.updateType, msg, process.patchMsg))
        }
    }

    private suspend fun download(file: File, url: String?, md5: String?): Boolean {
        if (url.isNullOrEmpty()) {
            Timber.d("update-patch download url is null or empty $file")
            return false
        }
        return suspendCancellableCoroutine { continuation ->
            SimpleDownloader.download(file, url, md5, progress = { totalSize, completeSize ->
                val percent = ((completeSize.toDouble() / totalSize) * 100).toInt()
                Timber.d("update-patch download progress $percent")
            }) {
                continuation.resume(it)
            }
        }
    }

    private fun trackUpdateStart(updateInfo: UpdateInfo, type: String) {
        Analytics.track(EventConstantsWrapper.UPDATE_START, updateInfo.getUpdateEventMap().also {
            it["update_type"] = type
            it["has_patch"] = if (updateInfo.patchInfo == null) "no" else "yes"
            it["open_patch"] = if (PandoraToggleWrapper.isOpenAppUpdateByPatch) "yes" else "no"
        })
    }

    private fun trackDownloadUpdateStart(updateInfo: UpdateInfo, type: String) {
        Analytics.track(EventConstantsWrapper.UPDATE_START_DOWNLOAD, updateInfo.getUpdateEventMap().also {
            it["update_type"] = type
        })
    }

    private fun trackDownloadUpdateEnd(
        updateInfo: UpdateInfo,
        cost: Long,
        type: String,
        succeed: Boolean,
        msg: String
    ) {
        Analytics.track(EventConstantsWrapper.UPDATE_DOWNLOAD_FINISH, updateInfo.getUpdateEventMap().also {
            it["update_type"] = type
            it["download_result"] = if (succeed) "succeed" else "failed"
            it["download_time"] = cost
            it["download_msg"] = msg
        })
    }

    private fun trackUpdateResult(result: UpdateResult) {
        Analytics.track(
            EventConstantsWrapper.UPDATE_DO_RESULT,
            result.updateInfo.getUpdateEventMap().also {
                it["update_type"] = result.updateType
                it["update_result"] = if (result.succeed) "succeed" else "failed"
                it["patch_result"] = if (result.patchSucceed) "succeed" else "failed"
                it["patch_msg"] = result.patchMsg
                it["update_msg"] = result.msg
            })
    }

    private suspend fun processUpdate() {
        var hasEmittedResult = false
        try {
            repository
                .getUpdateInfo()
                .map {
                    Timber.d("update-patch info:%s", it?.data)
                    it?.data
                }
                .catch { e ->
                    // 处理获取更新信息时的异常
                    Timber.e(e, "update-patch Error getting update info")
                    emit(null) // 发送空值，会被下面的 filterNotNull 过滤掉
                }
                .filterNotNull()
                .onEmpty {
                    // 处理空结果的情况
                    Timber.d("update-patch: No update info available")
                    hasEmittedResult = true
                    _updateInfoLiveData.postValue(null)
                }
                .flatMapConcat { processUpdate(it) }
                .flatMapConcatForPatch<ProcessPatch> { processPatch(it) }
                .flatMapConcatForPatch<CreateOldBaseApk> { createOldBaseApk(it) }
                .flatMapConcatForPatch<DownloadPatch> { downloadPatchFile(it) }
                .flatMapConcatForPatch<PatchOldBaseApk> { patchOldBaseApk(it) }
                .flatMapConcatForPatch<CreateChannelApk> { createChannelApk(it) }
                .catchSkipPatch()
                .flatMapConcatForResult<DownloadApk> { downloadApk(it) }
                .catch { e ->
                    // 处理整个流程中的任何未捕获异常
                    Timber.e(e, "update-patch Uncaught exception in update process flow")
                    emit(UpdateResult.failed(UpdateInfo(), "error", "Uncaught exception: ${e.message}"))
                }
                .collect {
                    Timber.d(
                        "update-patch flow result:${it.succeed} msg:${it.msg} patchMsg:%s",
                        it.patchMsg
                    )
                    trackUpdateResult(it)
                    hasEmittedResult = true
                    if (it.succeed) {
                        _updateInfoLiveData.postValue(it.updateInfo)
                        coroutineScope.launch(Dispatchers.IO) {
                            // todo delete this
                            bspatchDir.listFiles()?.forEach { file ->
                                kotlin.runCatching {
                                    file.deleteRecursively()
                                }
                            }
                        }
                    } else {
                        _updateInfoLiveData.postValue(null)
                    }
                }
        } catch (e: Exception) {
            // 捕获 processUpdate 函数中的任何异常
            Timber.e(e, "Exception in processUpdate")
            if (!hasEmittedResult) {
                _updateInfoLiveData.postValue(null)
                hasEmittedResult = true
            }
        } finally {
            // 确保在所有情况下都会更新 LiveData
            if (!hasEmittedResult) {
                Timber.d("update-patch: No result was emitted, posting null")
                _updateInfoLiveData.postValue(null)
                hasEmittedResult = true
            }
        }
    }

    private inline fun <reified T> Flow<UpdateProcess>.flatMapConcatForResult(crossinline transform: suspend (value: T) -> Flow<UpdateResult>) =
        flow {
            map { process ->
                when (process) {
                    is T -> {
                        transform(process)
                    }

                    is UpdateResult -> {
                        flow { emit(process) }
                    }

                    else -> {
                        flow {
                            emit(
                                UpdateResult.failed(
                                    process.updateInfo,
                                    "error",
                                    "process type error ${process.javaClass.name}"
                                )
                            )
                        }
                    }
                }
            }.collect {
                emitAll(it)
            }
        }

    private inline fun <reified T> Flow<UpdateProcess>.flatMapConcatForPatch(crossinline transform: suspend (value: T) -> Flow<UpdateProcess>) =
        flow {
            map { process ->
                if (process is T) {
                    kotlin.runCatching {
                        transform(process)
                    }.getOrElse { error ->
                        if (error !is SkipPatchException) {
                            throw SkipPatchException(process, "code error", error)
                        } else {
                            throw error
                        }
                    }
                } else {
                    flow { emit(process) }
                }
            }.collect {
                emitAll(it)
            }
        }

    private fun Flow<UpdateProcess>.catchSkipPatch() = catch { error ->
        if (error is SkipPatchException) {
            Timber.d("update-patch catchSkipError $error")
            emit(error.process.toDownloadApk(error.toMsg()))
        } else {
            // 不应该发生
            Timber.d("update-patch catchSkipError amazing error %s", Log.getStackTraceString(error))
            CrashReport.postCatchedException(CatchUpdateException(error))
        }
    }

    private suspend fun File.checkFileCorrect(md5: String?): Boolean {
        if (!exists() || isDirectory || length() <= 0) {
            Timber.e(
                "update-patch checkFileCorrect failed exists:%s isDirectory:%s length:%s file:%s, md5:%s",
                exists(),
                isDirectory,
                length(),
                this.name,
                md5
                    ?: "empty"
            )
            return false
        }
        return md5.isNullOrEmpty() || md5.equals(md5(), true)
    }

    private suspend fun File.md5(): String? {
        return this.file2Md5Low()
    }
}

private sealed interface UpdateProcess {
    val updateInfo: UpdateInfo

    val apkFile: File
        get() = updateApkFile

}

private data class UpdateResult(
    override val updateInfo: UpdateInfo,
    val succeed: Boolean,
    val msg: String,
    val patchMsg: String = "",
    val updateType: String,
    val patchSucceed: Boolean
) : UpdateProcess {

    companion object {
        fun succeed(
            updateInfo: UpdateInfo,
            updateType: String,
            patchSucceed: Boolean,
            patchMsg: String = "empty"
        ) = UpdateResult(updateInfo, true, "succeed", patchMsg, updateType, patchSucceed)

        fun failed(
            updateInfo: UpdateInfo,
            updateType: String,
            msg: String,
            patchMsg: String = "empty"
        ) = UpdateResult(updateInfo, false, msg, patchMsg, updateType, false)
    }
}

private data class DownloadApk(
    override val updateInfo: UpdateInfo,
    val updateType: String,
    val patchMsg: String
) : UpdateProcess

private data class DownloadPatch(override val updateInfo: UpdateInfo, val patchInfo: UpdatePatch) :
    UpdateProcess {
    val patchFile: File by lazy { patchInfo.getPatchFile() }
}

private data class ProcessPatch(override val updateInfo: UpdateInfo, val patchInfo: UpdatePatch) :
    UpdateProcess {
    val patchFile: File by lazy { patchInfo.getPatchFile() }
    val oldBaseApk: File by lazy { patchInfo.getOldBaseApk() }
    val newBaseApk: File by lazy { patchInfo.getNewBaseApk() }
    val channelApk: File by lazy { patchInfo.getChannelApk() }
}

private data class CreateOldBaseApk(
    override val updateInfo: UpdateInfo,
    val patchInfo: UpdatePatch
) : UpdateProcess {
    val patchFile: File by lazy { patchInfo.getPatchFile() }
    val oldBaseApk: File by lazy { patchInfo.getOldBaseApk() }
}

private data class PatchOldBaseApk(
    override val updateInfo: UpdateInfo,
    val patchInfo: UpdatePatch
) : UpdateProcess {
    val patchFile: File by lazy { patchInfo.getPatchFile() }
    val oldBaseApk: File by lazy { patchInfo.getOldBaseApk() }
    val newBaseApk: File by lazy { patchInfo.getNewBaseApk() }
}

private data class CreateChannelApk(
    override val updateInfo: UpdateInfo,
    val patchInfo: UpdatePatch
) : UpdateProcess {
    val newBaseApk: File by lazy { patchInfo.getNewBaseApk() }
    val channelApk: File by lazy { patchInfo.getChannelApk() }
}


private fun UpdatePatch.getPatchDir(): File {
    return File(
        bspatchDir,
        "patch-${patchMd5}"
    ).also { if (!it.exists()) it.mkdirs() }
}

private fun UpdatePatch.getPatchFile(): File {
    return File(getPatchDir(), "patch")
}

private fun UpdatePatch.getOldBaseApk(): File {
    return File(getPatchDir(), "old-base.apk")
}

private fun UpdatePatch.getNewBaseApk(): File {
    return File(getPatchDir(), "new-base.apk")
}

private fun UpdatePatch.getChannelApk(): File {
    return File(getPatchDir(), "channel.apk")
}

/**
 * 扩展函数，处理空的 Flow
 */
private suspend fun <T> Flow<T>.onEmpty(action: suspend () -> Unit): Flow<T> {
    var isEmpty = true
    return onCompletion { if (isEmpty) action() }
        .map {
            isEmpty = false
            it
        }
}

@KeepClass
private class CatchUpdateException(override val cause: Throwable) : Exception()

private class SkipPatchException(
    val process: UpdateProcess,
    val msg: String,
    override val cause: Throwable? = null
) : Exception() {
    override fun toString(): String {
        return "SkipPatchException(process=${process.javaClass.name}, msg='$msg', cause=$cause)"
    }

    fun toMsg(): String {
        return toString()
    }
}