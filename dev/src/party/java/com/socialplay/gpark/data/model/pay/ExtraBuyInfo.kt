package com.socialplay.gpark.data.model.pay

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2023/09/14
 *     desc   :
 *
 */
data class ExtraBuyInfo(

    val member: <PERSON><PERSON><PERSON>,
    val token: String,
    val title: String,
    val describe: String,
    var goodId: String?,
    val expireTime: String,
    val goods: ArrayList<MemberGearPosition>,
    val sceneCode: String,
){
    fun getExtraBuyRealPrice(): Int {
        goods.find { it.goodId == this.goodId }?.let {
            return it.price
        }
        return 0
    }
}