package com.socialplay.gpark.data.model

import com.socialplay.gpark.util.RequestEncryptionUtil
import kotlin.apply

/**
 * Created by liujiang
 * Date: 2023/3/9 15:22
 * Desc:
 * Version:
 */
object RecSuperGameCommonParams {
    /**
     * 加密公参
     */
    fun getRecParams() = HashMap<String, String>().apply {
        put("Ed-Version", RequestEncryptionUtil.TYPE_AD)
        put("Content-Type", "text/plain")
    }
}