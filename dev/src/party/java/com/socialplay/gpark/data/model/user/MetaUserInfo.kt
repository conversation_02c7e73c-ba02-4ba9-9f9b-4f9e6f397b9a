package com.socialplay.gpark.data.model.user

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.meta.box.biz.friend.model.LabelInfo
import com.socialplay.gpark.data.model.editor.Balance
import com.socialplay.gpark.util.StringUtil
import kotlinx.parcelize.Parcelize


/**
 *  <AUTHOR> taiyuan
 *  time   : 2021/5/11
 *  desc   : 用户信息描述
 */
@Parcelize
data class MetaUserInfo(
    @SerializedName(value = "uuid", alternate = ["uid"])
    var uuid: String? = null,              //用户ID
    var nickname: String? = null,          //用户昵称
    var sessionId: String? = null,          //sessionId,用于联运游戏的登录
    @SerializedName(value = "phoneNumber", alternate = ["phone"])
    var phoneNumber: String? = null,        //绑定手机号
    var realNameSource: Int = -1, // 实名来源： 0 乐园 1 支付宝
    var portrait: String? = null,          //用户头像
    var visitor: Boolean = false,          //是否是游客账号
    var age: Int = 0,                      //年龄
    var gender: Int = 0,                    //性别
    var bindEmail: String? = null,              //邮箱
    @SerializedName(value = "bindIdCard", alternate = ["isBindIdCard"])
    var bindIdCard: Boolean = false,       //是否实名
    var bindPhone: Boolean = false,        //是否绑定手机号
    // todo 后期换成 com/meta/box/data/model/ThirdBindInfo.kt:13
    var bindQQ: Boolean = false,           //是否绑定QQ
    // todo 后期换成 com/meta/box/data/model/ThirdBindInfo.kt:14
    @SerializedName(value = "bindWeChat", alternate = ["bindWinxin"])
    var bindWeChat: Boolean = false,       //是否绑定微信
    var bindDouyin: Boolean = false,       //是否绑定抖音
    var bindKuaishou: Boolean = false,       //是否绑定快手
    var leyuanUserFirstLogin: Boolean = false,        // 是否乐园用户，首次登录派对

    var userNumber: String? = null,        //233号

    var account: String? = null, // Account
    var birth: Long? = null, // 生日
    var city: String? = null,
    var signature: String? = null,
    var tags: List<UserTagInfo>? = null, // 用户标签
    var wholeBodyImage: String?, // 角色全身像png
    var haveWholeBodyImage: Boolean? = null, // 是否有角色形象
    var bgMaskImage: String? = null, // 带背景的角色形象
    val userRemainLightUpQuantity: List<Balance>? = null,
    var thirdBindInfo: ThirdBindInfo?, // 第三方绑定信息
    val friendTotal: Long,//好友数
    val followCount: Long, // 关注数
    val fansCount: Long, // 粉丝数
    val likeCount: Long, // 点赞数
    val isFollow: Boolean, // 是否关注
    val labelInfo: LabelInfo?,
) : Parcelable {

    companion object {
        fun isOfficial(tags: List<UserTagInfo>?): Boolean {
            return tags?.any { it.id == UserTagInfo.TAG_OFFICIAL } == true
        }

        fun isCreator(tags: List<UserTagInfo>?): Boolean {
            return tags?.any { it.id == UserTagInfo.TAG_CREATOR } == true
        }
    }

    var ootdPrivateSwitch: Boolean? = false
    var followerShowSwitch: Boolean? = true//公开展示关注和粉丝列表的开关
    var firstBinding: Boolean = false // 是否为第一次绑定
    var chatMessageSwitch: Boolean? = true //

    val firstBind: Boolean
        get() {
            return firstBinding
        }

    val hasBindAccPwd get() = true

    val tagIds get() = tags?.map { it.id }

    fun getParentEmail(): String? {
        return thirdBindInfo?.parentEmail?.bindId
    }

    fun hasAccount(): Boolean? {
        return !account.isNullOrBlank()
                || phoneNumber.isNullOrEmpty()
                || !thirdBindInfo?.leyuan?.nickname.isNullOrBlank()
                || !userNumber.isNullOrBlank()
    }

    fun getEncryptedEmail(isFamily: Boolean): String {
        val mEmail = if (isFamily) getParentEmail() else bindEmail
        if (mEmail.isNullOrEmpty()) return ""
        val start = mEmail.substring(0, mEmail.indexOf("@"))
        val end = mEmail.substring(mEmail.indexOf("@"), mEmail.length)
        val sb = StringBuilder()
        sb.append(start.substring(0, minOf(3, start.length))).append("******").append(end)
        return sb.toString()
    }

    fun getEncryptedAccount(): String {
        if (!account.isNullOrEmpty() && account?.isNotBlank() == true) {
            return account!!
        }
        if (!phoneNumber.isNullOrEmpty()) {
            return StringUtil.getStarPhoneNumber(phoneNumber)
        }
        if (thirdBindInfo != null && thirdBindInfo?.leyuan != null) {
            if (thirdBindInfo?.leyuan?.nickname != null) {
                return thirdBindInfo!!.leyuan!!.nickname!!
            }
        }

        if (!userNumber.isNullOrEmpty() && userNumber?.isNotBlank() == true) {
            return userNumber!!
        }
        return ""
    }

    fun isBanned(): Boolean {
        return tags?.any { it.id == UserTagInfo.TAG_BANNED } == true
    }

    fun isOfficial(): Boolean {
        return isOfficial(tags)
    }

    fun canTryOn(): Boolean {
        return ootdPrivateSwitch == true
    }

    fun canShowFollower(): Boolean? {
        return followerShowSwitch
    }
}

@Parcelize
data class UserTagInfo(
    val id: Int,
    val name: String?
) : Parcelable {
    companion object {
        const val TAG_BANNED = 1
        const val TAG_OFFICIAL = 9877
        const val TAG_CREATOR = 9887

        fun isOfficial(tagId: Int) = tagId == TAG_OFFICIAL
        fun isCreator(tagId: Int) = tagId == TAG_CREATOR
    }
}