package com.socialplay.gpark.data.model.user

import com.socialplay.gpark.util.KeepClass

/**
 * 授权结果bean
 */
@KeepClass
data class WXAuthResult(
    val result: Int,
    // 成功/失败码
    val authCode: String,
    // 错误报错信息
    val errorMsg: String = ""
) {
    companion object {
        const val RESULT_OK = 1
        const val RESULT_CANCEL = 2
        const val RESULT_ERROR = 3

        fun authOk(authCode: String) = WXAuthResult(RESULT_OK, authCode)

        fun authError(errorMsg: String) = WXAuthResult(RESULT_ERROR, "", errorMsg =  errorMsg)

        fun authCancel() = WXAuthResult(RESULT_CANCEL, "")
    }

    fun isSucceed() = result == RESULT_OK

    fun isCancel() = result == RESULT_CANCEL

    fun isError() = result == RESULT_ERROR
}