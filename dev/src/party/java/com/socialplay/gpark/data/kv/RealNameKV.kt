package com.socialplay.gpark.data.kv

import com.socialplay.gpark.util.DateUtil
import com.tencent.mmkv.MMKV
import timber.log.Timber

/**
 * @author: ning.wang
 * @date: 2021-06-15 3:26 下午
 * @desc:
 */
class RealNameKV(private val mmkv: MMKV, private val metaKV: MetaKV) {

    companion object {
        //更新实名信息
        private const val UPDATE_REAL_NAME_INFO = "update_real_name_info"

        private const val KEY_LAST_GAME = "real_name_last_game"

        private const val KEY_REAL_NAME_RETRIEVE_POPUP_COUNT = "real_name_retrieve_popup_count"
        private const val KEY_DOWNLOAD_GAME_REAL_NAME_COUNT = "key_download_game_real_name_count"
        private const val KEY_DOWNLOAD_GAME_REAL_NAME_INTERVAL = "key_download_game_real_name_interval"
    }

    /**
     * 必须实名认证
     */
    fun mustUpdateRealName() {
        Timber.tag("mustUpdateRealName")
        mmkv.putBoolean(UPDATE_REAL_NAME_INFO, true)
    }

    /**
     * 需要实名认证
     */
    fun isNeedUpdate(): Boolean {
        val isNeed = mmkv.getBoolean(UPDATE_REAL_NAME_INFO, false)
        Timber.tag("isNeedUpdate: $isNeed")
        return isNeed
    }

    /**
     * 重置实名认证，默认：不需要更新
     */
    fun resetUpdateStatus() {
        Timber.tag("resetUpdateStatus")
        mmkv.putBoolean(UPDATE_REAL_NAME_INFO, false)
    }

    fun getLastGamePkgName(): String {
        return mmkv.getString(KEY_LAST_GAME, "") ?: ""
    }

    fun setLastGamePkgName(pkgName: String) {
        mmkv.putString(KEY_LAST_GAME, pkgName)
    }

    fun getRealNameFlexibleDialogShownCount(): Int {
        val dayOfYear = DateUtil.dayOfYear()
        val year = DateUtil.getYear()
        mmkv.remove("key_real_name_flexible_dialog_shown_count_${year}_${dayOfYear - 1}")
        return mmkv.getInt("key_real_name_flexible_dialog_shown_count_${year}_$dayOfYear", 0)
    }

    fun addRealNameFlexibleDialogShownCount() {
        val dayOfYear = DateUtil.dayOfYear()
        val year = DateUtil.getYear()
        val key = "key_real_name_flexible_dialog_shown_count_${year}_$dayOfYear"
        val count = mmkv.getInt(key, 0)
        mmkv.putInt(key, count + 1)
    }

    //实名挽留每日限制提示次数
    var realNameRetrieveShownCount: Int
        get() {
            if (metaKV.time.dayOnce(KEY_REAL_NAME_RETRIEVE_POPUP_COUNT)) {
                mmkv.putInt(KEY_REAL_NAME_RETRIEVE_POPUP_COUNT, 0)
            }
            return mmkv.getInt(KEY_REAL_NAME_RETRIEVE_POPUP_COUNT, 0)
        }
        set(value) {
            mmkv.putInt(KEY_REAL_NAME_RETRIEVE_POPUP_COUNT, value)
        }

    //下载游戏的每日限制实名提示次数
    var downloadGameRealNameCount: Int
        get() {
            if (metaKV.time.dayOnce(KEY_DOWNLOAD_GAME_REAL_NAME_COUNT)) {
                mmkv.putInt(KEY_DOWNLOAD_GAME_REAL_NAME_COUNT, 0)
            }
            return mmkv.getInt(KEY_DOWNLOAD_GAME_REAL_NAME_COUNT, 0)
        }
        set(value) {
            mmkv.putInt(KEY_DOWNLOAD_GAME_REAL_NAME_COUNT, value)
        }

    //实名提示 下载游戏的个数
    var downloadGameRealNameInterval: Int
        get() = mmkv.getInt(KEY_DOWNLOAD_GAME_REAL_NAME_INTERVAL, 0)
        set(value) {
            mmkv.putInt(KEY_DOWNLOAD_GAME_REAL_NAME_INTERVAL, value)
        }
}