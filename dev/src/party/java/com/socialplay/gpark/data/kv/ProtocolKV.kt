package com.socialplay.gpark.data.kv

import com.socialplay.gpark.util.property.MMKVScope
import com.socialplay.gpark.util.property.kvProperty
import com.tencent.mmkv.MMKV

/**
 * @author: ning.wang
 * @date: 2021-05-31 11:21 上午
 * @desc:
 */
class ProtocolKV(override val mmkv: MMKV) : MMKVScope {
    companion object {
        //是否已经同意协议
        private const val KEY_PROTOCOL_AGREE = "key_protocol_agree"

        // 账号设置中的个性化推荐
        private const val KEY_SETTING_RECOMMEND_TOGGLE = "key_setting_recommend_toggle"

        //更新之后的协议是否同意
        private const val KEY_UPDATE_PROTOCOL_AGREE = "key_update_protocol_agree"

        private const val KEY_PRIVACY_MODE_FLAG = "key_privacy_mode_flag"

        private const val KEY_NEW_USER_GUIDE_IMAGE_FLAG = "key_new_user_guide_image_flag"
    }


    fun getProtocolAgree(): Boolean {
        return mmkv.getBoolean(KEY_PROTOCOL_AGREE, false)
    }

    fun saveProtocolAgree() {
        mmkv.putBoolean(KEY_PROTOCOL_AGREE, true)
    }

    fun getUpdateProtocolAgree(): Boolean {
        return mmkv.getBoolean(KEY_UPDATE_PROTOCOL_AGREE, false)
    }

    fun saveUpdateProtocolAgree() {
        mmkv.putBoolean(KEY_UPDATE_PROTOCOL_AGREE, true)
    }


    fun getSettingRecommendToggle(): Boolean {
        return mmkv.getBoolean(KEY_SETTING_RECOMMEND_TOGGLE, true)
    }

    fun saveSettingRecommendToggle(isToggle: Boolean) {
        mmkv.putBoolean(KEY_SETTING_RECOMMEND_TOGGLE, isToggle)
    }

    fun isPrivacyMode() : Boolean{
        return mmkv.getBoolean(KEY_PRIVACY_MODE_FLAG, false)
    }

    fun setPrivacyMode(privacyMode: Boolean) {
        mmkv.putBoolean(KEY_PRIVACY_MODE_FLAG, privacyMode)
    }

    var needCheckLocationPermission by kvProperty(false)
    var hasAlwaysDeniedLocationPermission by kvProperty(false)
    var lastPlayGameActivityPausedTimestamp by kvProperty(0L)
    var needUgcCreatorProtocol by kvProperty(true)

    // 用户隐私设置，是否隐藏对应内容
    var privacyRecentActivity by kvProperty(true) // 最近在玩
    var privacyUserRelation by kvProperty(true) // 关注/粉丝
    var privacyFriendRecommend by kvProperty(true) // 关注/粉丝

    var needShowNewUserGuide by kvProperty(true, key = KEY_NEW_USER_GUIDE_IMAGE_FLAG)

}