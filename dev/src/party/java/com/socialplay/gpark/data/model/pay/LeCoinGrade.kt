package com.socialplay.gpark.data.model.pay

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2023/12/22
 *     desc   :
 *
 */
data class LeCoinGrade(
    val name: String,
    val coinCount: Int,
    val id: String,
    val originalPrice: Int,
    val price: Int,
    val sceneCode: Int,
    val type: Int,
    val attachJson: String? = null,
    val ceaseless: Boolean? = null,
    val mark: Int? = null,
    var rights: List<LeCoinGradeRight>? = null,
    val token: String? = null,
    val cornerText:String? = null

) {
    var isSel = false
}
data class LeCoinGradeRight(
    val name: String,
    val title: String?,
    val desc: String?,
    val count: String
)