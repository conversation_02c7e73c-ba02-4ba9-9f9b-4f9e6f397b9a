package com.socialplay.gpark.data.model

/**
 * create by: bin on 2022/12/7
 */
data class Recharge(
    var attachJson: String?,
    var ceaseless: Boolean?,
    var id: Int?,
    /**
     * 谷歌/苹果商品id
     */
    val goodsId: String?,
    var name: String?,
    var baseCoinNum: Long?,
    var originalPrice: Int?,
    var price: Int?,
    var sceneCode: Int?,
    var type: Int?,
    val icon: String?,
    val cornerText: String?,
    val awardCoinNum: Long?,
    val show: Boolean?,
)
/*
原始的json数据:
{
  "id": 10088,
  "name": "6800派对币",
  "type": 128,
  "ceaseless": false,
  "originalPrice": 6800,
  "price": 6800,
  "sceneCode": 172,
  "cornerText": "超合适",
  "attachJson": "{}",
  "mark": 0,
  "pack": "233party",
  "icon": "https://cdn.233xyx.com/online/4mCGOvUofe8u1722828392647.png",
  "baseCoinNum": 6800,
  "awardCoinNum": 280,
  "goodsId": "10088"
}
*/