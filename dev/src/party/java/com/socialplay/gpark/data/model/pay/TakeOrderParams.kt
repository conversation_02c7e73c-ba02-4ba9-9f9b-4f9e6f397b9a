package com.socialplay.gpark.data.model.pay

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2023/09/28
 *     desc   :
 *
 */
data class TakeOrderParams(
    val products: List<ProductParty>
)

data class ProductParty(
    var code: String? = null,
    val count: Int,
    var extra: String? = null,
    var appKey: String? = null,
    var name: String? = null,
    val originAmount: Int,
    var outOrderId: String? = null,
    val payAmount: Int,
    val giveToken: String? = null,//赠送乐币的token
    val voucher: Voucher? = null,
    val sceneCode: String,
    val attachJson: String? = null,
    val token: String? = null, //顺手买商品token
    val promotionToken: String? = null,//充值乐币赠送权益 促销token
    val nodeId: Int, //当前商品节点id
    val rootId: Int //所属节点Id（父级节点id）
)

data class Voucher(
    val voucherId: String?= null
)