package com.socialplay.gpark.data.model


/**
 * 登录方式
 * [埋点文档] https://meta.feishu.cn/wiki/L4vFwdeNQiK46DkJ4DRcnE0nnLg
 */
enum class LoginWay(val way: String) {
    Account("multip_usernumber"),
    <PERSON>("multip_phone"),
    <PERSON><PERSON><PERSON>("wechat"),
    <PERSON><PERSON>("qq"),
    <PERSON><PERSON>("leyuan"),
    <PERSON><PERSON>("visitor"),
    Qr<PERSON><PERSON>("qrcode"),
    <PERSON><PERSON><PERSON>("douyin"),
    <PERSON><PERSON><PERSON><PERSON>("kuaishou"),
    <PERSON><PERSON>("onekey");

    fun toLoginFromType(): LoginFromType? {
        return when (this) {
            LoginWay.Account -> LoginFromType.Account
            LoginWay.Phone -> LoginFromType.Phone
            LoginWay.Wechat -> LoginFromType.Wechat
            LoginWay.QQ -> LoginFromType.QQ
            LoginWay.Leyuan -> LoginFromType.Leyuan
            LoginWay.Tourist -> LoginFromType.Tourist
            LoginWay.QrCode -> LoginFromType.QrCode
            LoginWay.Douyin -> LoginFromType.Douyin
            LoginWay.KuaiShou -> LoginFromType.KuaiShou
            LoginWay.Onekey -> LoginFromType.Onekey
            else -> null
        }
    }


    companion object {
        fun getDefaultSignWay(): LoginWay {
            return Phone
        }

        fun getLoginType(typeStr: String?): LoginWay? {
            return when (typeStr) {
                Account.way -> Account
                Phone.way -> Phone
                Wechat.way -> Wechat
                QQ.way -> QQ
                Leyuan.way -> Leyuan
                Tourist.way -> Tourist
                QrCode.way -> QrCode
                Douyin.way -> Douyin
                KuaiShou.way -> KuaiShou
                Onekey.way -> Onekey
                else -> null
            }
        }
    }
}

enum class LoginFromType {
    Account, Phone, Wechat, QQ, Leyuan, Tourist, QrCode, Douyin, KuaiShou, Onekey
}

const val LOGIN_TYPE_GPARK_ID = "multip_usernumber"

object LoginTypes {
    fun checkValid(loginType: String?, loginKey: String? = null): Boolean {
        return when (loginType) {
            LoginWay.Account.way -> {
                !loginKey.isNullOrEmpty()
            }

            LoginWay.Wechat.way, LoginWay.QQ.way, LoginWay.Leyuan.way -> {
                true
            }

            LoginWay.Phone.way -> {
                true
            }

            else -> {
                false
            }
        }
    }

    fun checkIsAccountLogin(loginType: String?, loginKey: String? = null): Boolean {
        return loginType == LoginWay.Account.way && !loginKey.isNullOrEmpty()
    }

}