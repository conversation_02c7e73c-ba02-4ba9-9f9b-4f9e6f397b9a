package com.socialplay.gpark.data.api

import com.socialplay.gpark.data.model.realname.RealNameCheckEncryptBody
import com.meta.box.data.model.realname.RealNameCheckResult
import com.socialplay.gpark.data.base.ApiResult
import com.socialplay.gpark.data.model.AdAnalyticQueryBody
import com.socialplay.gpark.data.model.PayOrderInfo
import com.socialplay.gpark.data.model.RecSuperGameCommonParams
import com.socialplay.gpark.data.model.Recharge
import com.socialplay.gpark.data.model.UpdateInfo
import com.socialplay.gpark.data.model.pay.PayChannelList
import com.socialplay.gpark.data.model.pay.PayResultEntity
import com.socialplay.gpark.data.model.pay.TakeOrderInfo
import com.socialplay.gpark.data.model.pay.TakeOrderParams
import com.socialplay.gpark.data.model.pay.TakeOrderResult
import com.socialplay.gpark.data.model.pay.UserBalance
import com.socialplay.gpark.data.model.realname.RealNameAutoInfo
import com.socialplay.gpark.data.model.realname.RealNameCardNoName
import com.socialplay.gpark.data.model.realname.RealNameConfig
import com.socialplay.gpark.data.model.realname.RealNameSkinVip
import com.socialplay.gpark.data.model.realname.RealNameSurplusGameTime
import com.socialplay.gpark.data.model.user.AccountLoginRequest
import com.socialplay.gpark.data.model.user.AuthInfoApiResult
import com.socialplay.gpark.data.model.user.OneKeyLoginRequest
import com.socialplay.gpark.data.model.user.VerifyPhoneCode
import com.socialplay.gpark.data.model.user.WXLoginRequest
import retrofit2.http.Body
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.GET
import retrofit2.http.HeaderMap
import retrofit2.http.POST
import retrofit2.http.Query

interface MetaApiWrapper : MetaApi {

    /**
     * 游客登录（双token）
     * [mock](https://mock.metaapp.cn/project/132/interface/api/46657)
     * [需求文档](https://meta.feishu.cn/wiki/QkwPwaYb3i79hukgxeucvVxunrf)
     */
    @POST("authorize/v2/dtoken/visitor/sign")
    suspend fun visitorLoginDToken(): ApiResult<AuthInfoApiResult>

    /**
     * 统一的登录接口，通过参数区分登录方式
     * [mock](https://mock.metaapp.cn/project/132/interface/api/7502)
     */
    @POST("authorize/v2/dtoken/user/sign")
    suspend fun loginUnite(@Body request: AccountLoginRequest): ApiResult<AuthInfoApiResult>

    /**
     * 抖音登录
     */
    @POST("/auth/v1/douyin/login")
    suspend fun douyinLogin(@Body request: WXLoginRequest): ApiResult<AuthInfoApiResult>

    /**
     * 快手登录
     */
    @POST("/auth/v1/kuaishou/login")
    suspend fun kwaiLogin(@Body request: WXLoginRequest): ApiResult<AuthInfoApiResult>

    /**
     * 本机一键登录（双token）
     * [mock](https://mock.metaapp.cn/project/132/interface/api/35623)
     */
    @POST("auth/v1/phone/onekey/login")
    suspend fun loginByOneKey(@Body request: OneKeyLoginRequest): ApiResult<AuthInfoApiResult>

    /**
     * 获取手机号登录验证码（同时也承接其他短信验证码的业务，如找回密码等）
     */
    @POST("/authorize/v2/sms/phone/code/send")
    suspend fun getLoginPhoneCode(@Body map: Map<String, String>): ApiResult<Boolean>

//    /**
//     * 手机号绑定
//     *
//     * @param map
//     * @return
//     */
//    @POST("authorize/v2/third/binding")
//    suspend fun bindPhone(@Body request: ThirdBindRequest): ApiResult<Boolean>
//
//    /**
//     * 更换手机号绑定
//     *
//     * @param map
//     * @return
//     */
//    @POST("/multiplatform/auth/v1/phone/change")
//    suspend fun changeNewPhone(@Body map: Map<String, String>): ApiResult<Boolean>

    /**
     * 验证换绑手机号 短信验证码
     */
    @POST("/authorize/v2/sms/phone/code/validate")
    suspend fun verifyPhoneCode(@Body request: VerifyPhoneCode): ApiResult<Boolean>

//    /**
//     * 换绑获取原手机验证码
//     */
//    @POST("bind/v1/sms/rebind")
//    suspend fun getVerifyCode(@Body map: Map<String, String>): ApiResult<Boolean>
//
//    /**
//     * 换绑获取新手机验证码
//     */
//    @POST("bind/v1/sms/new/bind")
//    suspend fun getNewVerifyCode(@Body map: Map<String, String>): ApiResult<Boolean>
//
//    /**
//     * 换绑获取原手机验证码
//     */
//    @POST("bind/v1/rebind/phone/check")
//    suspend fun verifyCode(@Body map: Map<String, String>): ApiResult<JsonElement>

    /**
     * 重置密码
     */
    @POST("/multiplatform/auth/v1/password/reset")
    suspend fun accountPasswordReset(@Body map: Map<String, String>): ApiResult<Boolean>

    /**
     * 获取充值商品列表
     */
    @GET("member/v1/product/list?type=128&abGoodsIds=")
    suspend fun getRecharges(): ApiResult<ArrayList<Recharge>>

    /**
     * mock接口: https://mock.metaapp.cn/project/27/interface/api/20159
     * 游戏内购买道具
     */
    @POST("order/v1/pd/mw/pay")
    suspend fun partyMWPay(@Body map: Map<String, @JvmSuppressWildcards Any?>): ApiResult<Any?>

    /**
     * 获取支付渠道
     */
    @POST("/payment/v1/channel/helppay")
    suspend fun getPayChannels(@Body map: Map<String, String>): ApiResult<PayChannelList>

    /**
     * Party 充值
     */
    @POST("/order/v1/self/create")
    suspend fun privilegePlaceOrderParty(@Body takeOrderInfo: TakeOrderInfo): ApiResult<TakeOrderResult>

    /**
     * Party: 新版mgs/联运 创建预支付订单
     */
    @POST("order/v1/pay/prepay")
    suspend fun createOrderPayment(@Body payInfo: PayOrderInfo): ApiResult<PayResultEntity>

    /**
     * Party: 新版mgs/联运 取消支付
     */
    @FormUrlEncoded
    @POST("order/v1/cancel")
    suspend fun cancelOrder(@Field("orderCode") orderCode: String): ApiResult<Boolean>

    /**
     * 新版mgs/联运 支付订单轮训支付结果
     */
    @FormUrlEncoded
    @POST("order/v1/pay/query")
    suspend fun rechargingLoopParty(@Field("orderCode") orderCode: String): ApiResult<Boolean>

    @POST("/order/v3/combined/create")
    suspend fun takeOrderV3(@Body takeOrderParams: TakeOrderParams): ApiResult<TakeOrderResult>

    /**
     * 联运 创建帮付订单
     */
    @POST("payment/helppay/help/order/create")
    suspend fun createHelpPayOrder(@Body map: Map<String, String>): ApiResult<PayResultEntity>

    @POST("/order/v3/combined/pay")
    suspend fun createOrderV3Payment(@Body payInfo: PayOrderInfo): ApiResult<PayResultEntity>

    /* ====================================== 实名认证 start ====================================== */
    @GET("/realname/v2/game/award/query")
    suspend fun realNameSkinVipV2BySync(@Query("gameId") gameId: Long): ApiResult<RealNameSkinVip>

    // 锁区情况游戏剩余时长
    @POST("/kzRQtrXpWuZb/v3/pTXGUs/cQJCmMLI/ypIPkjODRrr")
    suspend fun getRealNameSurplusGameTimeV3(
        @Body map: HashMap<String, String>,
        @HeaderMap headers: Map<String, String> = RecSuperGameCommonParams.getRecParams()
    ): ApiResult<RealNameSurplusGameTime>

    /**
     * 实名认证未成年提示文案
     *
     * @return
     */
    @GET("/realname/v1/edit/config/query")
    suspend fun getRealNameConfig(@Query("gameId") gameId: String?): ApiResult<RealNameConfig>


    /**
     * 实名认证检查
     */
    @POST("realname/v1/auth/check")
    suspend fun realNameCheck(@Body paramsMap: RealNameCheckEncryptBody): ApiResult<RealNameCheckResult>

    /**
     * 实名认证信息存储
     */
    @POST("realname/v1/auth/save")
    suspend fun realNameSave(@Body paramsMap: RealNameCardNoName): ApiResult<Any>

    /**
     * 实名认证信息存储
     */
    @POST("realname/v1/auth/delete")
    suspend fun realNameClear(@Body paramsMap: RealNameCardNoName): ApiResult<Any>

    /**
     * 获取实名认证信息
     */
    @GET("realname/v1/auth/detail")
    suspend fun realNameDetail(): ApiResult<RealNameAutoInfo>

    /* ====================================== 实名认证 end ====================================== */

    /**
     * 获取更新信息
     */
    @GET("version/v1/package/get")
    suspend fun getUpdateInfo(@Query("realVersionCode") realVersionCode: Int): ApiResult<UpdateInfo>


    /**
     * 广告埋点上报
     */
    @POST("ad/v1/statistics/stats")
    suspend fun reportAdAnalytic(@Body body: AdAnalyticQueryBody): ApiResult<Boolean>
}