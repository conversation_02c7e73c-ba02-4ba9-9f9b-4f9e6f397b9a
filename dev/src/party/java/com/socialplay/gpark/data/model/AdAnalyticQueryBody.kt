package com.socialplay.gpark.data.model

/**
 * Created by bo.li
 * Date: 2021/8/24
 * Desc:
 */
data class AdAnalyticQueryBody (
    val android_id: String,
    val appId: String,
    val appInfo: AdAnalyticAppInfo,
    val channel_package: String,
    val channelid: String,
    val deviceInfo: AdAnalyticDeviceInfo,
    val device_brand: String,
    val device_model: String,
    val device_product: String,
    val device_sys: String,
    val device_sys_version: String,
    val download_pkg: String,
    val dspId: String,
    val gamePackageName: String,
    val id: String,
    val imei: String,
    val kind: String,
    val local_ip_address: String,
    val local_mac_address: String,
    val local_timestamp: String,
    val locationInfo: AdAnalyticLocationInfo,
    val netInfo: AdAnalyticNetInfo,
    val network_state: String,
    val oaid: String,
    val onlyId: String,
    val pandoraAbGroup: String,
    val pandoraNewAbGroup: String,
    val pandoraSwitchAbGroup: String,
    val pandoraSwitchNewAbGroup: String,
    val requestId: String,
    val selfpackagename: String,
    val serv_extras: String,
    val show_param1: Long,
    val smid: String,
    val tel_operator: String,
    val tel_operator_name: String,
    val timestamp: String,
    val uuid: String
)

data class AdAnalyticAppInfo(
    val appChannel: String,
    val appName: String,
    val appPackage: String,
    val appVersionCode: Int,
    val appVersionName: String,
)

data class AdAnalyticDeviceInfo(
    val androidId: String,
    val deviceBrand: String,
    val deviceManufacturer: String,
    val deviceModel: String,
    val deviceName: String,
    val deviceOs: String,
    val deviceProduct: String,
    val deviceSys: String,
    val deviceSysVersion: String,
    val imei: String,
    val oaid: String,
    val onlyId: String,
    val screenDensity: Int,
    val screenDensityDpi: Int,
    val screenHeight: Int,
    val screenWidth: Int
)

data class AdAnalyticLocationInfo(
    val latitude: Double,
    val longitude: Double
)

data class AdAnalyticNetInfo(
    val ipAddress: String,
    val macAddress: String,
    val networkState: Int,
    val telOperator: String,
    val telOperatorName: String
)