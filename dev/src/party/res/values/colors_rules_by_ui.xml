<?xml version="1.0" encoding="utf-8"?>
<resources>
    // 跟ui约定的规范 key value  ---------------------------------------start
    // ui可以根据风格更改key映射到value的值
    <color name="color_maincolor">#89EB05</color>
    <color name="colorcolor_secondarycolor">#A967F7</color>

    <color name="buttontext_01">#212121</color>
    <color name="buttontext_hover_01">#212121</color>
    <color name="buttontext_disabled_p30_01">#4DBDBDBD</color>
    <color name="buttontext_02">#212121</color>
    <color name="buttontext_03">#53A902</color>
    <color name="buttontext_hover_02">#212121</color>
    <color name="buttontext_disabled_02">#BDBDBD</color>

    <color name="button_primary">#89EB05</color>
    <color name="button_disabled_p30">#4D89EB05</color>
    <color name="button_hover">#6CCA03</color>
    <color name="button_lightcolor">#DFFD99</color>
    <color name="button_primary_02">#F5F5F5</color>
    <color name="button_disabled_02">#F5F5F5</color>
    <color name="button_hover_02">#E0E0E0</color>

    <color name="text_primary">#212121</color>
    <color name="text_primary_p30">#4D212121</color>
    <color name="text_secondary">#757575</color>
    <color name="text_disabled">#BDBDBD</color>
    <color name="text_400">#E0E0E0</color>
    <color name="text_white">#FFFFFF</color>
    <color name="text_white_p90">#E6FFFFFF</color>
    <color name="text_white_p60">#99FFFFFF</color>
    <color name="text_white_p40">#66FFFFFF</color>
    <color name="text_fontmaincolor">#53A902</color>

    <color name="base_background">#F5F5F5</color>
    <color name="base_border">#EEEEEE</color>
    <color name="base_mask_p70">#B3000000</color>
    <color name="base_whire">#FFFFFF</color>

    <color name="accent_error">#FF4B30</color>
    <color name="accent_error_disabled">#FFE8D5</color>
    <color name="accent_success">#7AC931</color>
    <color name="accent_link">#0C75FF</color>

    <color name="icon_normal">#212121</color>
    <color name="icon_secondary">#757575</color>
    <color name="icon_disable">#BDBDBD</color>
    <color name="icon_white">#FFFFFF</color>

    <color name="grey_100">#FAFAFA</color>
    <color name="grey_200">#F5F5F5</color>
    <color name="grey_300">#EEEEEE</color>
    <color name="grey_400">#E0E0E0</color>
    <color name="grey_500">#BDBDBD</color>
    <color name="grey_600">#757575</color>
    <color name="grey_700">#616161</color>
    <color name="grey_800">#424242</color>
    <color name="grey_900">#212121</color>

    <color name="icon_red">#FF4B30</color>

    <color name="base_mask_p20">#33000000</color>
    // 跟ui约定的key value  ---------------------------------------end
</resources>