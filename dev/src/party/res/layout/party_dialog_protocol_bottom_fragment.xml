<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom">

        <FrameLayout
            android:id="@+id/fl_title_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_white_top_corner_30"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:background="@color/white"
                android:paddingVertical="@dimen/dp_12"
                android:text="@string/dialog_tip"
                android:textColor="@color/color_212121"
                android:textSize="@dimen/sp_13"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/iv_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_marginTop="@dimen/dp_14"
                android:layout_marginEnd="@dimen/dp_14"
                android:src="@drawable/close_icon" />

        </FrameLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:paddingBottom="@dimen/dp_4"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/fl_title_bar">

            <TextView
                android:id="@+id/tv_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:text="用户协议、隐私政策、儿童隐私保护指引"
                android:textColor="@color/color_0C75FF"
                android:textSize="@dimen/sp_14"
                app:layout_constraintBottom_toTopOf="@id/tv_agree"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_agree"
                style="@style/Button.S18.PoppinsBlack900.Height46"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_28"
                android:layout_marginBottom="@dimen/dp_44"
                android:gravity="center"
                android:paddingVertical="@dimen/dp_13"
                android:text="@string/party_agree_and_continue"
                android:textColor="@color/color_212121"
                android:textSize="@dimen/sp_15"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_content" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>