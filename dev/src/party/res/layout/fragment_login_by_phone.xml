<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_agreement"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_32"
        android:layout_marginBottom="@dimen/dp_32"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent">


        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/cb_agree"
            android:layout_width="@dimen/dp_16"
            android:layout_height="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_1"
            android:layout_marginRight="@dimen/dp_2"
            android:background="@drawable/s_agree_check"
            android:button="@null"
            android:src="@drawable/backup_icon_unselected"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"

            />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_agreement"
            style="@style/MetaTextView.S12.PoppinsLight300"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_18"
            android:gravity="center_horizontal"
            android:textColor="#53535E"
            android:textSize="@dimen/sp_12"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:uiLineHeight="@dimen/dp_18"
            tools:ignore="RtlSymmetry"
            tools:text="I have read and agreed to the user agreement and privacy policy" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/v_hot_check"
        android:layout_width="@dimen/dp_80"
        android:layout_height="@dimen/dp_60"
        android:layout_marginLeft="-12dp"
        android:clickable="true"
        android:layout_marginTop="-22dp"
        app:layout_constraintLeft_toLeftOf="@id/cl_agreement"
        app:layout_constraintTop_toTopOf="@id/cl_agreement" />

    <ViewStub
        android:id="@+id/vs_login_phone"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout="@layout/layout_login_phone"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <ViewStub
        android:id="@+id/vs_login_phone_code"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout="@layout/layout_sms_code"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="gone" />


    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/loading"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>