<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="36dp"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:background="@color/white"
    tools:ignore="MissingDefaultResource">

    <ImageView
        android:id="@+id/ivChannelIcon"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:scaleType="fitCenter"
        android:src="@drawable/icon_wechat_pay"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvChannelName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_weight="1"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="#212121"
        android:textSize="14sp"
        android:textStyle="bold"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivChannelIcon"
        tools:text="微信支付" />

    <ImageView
        android:id="@+id/ivSelect"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:scaleType="fitCenter"
        android:src="@drawable/iap_third_pay_unchecked"
        tools:src="@drawable/iap_third_pay_checked" />

</LinearLayout>