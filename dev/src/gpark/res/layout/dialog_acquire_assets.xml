<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@color/white"
    android:paddingTop="@dimen/dp_16"
    android:paddingBottom="@dimen/dp_16"
    android:paddingStart="@dimen/dp_0"
    android:paddingEnd="@dimen/dp_0"
    android:clipToPadding="false"
    android:backgroundTint="@color/white"
    android:gravity="center_horizontal"
    style="@style/shapeTopRound16Style">

    <!-- 顶部标题栏 -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/dp_0"
        android:paddingEnd="@dimen/dp_0"
        android:paddingTop="@dimen/dp_0"
        android:paddingBottom="@dimen/dp_0">

        <TextView
            android:id="@+id/tv_title"
            style="@style/MetaTextView.S14.PoppinsSemiBold600"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/acquire_assets_title"
            android:textColor="@color/colorPrimaryDark"
            android:paddingTop="@dimen/dp_12"
            android:paddingBottom="@dimen/dp_12" />

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="@dimen/dp_20"
            android:layout_height="@dimen/dp_20"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:src="@drawable/ic_share_x"
            android:contentDescription="@string/close"
            android:padding="@dimen/dp_4" />
    </RelativeLayout>

    <!-- 商品信息区 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingStart="@dimen/dp_16"
        android:paddingEnd="@dimen/dp_16"
        android:paddingTop="@dimen/dp_12"
        android:paddingBottom="@dimen/dp_12"
        android:gravity="center_vertical">

        <ImageView
            android:id="@+id/iv_goods"
            android:layout_width="@dimen/dp_68"
            android:layout_height="@dimen/dp_68"
            android:scaleType="centerCrop"
            android:src="@drawable/icon_placeholder_img_default"
            android:background="@drawable/bg_round_16dp"
            android:contentDescription="@string/goods_image" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:paddingStart="@dimen/dp_12">

            <TextView
                android:id="@+id/tv_goods_name"
                style="@style/MetaTextView.S14.PoppinsMedium500"
                android:text="@string/goods_name_sample"
                android:textColor="@color/colorPrimaryDark"
                android:maxLines="1"
                android:ellipsize="end" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginTop="@dimen/dp_4">

                <ImageView
                    android:layout_width="@dimen/dp_18"
                    android:layout_height="@dimen/dp_18"
                    android:src="@drawable/ic_game_share_logo"
                    android:contentDescription="@string/currency_icon" />

                <TextView
                    android:id="@+id/tv_goods_price"
                    style="@style/MetaTextView.S14.PoppinsSemiBold600"
                    android:text="@string/goods_price_sample"
                    android:textColor="@color/colorPrimaryDark"
                    android:layout_marginStart="@dimen/dp_4" />
            </LinearLayout>

            <TextView
                android:id="@+id/tv_goods_sold"
                style="@style/MetaTextView.S10.PoppinsRegular400"
                android:text="@string/goods_sold_sample"
                android:textColor="@color/textColorSecondary"
                android:layout_marginTop="@dimen/dp_4" />
        </LinearLayout>
    </LinearLayout>

    <!-- 分割线 -->
    <View
        android:layout_width="@dimen/dp_343"
        android:layout_height="@dimen/dp_1"
        android:background="@color/dividerColor"
        android:layout_gravity="center_horizontal" />

    <!-- 游戏名和价格 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingStart="@dimen/dp_16"
        android:paddingEnd="@dimen/dp_16"
        android:paddingTop="@dimen/dp_12"
        android:paddingBottom="@dimen/dp_0"
        android:gravity="center_vertical|space_between">

        <TextView
            android:id="@+id/tv_game_name"
            style="@style/MetaTextView.S14.PoppinsRegular400"
            android:text="@string/game_name_sample"
            android:textColor="@color/colorPrimaryDark"
            android:maxLines="1"
            android:ellipsize="end" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="@dimen/dp_16"
                android:layout_height="@dimen/dp_16"
                android:src="@drawable/ic_game_share_logo"
                android:contentDescription="@string/currency_icon" />

            <TextView
                android:id="@+id/tv_game_price"
                style="@style/MetaTextView.S12.PoppinsSemiBold600"
                android:text="@string/game_price_sample"
                android:textColor="@color/colorPrimaryDark"
                android:layout_marginStart="@dimen/dp_4" />
        </LinearLayout>
    </LinearLayout>

    <!-- 余额 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingStart="@dimen/dp_16"
        android:paddingEnd="@dimen/dp_16"
        android:paddingTop="@dimen/dp_8"
        android:paddingBottom="@dimen/dp_0"
        android:gravity="center_vertical|space_between">

        <TextView
            android:id="@+id/tv_balance_label"
            style="@style/MetaTextView.S14.PoppinsRegular400"
            android:text="@string/balance_label"
            android:textColor="@color/colorPrimaryDark" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="@dimen/dp_16"
                android:layout_height="@dimen/dp_16"
                android:src="@drawable/ic_game_share_logo"
                android:contentDescription="@string/currency_icon" />

            <TextView
                android:id="@+id/tv_balance"
                style="@style/MetaTextView.S12.PoppinsSemiBold600"
                android:text="@string/balance_sample"
                android:textColor="@color/colorPrimaryDark"
                android:layout_marginStart="@dimen/dp_4" />
        </LinearLayout>
    </LinearLayout>

    <!-- 分割线 -->
    <View
        android:layout_width="@dimen/dp_343"
        android:layout_height="@dimen/dp_1"
        android:background="@color/dividerColor"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_12" />

    <!-- 创作者信息 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingStart="@dimen/dp_16"
        android:paddingEnd="@dimen/dp_16"
        android:paddingTop="@dimen/dp_12"
        android:paddingBottom="@dimen/dp_0"
        android:gravity="center_vertical|space_between">

        <TextView
            android:id="@+id/tv_creator_label"
            style="@style/MetaTextView.S14.PoppinsRegular400"
            android:text="@string/creator_label"
            android:textColor="@color/colorPrimaryDark" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="@dimen/dp_18"
                android:layout_height="@dimen/dp_18"
                android:src="@drawable/icon_default_avatar"
                android:background="@drawable/avatar_inner_stroke"
                android:contentDescription="@string/creator_avatar" />

            <TextView
                android:id="@+id/tv_creator_name"
                style="@style/MetaTextView.S12.PoppinsRegular400"
                android:text="@string/creator_name_sample"
                android:textColor="@color/textColorSecondary"
                android:layout_marginStart="@dimen/dp_4" />
        </LinearLayout>
    </LinearLayout>

    <!-- 支付按钮 -->
    <Button
        android:id="@+id/btn_pay"
        android:layout_width="@dimen/dp_279"
        android:layout_height="@dimen/dp_48"
        android:layout_marginTop="@dimen/dp_24"
        android:layout_gravity="center_horizontal"
        android:background="@drawable/bg_btn_pay_yellow"
        android:text="@string/pay_btn_sample"
        android:textColor="@color/colorPrimaryDark"
        style="@style/MetaTextView.S16.PoppinsSemiBold600" />

    <!-- Home Indicator 占位 -->
    <View
        android:layout_width="@dimen/dp_134"
        android:layout_height="@dimen/dp_5"
        android:layout_marginTop="@dimen/dp_12"
        android:background="@drawable/bg_home_indicator" />

</LinearLayout> 