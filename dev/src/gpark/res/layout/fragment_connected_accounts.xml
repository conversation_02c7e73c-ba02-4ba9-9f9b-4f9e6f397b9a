<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <!--顶栏-->
    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/statusBarPlaceholder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/tbl_title_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:isDividerVisible="false"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/statusBarPlaceholder"
        app:title_text="@string/account_setting_connected_accounts"
        app:title_text_color="@color/textColorPrimary" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_facebook"
        android:layout_width="match_parent"
        android:visibility="gone"
        android:layout_height="wrap_content"
        android:background="?attr/selectableItemBackground"
        android:minHeight="@dimen/dp_72"
        app:layout_constraintTop_toBottomOf="@id/tbl_title_bar">

        <ImageView
            android:id="@+id/iv_facebook_icon"
            android:layout_width="@dimen/dp_40"
            android:layout_height="@dimen/dp_40"
            android:layout_marginStart="@dimen/dp_24"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/icon_login_facebook" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/MetaTextView.S14.PoppinsRegular400"
            android:id="@+id/tv_facebook_status_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_8"
            android:text="@string/login_with_facebook"
            android:textSize="@dimen/sp_16"
            app:layout_constraintBottom_toTopOf="@+id/tv_facebook_nickname"
            app:layout_constraintStart_toEndOf="@id/iv_facebook_icon"
            app:layout_constraintTop_toTopOf="@id/iv_facebook_icon"
            app:layout_constraintVertical_chainStyle="packed" />


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_facebook_nickname"
            style="@style/MetaTextView.S15.PoppinsRegular400.CenterVertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_8"
            android:textSize="@dimen/sp_12"
            app:layout_constraintBottom_toBottomOf="@id/iv_facebook_icon"
            app:layout_constraintStart_toEndOf="@id/iv_facebook_icon"
            app:layout_constraintTop_toBottomOf="@+id/tv_facebook_status_title"
            tools:text="zucca" />


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_facebook_op"
            style="@style/MetaTextView.S12.PoppinsRegular400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:minWidth="@dimen/dp_86"
            android:paddingHorizontal="@dimen/dp_8"
            android:paddingVertical="@dimen/dp_6"
            android:textColor="@color/white"
            android:textSize="11sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/iv_facebook_more"
            app:layout_constraintTop_toTopOf="parent"
            tools:background="@drawable/bg_connected_accounts_connect"
            tools:text="@string/connected_accounts_op_connect" />

        <ImageView
            android:id="@+id/iv_facebook_more"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/icon_more_arrow_gray"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="@color/textColorPrimary" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginStart="@dimen/dp_24"
            android:background="@color/color_account_line"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_google"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/selectableItemBackground"
        android:minHeight="@dimen/dp_72"
        app:layout_constraintTop_toBottomOf="@id/cl_facebook">

        <ImageView
            android:id="@+id/iv_google_icon"
            android:layout_width="@dimen/dp_40"
            android:layout_height="@dimen/dp_40"
            android:layout_marginStart="@dimen/dp_24"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/icon_login_google" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_google_status_title"
            style="@style/MetaTextView.S14.PoppinsRegular400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_8"
            android:text="@string/login_with_google"
            android:textColor="@color/textColorPrimary"
            android:textSize="@dimen/sp_16"
            app:layout_constraintBottom_toTopOf="@+id/tv_google_nickname"
            app:layout_constraintStart_toEndOf="@id/iv_google_icon"
            app:layout_constraintTop_toTopOf="@id/iv_google_icon"
            app:layout_constraintVertical_chainStyle="packed" />


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_google_nickname"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_8"
            android:textColor="@color/textColorPrimary"
            android:textSize="@dimen/sp_12"
            app:layout_constraintBottom_toBottomOf="@id/iv_google_icon"
            app:layout_constraintStart_toEndOf="@id/iv_google_icon"
            app:layout_constraintTop_toBottomOf="@+id/tv_google_status_title"
            tools:text="zucca" />


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_google_op"
            style="@style/MetaTextView.S12.PoppinsRegular400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:minWidth="@dimen/dp_86"
            android:paddingHorizontal="@dimen/dp_8"
            android:paddingVertical="@dimen/dp_6"
            android:textColor="@color/textColorPrimary"
            android:textSize="11sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/iv_google_more"
            app:layout_constraintTop_toTopOf="parent"
            tools:background="@drawable/bg_connected_accounts_disconnect"
            tools:text="@string/connected_accounts_op_disconnect" />

        <ImageView
            android:id="@+id/iv_google_more"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/icon_more_arrow_gray"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="@color/textColorPrimary" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginStart="@dimen/dp_24"
            android:background="@color/color_account_line"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>


    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/vLoading"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black_10"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>