package com.socialplay.gpark.function.apm.leak

import android.app.Application
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.function.startup.core.ProcessType
import leakcanary.EventListener
import leakcanary.LeakCanary
import leakcanary.ToastEventListener
import timber.log.Timber

/**
 * Created by bo.li
 * Date: 2024/3/26
 * Desc: LeakCanary配置
 */
object LeakCanaryConfig {

    private lateinit var processType: ProcessType
    private lateinit var application: Application

    private val uploader by lazy {
        LeakUploader()
    }

    const val TAG = "check_leak_debug"

    fun init(application: Application, processType: ProcessType) {
        Timber.tag(TAG).i("init start")
        LeakCanaryConfig.application = application
        LeakCanaryConfig.processType = processType

        val analysisUploadListener = EventListener { event ->
            when (event) {
                is EventListener.Event.HeapAnalysisDone<*> -> {
                    Timber.tag(TAG).i("HeapAnalysisDone thread:${Thread.currentThread().name}")
                    uploader.upload(event.heapAnalysis)
                }

                is EventListener.Event.HeapAnalysisProgress -> {
                    Timber.tag(TAG)
                        .i("HeapAnalysisProgress step: ${event.step}, progress:${event.progressPercent}")
                }

                is EventListener.Event.HeapDumpFailed -> {
                    Timber.tag(TAG).e("HeapDumpFailed exception:${event.exception}")
                }

                else -> {
                    Timber.tag(TAG).d("heapAnalysis else: ${event}")
                }
            }
        }

        val listeners =
            LeakCanary.config.eventListeners - ToastEventListener + analysisUploadListener

        LeakCanary.config = LeakCanary.config.copy(
            eventListeners = listeners,
            dumpHeap = BuildConfig.LEAK_CANARY_ENABLE,
            retainedVisibleThreshold = 7
        )
        Timber.tag(TAG)
            .i("init end, enable:${BuildConfig.LEAK_CANARY_ENABLE} listener:${listeners}")
    }
}