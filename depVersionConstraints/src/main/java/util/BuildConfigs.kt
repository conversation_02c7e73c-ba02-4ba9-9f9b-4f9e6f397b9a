package util

import org.gradle.api.Project
import org.gradle.kotlin.dsl.extra

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/09/10
 * desc   :
 * </pre>
 */
object BuildConfigs {

    private const val STRING_LIST = "String[]"
    private const val STRING = "String"
    private const val INT = "int"
    private const val LONG = "long"
    private const val BOOLEAN = "boolean"

    fun fieldExtraString(project: Project, name: String): Triple<String, String, String> {
        val value = project.rootProject.extra.get(name).toString()
        return fieldString(name, value)
    }

    fun fieldBoolean(name: String, value: Boolean): Triple<String, String, String> {
        return field(BOOLEAN, name, value)
    }

    fun fieldVarBoolean(name: String, value: Boolean): Triple<String, String, String> {
        return field(BOOLEAN, name, varBoolean(value))
    }

    fun fieldVarInt(name: String, value: Int): Triple<String, String, String> {
        return field(INT, name, varInt(value))
    }

    fun fieldInt(name: String, value: Int): Triple<String, String, String> {
        return field(INT, name, value)
    }

    fun fieldLong(name: String, value: Long): Triple<String, String, String> {
        return field(LONG, name, value)
    }

    fun fieldVarString(name: String, value: String): Triple<String, String, String> {
        return field(STRING, name, varString(value))
    }

    fun fieldString(name: String, value: String): Triple<String, String, String> {
        return field(STRING, name, string(value))
    }

    fun fieldStringNullable(name: String, value: String?): Triple<String, String, String>? {
        return if (value == null) {
            null
        } else {
            fieldString(name, value)
        }
    }

    fun fieldStringList(name: String, value: String): Triple<String, String, String> {
        return Triple(STRING_LIST, name, value)
    }

    private fun field(type: String, name: String, value: Any): Triple<String, String, String> {
        return Triple(type, name, value.toString())
    }

    private fun varBoolean(value: Boolean): String {
        return "null == null ?  $value : false"
    }

    private fun varInt(value: Int): String {
        return "null == null ?  $value : 0"
    }

    private fun string(value: String): String {
        return "\"$value\""
    }

    private fun varString(value: String): String {
        return "null == null ?  \"$value\" : \"\""
    }
}