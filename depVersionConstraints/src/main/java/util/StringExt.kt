package util

import org.json.JSONArray

fun String.upperFirst(): String {
    if (isNullOrEmpty()) {
        return this
    }
    val chars = this.toCharArray()

    if (chars[0] in 'a'..'z') {
        chars[0] = chars[0].uppercaseChar()
    }

    return String(chars)

}

private fun String.toEnvArray(): Array<String> {
    //    {"http://test-oversea-api.meta-verse.co","http://test-oversea-api.meta-verse.co"}
    val openBraceIndex = this.indexOf("{")
    val closeBraceIndex = this.lastIndexOf("}")

    val jsonStr = StringBuilder(this)
        .replace(openBraceIndex, openBraceIndex + 1, "[")
        .replace(closeBraceIndex, closeBraceIndex + 1, "]")
        .toString()

    val jsonArray = JSONArray(jsonStr)

    val envArray = Array(jsonArray.length()) { "" }

    for (i in envArray.indices) {
        envArray[i] = jsonArray.getString(i)
    }

    return envArray
}