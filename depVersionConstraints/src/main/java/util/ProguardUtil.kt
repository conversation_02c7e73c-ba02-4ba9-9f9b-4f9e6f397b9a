package util

import org.gradle.api.Project
import java.io.File
import java.util.*

/**
 *     author : wei.zhu
 *     e-mail : <EMAIL>
 *     time   : 2023/02/28
 *     desc   :
 */
object ProguardUtil {

    //现成的字典 https://github.com/sollyu/gradle-proguard-dic/blob/master/proguard-dic-9.txt
    fun genDict(project: Project, fileName: String = "dic.txt") {
        val dicFile = File(project.buildDir, fileName)
        if (!project.buildDir.exists()) {
            project.buildDir.mkdirs()
        }

        val r = Random()
        val start = r.nextInt(1000) + 0x0100
        val end = start + 0x4000

        val codePointsUppers = (start..end).filter {
            Character.isValidCodePoint(it) && Character.isJavaIdentifierPart(it)
        }.shuffled()

        val codePointsLowers = codePointsUppers.toMutableList().shuffled()

        val dict = mutableListOf<Char>()

        for (i in codePointsUppers.indices) {
            val m = r.nextInt(codePointsUppers.size - 3)
            val n = m + 3

            (m..n).forEach {
                val upper = codePointsUppers[it]
                val lower = codePointsLowers[i]

                dict.add((upper + lower).toChar())
            }
        }


        dicFile.writeText(dict.joinToString(System.lineSeparator()))
    }
}