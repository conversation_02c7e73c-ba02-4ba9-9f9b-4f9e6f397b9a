import org.json.JSONArray
import util.DateUtil
import java.util.Date

enum class Flavor(val flag: String) {
    G_PARK("gpark"),
    PARTY("party")
}

enum class ItemType(val flag: String) {
    GRADLE_CONFIG("buildGradle"),
    BUILD_CONFIG("buildconfig"),
    MANIFEST("manifest"),
    RESOURCE("resource");
}

enum class BuildConfigType(val type: String) {
    AUTO(""),
    STRING_ARRAY("String[]"),
    STRING("String"),
    INT("int"),
    LONG("long"),
    BOOLEAN("boolean");
}

data class ConfigField<V>(
    val itemType: Set<ItemType>,
    val buildConfigType: BuildConfigType = BuildConfigType.AUTO,
    val name: String,
    val value: Map<Flavor, V>,
) {
    fun item(flavor: Flavor, type: ItemType, configs: EnvConfigs): Triple<String, String, String>? {
        val v = configs.getValFromProperties(flavor, name) ?: value[flavor] ?: return null
        when (type) {
            ItemType.GRADLE_CONFIG -> {
                return Triple("", name, v.toString())
            }

            ItemType.RESOURCE -> {
                return Triple("string", name.lowercase(), v.toString())
            }

            ItemType.MANIFEST -> {
                return Triple("", name, v.toString())
            }

            ItemType.BUILD_CONFIG -> {
                val buildType = if (buildConfigType == BuildConfigType.AUTO) {
                    when (v) {
                        is Boolean -> BuildConfigType.BOOLEAN
                        is Int -> BuildConfigType.INT
                        else -> BuildConfigType.STRING
                    }
                } else {
                    buildConfigType
                }
                // 这里故意加 null == null ?, 作用是避免编译器优化, 之后就可以通过反射修改 BuildConfig 的值
                return when (buildType) {
                    BuildConfigType.STRING -> {
                        Triple(buildType.type, name, string(v.toString()))
                    }
                    BuildConfigType.STRING_ARRAY -> {
                        Triple(buildType.type, name, stringArray(v.toString()))
                    }
                    else -> {
                        Triple(buildType.type, name, "null == null ? $v : $v")
                    }
                }
            }

            else -> {
                throw IllegalArgumentException("ConfigField.item(flavor: Flavor, type: ItemType) method not support $type")
            }
        }
    }
}

private fun string(value: String): String {
    return "null == null ? \"$value\" : \"\""
}

private fun stringArray(value: String): String {
    return "null == null ? new String[]$value : new String[]{}"
}

private fun int(value: String): String {
    return "null == null ? $value : $value"
}

private fun long(value: String): String {
    return "null == null ? ${value}L : ${value}L"
}

private fun bool(value: String): String {
    return "null == null ? $value : $value"
}

private fun String.toEnvArray(): Array<String> {
    //    {"http://test-oversea-api.meta-verse.co","http://test-oversea-api.meta-verse.co"}
    val openBraceIndex = this.indexOf("{")
    val closeBraceIndex = this.lastIndexOf("}")

    val jsonStr = StringBuilder(this)
        .replace(openBraceIndex, openBraceIndex + 1, "[")
        .replace(closeBraceIndex, closeBraceIndex + 1, "]")
        .toString()

    val jsonArray = JSONArray(jsonStr)

    val envArray = Array(jsonArray.length()) { "" }

    for (i in envArray.indices) {
        envArray[i] = jsonArray.getString(i)
    }

    return envArray
}

private fun String.getEnvValue(envType: String, envScope: String): String {
    val env: String = envType
    val envArray = envScope.toEnvArray()
    val indexOfCurrentEnv = envArray.indexOf(env)

    check(indexOfCurrentEnv >= 0) {
        "Env:$env not found in $envScope"
    }

    val envVariable = this.toEnvArray()

    return envVariable[indexOfCurrentEnv]
}

fun EnvConfigs.getBuildConfigs(flavor: Flavor, date: Date): Set<Triple<String, String, String>> {
    val buildConfigs = this.configs.filter { config ->
        config.value.containsKey(flavor) && config.itemType.contains(
            ItemType.BUILD_CONFIG
        )
    }.distinctBy { it.name }
    val buildConfigMap = buildConfigs.associateBy({ it.name }, {
        (getValFromProperties(flavor, it.name) ?: it.value[flavor]).toString()
    })

    val result = buildConfigs.mapNotNull { it.item(flavor, ItemType.BUILD_CONFIG, this) }.toMutableSet()
    val domainUrlPrefix = buildConfigMap["DOMAIN_URL_PREFIX"] ?: ""
    val domainName = buildConfigMap["DOMAIN_NAME"] ?: ""
    val envType = buildConfigMap["ENV_TYPE"] ?: ""
    val envScope = buildConfigMap["ENV_SCOPE"] ?: ""
    result.addAll(
        setOf(
            Triple(
                BuildConfigType.STRING.type,
                "VERSION_NAME",
                string(Version.varyName(flavor)),
            ),
            Triple(
                BuildConfigType.INT.type,
                "VERSION_CODE",
                int(Version.varyCode(flavor).toString()),
            ),
            Triple(
                BuildConfigType.STRING.type,
                "BUILD_TIME",
                string(DateUtil.buildTime(date)),
            ),
            Triple(
                BuildConfigType.LONG.type,
                "BUILD_ID",
                long(date.time.toString()),
            ),
            Triple(
                BuildConfigType.STRING.type,
                "PD_ENV_TYPE",
                string(buildConfigMap["ENV_TYPE"] ?: "")
            ),
            Triple(
                BuildConfigType.STRING.type,
                "BASE_URL",
                string(
                    "${domainUrlPrefix.getEnvValue(envType, envScope)}${
                        domainName.getEnvValue(envType, envScope)
                    }/"
                ),
            ),
        )
    )
    return result
}

fun EnvConfigs.getManifests(flavor: Flavor): Map<String, Any> {
    val configs = this.configs.filter { config ->
        config.value.containsKey(flavor) && config.itemType.contains(
            ItemType.MANIFEST
        )
    }.mapNotNull {
        it.item(flavor, ItemType.MANIFEST, this)
    }.toSet()
    val map = mutableMapOf<String, Any>()
    configs.forEach {
        map[it.second] = it.third
    }
    return map
}

fun EnvConfigs.getResPlaceholder(flavor: Flavor): Set<Triple<String, String, String>> {
    return this.configs.filter { config ->
        config.value.containsKey(flavor) && config.itemType.contains(
            ItemType.RESOURCE
        )
    }.mapNotNull {
        it.item(flavor, ItemType.RESOURCE, this)
    }.toSet()
}

fun EnvConfigs.getBuildGradleConfigs(flavor: Flavor): Map<String, String> {
    val configs = this.configs.filter { config ->
        config.value.containsKey(flavor) && config.itemType.contains(
            ItemType.GRADLE_CONFIG
        )
    }.mapNotNull {
        it.item(flavor, ItemType.GRADLE_CONFIG, this)
    }.toSet()
    val map = mutableMapOf<String, String>()
    configs.forEach {
        map[it.second] = it.third
    }
    println("EnvConfigs.getBuildGradleConfigs=${map}")
    return map
}