
ext.gradlePlugin = [:]
gradlePlugin.androidBuildGradle = "com.android.tools.build:gradle:8.2.2"
gradlePlugin.kotlinGradlePlugin = "org.jetbrains.kotlin:kotlin-gradle-plugin:2.0.0"
gradlePlugin.ksp = "com.google.devtools.ksp:com.google.devtools.ksp.gradle.plugin:2.0.0-1.0.21"
gradlePlugin.navigationSafeArgsGradlePlugin = "androidx.navigation:navigation-safe-args-gradle-plugin:2.5.1"
gradlePlugin.googleServices = "com.google.gms:google-services:4.3.15"
gradlePlugin.firebaseCrashlyticsGradle = "com.google.firebase:firebase-crashlytics-gradle:2.7.1"
gradlePlugin.appLovinQualityServiceGradlePlugin = "com.applovin.quality:AppLovinQualityServiceGradlePlugin:4.3.9"
gradlePlugin.aabResGuardPlugin = "com.bytedance.android:aabresguard-plugin:0.1.18"
gradlePlugin.mwPlugin = "com.meta.mw.gradle:mw-plugin:1.3.6"
gradlePlugin.metaPlugin = "com.meta.build.gradle:meta-plugin:2.5.0000"
gradlePlugin.metaCompatPlugin = "com.meta.compat:code-skip:0.0.48"
gradlePlugin.multilingual = "com.bin.android:plugin-multilingual:0.4.2"
gradlePlugin.fontscale = "com.meta.plugin:fontscale:0.0.5"



