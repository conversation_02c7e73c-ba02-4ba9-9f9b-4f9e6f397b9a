package com.socialplay.gpark.overseabridge.bridge

import android.app.Activity
import android.net.Uri
import com.socialplay.gpark.data.model.share.ShareData
import com.socialplay.gpark.function.overseabridge.bridge.ITiktokSdkBridge
import com.socialplay.gpark.util.ToastUtil

class TiktokSdkBridgeImpl : ITiktokSdkBridge {

    override fun isEnable(): Boolean {
        return false
    }

    override fun init() {
    }

    override fun share(videoUri: Uri, context: Activity, shareText: String) {
        ToastUtil.showShort(context, "not impl")
    }

    override fun share(context: Activity, shareData: ShareData): Boolean {
        ToastUtil.showShort(context, "not impl")
        return false
    }
}