package com.socialplay.gpark.overseabridge

import com.socialplay.gpark.function.overseabridge.IOverseaBridgeProvider
import com.socialplay.gpark.function.overseabridge.bridge.*
import com.socialplay.gpark.overseabridge.bridge.*

/**
 * 在子模块反射获取此实现
 */
class OverseaBridgeProviderImpl : IOverseaBridgeProvider {
    override fun provideFaceBookSdkBridge(): IFaceBookSdkBridge {
        return FaceBookSdkBridgeImpl()
    }

    override fun provideTiktokSdkBridge(): ITiktokSdkBridge {
        return TiktokSdkBridgeImpl()
    }

    override fun provideGoogleSdkBridge(): IGoogleSdkBridge {
        return GoogleSdkBridgeImpl()
    }

    override fun provideFirebaseSdkBridge(): IFirebaseSdkBridge {
        return FirebaseSdkBridgeImpl()
    }

    override fun provideAdSdkBridge(): IAdSdkBridge {
        return AdSdkBridgeImpl()
    }

    override fun provideFcmSdkBridge(): IFcmSdkBridge {
        return FcmSdkBridgeImpl()
    }

    override fun provideUpdateBridge(): IUpdateBride {
        return UpdateBridgeImpl()
    }

    override fun provideSolarEngineBridge(): ISolarEngineBridge {
        return SolarEngineSdkBridgeImpl()
    }

    override fun provideSnapchatSdkBridge(): ISnapchatSdkBridge {
        return SnapchatSdkBridgeImpl()
    }
}