import com.android.build.gradle.internal.api.BaseVariantOutputImpl
import java.text.SimpleDateFormat
import java.util.Locale

plugins {
    id("com.android.application")
    kotlin("android")
    id("kotlin-parcelize")
    id("androidx.navigation.safeargs.kotlin")
    id("com.bytedance.android.aabResGuard")
    id("fontscale-plugin")
}
val gradleConfigs:Map<String,String> = EnvConfigs.getBuildGradleConfigs(Flavor.G_PARK)

android {
    namespace = gradleConfigs["APPLICATION_ID"] + ".app"
    compileSdk = Version.COMPILE_SDK
    defaultConfig {
        applicationId = gradleConfigs["APPLICATION_ID"]
        minSdk = Version.MIN_SDK
        targetSdk = Version.TARGET_SDK
        versionCode = Version.varyCode(Flavor.G_PARK)
        versionName = Version.varyName(Flavor.G_PARK)

        ndk {
            abiFilters.add("arm64-v8a")
            abiFilters.add("armeabi-v7a")
        }

        EnvConfigs.getManifests(Flavor.G_PARK).forEach {
            manifestPlaceholders[it.key] = it.value
        }
    }

    signingConfigs {
        maybeCreate("release").run {
            storeFile = file(gradleConfigs["APK_SIGNING_FILE_PATH"] ?: "")
            keyAlias = gradleConfigs["APK_SIGNING_KEY_ALIAS"]
            keyPassword = gradleConfigs["APK_SIGNING_PASSWORD"]
            storePassword = gradleConfigs["APK_SIGNING_PASSWORD"]
            enableV1Signing = true
            enableV2Signing = true
        }
    }

    buildTypes {
        release {
            signingConfig = signingConfigs["release"]
            isShrinkResources = true
            isMinifyEnabled = true
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                file(project(":dev").file("proguard-rules.pro")),
                "proguard-rules.pro"
            )
        }
        debug {
            signingConfig = signingConfigs["release"]
            versionNameSuffix = "-debug"
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = "17"
    }

    buildFeatures {
        viewBinding = true
    }

    val main by sourceSets
    main.run {
        java.srcDirs(
            "src/main/java",
        )
    }

    bundle {
        language {
            // Specifies that the app bundle should not support
            // configuration APKs for language resources. These
            // resources are instead packaged with each base and
            // feature APK.
            enableSplit = false
        }
        abi {
            enableSplit = true
        }
    }

    applicationVariants.all {
        val variant = this
        outputs.map { it as BaseVariantOutputImpl }
            .forEach { output ->
                val date = SimpleDateFormat(
                    "yyyy-MM-dd_HH_mm_ss_SSS",
                    Locale.ROOT
                ).format(System.currentTimeMillis())

                if (output.outputFileName.endsWith(".apk")) {
                    val flavorName = output.outputFileName.split("-")[0]
                    val fileName =
                        "${flavorName}-${variant.buildType.name}-${variant.versionName}-${date}.apk"
                    output.outputFileName = fileName
                }
            }
    }

    assetPacks.add(":asset_engine_obb")
    packaging {
        jniLibs {
            excludes.add("lib/arm64-v8a/libagora_audio_beauty_extension.so")
            excludes.add("lib/armeabi-v7a/libagora_audio_beauty_extension.so")

            excludes.add("lib/armeabi-v7a/libagora_udrm3_extension.so")
            excludes.add("lib/arm64-v8a/libagora_udrm3_extension.so")



            excludes.add("lib/armeabi-v7a/libagora_ai_echo_cancellation_extension.so")
            excludes.add("lib/arm64-v8a/libagora_ai_echo_cancellation_extension.so")

            excludes.add("lib/armeabi-v7a/libagora_drm_loader_extension.so")
            excludes.add("lib/arm64-v8a/libagora_drm_loader_extension.so")
            useLegacyPackaging = true
        }

    }

    flavorDimensions += "app"

    productFlavors {
        create("Gpark") {
            dimension = "app"
            // Gpark-Def
            missingDimensionStrategy("product", "gpark", "party")
            missingDimensionStrategy("channel", "def", "douyin")
        }
    }
}

aabResGuard {

    // Mapping file used for incremental obfuscation
    //    mappingFile = file("mapping.txt").toPath()

    // White list rules
    whiteList = setOf(
        "*.R.raw.*",
        "*.R.drawable.icon",

        "*.R.array.rc_emoji_code",
        "*.R.array.rc_emoji_description",
        "*.R.string.rc_notification_channel_name",
        "*.R.string.rc_notification_ticker_text",
        "*.R.drawable.notification_small_icon",

        //UE
        "*.R.style.UE4SplashTheme",
        "*.R.drawable.ic_notification_simple",
        "*.R.drawable.ic_notification",
        "*.R.drawable.mwpageframe",
        "*.R.drawable.mwpageframe_portrait",

        // Google-services & Firebase
        "*.R.string.google_app_id",
        "*.R.string.gcm_defaultSenderId",
        "*.R.string.default_web_client_id",
        "*.R.string.ga_trackingId",
        "*.R.string.firebase_database_url",
        "*.R.string.google_api_key",
        "*.R.string.google_crash_reporting_api_key",
        "*.R.string.default_web_client_id",
        "*.R.string.gcm_defaultSenderId",
        "*.R.string.google_app_id",
        "*.R.string.google_crash_reporting_api_key",
        "*.R.string.google_storage_bucket",
        "*.R.string.project_id"
    )

    enableObfuscate = true

    // Obfuscated file name, must end with '.aab'
    obfuscatedBundleFileName = "obfuscated-app.aab"

    // Whether to allow the merge of duplicate resources
    mergeDuplicatedRes = false

    // Whether to allow filter files
    enableFilterFiles = false

    // switch of filter strings
    enableFilterStrings = false

    // strings will be filtered in this file
    unusedStringPath = file("unused.txt").canonicalPath

    // keep en,en-xx,zh,zh-xx etc. remove others.
    languageWhiteList = setOf("en", "zh")
}

fontScale {
//    targetBuildType = "release"
    classes = emptyList()
}

dependencies {
    implementation(Libs.FRAGMENT)
    implementation(Libs.TIMBER)
    implementation(Libs.PANDORA)
    implementation(Libs.LIFECYCLE_RUNTIME)
    implementation(Libs.LIFECYCLE_LIVEDATA)
    implementation(Libs.EXOPLAYER_UI)
    implementation(Libs.EXOPLAYER_CORE)
    implementation(project(":dev"))
//    implementation(project(":dev", configuration = "gparkDef"))
}



BuildFlow.init(project)